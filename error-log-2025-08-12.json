{"sessionInfo": {"startTime": "2025-08-12T08:01:00.658Z", "lastUpdate": "2025-08-12T08:18:59.511Z", "totalErrors": 143, "errorCounts": {"RETRY_ATTEMPT_getPurchaseDetails": 38, "RETRY_ATTEMPT_refundStatusBatch_2": 2, "BATCH_OPERATION_ERRORS_refundStatusBatch": 32, "BATCH_REFUND_CHECK_ERRORS_refundStatusBatch": 32, "RETRY_ATTEMPT_refundStatusBatch_1": 6, "RETRY_ATTEMPT_refundStatusBatch_0": 30, "RETRY_ATTEMPT_getTotalPurchased": 2, "DATA_RECOVERY_getTotalPurchased": 1}}, "errors": [{"id": "ERR_1754985683440_uegfsehk1", "timestamp": "2025-08-12T08:01:23.440Z", "type": "RETRY_ATTEMPT", "operation": "getPurchaseDetails", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for getPurchaseDetails: Not a number", "severity": "info", "context": {"contractAddress": "0:8D8A3E5F75898348CF8AFB2EB533E554DBE2615BDF66ED8C7CBDB37BDC54F209", "purchaseId": 3, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985683443_q2ney8gqc", "timestamp": "2025-08-12T08:01:23.443Z", "type": "RETRY_ATTEMPT", "operation": "refundStatusBatch_2", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for refundStatusBatch_2: Not a number", "severity": "info", "context": {"contractAddress": "0:8D8A3E5F75898348CF8AFB2EB533E554DBE2615BDF66ED8C7CBDB37BDC54F209", "totalPurchases": 3, "batchIndex": 2, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985683445_yfyllpx88", "timestamp": "2025-08-12T08:01:23.445Z", "type": "BATCH_OPERATION_ERRORS", "operation": "refundStatusBatch", "message": "Batch operation completed with 1/3 failures", "severity": "warning", "context": {"contractAddress": "0:8D8A3E5F75898348CF8AFB2EB533E554DBE2615BDF66ED8C7CBDB37BDC54F209", "totalPurchases": 3, "totalOperations": 3, "failedOperations": 1, "errors": [{"index": 2, "message": "Not a number"}]}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985683448_4d4mxzm40", "timestamp": "2025-08-12T08:01:23.448Z", "type": "BATCH_REFUND_CHECK_ERRORS", "operation": "refundStatusBatch", "message": "Failed to check refund status for 1/3 purchases", "severity": "warning", "context": {"contractAddress": "0:8D8A3E5F75898348CF8AFB2EB533E554DBE2615BDF66ED8C7CBDB37BDC54F209", "totalPurchases": 3, "successfulChecks": 2, "failedChecks": 1}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985711785_spivyokl5", "timestamp": "2025-08-12T08:01:51.785Z", "type": "RETRY_ATTEMPT", "operation": "getPurchaseDetails", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for getPurchaseDetails: Not a number", "severity": "info", "context": {"contractAddress": "0:6886C69FD7C0894C1D0B3E8E94FE84BCFC87C4EFBBBC03A512205A57C714FB28", "purchaseId": 2, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985711793_ou61wpq4t", "timestamp": "2025-08-12T08:01:51.793Z", "type": "RETRY_ATTEMPT", "operation": "refundStatusBatch_1", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for refundStatusBatch_1: Not a number", "severity": "info", "context": {"contractAddress": "0:6886C69FD7C0894C1D0B3E8E94FE84BCFC87C4EFBBBC03A512205A57C714FB28", "totalPurchases": 2, "batchIndex": 1, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985711795_h3eyz3e1e", "timestamp": "2025-08-12T08:01:51.795Z", "type": "BATCH_OPERATION_ERRORS", "operation": "refundStatusBatch", "message": "Batch operation completed with 1/2 failures", "severity": "warning", "context": {"contractAddress": "0:6886C69FD7C0894C1D0B3E8E94FE84BCFC87C4EFBBBC03A512205A57C714FB28", "totalPurchases": 2, "totalOperations": 2, "failedOperations": 1, "errors": [{"index": 1, "message": "Not a number"}]}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985711797_kym2b79m1", "timestamp": "2025-08-12T08:01:51.797Z", "type": "BATCH_REFUND_CHECK_ERRORS", "operation": "refundStatusBatch", "message": "Failed to check refund status for 1/2 purchases", "severity": "warning", "context": {"contractAddress": "0:6886C69FD7C0894C1D0B3E8E94FE84BCFC87C4EFBBBC03A512205A57C714FB28", "totalPurchases": 2, "successfulChecks": 1, "failedChecks": 1}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985720331_l7tr7amps", "timestamp": "2025-08-12T08:02:00.331Z", "type": "RETRY_ATTEMPT", "operation": "getPurchaseDetails", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for getPurchaseDetails: Not a number", "severity": "info", "context": {"contractAddress": "0:06B676A2429E7F2DA0E6173B5A9410CCB624E2B7BD660B54C2D8FEF42290C508", "purchaseId": 1, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985720333_sea3gclkg", "timestamp": "2025-08-12T08:02:00.333Z", "type": "RETRY_ATTEMPT", "operation": "refundStatusBatch_0", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for refundStatusBatch_0: Not a number", "severity": "info", "context": {"contractAddress": "0:06B676A2429E7F2DA0E6173B5A9410CCB624E2B7BD660B54C2D8FEF42290C508", "totalPurchases": 1, "batchIndex": 0, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985720334_pimhyok4l", "timestamp": "2025-08-12T08:02:00.334Z", "type": "BATCH_OPERATION_ERRORS", "operation": "refundStatusBatch", "message": "Batch operation completed with 1/1 failures", "severity": "critical", "context": {"contractAddress": "0:06B676A2429E7F2DA0E6173B5A9410CCB624E2B7BD660B54C2D8FEF42290C508", "totalPurchases": 1, "totalOperations": 1, "failedOperations": 1, "errors": [{"index": 0, "message": "Not a number"}]}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985720334_0q8tgho0y", "timestamp": "2025-08-12T08:02:00.334Z", "type": "BATCH_REFUND_CHECK_ERRORS", "operation": "refundStatusBatch", "message": "Failed to check refund status for 1/1 purchases", "severity": "critical", "context": {"contractAddress": "0:06B676A2429E7F2DA0E6173B5A9410CCB624E2B7BD660B54C2D8FEF42290C508", "totalPurchases": 1, "successfulChecks": 0, "failedChecks": 1}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985723867_t9nqu811f", "timestamp": "2025-08-12T08:02:03.867Z", "type": "RETRY_ATTEMPT", "operation": "getPurchaseDetails", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for getPurchaseDetails: Not a number", "severity": "info", "context": {"contractAddress": "0:369B3E84C723535ED7A5B02505374F32B5D0DB787C08F4E79CE2DBEBA07D3D2E", "purchaseId": 1, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985723888_ng8tjpf2u", "timestamp": "2025-08-12T08:02:03.894Z", "type": "RETRY_ATTEMPT", "operation": "refundStatusBatch_0", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for refundStatusBatch_0: Not a number", "severity": "info", "context": {"contractAddress": "0:369B3E84C723535ED7A5B02505374F32B5D0DB787C08F4E79CE2DBEBA07D3D2E", "totalPurchases": 1, "batchIndex": 0, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985723921_s3e0ij80h", "timestamp": "2025-08-12T08:02:03.921Z", "type": "BATCH_OPERATION_ERRORS", "operation": "refundStatusBatch", "message": "Batch operation completed with 1/1 failures", "severity": "critical", "context": {"contractAddress": "0:369B3E84C723535ED7A5B02505374F32B5D0DB787C08F4E79CE2DBEBA07D3D2E", "totalPurchases": 1, "totalOperations": 1, "failedOperations": 1, "errors": [{"index": 0, "message": "Not a number"}]}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985723944_ba7m0byr3", "timestamp": "2025-08-12T08:02:03.944Z", "type": "BATCH_REFUND_CHECK_ERRORS", "operation": "refundStatusBatch", "message": "Failed to check refund status for 1/1 purchases", "severity": "critical", "context": {"contractAddress": "0:369B3E84C723535ED7A5B02505374F32B5D0DB787C08F4E79CE2DBEBA07D3D2E", "totalPurchases": 1, "successfulChecks": 0, "failedChecks": 1}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985728071_ahjetza7e", "timestamp": "2025-08-12T08:02:08.071Z", "type": "RETRY_ATTEMPT", "operation": "getPurchaseDetails", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for getPurchaseDetails: Not a number", "severity": "info", "context": {"contractAddress": "0:564374B41D440D91501D204E6301390B95A1806252485718B22C2B212456D735", "purchaseId": 1, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985728072_6ggefhep2", "timestamp": "2025-08-12T08:02:08.072Z", "type": "RETRY_ATTEMPT", "operation": "refundStatusBatch_0", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for refundStatusBatch_0: Not a number", "severity": "info", "context": {"contractAddress": "0:564374B41D440D91501D204E6301390B95A1806252485718B22C2B212456D735", "totalPurchases": 1, "batchIndex": 0, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985728072_d8p7nfyvl", "timestamp": "2025-08-12T08:02:08.072Z", "type": "BATCH_OPERATION_ERRORS", "operation": "refundStatusBatch", "message": "Batch operation completed with 1/1 failures", "severity": "critical", "context": {"contractAddress": "0:564374B41D440D91501D204E6301390B95A1806252485718B22C2B212456D735", "totalPurchases": 1, "totalOperations": 1, "failedOperations": 1, "errors": [{"index": 0, "message": "Not a number"}]}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985728073_fr2hwk0kf", "timestamp": "2025-08-12T08:02:08.073Z", "type": "BATCH_REFUND_CHECK_ERRORS", "operation": "refundStatusBatch", "message": "Failed to check refund status for 1/1 purchases", "severity": "critical", "context": {"contractAddress": "0:564374B41D440D91501D204E6301390B95A1806252485718B22C2B212456D735", "totalPurchases": 1, "successfulChecks": 0, "failedChecks": 1}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985736292_s7m6j9n5o", "timestamp": "2025-08-12T08:02:16.292Z", "type": "RETRY_ATTEMPT", "operation": "getPurchaseDetails", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for getPurchaseDetails: Not a number", "severity": "info", "context": {"contractAddress": "0:93525F47F759C9CA7B37FBE18DED510531D2A85A8095020518BF7DCB9DB49908", "purchaseId": 1, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985736293_1pwr1b4sl", "timestamp": "2025-08-12T08:02:16.293Z", "type": "RETRY_ATTEMPT", "operation": "refundStatusBatch_0", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for refundStatusBatch_0: Not a number", "severity": "info", "context": {"contractAddress": "0:93525F47F759C9CA7B37FBE18DED510531D2A85A8095020518BF7DCB9DB49908", "totalPurchases": 1, "batchIndex": 0, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985736294_b7rzpzr67", "timestamp": "2025-08-12T08:02:16.294Z", "type": "BATCH_OPERATION_ERRORS", "operation": "refundStatusBatch", "message": "Batch operation completed with 1/1 failures", "severity": "critical", "context": {"contractAddress": "0:93525F47F759C9CA7B37FBE18DED510531D2A85A8095020518BF7DCB9DB49908", "totalPurchases": 1, "totalOperations": 1, "failedOperations": 1, "errors": [{"index": 0, "message": "Not a number"}]}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985736294_vj752v6zi", "timestamp": "2025-08-12T08:02:16.294Z", "type": "BATCH_REFUND_CHECK_ERRORS", "operation": "refundStatusBatch", "message": "Failed to check refund status for 1/1 purchases", "severity": "critical", "context": {"contractAddress": "0:93525F47F759C9CA7B37FBE18DED510531D2A85A8095020518BF7DCB9DB49908", "totalPurchases": 1, "successfulChecks": 0, "failedChecks": 1}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985788054_ofp1oenyp", "timestamp": "2025-08-12T08:03:08.054Z", "type": "RETRY_ATTEMPT", "operation": "getPurchaseDetails", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for getPurchaseDetails: Not a number", "severity": "info", "context": {"contractAddress": "0:B9C6A85BBFC31FC5DD5CED6A77B0EF27B5FC151F1141ADF35A2058A3F2E5A2D1", "purchaseId": 1, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985788057_cqzj4rrcj", "timestamp": "2025-08-12T08:03:08.057Z", "type": "RETRY_ATTEMPT", "operation": "refundStatusBatch_0", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for refundStatusBatch_0: Not a number", "severity": "info", "context": {"contractAddress": "0:B9C6A85BBFC31FC5DD5CED6A77B0EF27B5FC151F1141ADF35A2058A3F2E5A2D1", "totalPurchases": 1, "batchIndex": 0, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985788058_erpnqrgdx", "timestamp": "2025-08-12T08:03:08.058Z", "type": "BATCH_OPERATION_ERRORS", "operation": "refundStatusBatch", "message": "Batch operation completed with 1/1 failures", "severity": "critical", "context": {"contractAddress": "0:B9C6A85BBFC31FC5DD5CED6A77B0EF27B5FC151F1141ADF35A2058A3F2E5A2D1", "totalPurchases": 1, "totalOperations": 1, "failedOperations": 1, "errors": [{"index": 0, "message": "Not a number"}]}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985788061_l9b5eiwlg", "timestamp": "2025-08-12T08:03:08.061Z", "type": "BATCH_REFUND_CHECK_ERRORS", "operation": "refundStatusBatch", "message": "Failed to check refund status for 1/1 purchases", "severity": "critical", "context": {"contractAddress": "0:B9C6A85BBFC31FC5DD5CED6A77B0EF27B5FC151F1141ADF35A2058A3F2E5A2D1", "totalPurchases": 1, "successfulChecks": 0, "failedChecks": 1}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985798369_roflczgq1", "timestamp": "2025-08-12T08:03:18.369Z", "type": "RETRY_ATTEMPT", "operation": "getPurchaseDetails", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for getPurchaseDetails: Not a number", "severity": "info", "context": {"contractAddress": "0:ECC5D3C5ED8BF16B4F9D6C70FFB3CFAE6779171168850941CCD841558B42B8C2", "purchaseId": 1, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985798370_ql8t7v6tb", "timestamp": "2025-08-12T08:03:18.370Z", "type": "RETRY_ATTEMPT", "operation": "refundStatusBatch_0", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for refundStatusBatch_0: Not a number", "severity": "info", "context": {"contractAddress": "0:ECC5D3C5ED8BF16B4F9D6C70FFB3CFAE6779171168850941CCD841558B42B8C2", "totalPurchases": 3, "batchIndex": 0, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985799383_y66tiekwl", "timestamp": "2025-08-12T08:03:19.383Z", "type": "RETRY_ATTEMPT", "operation": "getPurchaseDetails", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for getPurchaseDetails: Not a number", "severity": "info", "context": {"contractAddress": "0:ECC5D3C5ED8BF16B4F9D6C70FFB3CFAE6779171168850941CCD841558B42B8C2", "purchaseId": 2, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985799385_9axjsenge", "timestamp": "2025-08-12T08:03:19.385Z", "type": "RETRY_ATTEMPT", "operation": "refundStatusBatch_1", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for refundStatusBatch_1: Not a number", "severity": "info", "context": {"contractAddress": "0:ECC5D3C5ED8BF16B4F9D6C70FFB3CFAE6779171168850941CCD841558B42B8C2", "totalPurchases": 3, "batchIndex": 1, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985800367_gz9k27u0r", "timestamp": "2025-08-12T08:03:20.367Z", "type": "RETRY_ATTEMPT", "operation": "getPurchaseDetails", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for getPurchaseDetails: Not a number", "severity": "info", "context": {"contractAddress": "0:ECC5D3C5ED8BF16B4F9D6C70FFB3CFAE6779171168850941CCD841558B42B8C2", "purchaseId": 3, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985800369_ylumgdsyg", "timestamp": "2025-08-12T08:03:20.369Z", "type": "RETRY_ATTEMPT", "operation": "refundStatusBatch_2", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for refundStatusBatch_2: Not a number", "severity": "info", "context": {"contractAddress": "0:ECC5D3C5ED8BF16B4F9D6C70FFB3CFAE6779171168850941CCD841558B42B8C2", "totalPurchases": 3, "batchIndex": 2, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985800370_vda5d806o", "timestamp": "2025-08-12T08:03:20.370Z", "type": "BATCH_OPERATION_ERRORS", "operation": "refundStatusBatch", "message": "Batch operation completed with 3/3 failures", "severity": "critical", "context": {"contractAddress": "0:ECC5D3C5ED8BF16B4F9D6C70FFB3CFAE6779171168850941CCD841558B42B8C2", "totalPurchases": 3, "totalOperations": 3, "failedOperations": 3, "errors": [{"index": 0, "message": "Not a number"}, {"index": 1, "message": "Not a number"}, {"index": 2, "message": "Not a number"}]}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985800371_5cm1rbgvo", "timestamp": "2025-08-12T08:03:20.371Z", "type": "BATCH_REFUND_CHECK_ERRORS", "operation": "refundStatusBatch", "message": "Failed to check refund status for 3/3 purchases", "severity": "critical", "context": {"contractAddress": "0:ECC5D3C5ED8BF16B4F9D6C70FFB3CFAE6779171168850941CCD841558B42B8C2", "totalPurchases": 3, "successfulChecks": 0, "failedChecks": 3}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985807339_mi03h1pe7", "timestamp": "2025-08-12T08:03:27.339Z", "type": "RETRY_ATTEMPT", "operation": "getPurchaseDetails", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for getPurchaseDetails: Not a number", "severity": "info", "context": {"contractAddress": "0:334D76AE398EA3254E9DBC2D13A87A667AF796EAF23648EF2F949D5E65E48BA4", "purchaseId": 1, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985807344_n0msv6sci", "timestamp": "2025-08-12T08:03:27.344Z", "type": "RETRY_ATTEMPT", "operation": "refundStatusBatch_0", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for refundStatusBatch_0: Not a number", "severity": "info", "context": {"contractAddress": "0:334D76AE398EA3254E9DBC2D13A87A667AF796EAF23648EF2F949D5E65E48BA4", "totalPurchases": 2, "batchIndex": 0, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985808357_xse0f5vsr", "timestamp": "2025-08-12T08:03:28.357Z", "type": "RETRY_ATTEMPT", "operation": "getPurchaseDetails", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for getPurchaseDetails: Not a number", "severity": "info", "context": {"contractAddress": "0:334D76AE398EA3254E9DBC2D13A87A667AF796EAF23648EF2F949D5E65E48BA4", "purchaseId": 2, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985808358_m8903xihc", "timestamp": "2025-08-12T08:03:28.358Z", "type": "RETRY_ATTEMPT", "operation": "refundStatusBatch_1", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for refundStatusBatch_1: Not a number", "severity": "info", "context": {"contractAddress": "0:334D76AE398EA3254E9DBC2D13A87A667AF796EAF23648EF2F949D5E65E48BA4", "totalPurchases": 2, "batchIndex": 1, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985808359_3wqilz8us", "timestamp": "2025-08-12T08:03:28.359Z", "type": "BATCH_OPERATION_ERRORS", "operation": "refundStatusBatch", "message": "Batch operation completed with 2/2 failures", "severity": "critical", "context": {"contractAddress": "0:334D76AE398EA3254E9DBC2D13A87A667AF796EAF23648EF2F949D5E65E48BA4", "totalPurchases": 2, "totalOperations": 2, "failedOperations": 2, "errors": [{"index": 0, "message": "Not a number"}, {"index": 1, "message": "Not a number"}]}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985808360_bwlwo8405", "timestamp": "2025-08-12T08:03:28.360Z", "type": "BATCH_REFUND_CHECK_ERRORS", "operation": "refundStatusBatch", "message": "Failed to check refund status for 2/2 purchases", "severity": "critical", "context": {"contractAddress": "0:334D76AE398EA3254E9DBC2D13A87A667AF796EAF23648EF2F949D5E65E48BA4", "totalPurchases": 2, "successfulChecks": 0, "failedChecks": 2}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985843398_hxeoncn5e", "timestamp": "2025-08-12T08:04:03.398Z", "type": "RETRY_ATTEMPT", "operation": "getPurchaseDetails", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for getPurchaseDetails: Not a number", "severity": "info", "context": {"contractAddress": "0:92B601E6B6DA3499F4DBB956D435125B64900FFC57405757DFA0A2A2524FB541", "purchaseId": 1, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985843399_da9tsp3ld", "timestamp": "2025-08-12T08:04:03.399Z", "type": "RETRY_ATTEMPT", "operation": "refundStatusBatch_0", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for refundStatusBatch_0: Not a number", "severity": "info", "context": {"contractAddress": "0:92B601E6B6DA3499F4DBB956D435125B64900FFC57405757DFA0A2A2524FB541", "totalPurchases": 1, "batchIndex": 0, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985843400_xa56pzl5w", "timestamp": "2025-08-12T08:04:03.400Z", "type": "BATCH_OPERATION_ERRORS", "operation": "refundStatusBatch", "message": "Batch operation completed with 1/1 failures", "severity": "critical", "context": {"contractAddress": "0:92B601E6B6DA3499F4DBB956D435125B64900FFC57405757DFA0A2A2524FB541", "totalPurchases": 1, "totalOperations": 1, "failedOperations": 1, "errors": [{"index": 0, "message": "Not a number"}]}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985843401_chxmgpp0n", "timestamp": "2025-08-12T08:04:03.401Z", "type": "BATCH_REFUND_CHECK_ERRORS", "operation": "refundStatusBatch", "message": "Failed to check refund status for 1/1 purchases", "severity": "critical", "context": {"contractAddress": "0:92B601E6B6DA3499F4DBB956D435125B64900FFC57405757DFA0A2A2524FB541", "totalPurchases": 1, "successfulChecks": 0, "failedChecks": 1}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985860826_bk0doa2ju", "timestamp": "2025-08-12T08:04:20.826Z", "type": "RETRY_ATTEMPT", "operation": "getPurchaseDetails", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for getPurchaseDetails: Not a number", "severity": "info", "context": {"contractAddress": "0:4603B642BFA3050E1C32A75B95BCE46550F76B8C6ED95648685BFB9050E5939E", "purchaseId": 1, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985860827_o3nwey74s", "timestamp": "2025-08-12T08:04:20.827Z", "type": "RETRY_ATTEMPT", "operation": "refundStatusBatch_0", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for refundStatusBatch_0: Not a number", "severity": "info", "context": {"contractAddress": "0:4603B642BFA3050E1C32A75B95BCE46550F76B8C6ED95648685BFB9050E5939E", "totalPurchases": 1, "batchIndex": 0, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985860828_hll0136ft", "timestamp": "2025-08-12T08:04:20.828Z", "type": "BATCH_OPERATION_ERRORS", "operation": "refundStatusBatch", "message": "Batch operation completed with 1/1 failures", "severity": "critical", "context": {"contractAddress": "0:4603B642BFA3050E1C32A75B95BCE46550F76B8C6ED95648685BFB9050E5939E", "totalPurchases": 1, "totalOperations": 1, "failedOperations": 1, "errors": [{"index": 0, "message": "Not a number"}]}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985860828_ielt7rvxa", "timestamp": "2025-08-12T08:04:20.828Z", "type": "BATCH_REFUND_CHECK_ERRORS", "operation": "refundStatusBatch", "message": "Failed to check refund status for 1/1 purchases", "severity": "critical", "context": {"contractAddress": "0:4603B642BFA3050E1C32A75B95BCE46550F76B8C6ED95648685BFB9050E5939E", "totalPurchases": 1, "successfulChecks": 0, "failedChecks": 1}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985871847_o69tkgeuc", "timestamp": "2025-08-12T08:04:31.847Z", "type": "RETRY_ATTEMPT", "operation": "getPurchaseDetails", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for getPurchaseDetails: Not a number", "severity": "info", "context": {"contractAddress": "0:A50A499A1FC7BBB7D1A167598545E95BF61524725CE9B1F710DC05C4E3F64193", "purchaseId": 1, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985871849_7qdl4xrdr", "timestamp": "2025-08-12T08:04:31.849Z", "type": "RETRY_ATTEMPT", "operation": "refundStatusBatch_0", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for refundStatusBatch_0: Not a number", "severity": "info", "context": {"contractAddress": "0:A50A499A1FC7BBB7D1A167598545E95BF61524725CE9B1F710DC05C4E3F64193", "totalPurchases": 1, "batchIndex": 0, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985871850_6amsexpw2", "timestamp": "2025-08-12T08:04:31.850Z", "type": "BATCH_OPERATION_ERRORS", "operation": "refundStatusBatch", "message": "Batch operation completed with 1/1 failures", "severity": "critical", "context": {"contractAddress": "0:A50A499A1FC7BBB7D1A167598545E95BF61524725CE9B1F710DC05C4E3F64193", "totalPurchases": 1, "totalOperations": 1, "failedOperations": 1, "errors": [{"index": 0, "message": "Not a number"}]}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985871852_445lywedy", "timestamp": "2025-08-12T08:04:31.852Z", "type": "BATCH_REFUND_CHECK_ERRORS", "operation": "refundStatusBatch", "message": "Failed to check refund status for 1/1 purchases", "severity": "critical", "context": {"contractAddress": "0:A50A499A1FC7BBB7D1A167598545E95BF61524725CE9B1F710DC05C4E3F64193", "totalPurchases": 1, "successfulChecks": 0, "failedChecks": 1}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985884942_n5wdg8a94", "timestamp": "2025-08-12T08:04:44.942Z", "type": "RETRY_ATTEMPT", "operation": "getPurchaseDetails", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for getPurchaseDetails: Not a number", "severity": "info", "context": {"contractAddress": "0:B2D7B<PERSON>CCDE5CADA298FBEEEB5C3FB762F050F17320B8138BCD6060CD879159A", "purchaseId": 1, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985884944_pqv81frcb", "timestamp": "2025-08-12T08:04:44.944Z", "type": "RETRY_ATTEMPT", "operation": "refundStatusBatch_0", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for refundStatusBatch_0: Not a number", "severity": "info", "context": {"contractAddress": "0:B2D7B<PERSON>CCDE5CADA298FBEEEB5C3FB762F050F17320B8138BCD6060CD879159A", "totalPurchases": 3, "batchIndex": 0, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985885907_lyo01viln", "timestamp": "2025-08-12T08:04:45.907Z", "type": "BATCH_OPERATION_ERRORS", "operation": "refundStatusBatch", "message": "Batch operation completed with 1/3 failures", "severity": "warning", "context": {"contractAddress": "0:B2D7B<PERSON>CCDE5CADA298FBEEEB5C3FB762F050F17320B8138BCD6060CD879159A", "totalPurchases": 3, "totalOperations": 3, "failedOperations": 1, "errors": [{"index": 0, "message": "Not a number"}]}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985885909_v2jfsmlgh", "timestamp": "2025-08-12T08:04:45.909Z", "type": "BATCH_REFUND_CHECK_ERRORS", "operation": "refundStatusBatch", "message": "Failed to check refund status for 1/3 purchases", "severity": "warning", "context": {"contractAddress": "0:B2D7B<PERSON>CCDE5CADA298FBEEEB5C3FB762F050F17320B8138BCD6060CD879159A", "totalPurchases": 3, "successfulChecks": 2, "failedChecks": 1}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985893315_3jkkceub6", "timestamp": "2025-08-12T08:04:53.315Z", "type": "RETRY_ATTEMPT", "operation": "getPurchaseDetails", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for getPurchaseDetails: Not a number", "severity": "info", "context": {"contractAddress": "0:08357E0E4502BCD5401930BAF338468F980A8E4AFA583FC59A50B04CB86336B6", "purchaseId": 1, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985893316_r0kp1m6qb", "timestamp": "2025-08-12T08:04:53.316Z", "type": "RETRY_ATTEMPT", "operation": "refundStatusBatch_0", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for refundStatusBatch_0: Not a number", "severity": "info", "context": {"contractAddress": "0:08357E0E4502BCD5401930BAF338468F980A8E4AFA583FC59A50B04CB86336B6", "totalPurchases": 1, "batchIndex": 0, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985893316_s6ps941fq", "timestamp": "2025-08-12T08:04:53.316Z", "type": "BATCH_OPERATION_ERRORS", "operation": "refundStatusBatch", "message": "Batch operation completed with 1/1 failures", "severity": "critical", "context": {"contractAddress": "0:08357E0E4502BCD5401930BAF338468F980A8E4AFA583FC59A50B04CB86336B6", "totalPurchases": 1, "totalOperations": 1, "failedOperations": 1, "errors": [{"index": 0, "message": "Not a number"}]}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985893317_ada0ol2ze", "timestamp": "2025-08-12T08:04:53.317Z", "type": "BATCH_REFUND_CHECK_ERRORS", "operation": "refundStatusBatch", "message": "Failed to check refund status for 1/1 purchases", "severity": "critical", "context": {"contractAddress": "0:08357E0E4502BCD5401930BAF338468F980A8E4AFA583FC59A50B04CB86336B6", "totalPurchases": 1, "successfulChecks": 0, "failedChecks": 1}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985910838_dhp6xni2y", "timestamp": "2025-08-12T08:05:10.838Z", "type": "RETRY_ATTEMPT", "operation": "getPurchaseDetails", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for getPurchaseDetails: Not a number", "severity": "info", "context": {"contractAddress": "0:3CF34039565629EBCFA7E44BED8F5F074739B1186DE12BD9F4E980A688011CA3", "purchaseId": 1, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985910839_47z5jz3ty", "timestamp": "2025-08-12T08:05:10.839Z", "type": "RETRY_ATTEMPT", "operation": "refundStatusBatch_0", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for refundStatusBatch_0: Not a number", "severity": "info", "context": {"contractAddress": "0:3CF34039565629EBCFA7E44BED8F5F074739B1186DE12BD9F4E980A688011CA3", "totalPurchases": 2, "batchIndex": 0, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985911871_43zs3kbvi", "timestamp": "2025-08-12T08:05:11.871Z", "type": "RETRY_ATTEMPT", "operation": "getPurchaseDetails", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for getPurchaseDetails: Not a number", "severity": "info", "context": {"contractAddress": "0:3CF34039565629EBCFA7E44BED8F5F074739B1186DE12BD9F4E980A688011CA3", "purchaseId": 2, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985911872_3jq53jl2e", "timestamp": "2025-08-12T08:05:11.872Z", "type": "RETRY_ATTEMPT", "operation": "refundStatusBatch_1", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for refundStatusBatch_1: Not a number", "severity": "info", "context": {"contractAddress": "0:3CF34039565629EBCFA7E44BED8F5F074739B1186DE12BD9F4E980A688011CA3", "totalPurchases": 2, "batchIndex": 1, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985911873_y9uur9n7a", "timestamp": "2025-08-12T08:05:11.873Z", "type": "BATCH_OPERATION_ERRORS", "operation": "refundStatusBatch", "message": "Batch operation completed with 2/2 failures", "severity": "critical", "context": {"contractAddress": "0:3CF34039565629EBCFA7E44BED8F5F074739B1186DE12BD9F4E980A688011CA3", "totalPurchases": 2, "totalOperations": 2, "failedOperations": 2, "errors": [{"index": 0, "message": "Not a number"}, {"index": 1, "message": "Not a number"}]}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985911874_hik07v3i2", "timestamp": "2025-08-12T08:05:11.874Z", "type": "BATCH_REFUND_CHECK_ERRORS", "operation": "refundStatusBatch", "message": "Failed to check refund status for 2/2 purchases", "severity": "critical", "context": {"contractAddress": "0:3CF34039565629EBCFA7E44BED8F5F074739B1186DE12BD9F4E980A688011CA3", "totalPurchases": 2, "successfulChecks": 0, "failedChecks": 2}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985950990_0b6jgdc6l", "timestamp": "2025-08-12T08:05:50.990Z", "type": "RETRY_ATTEMPT", "operation": "getPurchaseDetails", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for getPurchaseDetails: Not a number", "severity": "info", "context": {"contractAddress": "0:BBBA31DA569C209B317E726E4677D1CDD414C791229719330F0C133C5EFF04F2", "purchaseId": 1, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985950991_fkzfmavqv", "timestamp": "2025-08-12T08:05:50.991Z", "type": "RETRY_ATTEMPT", "operation": "refundStatusBatch_0", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for refundStatusBatch_0: Not a number", "severity": "info", "context": {"contractAddress": "0:BBBA31DA569C209B317E726E4677D1CDD414C791229719330F0C133C5EFF04F2", "totalPurchases": 1, "batchIndex": 0, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985950992_1bk5lkblr", "timestamp": "2025-08-12T08:05:50.992Z", "type": "BATCH_OPERATION_ERRORS", "operation": "refundStatusBatch", "message": "Batch operation completed with 1/1 failures", "severity": "critical", "context": {"contractAddress": "0:BBBA31DA569C209B317E726E4677D1CDD414C791229719330F0C133C5EFF04F2", "totalPurchases": 1, "totalOperations": 1, "failedOperations": 1, "errors": [{"index": 0, "message": "Not a number"}]}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754985950994_0pefltu6w", "timestamp": "2025-08-12T08:05:50.994Z", "type": "BATCH_REFUND_CHECK_ERRORS", "operation": "refundStatusBatch", "message": "Failed to check refund status for 1/1 purchases", "severity": "critical", "context": {"contractAddress": "0:BBBA31DA569C209B317E726E4677D1CDD414C791229719330F0C133C5EFF04F2", "totalPurchases": 1, "successfulChecks": 0, "failedChecks": 1}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986015785_ctx0gra0m", "timestamp": "2025-08-12T08:06:55.785Z", "type": "RETRY_ATTEMPT", "operation": "getPurchaseDetails", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for getPurchaseDetails: Not a number", "severity": "info", "context": {"contractAddress": "0:1CE3589DFF135CDA16CB7ACF1631C09BBFBE71BCF34FDE88D4501E5F23F8E428", "purchaseId": 1, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986015788_kml882mmt", "timestamp": "2025-08-12T08:06:55.788Z", "type": "RETRY_ATTEMPT", "operation": "refundStatusBatch_0", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for refundStatusBatch_0: Not a number", "severity": "info", "context": {"contractAddress": "0:1CE3589DFF135CDA16CB7ACF1631C09BBFBE71BCF34FDE88D4501E5F23F8E428", "totalPurchases": 1, "batchIndex": 0, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986015789_clces26rq", "timestamp": "2025-08-12T08:06:55.789Z", "type": "BATCH_OPERATION_ERRORS", "operation": "refundStatusBatch", "message": "Batch operation completed with 1/1 failures", "severity": "critical", "context": {"contractAddress": "0:1CE3589DFF135CDA16CB7ACF1631C09BBFBE71BCF34FDE88D4501E5F23F8E428", "totalPurchases": 1, "totalOperations": 1, "failedOperations": 1, "errors": [{"index": 0, "message": "Not a number"}]}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986015790_l8wajkok6", "timestamp": "2025-08-12T08:06:55.790Z", "type": "BATCH_REFUND_CHECK_ERRORS", "operation": "refundStatusBatch", "message": "Failed to check refund status for 1/1 purchases", "severity": "critical", "context": {"contractAddress": "0:1CE3589DFF135CDA16CB7ACF1631C09BBFBE71BCF34FDE88D4501E5F23F8E428", "totalPurchases": 1, "successfulChecks": 0, "failedChecks": 1}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986091373_t86d74cdz", "timestamp": "2025-08-12T08:08:11.373Z", "type": "RETRY_ATTEMPT", "operation": "getPurchaseDetails", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for getPurchaseDetails: Not a number", "severity": "info", "context": {"contractAddress": "0:6B3564B0243154BCE5C5F6B554A8926B83DFD1E3039BE5EEB9AB95F2A622FE09", "purchaseId": 1, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986091376_mraue21rd", "timestamp": "2025-08-12T08:08:11.376Z", "type": "RETRY_ATTEMPT", "operation": "refundStatusBatch_0", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for refundStatusBatch_0: Not a number", "severity": "info", "context": {"contractAddress": "0:6B3564B0243154BCE5C5F6B554A8926B83DFD1E3039BE5EEB9AB95F2A622FE09", "totalPurchases": 1, "batchIndex": 0, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986091377_kijjqyz21", "timestamp": "2025-08-12T08:08:11.377Z", "type": "BATCH_OPERATION_ERRORS", "operation": "refundStatusBatch", "message": "Batch operation completed with 1/1 failures", "severity": "critical", "context": {"contractAddress": "0:6B3564B0243154BCE5C5F6B554A8926B83DFD1E3039BE5EEB9AB95F2A622FE09", "totalPurchases": 1, "totalOperations": 1, "failedOperations": 1, "errors": [{"index": 0, "message": "Not a number"}]}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986091378_cczzy7g35", "timestamp": "2025-08-12T08:08:11.378Z", "type": "BATCH_REFUND_CHECK_ERRORS", "operation": "refundStatusBatch", "message": "Failed to check refund status for 1/1 purchases", "severity": "critical", "context": {"contractAddress": "0:6B3564B0243154BCE5C5F6B554A8926B83DFD1E3039BE5EEB9AB95F2A622FE09", "totalPurchases": 1, "successfulChecks": 0, "failedChecks": 1}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986154273_n12k5mj2r", "timestamp": "2025-08-12T08:09:14.273Z", "type": "RETRY_ATTEMPT", "operation": "getPurchaseDetails", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for getPurchaseDetails: Not a number", "severity": "info", "context": {"contractAddress": "0:E09E151869A8AE4A07EE18F000EA1ECB714CB062A22E0CAEB7D825D0FAD8BD2A", "purchaseId": 1, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986154282_hr0j69gj0", "timestamp": "2025-08-12T08:09:14.283Z", "type": "RETRY_ATTEMPT", "operation": "refundStatusBatch_0", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for refundStatusBatch_0: Not a number", "severity": "info", "context": {"contractAddress": "0:E09E151869A8AE4A07EE18F000EA1ECB714CB062A22E0CAEB7D825D0FAD8BD2A", "totalPurchases": 1, "batchIndex": 0, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986154285_atpbyco7x", "timestamp": "2025-08-12T08:09:14.285Z", "type": "BATCH_OPERATION_ERRORS", "operation": "refundStatusBatch", "message": "Batch operation completed with 1/1 failures", "severity": "critical", "context": {"contractAddress": "0:E09E151869A8AE4A07EE18F000EA1ECB714CB062A22E0CAEB7D825D0FAD8BD2A", "totalPurchases": 1, "totalOperations": 1, "failedOperations": 1, "errors": [{"index": 0, "message": "Not a number"}]}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986154286_e505hre3i", "timestamp": "2025-08-12T08:09:14.286Z", "type": "BATCH_REFUND_CHECK_ERRORS", "operation": "refundStatusBatch", "message": "Failed to check refund status for 1/1 purchases", "severity": "critical", "context": {"contractAddress": "0:E09E151869A8AE4A07EE18F000EA1ECB714CB062A22E0CAEB7D825D0FAD8BD2A", "totalPurchases": 1, "successfulChecks": 0, "failedChecks": 1}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986164541_jfmz7hfto", "timestamp": "2025-08-12T08:09:24.541Z", "type": "RETRY_ATTEMPT", "operation": "getPurchaseDetails", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for getPurchaseDetails: Not a number", "severity": "info", "context": {"contractAddress": "0:2E2582D2EBC9CC3EBB00ED0C35673B03E2B080FB60E9199A2FD0C64ED646670D", "purchaseId": 1, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986164543_50hjl0hkq", "timestamp": "2025-08-12T08:09:24.543Z", "type": "RETRY_ATTEMPT", "operation": "refundStatusBatch_0", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for refundStatusBatch_0: Not a number", "severity": "info", "context": {"contractAddress": "0:2E2582D2EBC9CC3EBB00ED0C35673B03E2B080FB60E9199A2FD0C64ED646670D", "totalPurchases": 1, "batchIndex": 0, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986164544_41a0hyddc", "timestamp": "2025-08-12T08:09:24.544Z", "type": "BATCH_OPERATION_ERRORS", "operation": "refundStatusBatch", "message": "Batch operation completed with 1/1 failures", "severity": "critical", "context": {"contractAddress": "0:2E2582D2EBC9CC3EBB00ED0C35673B03E2B080FB60E9199A2FD0C64ED646670D", "totalPurchases": 1, "totalOperations": 1, "failedOperations": 1, "errors": [{"index": 0, "message": "Not a number"}]}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986164545_jlmb5ybme", "timestamp": "2025-08-12T08:09:24.545Z", "type": "BATCH_REFUND_CHECK_ERRORS", "operation": "refundStatusBatch", "message": "Failed to check refund status for 1/1 purchases", "severity": "critical", "context": {"contractAddress": "0:2E2582D2EBC9CC3EBB00ED0C35673B03E2B080FB60E9199A2FD0C64ED646670D", "totalPurchases": 1, "successfulChecks": 0, "failedChecks": 1}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986181340_mwcjexj5m", "timestamp": "2025-08-12T08:09:41.340Z", "type": "RETRY_ATTEMPT", "operation": "getPurchaseDetails", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for getPurchaseDetails: Not a number", "severity": "info", "context": {"contractAddress": "0:AB62EFDCDA102F9F26C18A774992C5F00C1E1B03E017F691C02239CADD9BEDC7", "purchaseId": 1, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986181343_0reaa55yq", "timestamp": "2025-08-12T08:09:41.343Z", "type": "RETRY_ATTEMPT", "operation": "refundStatusBatch_0", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for refundStatusBatch_0: Not a number", "severity": "info", "context": {"contractAddress": "0:AB62EFDCDA102F9F26C18A774992C5F00C1E1B03E017F691C02239CADD9BEDC7", "totalPurchases": 1, "batchIndex": 0, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986181345_nfgtx618s", "timestamp": "2025-08-12T08:09:41.345Z", "type": "BATCH_OPERATION_ERRORS", "operation": "refundStatusBatch", "message": "Batch operation completed with 1/1 failures", "severity": "critical", "context": {"contractAddress": "0:AB62EFDCDA102F9F26C18A774992C5F00C1E1B03E017F691C02239CADD9BEDC7", "totalPurchases": 1, "totalOperations": 1, "failedOperations": 1, "errors": [{"index": 0, "message": "Not a number"}]}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986181347_haffshrc2", "timestamp": "2025-08-12T08:09:41.347Z", "type": "BATCH_REFUND_CHECK_ERRORS", "operation": "refundStatusBatch", "message": "Failed to check refund status for 1/1 purchases", "severity": "critical", "context": {"contractAddress": "0:AB62EFDCDA102F9F26C18A774992C5F00C1E1B03E017F691C02239CADD9BEDC7", "totalPurchases": 1, "successfulChecks": 0, "failedChecks": 1}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986252028_fzt30dn6p", "timestamp": "2025-08-12T08:10:52.028Z", "type": "RETRY_ATTEMPT", "operation": "getPurchaseDetails", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for getPurchaseDetails: Not a number", "severity": "info", "context": {"contractAddress": "0:474BC67165423BDB1DF1A6EC5D74CA3F090885A78FA6EE0AED45AD5C036BCAD1", "purchaseId": 1, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986252030_snolla7zl", "timestamp": "2025-08-12T08:10:52.030Z", "type": "RETRY_ATTEMPT", "operation": "refundStatusBatch_0", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for refundStatusBatch_0: Not a number", "severity": "info", "context": {"contractAddress": "0:474BC67165423BDB1DF1A6EC5D74CA3F090885A78FA6EE0AED45AD5C036BCAD1", "totalPurchases": 1, "batchIndex": 0, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986252033_uwg7djqw5", "timestamp": "2025-08-12T08:10:52.033Z", "type": "BATCH_OPERATION_ERRORS", "operation": "refundStatusBatch", "message": "Batch operation completed with 1/1 failures", "severity": "critical", "context": {"contractAddress": "0:474BC67165423BDB1DF1A6EC5D74CA3F090885A78FA6EE0AED45AD5C036BCAD1", "totalPurchases": 1, "totalOperations": 1, "failedOperations": 1, "errors": [{"index": 0, "message": "Not a number"}]}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986252034_ta030jvz0", "timestamp": "2025-08-12T08:10:52.034Z", "type": "BATCH_REFUND_CHECK_ERRORS", "operation": "refundStatusBatch", "message": "Failed to check refund status for 1/1 purchases", "severity": "critical", "context": {"contractAddress": "0:474BC67165423BDB1DF1A6EC5D74CA3F090885A78FA6EE0AED45AD5C036BCAD1", "totalPurchases": 1, "successfulChecks": 0, "failedChecks": 1}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986279732_er3wewacf", "timestamp": "2025-08-12T08:11:19.732Z", "type": "RETRY_ATTEMPT", "operation": "getPurchaseDetails", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for getPurchaseDetails: Not a number", "severity": "info", "context": {"contractAddress": "0:2CB7FDBED095127F8F52A732AFE291B1F05E0EC41B76D71B54691CC91B6078C4", "purchaseId": 1, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986279734_r4ubkl9nn", "timestamp": "2025-08-12T08:11:19.734Z", "type": "RETRY_ATTEMPT", "operation": "refundStatusBatch_0", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for refundStatusBatch_0: Not a number", "severity": "info", "context": {"contractAddress": "0:2CB7FDBED095127F8F52A732AFE291B1F05E0EC41B76D71B54691CC91B6078C4", "totalPurchases": 2, "batchIndex": 0, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986280732_pgcx39boi", "timestamp": "2025-08-12T08:11:20.732Z", "type": "RETRY_ATTEMPT", "operation": "getPurchaseDetails", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for getPurchaseDetails: Not a number", "severity": "info", "context": {"contractAddress": "0:2CB7FDBED095127F8F52A732AFE291B1F05E0EC41B76D71B54691CC91B6078C4", "purchaseId": 2, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986280734_k0tu93e1o", "timestamp": "2025-08-12T08:11:20.734Z", "type": "RETRY_ATTEMPT", "operation": "refundStatusBatch_1", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for refundStatusBatch_1: Not a number", "severity": "info", "context": {"contractAddress": "0:2CB7FDBED095127F8F52A732AFE291B1F05E0EC41B76D71B54691CC91B6078C4", "totalPurchases": 2, "batchIndex": 1, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986280736_zsow9s42m", "timestamp": "2025-08-12T08:11:20.736Z", "type": "BATCH_OPERATION_ERRORS", "operation": "refundStatusBatch", "message": "Batch operation completed with 2/2 failures", "severity": "critical", "context": {"contractAddress": "0:2CB7FDBED095127F8F52A732AFE291B1F05E0EC41B76D71B54691CC91B6078C4", "totalPurchases": 2, "totalOperations": 2, "failedOperations": 2, "errors": [{"index": 0, "message": "Not a number"}, {"index": 1, "message": "Not a number"}]}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986280737_228e09av0", "timestamp": "2025-08-12T08:11:20.737Z", "type": "BATCH_REFUND_CHECK_ERRORS", "operation": "refundStatusBatch", "message": "Failed to check refund status for 2/2 purchases", "severity": "critical", "context": {"contractAddress": "0:2CB7FDBED095127F8F52A732AFE291B1F05E0EC41B76D71B54691CC91B6078C4", "totalPurchases": 2, "successfulChecks": 0, "failedChecks": 2}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986323145_r0yq8x0hw", "timestamp": "2025-08-12T08:12:03.145Z", "type": "RETRY_ATTEMPT", "operation": "getPurchaseDetails", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for getPurchaseDetails: Not a number", "severity": "info", "context": {"contractAddress": "0:086B0E489364C0DDA1797CAECB84A8CC9061BA409029DB111F3F3F893F1946A8", "purchaseId": 1, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986323147_d1qn5a5m4", "timestamp": "2025-08-12T08:12:03.147Z", "type": "RETRY_ATTEMPT", "operation": "refundStatusBatch_0", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for refundStatusBatch_0: Not a number", "severity": "info", "context": {"contractAddress": "0:086B0E489364C0DDA1797CAECB84A8CC9061BA409029DB111F3F3F893F1946A8", "totalPurchases": 1, "batchIndex": 0, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986323150_cuwbsc0ub", "timestamp": "2025-08-12T08:12:03.150Z", "type": "BATCH_OPERATION_ERRORS", "operation": "refundStatusBatch", "message": "Batch operation completed with 1/1 failures", "severity": "critical", "context": {"contractAddress": "0:086B0E489364C0DDA1797CAECB84A8CC9061BA409029DB111F3F3F893F1946A8", "totalPurchases": 1, "totalOperations": 1, "failedOperations": 1, "errors": [{"index": 0, "message": "Not a number"}]}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986323151_6vcg71q43", "timestamp": "2025-08-12T08:12:03.151Z", "type": "BATCH_REFUND_CHECK_ERRORS", "operation": "refundStatusBatch", "message": "Failed to check refund status for 1/1 purchases", "severity": "critical", "context": {"contractAddress": "0:086B0E489364C0DDA1797CAECB84A8CC9061BA409029DB111F3F3F893F1946A8", "totalPurchases": 1, "successfulChecks": 0, "failedChecks": 1}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986384786_h53n1qwli", "timestamp": "2025-08-12T08:13:04.786Z", "type": "RETRY_ATTEMPT", "operation": "getPurchaseDetails", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for getPurchaseDetails: Not a number", "severity": "info", "context": {"contractAddress": "0:6EF71301871F7B02A1C2D40B58ADC2F3D51399B0939A0D08B7A947A58846ED0B", "purchaseId": 1, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986384789_w9qivm715", "timestamp": "2025-08-12T08:13:04.789Z", "type": "RETRY_ATTEMPT", "operation": "refundStatusBatch_0", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for refundStatusBatch_0: Not a number", "severity": "info", "context": {"contractAddress": "0:6EF71301871F7B02A1C2D40B58ADC2F3D51399B0939A0D08B7A947A58846ED0B", "totalPurchases": 2, "batchIndex": 0, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986385741_yrdrv5uub", "timestamp": "2025-08-12T08:13:05.741Z", "type": "RETRY_ATTEMPT", "operation": "getPurchaseDetails", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for getPurchaseDetails: Not a number", "severity": "info", "context": {"contractAddress": "0:6EF71301871F7B02A1C2D40B58ADC2F3D51399B0939A0D08B7A947A58846ED0B", "purchaseId": 2, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986385742_rr39d7p2u", "timestamp": "2025-08-12T08:13:05.742Z", "type": "RETRY_ATTEMPT", "operation": "refundStatusBatch_1", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for refundStatusBatch_1: Not a number", "severity": "info", "context": {"contractAddress": "0:6EF71301871F7B02A1C2D40B58ADC2F3D51399B0939A0D08B7A947A58846ED0B", "totalPurchases": 2, "batchIndex": 1, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986385744_if1x53tyr", "timestamp": "2025-08-12T08:13:05.744Z", "type": "BATCH_OPERATION_ERRORS", "operation": "refundStatusBatch", "message": "Batch operation completed with 2/2 failures", "severity": "critical", "context": {"contractAddress": "0:6EF71301871F7B02A1C2D40B58ADC2F3D51399B0939A0D08B7A947A58846ED0B", "totalPurchases": 2, "totalOperations": 2, "failedOperations": 2, "errors": [{"index": 0, "message": "Not a number"}, {"index": 1, "message": "Not a number"}]}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986385745_9p72tzw6d", "timestamp": "2025-08-12T08:13:05.745Z", "type": "BATCH_REFUND_CHECK_ERRORS", "operation": "refundStatusBatch", "message": "Failed to check refund status for 2/2 purchases", "severity": "critical", "context": {"contractAddress": "0:6EF71301871F7B02A1C2D40B58ADC2F3D51399B0939A0D08B7A947A58846ED0B", "totalPurchases": 2, "successfulChecks": 0, "failedChecks": 2}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986389573_aylvpvis7", "timestamp": "2025-08-12T08:13:09.573Z", "type": "RETRY_ATTEMPT", "operation": "getPurchaseDetails", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for getPurchaseDetails: Not a number", "severity": "info", "context": {"contractAddress": "0:28700AE8DACD5BA5867C12F6E0CA20F9BC701FB727C549F8A5631C444CEFFFDF", "purchaseId": 1, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986389573_yquge8pzz", "timestamp": "2025-08-12T08:13:09.573Z", "type": "RETRY_ATTEMPT", "operation": "refundStatusBatch_0", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for refundStatusBatch_0: Not a number", "severity": "info", "context": {"contractAddress": "0:28700AE8DACD5BA5867C12F6E0CA20F9BC701FB727C549F8A5631C444CEFFFDF", "totalPurchases": 1, "batchIndex": 0, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986389574_6xnlfr3rg", "timestamp": "2025-08-12T08:13:09.574Z", "type": "BATCH_OPERATION_ERRORS", "operation": "refundStatusBatch", "message": "Batch operation completed with 1/1 failures", "severity": "critical", "context": {"contractAddress": "0:28700AE8DACD5BA5867C12F6E0CA20F9BC701FB727C549F8A5631C444CEFFFDF", "totalPurchases": 1, "totalOperations": 1, "failedOperations": 1, "errors": [{"index": 0, "message": "Not a number"}]}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986389575_7gd3ny5wm", "timestamp": "2025-08-12T08:13:09.575Z", "type": "BATCH_REFUND_CHECK_ERRORS", "operation": "refundStatusBatch", "message": "Failed to check refund status for 1/1 purchases", "severity": "critical", "context": {"contractAddress": "0:28700AE8DACD5BA5867C12F6E0CA20F9BC701FB727C549F8A5631C444CEFFFDF", "totalPurchases": 1, "successfulChecks": 0, "failedChecks": 1}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986396183_1vy94tgr6", "timestamp": "2025-08-12T08:13:16.183Z", "type": "RETRY_ATTEMPT", "operation": "getPurchaseDetails", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for getPurchaseDetails: Not a number", "severity": "info", "context": {"contractAddress": "0:2400C4E1589CEADE31DCC8024E4B080A2086BD532FB41BAC800F4F09400322A8", "purchaseId": 1, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986396186_2wpk3bu3i", "timestamp": "2025-08-12T08:13:16.186Z", "type": "RETRY_ATTEMPT", "operation": "refundStatusBatch_0", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for refundStatusBatch_0: Not a number", "severity": "info", "context": {"contractAddress": "0:2400C4E1589CEADE31DCC8024E4B080A2086BD532FB41BAC800F4F09400322A8", "totalPurchases": 1, "batchIndex": 0, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986396194_g784ln7aq", "timestamp": "2025-08-12T08:13:16.194Z", "type": "BATCH_OPERATION_ERRORS", "operation": "refundStatusBatch", "message": "Batch operation completed with 1/1 failures", "severity": "critical", "context": {"contractAddress": "0:2400C4E1589CEADE31DCC8024E4B080A2086BD532FB41BAC800F4F09400322A8", "totalPurchases": 1, "totalOperations": 1, "failedOperations": 1, "errors": [{"index": 0, "message": "Not a number"}]}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986396197_itrzwje8d", "timestamp": "2025-08-12T08:13:16.197Z", "type": "BATCH_REFUND_CHECK_ERRORS", "operation": "refundStatusBatch", "message": "Failed to check refund status for 1/1 purchases", "severity": "critical", "context": {"contractAddress": "0:2400C4E1589CEADE31DCC8024E4B080A2086BD532FB41BAC800F4F09400322A8", "totalPurchases": 1, "successfulChecks": 0, "failedChecks": 1}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986422931_a9q4l6qxs", "timestamp": "2025-08-12T08:13:42.931Z", "type": "RETRY_ATTEMPT", "operation": "getPurchaseDetails", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for getPurchaseDetails: Not a number", "severity": "info", "context": {"contractAddress": "0:23E557001111416DFE3545F306551AA97CD292E9421D9C40B5DF658E1A128170", "purchaseId": 1, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986422934_tx4u3hd1a", "timestamp": "2025-08-12T08:13:42.934Z", "type": "RETRY_ATTEMPT", "operation": "refundStatusBatch_0", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for refundStatusBatch_0: Not a number", "severity": "info", "context": {"contractAddress": "0:23E557001111416DFE3545F306551AA97CD292E9421D9C40B5DF658E1A128170", "totalPurchases": 2, "batchIndex": 0, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986423911_wr7rs9608", "timestamp": "2025-08-12T08:13:43.911Z", "type": "BATCH_OPERATION_ERRORS", "operation": "refundStatusBatch", "message": "Batch operation completed with 1/2 failures", "severity": "warning", "context": {"contractAddress": "0:23E557001111416DFE3545F306551AA97CD292E9421D9C40B5DF658E1A128170", "totalPurchases": 2, "totalOperations": 2, "failedOperations": 1, "errors": [{"index": 0, "message": "Not a number"}]}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986423913_mqd8jyii1", "timestamp": "2025-08-12T08:13:43.913Z", "type": "BATCH_REFUND_CHECK_ERRORS", "operation": "refundStatusBatch", "message": "Failed to check refund status for 1/2 purchases", "severity": "warning", "context": {"contractAddress": "0:23E557001111416DFE3545F306551AA97CD292E9421D9C40B5DF658E1A128170", "totalPurchases": 2, "successfulChecks": 1, "failedChecks": 1}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986486483_82utu9im2", "timestamp": "2025-08-12T08:14:46.483Z", "type": "RETRY_ATTEMPT", "operation": "getPurchaseDetails", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for getPurchaseDetails: Not a number", "severity": "info", "context": {"contractAddress": "0:EC522B48A516108A00C2328F6DFC3C718690A8E383DA09F7BDED9392BB6B2274", "purchaseId": 1, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986486487_hzfgy3evk", "timestamp": "2025-08-12T08:14:46.487Z", "type": "RETRY_ATTEMPT", "operation": "refundStatusBatch_0", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for refundStatusBatch_0: Not a number", "severity": "info", "context": {"contractAddress": "0:EC522B48A516108A00C2328F6DFC3C718690A8E383DA09F7BDED9392BB6B2274", "totalPurchases": 1, "batchIndex": 0, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986486489_7al4isull", "timestamp": "2025-08-12T08:14:46.489Z", "type": "BATCH_OPERATION_ERRORS", "operation": "refundStatusBatch", "message": "Batch operation completed with 1/1 failures", "severity": "critical", "context": {"contractAddress": "0:EC522B48A516108A00C2328F6DFC3C718690A8E383DA09F7BDED9392BB6B2274", "totalPurchases": 1, "totalOperations": 1, "failedOperations": 1, "errors": [{"index": 0, "message": "Not a number"}]}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986486490_4hnjk5dlu", "timestamp": "2025-08-12T08:14:46.490Z", "type": "BATCH_REFUND_CHECK_ERRORS", "operation": "refundStatusBatch", "message": "Failed to check refund status for 1/1 purchases", "severity": "critical", "context": {"contractAddress": "0:EC522B48A516108A00C2328F6DFC3C718690A8E383DA09F7BDED9392BB6B2274", "totalPurchases": 1, "successfulChecks": 0, "failedChecks": 1}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986526920_dz47kehqp", "timestamp": "2025-08-12T08:15:26.920Z", "type": "RETRY_ATTEMPT", "operation": "getPurchaseDetails", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for getPurchaseDetails: Not a number", "severity": "info", "context": {"contractAddress": "0:12E9068AE08A776ABCB3C8AE3683516741DC6224E8A11651758F2A0970DDA8AC", "purchaseId": 1, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986526923_9yg6negsu", "timestamp": "2025-08-12T08:15:26.923Z", "type": "RETRY_ATTEMPT", "operation": "refundStatusBatch_0", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for refundStatusBatch_0: Not a number", "severity": "info", "context": {"contractAddress": "0:12E9068AE08A776ABCB3C8AE3683516741DC6224E8A11651758F2A0970DDA8AC", "totalPurchases": 1, "batchIndex": 0, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986526924_59ccroiza", "timestamp": "2025-08-12T08:15:26.924Z", "type": "BATCH_OPERATION_ERRORS", "operation": "refundStatusBatch", "message": "Batch operation completed with 1/1 failures", "severity": "critical", "context": {"contractAddress": "0:12E9068AE08A776ABCB3C8AE3683516741DC6224E8A11651758F2A0970DDA8AC", "totalPurchases": 1, "totalOperations": 1, "failedOperations": 1, "errors": [{"index": 0, "message": "Not a number"}]}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986526925_sodksb8y8", "timestamp": "2025-08-12T08:15:26.925Z", "type": "BATCH_REFUND_CHECK_ERRORS", "operation": "refundStatusBatch", "message": "Failed to check refund status for 1/1 purchases", "severity": "critical", "context": {"contractAddress": "0:12E9068AE08A776ABCB3C8AE3683516741DC6224E8A11651758F2A0970DDA8AC", "totalPurchases": 1, "successfulChecks": 0, "failedChecks": 1}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986590661_ydirc9obh", "timestamp": "2025-08-12T08:16:30.661Z", "type": "RETRY_ATTEMPT", "operation": "getTotalPurchased", "attempt": 1, "maxAttempts": 5, "reason": "timeout of 30000ms exceeded", "message": "Retry attempt 1/5 for getTotalPurchased: timeout of 30000ms exceeded", "severity": "info", "context": {"contractAddress": "0:B218109C48E399A5D478EB0C3BDAA7F199BC4DD8760F5C7B06B4895C9F535805", "isCritical": true, "errorType": "AxiosError"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986622390_122q09zxd", "timestamp": "2025-08-12T08:17:02.390Z", "type": "RETRY_ATTEMPT", "operation": "getTotalPurchased", "attempt": 2, "maxAttempts": 5, "reason": "timeout of 30000ms exceeded", "message": "Retry attempt 2/5 for getTotalPurchased: timeout of 30000ms exceeded", "severity": "info", "context": {"contractAddress": "0:B218109C48E399A5D478EB0C3BDAA7F199BC4DD8760F5C7B06B4895C9F535805", "isCritical": true, "errorType": "AxiosError"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986627345_gccdt0c8v", "timestamp": "2025-08-12T08:17:07.345Z", "type": "DATA_RECOVERY", "operation": "getTotalPurchased", "recoveryMethod": "Succeeded on attempt 3", "originalError": "timeout of 30000ms exceeded", "message": "Data recovery applied: Succeeded on attempt 3 for getTotalPurchased", "severity": "warning", "context": {"contractAddress": "0:B218109C48E399A5D478EB0C3BDAA7F199BC4DD8760F5C7B06B4895C9F535805", "totalAttempts": 3, "isCritical": true}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986655125_s40cn1yez", "timestamp": "2025-08-12T08:17:35.125Z", "type": "RETRY_ATTEMPT", "operation": "getPurchaseDetails", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for getPurchaseDetails: Not a number", "severity": "info", "context": {"contractAddress": "0:7534A26D8B56B37A8A01F3AB3EE5420C82B2C53540B133D1C5908A208F769856", "purchaseId": 1, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986655128_0weeav77j", "timestamp": "2025-08-12T08:17:35.128Z", "type": "RETRY_ATTEMPT", "operation": "refundStatusBatch_0", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for refundStatusBatch_0: Not a number", "severity": "info", "context": {"contractAddress": "0:7534A26D8B56B37A8A01F3AB3EE5420C82B2C53540B133D1C5908A208F769856", "totalPurchases": 1, "batchIndex": 0, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986655131_5lx5xmh4u", "timestamp": "2025-08-12T08:17:35.131Z", "type": "BATCH_OPERATION_ERRORS", "operation": "refundStatusBatch", "message": "Batch operation completed with 1/1 failures", "severity": "critical", "context": {"contractAddress": "0:7534A26D8B56B37A8A01F3AB3EE5420C82B2C53540B133D1C5908A208F769856", "totalPurchases": 1, "totalOperations": 1, "failedOperations": 1, "errors": [{"index": 0, "message": "Not a number"}]}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986655133_b5ixlwjiu", "timestamp": "2025-08-12T08:17:35.133Z", "type": "BATCH_REFUND_CHECK_ERRORS", "operation": "refundStatusBatch", "message": "Failed to check refund status for 1/1 purchases", "severity": "critical", "context": {"contractAddress": "0:7534A26D8B56B37A8A01F3AB3EE5420C82B2C53540B133D1C5908A208F769856", "totalPurchases": 1, "successfulChecks": 0, "failedChecks": 1}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986739503_6yj4h8hvc", "timestamp": "2025-08-12T08:18:59.503Z", "type": "RETRY_ATTEMPT", "operation": "getPurchaseDetails", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for getPurchaseDetails: Not a number", "severity": "info", "context": {"contractAddress": "0:39F15DC1FEA8A4C5377F7BB26441FFDD6A008037898B44AE5681A305FE72EA91", "purchaseId": 1, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986739506_ipmxvcrz3", "timestamp": "2025-08-12T08:18:59.507Z", "type": "RETRY_ATTEMPT", "operation": "refundStatusBatch_0", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for refundStatusBatch_0: Not a number", "severity": "info", "context": {"contractAddress": "0:39F15DC1FEA8A4C5377F7BB26441FFDD6A008037898B44AE5681A305FE72EA91", "totalPurchases": 1, "batchIndex": 0, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986739509_auh7d85xu", "timestamp": "2025-08-12T08:18:59.510Z", "type": "BATCH_OPERATION_ERRORS", "operation": "refundStatusBatch", "message": "Batch operation completed with 1/1 failures", "severity": "critical", "context": {"contractAddress": "0:39F15DC1FEA8A4C5377F7BB26441FFDD6A008037898B44AE5681A305FE72EA91", "totalPurchases": 1, "totalOperations": 1, "failedOperations": 1, "errors": [{"index": 0, "message": "Not a number"}]}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986739511_fj152yw54", "timestamp": "2025-08-12T08:18:59.511Z", "type": "BATCH_REFUND_CHECK_ERRORS", "operation": "refundStatusBatch", "message": "Failed to check refund status for 1/1 purchases", "severity": "critical", "context": {"contractAddress": "0:39F15DC1FEA8A4C5377F7BB26441FFDD6A008037898B44AE5681A305FE72EA91", "totalPurchases": 1, "successfulChecks": 0, "failedChecks": 1}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}]}