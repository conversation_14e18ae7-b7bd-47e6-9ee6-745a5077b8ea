{"sessionInfo": {"startTime": "2025-08-12T09:03:07.904Z", "lastUpdate": "2025-08-12T09:03:14.630Z", "totalErrors": 2, "errorCounts": {"RETRY_ATTEMPT_getPurchaseDetails": 1, "CONTRACT_ERROR_checkRefundStatus": 1}}, "errors": [{"id": "ERR_1754989394619_2jgqhkaba", "timestamp": "2025-08-12T09:03:14.619Z", "type": "RETRY_ATTEMPT", "operation": "getPurchaseDetails", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for getPurchaseDetails: Not a number", "severity": "info", "context": {"contractAddress": "0:8D8A3E5F75898348CF8AFB2EB533E554DBE2615BDF66ED8C7CBDB37BDC54F209", "purchaseId": 3, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T09:03:07.904Z"}, {"id": "ERR_1754989394630_p00564qe5", "timestamp": "2025-08-12T09:03:14.630Z", "type": "CONTRACT_ERROR", "operation": "checkRefundStatus", "contractAddress": "0:8D8A3E5F75898348CF8AFB2EB533E554DBE2615BDF66ED8C7CBDB37BDC54F209", "message": "Not a number", "stack": "Error: Not a number\n    at TupleReader.readBigNumber (/Users/<USER>/code/tbook/onton_stats/node_modules/.pnpm/@ton+core@0.61.0_@ton+crypto@3.3.0/node_modules/@ton/core/dist/tuple/reader.js:41:19)\n    at loadTuplePurchaseRecord (file:///Users/<USER>/code/tbook/onton_stats/build/UserPurchase/UserPurchase_UserPurchase.ts:896:24)\n    at UserPurchase.getPurchaseDetails (file:///Users/<USER>/code/tbook/onton_stats/build/UserPurchase/UserPurchase_UserPurchase.ts:1265:35)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async RetryManager.executeWithRetry (file:///Users/<USER>/code/tbook/onton_stats/src/retry-manager.js:33:32)\n    at async RefundFixer.fixContractRefundData (file:///Users/<USER>/code/tbook/onton_stats/src/refund-fixer.js:144:49)\n    at async RefundFixer.fixAllRefundData (file:///Users/<USER>/code/tbook/onton_stats/src/refund-fixer.js:55:36)\n    at async main (file:///Users/<USER>/code/tbook/onton_stats/src/refund-fixer.js:323:13)", "severity": "error", "context": {"purchaseId": 3, "severity": "warning"}, "retryable": false, "sessionStartTime": "2025-08-12T09:03:07.904Z"}]}