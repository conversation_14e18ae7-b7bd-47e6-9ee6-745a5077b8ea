import {
    Cell,
    Slice,
    Address,
    Builder,
    begin<PERSON>ell,
    ComputeError,
    <PERSON>pleItem,
    TupleReader,
    Dictionary,
    contractAddress,
    address,
    ContractProvider,
    Sender,
    Contract,
    ContractAB<PERSON>,
    ABIType,
    ABIGetter,
    ABIReceiver,
    TupleBuilder,
    DictionaryValue
} from '@ton/core';

export type DataSize = {
    $$type: 'DataSize';
    cells: bigint;
    bits: bigint;
    refs: bigint;
}

export function storeDataSize(src: DataSize) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeInt(src.cells, 257);
        b_0.storeInt(src.bits, 257);
        b_0.storeInt(src.refs, 257);
    };
}

export function loadDataSize(slice: Slice) {
    const sc_0 = slice;
    const _cells = sc_0.loadIntBig(257);
    const _bits = sc_0.loadIntBig(257);
    const _refs = sc_0.loadIntBig(257);
    return { $$type: 'DataSize' as const, cells: _cells, bits: _bits, refs: _refs };
}

export function loadTupleDataSize(source: <PERSON>pleReader) {
    const _cells = source.readBigNumber();
    const _bits = source.readBigNumber();
    const _refs = source.readBigNumber();
    return { $$type: 'DataSize' as const, cells: _cells, bits: _bits, refs: _refs };
}

export function loadGetterTupleDataSize(source: TupleReader) {
    const _cells = source.readBigNumber();
    const _bits = source.readBigNumber();
    const _refs = source.readBigNumber();
    return { $$type: 'DataSize' as const, cells: _cells, bits: _bits, refs: _refs };
}

export function storeTupleDataSize(source: DataSize) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.cells);
    builder.writeNumber(source.bits);
    builder.writeNumber(source.refs);
    return builder.build();
}

export function dictValueParserDataSize(): DictionaryValue<DataSize> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeDataSize(src)).endCell());
        },
        parse: (src) => {
            return loadDataSize(src.loadRef().beginParse());
        }
    }
}

export type SignedBundle = {
    $$type: 'SignedBundle';
    signature: Buffer;
    signedData: Slice;
}

export function storeSignedBundle(src: SignedBundle) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeBuffer(src.signature);
        b_0.storeBuilder(src.signedData.asBuilder());
    };
}

export function loadSignedBundle(slice: Slice) {
    const sc_0 = slice;
    const _signature = sc_0.loadBuffer(64);
    const _signedData = sc_0;
    return { $$type: 'SignedBundle' as const, signature: _signature, signedData: _signedData };
}

export function loadTupleSignedBundle(source: TupleReader) {
    const _signature = source.readBuffer();
    const _signedData = source.readCell().asSlice();
    return { $$type: 'SignedBundle' as const, signature: _signature, signedData: _signedData };
}

export function loadGetterTupleSignedBundle(source: TupleReader) {
    const _signature = source.readBuffer();
    const _signedData = source.readCell().asSlice();
    return { $$type: 'SignedBundle' as const, signature: _signature, signedData: _signedData };
}

export function storeTupleSignedBundle(source: SignedBundle) {
    const builder = new TupleBuilder();
    builder.writeBuffer(source.signature);
    builder.writeSlice(source.signedData.asCell());
    return builder.build();
}

export function dictValueParserSignedBundle(): DictionaryValue<SignedBundle> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeSignedBundle(src)).endCell());
        },
        parse: (src) => {
            return loadSignedBundle(src.loadRef().beginParse());
        }
    }
}

export type StateInit = {
    $$type: 'StateInit';
    code: Cell;
    data: Cell;
}

export function storeStateInit(src: StateInit) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeRef(src.code);
        b_0.storeRef(src.data);
    };
}

export function loadStateInit(slice: Slice) {
    const sc_0 = slice;
    const _code = sc_0.loadRef();
    const _data = sc_0.loadRef();
    return { $$type: 'StateInit' as const, code: _code, data: _data };
}

export function loadTupleStateInit(source: TupleReader) {
    const _code = source.readCell();
    const _data = source.readCell();
    return { $$type: 'StateInit' as const, code: _code, data: _data };
}

export function loadGetterTupleStateInit(source: TupleReader) {
    const _code = source.readCell();
    const _data = source.readCell();
    return { $$type: 'StateInit' as const, code: _code, data: _data };
}

export function storeTupleStateInit(source: StateInit) {
    const builder = new TupleBuilder();
    builder.writeCell(source.code);
    builder.writeCell(source.data);
    return builder.build();
}

export function dictValueParserStateInit(): DictionaryValue<StateInit> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeStateInit(src)).endCell());
        },
        parse: (src) => {
            return loadStateInit(src.loadRef().beginParse());
        }
    }
}

export type Context = {
    $$type: 'Context';
    bounceable: boolean;
    sender: Address;
    value: bigint;
    raw: Slice;
}

export function storeContext(src: Context) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeBit(src.bounceable);
        b_0.storeAddress(src.sender);
        b_0.storeInt(src.value, 257);
        b_0.storeRef(src.raw.asCell());
    };
}

export function loadContext(slice: Slice) {
    const sc_0 = slice;
    const _bounceable = sc_0.loadBit();
    const _sender = sc_0.loadAddress();
    const _value = sc_0.loadIntBig(257);
    const _raw = sc_0.loadRef().asSlice();
    return { $$type: 'Context' as const, bounceable: _bounceable, sender: _sender, value: _value, raw: _raw };
}

export function loadTupleContext(source: TupleReader) {
    const _bounceable = source.readBoolean();
    const _sender = source.readAddress();
    const _value = source.readBigNumber();
    const _raw = source.readCell().asSlice();
    return { $$type: 'Context' as const, bounceable: _bounceable, sender: _sender, value: _value, raw: _raw };
}

export function loadGetterTupleContext(source: TupleReader) {
    const _bounceable = source.readBoolean();
    const _sender = source.readAddress();
    const _value = source.readBigNumber();
    const _raw = source.readCell().asSlice();
    return { $$type: 'Context' as const, bounceable: _bounceable, sender: _sender, value: _value, raw: _raw };
}

export function storeTupleContext(source: Context) {
    const builder = new TupleBuilder();
    builder.writeBoolean(source.bounceable);
    builder.writeAddress(source.sender);
    builder.writeNumber(source.value);
    builder.writeSlice(source.raw.asCell());
    return builder.build();
}

export function dictValueParserContext(): DictionaryValue<Context> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeContext(src)).endCell());
        },
        parse: (src) => {
            return loadContext(src.loadRef().beginParse());
        }
    }
}

export type SendParameters = {
    $$type: 'SendParameters';
    mode: bigint;
    body: Cell | null;
    code: Cell | null;
    data: Cell | null;
    value: bigint;
    to: Address;
    bounce: boolean;
}

export function storeSendParameters(src: SendParameters) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeInt(src.mode, 257);
        if (src.body !== null && src.body !== undefined) { b_0.storeBit(true).storeRef(src.body); } else { b_0.storeBit(false); }
        if (src.code !== null && src.code !== undefined) { b_0.storeBit(true).storeRef(src.code); } else { b_0.storeBit(false); }
        if (src.data !== null && src.data !== undefined) { b_0.storeBit(true).storeRef(src.data); } else { b_0.storeBit(false); }
        b_0.storeInt(src.value, 257);
        b_0.storeAddress(src.to);
        b_0.storeBit(src.bounce);
    };
}

export function loadSendParameters(slice: Slice) {
    const sc_0 = slice;
    const _mode = sc_0.loadIntBig(257);
    const _body = sc_0.loadBit() ? sc_0.loadRef() : null;
    const _code = sc_0.loadBit() ? sc_0.loadRef() : null;
    const _data = sc_0.loadBit() ? sc_0.loadRef() : null;
    const _value = sc_0.loadIntBig(257);
    const _to = sc_0.loadAddress();
    const _bounce = sc_0.loadBit();
    return { $$type: 'SendParameters' as const, mode: _mode, body: _body, code: _code, data: _data, value: _value, to: _to, bounce: _bounce };
}

export function loadTupleSendParameters(source: TupleReader) {
    const _mode = source.readBigNumber();
    const _body = source.readCellOpt();
    const _code = source.readCellOpt();
    const _data = source.readCellOpt();
    const _value = source.readBigNumber();
    const _to = source.readAddress();
    const _bounce = source.readBoolean();
    return { $$type: 'SendParameters' as const, mode: _mode, body: _body, code: _code, data: _data, value: _value, to: _to, bounce: _bounce };
}

export function loadGetterTupleSendParameters(source: TupleReader) {
    const _mode = source.readBigNumber();
    const _body = source.readCellOpt();
    const _code = source.readCellOpt();
    const _data = source.readCellOpt();
    const _value = source.readBigNumber();
    const _to = source.readAddress();
    const _bounce = source.readBoolean();
    return { $$type: 'SendParameters' as const, mode: _mode, body: _body, code: _code, data: _data, value: _value, to: _to, bounce: _bounce };
}

export function storeTupleSendParameters(source: SendParameters) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.mode);
    builder.writeCell(source.body);
    builder.writeCell(source.code);
    builder.writeCell(source.data);
    builder.writeNumber(source.value);
    builder.writeAddress(source.to);
    builder.writeBoolean(source.bounce);
    return builder.build();
}

export function dictValueParserSendParameters(): DictionaryValue<SendParameters> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeSendParameters(src)).endCell());
        },
        parse: (src) => {
            return loadSendParameters(src.loadRef().beginParse());
        }
    }
}

export type MessageParameters = {
    $$type: 'MessageParameters';
    mode: bigint;
    body: Cell | null;
    value: bigint;
    to: Address;
    bounce: boolean;
}

export function storeMessageParameters(src: MessageParameters) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeInt(src.mode, 257);
        if (src.body !== null && src.body !== undefined) { b_0.storeBit(true).storeRef(src.body); } else { b_0.storeBit(false); }
        b_0.storeInt(src.value, 257);
        b_0.storeAddress(src.to);
        b_0.storeBit(src.bounce);
    };
}

export function loadMessageParameters(slice: Slice) {
    const sc_0 = slice;
    const _mode = sc_0.loadIntBig(257);
    const _body = sc_0.loadBit() ? sc_0.loadRef() : null;
    const _value = sc_0.loadIntBig(257);
    const _to = sc_0.loadAddress();
    const _bounce = sc_0.loadBit();
    return { $$type: 'MessageParameters' as const, mode: _mode, body: _body, value: _value, to: _to, bounce: _bounce };
}

export function loadTupleMessageParameters(source: TupleReader) {
    const _mode = source.readBigNumber();
    const _body = source.readCellOpt();
    const _value = source.readBigNumber();
    const _to = source.readAddress();
    const _bounce = source.readBoolean();
    return { $$type: 'MessageParameters' as const, mode: _mode, body: _body, value: _value, to: _to, bounce: _bounce };
}

export function loadGetterTupleMessageParameters(source: TupleReader) {
    const _mode = source.readBigNumber();
    const _body = source.readCellOpt();
    const _value = source.readBigNumber();
    const _to = source.readAddress();
    const _bounce = source.readBoolean();
    return { $$type: 'MessageParameters' as const, mode: _mode, body: _body, value: _value, to: _to, bounce: _bounce };
}

export function storeTupleMessageParameters(source: MessageParameters) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.mode);
    builder.writeCell(source.body);
    builder.writeNumber(source.value);
    builder.writeAddress(source.to);
    builder.writeBoolean(source.bounce);
    return builder.build();
}

export function dictValueParserMessageParameters(): DictionaryValue<MessageParameters> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeMessageParameters(src)).endCell());
        },
        parse: (src) => {
            return loadMessageParameters(src.loadRef().beginParse());
        }
    }
}

export type DeployParameters = {
    $$type: 'DeployParameters';
    mode: bigint;
    body: Cell | null;
    value: bigint;
    bounce: boolean;
    init: StateInit;
}

export function storeDeployParameters(src: DeployParameters) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeInt(src.mode, 257);
        if (src.body !== null && src.body !== undefined) { b_0.storeBit(true).storeRef(src.body); } else { b_0.storeBit(false); }
        b_0.storeInt(src.value, 257);
        b_0.storeBit(src.bounce);
        b_0.store(storeStateInit(src.init));
    };
}

export function loadDeployParameters(slice: Slice) {
    const sc_0 = slice;
    const _mode = sc_0.loadIntBig(257);
    const _body = sc_0.loadBit() ? sc_0.loadRef() : null;
    const _value = sc_0.loadIntBig(257);
    const _bounce = sc_0.loadBit();
    const _init = loadStateInit(sc_0);
    return { $$type: 'DeployParameters' as const, mode: _mode, body: _body, value: _value, bounce: _bounce, init: _init };
}

export function loadTupleDeployParameters(source: TupleReader) {
    const _mode = source.readBigNumber();
    const _body = source.readCellOpt();
    const _value = source.readBigNumber();
    const _bounce = source.readBoolean();
    const _init = loadTupleStateInit(source);
    return { $$type: 'DeployParameters' as const, mode: _mode, body: _body, value: _value, bounce: _bounce, init: _init };
}

export function loadGetterTupleDeployParameters(source: TupleReader) {
    const _mode = source.readBigNumber();
    const _body = source.readCellOpt();
    const _value = source.readBigNumber();
    const _bounce = source.readBoolean();
    const _init = loadGetterTupleStateInit(source);
    return { $$type: 'DeployParameters' as const, mode: _mode, body: _body, value: _value, bounce: _bounce, init: _init };
}

export function storeTupleDeployParameters(source: DeployParameters) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.mode);
    builder.writeCell(source.body);
    builder.writeNumber(source.value);
    builder.writeBoolean(source.bounce);
    builder.writeTuple(storeTupleStateInit(source.init));
    return builder.build();
}

export function dictValueParserDeployParameters(): DictionaryValue<DeployParameters> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeDeployParameters(src)).endCell());
        },
        parse: (src) => {
            return loadDeployParameters(src.loadRef().beginParse());
        }
    }
}

export type StdAddress = {
    $$type: 'StdAddress';
    workchain: bigint;
    address: bigint;
}

export function storeStdAddress(src: StdAddress) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeInt(src.workchain, 8);
        b_0.storeUint(src.address, 256);
    };
}

export function loadStdAddress(slice: Slice) {
    const sc_0 = slice;
    const _workchain = sc_0.loadIntBig(8);
    const _address = sc_0.loadUintBig(256);
    return { $$type: 'StdAddress' as const, workchain: _workchain, address: _address };
}

export function loadTupleStdAddress(source: TupleReader) {
    const _workchain = source.readBigNumber();
    const _address = source.readBigNumber();
    return { $$type: 'StdAddress' as const, workchain: _workchain, address: _address };
}

export function loadGetterTupleStdAddress(source: TupleReader) {
    const _workchain = source.readBigNumber();
    const _address = source.readBigNumber();
    return { $$type: 'StdAddress' as const, workchain: _workchain, address: _address };
}

export function storeTupleStdAddress(source: StdAddress) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.workchain);
    builder.writeNumber(source.address);
    return builder.build();
}

export function dictValueParserStdAddress(): DictionaryValue<StdAddress> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeStdAddress(src)).endCell());
        },
        parse: (src) => {
            return loadStdAddress(src.loadRef().beginParse());
        }
    }
}

export type VarAddress = {
    $$type: 'VarAddress';
    workchain: bigint;
    address: Slice;
}

export function storeVarAddress(src: VarAddress) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeInt(src.workchain, 32);
        b_0.storeRef(src.address.asCell());
    };
}

export function loadVarAddress(slice: Slice) {
    const sc_0 = slice;
    const _workchain = sc_0.loadIntBig(32);
    const _address = sc_0.loadRef().asSlice();
    return { $$type: 'VarAddress' as const, workchain: _workchain, address: _address };
}

export function loadTupleVarAddress(source: TupleReader) {
    const _workchain = source.readBigNumber();
    const _address = source.readCell().asSlice();
    return { $$type: 'VarAddress' as const, workchain: _workchain, address: _address };
}

export function loadGetterTupleVarAddress(source: TupleReader) {
    const _workchain = source.readBigNumber();
    const _address = source.readCell().asSlice();
    return { $$type: 'VarAddress' as const, workchain: _workchain, address: _address };
}

export function storeTupleVarAddress(source: VarAddress) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.workchain);
    builder.writeSlice(source.address.asCell());
    return builder.build();
}

export function dictValueParserVarAddress(): DictionaryValue<VarAddress> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeVarAddress(src)).endCell());
        },
        parse: (src) => {
            return loadVarAddress(src.loadRef().beginParse());
        }
    }
}

export type BasechainAddress = {
    $$type: 'BasechainAddress';
    hash: bigint | null;
}

export function storeBasechainAddress(src: BasechainAddress) {
    return (builder: Builder) => {
        const b_0 = builder;
        if (src.hash !== null && src.hash !== undefined) { b_0.storeBit(true).storeInt(src.hash, 257); } else { b_0.storeBit(false); }
    };
}

export function loadBasechainAddress(slice: Slice) {
    const sc_0 = slice;
    const _hash = sc_0.loadBit() ? sc_0.loadIntBig(257) : null;
    return { $$type: 'BasechainAddress' as const, hash: _hash };
}

export function loadTupleBasechainAddress(source: TupleReader) {
    const _hash = source.readBigNumberOpt();
    return { $$type: 'BasechainAddress' as const, hash: _hash };
}

export function loadGetterTupleBasechainAddress(source: TupleReader) {
    const _hash = source.readBigNumberOpt();
    return { $$type: 'BasechainAddress' as const, hash: _hash };
}

export function storeTupleBasechainAddress(source: BasechainAddress) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.hash);
    return builder.build();
}

export function dictValueParserBasechainAddress(): DictionaryValue<BasechainAddress> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeBasechainAddress(src)).endCell());
        },
        parse: (src) => {
            return loadBasechainAddress(src.loadRef().beginParse());
        }
    }
}

export type ChangeOwner = {
    $$type: 'ChangeOwner';
    queryId: bigint;
    newOwner: Address;
}

export function storeChangeOwner(src: ChangeOwner) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(2174598809, 32);
        b_0.storeUint(src.queryId, 64);
        b_0.storeAddress(src.newOwner);
    };
}

export function loadChangeOwner(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 2174598809) { throw Error('Invalid prefix'); }
    const _queryId = sc_0.loadUintBig(64);
    const _newOwner = sc_0.loadAddress();
    return { $$type: 'ChangeOwner' as const, queryId: _queryId, newOwner: _newOwner };
}

export function loadTupleChangeOwner(source: TupleReader) {
    const _queryId = source.readBigNumber();
    const _newOwner = source.readAddress();
    return { $$type: 'ChangeOwner' as const, queryId: _queryId, newOwner: _newOwner };
}

export function loadGetterTupleChangeOwner(source: TupleReader) {
    const _queryId = source.readBigNumber();
    const _newOwner = source.readAddress();
    return { $$type: 'ChangeOwner' as const, queryId: _queryId, newOwner: _newOwner };
}

export function storeTupleChangeOwner(source: ChangeOwner) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.queryId);
    builder.writeAddress(source.newOwner);
    return builder.build();
}

export function dictValueParserChangeOwner(): DictionaryValue<ChangeOwner> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeChangeOwner(src)).endCell());
        },
        parse: (src) => {
            return loadChangeOwner(src.loadRef().beginParse());
        }
    }
}

export type ChangeOwnerOk = {
    $$type: 'ChangeOwnerOk';
    queryId: bigint;
    newOwner: Address;
}

export function storeChangeOwnerOk(src: ChangeOwnerOk) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(846932810, 32);
        b_0.storeUint(src.queryId, 64);
        b_0.storeAddress(src.newOwner);
    };
}

export function loadChangeOwnerOk(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 846932810) { throw Error('Invalid prefix'); }
    const _queryId = sc_0.loadUintBig(64);
    const _newOwner = sc_0.loadAddress();
    return { $$type: 'ChangeOwnerOk' as const, queryId: _queryId, newOwner: _newOwner };
}

export function loadTupleChangeOwnerOk(source: TupleReader) {
    const _queryId = source.readBigNumber();
    const _newOwner = source.readAddress();
    return { $$type: 'ChangeOwnerOk' as const, queryId: _queryId, newOwner: _newOwner };
}

export function loadGetterTupleChangeOwnerOk(source: TupleReader) {
    const _queryId = source.readBigNumber();
    const _newOwner = source.readAddress();
    return { $$type: 'ChangeOwnerOk' as const, queryId: _queryId, newOwner: _newOwner };
}

export function storeTupleChangeOwnerOk(source: ChangeOwnerOk) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.queryId);
    builder.writeAddress(source.newOwner);
    return builder.build();
}

export function dictValueParserChangeOwnerOk(): DictionaryValue<ChangeOwnerOk> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeChangeOwnerOk(src)).endCell());
        },
        parse: (src) => {
            return loadChangeOwnerOk(src.loadRef().beginParse());
        }
    }
}

export type Deploy = {
    $$type: 'Deploy';
    queryId: bigint;
}

export function storeDeploy(src: Deploy) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(2490013878, 32);
        b_0.storeUint(src.queryId, 64);
    };
}

export function loadDeploy(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 2490013878) { throw Error('Invalid prefix'); }
    const _queryId = sc_0.loadUintBig(64);
    return { $$type: 'Deploy' as const, queryId: _queryId };
}

export function loadTupleDeploy(source: TupleReader) {
    const _queryId = source.readBigNumber();
    return { $$type: 'Deploy' as const, queryId: _queryId };
}

export function loadGetterTupleDeploy(source: TupleReader) {
    const _queryId = source.readBigNumber();
    return { $$type: 'Deploy' as const, queryId: _queryId };
}

export function storeTupleDeploy(source: Deploy) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.queryId);
    return builder.build();
}

export function dictValueParserDeploy(): DictionaryValue<Deploy> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeDeploy(src)).endCell());
        },
        parse: (src) => {
            return loadDeploy(src.loadRef().beginParse());
        }
    }
}

export type DeployOk = {
    $$type: 'DeployOk';
    queryId: bigint;
}

export function storeDeployOk(src: DeployOk) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(2952335191, 32);
        b_0.storeUint(src.queryId, 64);
    };
}

export function loadDeployOk(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 2952335191) { throw Error('Invalid prefix'); }
    const _queryId = sc_0.loadUintBig(64);
    return { $$type: 'DeployOk' as const, queryId: _queryId };
}

export function loadTupleDeployOk(source: TupleReader) {
    const _queryId = source.readBigNumber();
    return { $$type: 'DeployOk' as const, queryId: _queryId };
}

export function loadGetterTupleDeployOk(source: TupleReader) {
    const _queryId = source.readBigNumber();
    return { $$type: 'DeployOk' as const, queryId: _queryId };
}

export function storeTupleDeployOk(source: DeployOk) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.queryId);
    return builder.build();
}

export function dictValueParserDeployOk(): DictionaryValue<DeployOk> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeDeployOk(src)).endCell());
        },
        parse: (src) => {
            return loadDeployOk(src.loadRef().beginParse());
        }
    }
}

export type FactoryDeploy = {
    $$type: 'FactoryDeploy';
    queryId: bigint;
    cashback: Address;
}

export function storeFactoryDeploy(src: FactoryDeploy) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(1829761339, 32);
        b_0.storeUint(src.queryId, 64);
        b_0.storeAddress(src.cashback);
    };
}

export function loadFactoryDeploy(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 1829761339) { throw Error('Invalid prefix'); }
    const _queryId = sc_0.loadUintBig(64);
    const _cashback = sc_0.loadAddress();
    return { $$type: 'FactoryDeploy' as const, queryId: _queryId, cashback: _cashback };
}

export function loadTupleFactoryDeploy(source: TupleReader) {
    const _queryId = source.readBigNumber();
    const _cashback = source.readAddress();
    return { $$type: 'FactoryDeploy' as const, queryId: _queryId, cashback: _cashback };
}

export function loadGetterTupleFactoryDeploy(source: TupleReader) {
    const _queryId = source.readBigNumber();
    const _cashback = source.readAddress();
    return { $$type: 'FactoryDeploy' as const, queryId: _queryId, cashback: _cashback };
}

export function storeTupleFactoryDeploy(source: FactoryDeploy) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.queryId);
    builder.writeAddress(source.cashback);
    return builder.build();
}

export function dictValueParserFactoryDeploy(): DictionaryValue<FactoryDeploy> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeFactoryDeploy(src)).endCell());
        },
        parse: (src) => {
            return loadFactoryDeploy(src.loadRef().beginParse());
        }
    }
}

export type JettonTransfer = {
    $$type: 'JettonTransfer';
    query_id: bigint;
    amount: bigint;
    destination: Address;
    response_destination: Address;
    custom_payload: Cell | null;
    forward_ton_amount: bigint;
    forward_payload: Cell | null;
}

export function storeJettonTransfer(src: JettonTransfer) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(260734629, 32);
        b_0.storeUint(src.query_id, 64);
        b_0.storeCoins(src.amount);
        b_0.storeAddress(src.destination);
        b_0.storeAddress(src.response_destination);
        if (src.custom_payload !== null && src.custom_payload !== undefined) { b_0.storeBit(true).storeRef(src.custom_payload); } else { b_0.storeBit(false); }
        b_0.storeCoins(src.forward_ton_amount);
        if (src.forward_payload !== null && src.forward_payload !== undefined) { b_0.storeBit(true).storeRef(src.forward_payload); } else { b_0.storeBit(false); }
    };
}

export function loadJettonTransfer(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 260734629) { throw Error('Invalid prefix'); }
    const _query_id = sc_0.loadUintBig(64);
    const _amount = sc_0.loadCoins();
    const _destination = sc_0.loadAddress();
    const _response_destination = sc_0.loadAddress();
    const _custom_payload = sc_0.loadBit() ? sc_0.loadRef() : null;
    const _forward_ton_amount = sc_0.loadCoins();
    const _forward_payload = sc_0.loadBit() ? sc_0.loadRef() : null;
    return { $$type: 'JettonTransfer' as const, query_id: _query_id, amount: _amount, destination: _destination, response_destination: _response_destination, custom_payload: _custom_payload, forward_ton_amount: _forward_ton_amount, forward_payload: _forward_payload };
}

export function loadTupleJettonTransfer(source: TupleReader) {
    const _query_id = source.readBigNumber();
    const _amount = source.readBigNumber();
    const _destination = source.readAddress();
    const _response_destination = source.readAddress();
    const _custom_payload = source.readCellOpt();
    const _forward_ton_amount = source.readBigNumber();
    const _forward_payload = source.readCellOpt();
    return { $$type: 'JettonTransfer' as const, query_id: _query_id, amount: _amount, destination: _destination, response_destination: _response_destination, custom_payload: _custom_payload, forward_ton_amount: _forward_ton_amount, forward_payload: _forward_payload };
}

export function loadGetterTupleJettonTransfer(source: TupleReader) {
    const _query_id = source.readBigNumber();
    const _amount = source.readBigNumber();
    const _destination = source.readAddress();
    const _response_destination = source.readAddress();
    const _custom_payload = source.readCellOpt();
    const _forward_ton_amount = source.readBigNumber();
    const _forward_payload = source.readCellOpt();
    return { $$type: 'JettonTransfer' as const, query_id: _query_id, amount: _amount, destination: _destination, response_destination: _response_destination, custom_payload: _custom_payload, forward_ton_amount: _forward_ton_amount, forward_payload: _forward_payload };
}

export function storeTupleJettonTransfer(source: JettonTransfer) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.query_id);
    builder.writeNumber(source.amount);
    builder.writeAddress(source.destination);
    builder.writeAddress(source.response_destination);
    builder.writeCell(source.custom_payload);
    builder.writeNumber(source.forward_ton_amount);
    builder.writeCell(source.forward_payload);
    return builder.build();
}

export function dictValueParserJettonTransfer(): DictionaryValue<JettonTransfer> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeJettonTransfer(src)).endCell());
        },
        parse: (src) => {
            return loadJettonTransfer(src.loadRef().beginParse());
        }
    }
}

export type JettonTransferNotification = {
    $$type: 'JettonTransferNotification';
    query_id: bigint;
    amount: bigint;
    sender: Address;
    forward_payload: Cell | null;
}

export function storeJettonTransferNotification(src: JettonTransferNotification) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(1935855772, 32);
        b_0.storeUint(src.query_id, 64);
        b_0.storeCoins(src.amount);
        b_0.storeAddress(src.sender);
        if (src.forward_payload !== null && src.forward_payload !== undefined) { b_0.storeBit(true).storeRef(src.forward_payload); } else { b_0.storeBit(false); }
    };
}

export function loadJettonTransferNotification(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 1935855772) { throw Error('Invalid prefix'); }
    const _query_id = sc_0.loadUintBig(64);
    const _amount = sc_0.loadCoins();
    const _sender = sc_0.loadAddress();
    const _forward_payload = sc_0.loadBit() ? sc_0.loadRef() : null;
    return { $$type: 'JettonTransferNotification' as const, query_id: _query_id, amount: _amount, sender: _sender, forward_payload: _forward_payload };
}

export function loadTupleJettonTransferNotification(source: TupleReader) {
    const _query_id = source.readBigNumber();
    const _amount = source.readBigNumber();
    const _sender = source.readAddress();
    const _forward_payload = source.readCellOpt();
    return { $$type: 'JettonTransferNotification' as const, query_id: _query_id, amount: _amount, sender: _sender, forward_payload: _forward_payload };
}

export function loadGetterTupleJettonTransferNotification(source: TupleReader) {
    const _query_id = source.readBigNumber();
    const _amount = source.readBigNumber();
    const _sender = source.readAddress();
    const _forward_payload = source.readCellOpt();
    return { $$type: 'JettonTransferNotification' as const, query_id: _query_id, amount: _amount, sender: _sender, forward_payload: _forward_payload };
}

export function storeTupleJettonTransferNotification(source: JettonTransferNotification) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.query_id);
    builder.writeNumber(source.amount);
    builder.writeAddress(source.sender);
    builder.writeCell(source.forward_payload);
    return builder.build();
}

export function dictValueParserJettonTransferNotification(): DictionaryValue<JettonTransferNotification> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeJettonTransferNotification(src)).endCell());
        },
        parse: (src) => {
            return loadJettonTransferNotification(src.loadRef().beginParse());
        }
    }
}

export type JettonBurn = {
    $$type: 'JettonBurn';
    query_id: bigint;
    amount: bigint;
    response_destination: Address;
    custom_payload: Cell | null;
}

export function storeJettonBurn(src: JettonBurn) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(1499400124, 32);
        b_0.storeUint(src.query_id, 64);
        b_0.storeCoins(src.amount);
        b_0.storeAddress(src.response_destination);
        if (src.custom_payload !== null && src.custom_payload !== undefined) { b_0.storeBit(true).storeRef(src.custom_payload); } else { b_0.storeBit(false); }
    };
}

export function loadJettonBurn(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 1499400124) { throw Error('Invalid prefix'); }
    const _query_id = sc_0.loadUintBig(64);
    const _amount = sc_0.loadCoins();
    const _response_destination = sc_0.loadAddress();
    const _custom_payload = sc_0.loadBit() ? sc_0.loadRef() : null;
    return { $$type: 'JettonBurn' as const, query_id: _query_id, amount: _amount, response_destination: _response_destination, custom_payload: _custom_payload };
}

export function loadTupleJettonBurn(source: TupleReader) {
    const _query_id = source.readBigNumber();
    const _amount = source.readBigNumber();
    const _response_destination = source.readAddress();
    const _custom_payload = source.readCellOpt();
    return { $$type: 'JettonBurn' as const, query_id: _query_id, amount: _amount, response_destination: _response_destination, custom_payload: _custom_payload };
}

export function loadGetterTupleJettonBurn(source: TupleReader) {
    const _query_id = source.readBigNumber();
    const _amount = source.readBigNumber();
    const _response_destination = source.readAddress();
    const _custom_payload = source.readCellOpt();
    return { $$type: 'JettonBurn' as const, query_id: _query_id, amount: _amount, response_destination: _response_destination, custom_payload: _custom_payload };
}

export function storeTupleJettonBurn(source: JettonBurn) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.query_id);
    builder.writeNumber(source.amount);
    builder.writeAddress(source.response_destination);
    builder.writeCell(source.custom_payload);
    return builder.build();
}

export function dictValueParserJettonBurn(): DictionaryValue<JettonBurn> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeJettonBurn(src)).endCell());
        },
        parse: (src) => {
            return loadJettonBurn(src.loadRef().beginParse());
        }
    }
}

export type JettonExcesses = {
    $$type: 'JettonExcesses';
    query_id: bigint;
}

export function storeJettonExcesses(src: JettonExcesses) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(3576854235, 32);
        b_0.storeUint(src.query_id, 64);
    };
}

export function loadJettonExcesses(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 3576854235) { throw Error('Invalid prefix'); }
    const _query_id = sc_0.loadUintBig(64);
    return { $$type: 'JettonExcesses' as const, query_id: _query_id };
}

export function loadTupleJettonExcesses(source: TupleReader) {
    const _query_id = source.readBigNumber();
    return { $$type: 'JettonExcesses' as const, query_id: _query_id };
}

export function loadGetterTupleJettonExcesses(source: TupleReader) {
    const _query_id = source.readBigNumber();
    return { $$type: 'JettonExcesses' as const, query_id: _query_id };
}

export function storeTupleJettonExcesses(source: JettonExcesses) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.query_id);
    return builder.build();
}

export function dictValueParserJettonExcesses(): DictionaryValue<JettonExcesses> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeJettonExcesses(src)).endCell());
        },
        parse: (src) => {
            return loadJettonExcesses(src.loadRef().beginParse());
        }
    }
}

export type JettonInternalTransfer = {
    $$type: 'JettonInternalTransfer';
    query_id: bigint;
    amount: bigint;
    from: Address;
    response_address: Address;
    forward_ton_amount: bigint;
    forward_payload: Cell | null;
}

export function storeJettonInternalTransfer(src: JettonInternalTransfer) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(395134233, 32);
        b_0.storeUint(src.query_id, 64);
        b_0.storeCoins(src.amount);
        b_0.storeAddress(src.from);
        b_0.storeAddress(src.response_address);
        b_0.storeCoins(src.forward_ton_amount);
        if (src.forward_payload !== null && src.forward_payload !== undefined) { b_0.storeBit(true).storeRef(src.forward_payload); } else { b_0.storeBit(false); }
    };
}

export function loadJettonInternalTransfer(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 395134233) { throw Error('Invalid prefix'); }
    const _query_id = sc_0.loadUintBig(64);
    const _amount = sc_0.loadCoins();
    const _from = sc_0.loadAddress();
    const _response_address = sc_0.loadAddress();
    const _forward_ton_amount = sc_0.loadCoins();
    const _forward_payload = sc_0.loadBit() ? sc_0.loadRef() : null;
    return { $$type: 'JettonInternalTransfer' as const, query_id: _query_id, amount: _amount, from: _from, response_address: _response_address, forward_ton_amount: _forward_ton_amount, forward_payload: _forward_payload };
}

export function loadTupleJettonInternalTransfer(source: TupleReader) {
    const _query_id = source.readBigNumber();
    const _amount = source.readBigNumber();
    const _from = source.readAddress();
    const _response_address = source.readAddress();
    const _forward_ton_amount = source.readBigNumber();
    const _forward_payload = source.readCellOpt();
    return { $$type: 'JettonInternalTransfer' as const, query_id: _query_id, amount: _amount, from: _from, response_address: _response_address, forward_ton_amount: _forward_ton_amount, forward_payload: _forward_payload };
}

export function loadGetterTupleJettonInternalTransfer(source: TupleReader) {
    const _query_id = source.readBigNumber();
    const _amount = source.readBigNumber();
    const _from = source.readAddress();
    const _response_address = source.readAddress();
    const _forward_ton_amount = source.readBigNumber();
    const _forward_payload = source.readCellOpt();
    return { $$type: 'JettonInternalTransfer' as const, query_id: _query_id, amount: _amount, from: _from, response_address: _response_address, forward_ton_amount: _forward_ton_amount, forward_payload: _forward_payload };
}

export function storeTupleJettonInternalTransfer(source: JettonInternalTransfer) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.query_id);
    builder.writeNumber(source.amount);
    builder.writeAddress(source.from);
    builder.writeAddress(source.response_address);
    builder.writeNumber(source.forward_ton_amount);
    builder.writeCell(source.forward_payload);
    return builder.build();
}

export function dictValueParserJettonInternalTransfer(): DictionaryValue<JettonInternalTransfer> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeJettonInternalTransfer(src)).endCell());
        },
        parse: (src) => {
            return loadJettonInternalTransfer(src.loadRef().beginParse());
        }
    }
}

export type JettonBurnNotification = {
    $$type: 'JettonBurnNotification';
    query_id: bigint;
    amount: bigint;
    sender: Address;
    response_destination: Address;
}

export function storeJettonBurnNotification(src: JettonBurnNotification) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(2078119902, 32);
        b_0.storeUint(src.query_id, 64);
        b_0.storeCoins(src.amount);
        b_0.storeAddress(src.sender);
        b_0.storeAddress(src.response_destination);
    };
}

export function loadJettonBurnNotification(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 2078119902) { throw Error('Invalid prefix'); }
    const _query_id = sc_0.loadUintBig(64);
    const _amount = sc_0.loadCoins();
    const _sender = sc_0.loadAddress();
    const _response_destination = sc_0.loadAddress();
    return { $$type: 'JettonBurnNotification' as const, query_id: _query_id, amount: _amount, sender: _sender, response_destination: _response_destination };
}

export function loadTupleJettonBurnNotification(source: TupleReader) {
    const _query_id = source.readBigNumber();
    const _amount = source.readBigNumber();
    const _sender = source.readAddress();
    const _response_destination = source.readAddress();
    return { $$type: 'JettonBurnNotification' as const, query_id: _query_id, amount: _amount, sender: _sender, response_destination: _response_destination };
}

export function loadGetterTupleJettonBurnNotification(source: TupleReader) {
    const _query_id = source.readBigNumber();
    const _amount = source.readBigNumber();
    const _sender = source.readAddress();
    const _response_destination = source.readAddress();
    return { $$type: 'JettonBurnNotification' as const, query_id: _query_id, amount: _amount, sender: _sender, response_destination: _response_destination };
}

export function storeTupleJettonBurnNotification(source: JettonBurnNotification) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.query_id);
    builder.writeNumber(source.amount);
    builder.writeAddress(source.sender);
    builder.writeAddress(source.response_destination);
    return builder.build();
}

export function dictValueParserJettonBurnNotification(): DictionaryValue<JettonBurnNotification> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeJettonBurnNotification(src)).endCell());
        },
        parse: (src) => {
            return loadJettonBurnNotification(src.loadRef().beginParse());
        }
    }
}

export type WalletData = {
    $$type: 'WalletData';
    balance: bigint;
    owner: Address;
    jetton: Address;
    jetton_wallet_code: Cell;
}

export function storeWalletData(src: WalletData) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeCoins(src.balance);
        b_0.storeAddress(src.owner);
        b_0.storeAddress(src.jetton);
        b_0.storeRef(src.jetton_wallet_code);
    };
}

export function loadWalletData(slice: Slice) {
    const sc_0 = slice;
    const _balance = sc_0.loadCoins();
    const _owner = sc_0.loadAddress();
    const _jetton = sc_0.loadAddress();
    const _jetton_wallet_code = sc_0.loadRef();
    return { $$type: 'WalletData' as const, balance: _balance, owner: _owner, jetton: _jetton, jetton_wallet_code: _jetton_wallet_code };
}

export function loadTupleWalletData(source: TupleReader) {
    const _balance = source.readBigNumber();
    const _owner = source.readAddress();
    const _jetton = source.readAddress();
    const _jetton_wallet_code = source.readCell();
    return { $$type: 'WalletData' as const, balance: _balance, owner: _owner, jetton: _jetton, jetton_wallet_code: _jetton_wallet_code };
}

export function loadGetterTupleWalletData(source: TupleReader) {
    const _balance = source.readBigNumber();
    const _owner = source.readAddress();
    const _jetton = source.readAddress();
    const _jetton_wallet_code = source.readCell();
    return { $$type: 'WalletData' as const, balance: _balance, owner: _owner, jetton: _jetton, jetton_wallet_code: _jetton_wallet_code };
}

export function storeTupleWalletData(source: WalletData) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.balance);
    builder.writeAddress(source.owner);
    builder.writeAddress(source.jetton);
    builder.writeCell(source.jetton_wallet_code);
    return builder.build();
}

export function dictValueParserWalletData(): DictionaryValue<WalletData> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeWalletData(src)).endCell());
        },
        parse: (src) => {
            return loadWalletData(src.loadRef().beginParse());
        }
    }
}

export type CreateUserPurchase = {
    $$type: 'CreateUserPurchase';
    user: Address;
    amount: bigint;
    tokens: bigint;
    currency: bigint;
    purchase_method: bigint;
    nonce: bigint;
    round_number: bigint;
    usdt_equivalent_amount: bigint;
}

export function storeCreateUserPurchase(src: CreateUserPurchase) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(1098075148, 32);
        b_0.storeAddress(src.user);
        b_0.storeCoins(src.amount);
        b_0.storeCoins(src.tokens);
        b_0.storeUint(src.currency, 8);
        b_0.storeUint(src.purchase_method, 8);
        b_0.storeUint(src.nonce, 64);
        b_0.storeUint(src.round_number, 32);
        b_0.storeCoins(src.usdt_equivalent_amount);
    };
}

export function loadCreateUserPurchase(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 1098075148) { throw Error('Invalid prefix'); }
    const _user = sc_0.loadAddress();
    const _amount = sc_0.loadCoins();
    const _tokens = sc_0.loadCoins();
    const _currency = sc_0.loadUintBig(8);
    const _purchase_method = sc_0.loadUintBig(8);
    const _nonce = sc_0.loadUintBig(64);
    const _round_number = sc_0.loadUintBig(32);
    const _usdt_equivalent_amount = sc_0.loadCoins();
    return { $$type: 'CreateUserPurchase' as const, user: _user, amount: _amount, tokens: _tokens, currency: _currency, purchase_method: _purchase_method, nonce: _nonce, round_number: _round_number, usdt_equivalent_amount: _usdt_equivalent_amount };
}

export function loadTupleCreateUserPurchase(source: TupleReader) {
    const _user = source.readAddress();
    const _amount = source.readBigNumber();
    const _tokens = source.readBigNumber();
    const _currency = source.readBigNumber();
    const _purchase_method = source.readBigNumber();
    const _nonce = source.readBigNumber();
    const _round_number = source.readBigNumber();
    const _usdt_equivalent_amount = source.readBigNumber();
    return { $$type: 'CreateUserPurchase' as const, user: _user, amount: _amount, tokens: _tokens, currency: _currency, purchase_method: _purchase_method, nonce: _nonce, round_number: _round_number, usdt_equivalent_amount: _usdt_equivalent_amount };
}

export function loadGetterTupleCreateUserPurchase(source: TupleReader) {
    const _user = source.readAddress();
    const _amount = source.readBigNumber();
    const _tokens = source.readBigNumber();
    const _currency = source.readBigNumber();
    const _purchase_method = source.readBigNumber();
    const _nonce = source.readBigNumber();
    const _round_number = source.readBigNumber();
    const _usdt_equivalent_amount = source.readBigNumber();
    return { $$type: 'CreateUserPurchase' as const, user: _user, amount: _amount, tokens: _tokens, currency: _currency, purchase_method: _purchase_method, nonce: _nonce, round_number: _round_number, usdt_equivalent_amount: _usdt_equivalent_amount };
}

export function storeTupleCreateUserPurchase(source: CreateUserPurchase) {
    const builder = new TupleBuilder();
    builder.writeAddress(source.user);
    builder.writeNumber(source.amount);
    builder.writeNumber(source.tokens);
    builder.writeNumber(source.currency);
    builder.writeNumber(source.purchase_method);
    builder.writeNumber(source.nonce);
    builder.writeNumber(source.round_number);
    builder.writeNumber(source.usdt_equivalent_amount);
    return builder.build();
}

export function dictValueParserCreateUserPurchase(): DictionaryValue<CreateUserPurchase> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeCreateUserPurchase(src)).endCell());
        },
        parse: (src) => {
            return loadCreateUserPurchase(src.loadRef().beginParse());
        }
    }
}

export type Refund = {
    $$type: 'Refund';
    purchase_id: bigint;
}

export function storeRefund(src: Refund) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(836089260, 32);
        b_0.storeUint(src.purchase_id, 32);
    };
}

export function loadRefund(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 836089260) { throw Error('Invalid prefix'); }
    const _purchase_id = sc_0.loadUintBig(32);
    return { $$type: 'Refund' as const, purchase_id: _purchase_id };
}

export function loadTupleRefund(source: TupleReader) {
    const _purchase_id = source.readBigNumber();
    return { $$type: 'Refund' as const, purchase_id: _purchase_id };
}

export function loadGetterTupleRefund(source: TupleReader) {
    const _purchase_id = source.readBigNumber();
    return { $$type: 'Refund' as const, purchase_id: _purchase_id };
}

export function storeTupleRefund(source: Refund) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.purchase_id);
    return builder.build();
}

export function dictValueParserRefund(): DictionaryValue<Refund> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeRefund(src)).endCell());
        },
        parse: (src) => {
            return loadRefund(src.loadRef().beginParse());
        }
    }
}

export type ProcessRefund = {
    $$type: 'ProcessRefund';
    user: Address;
    amount: bigint;
    fee: bigint;
    currency: bigint;
    round_number: bigint;
    usdt_equivalent_amount: bigint;
}

export function storeProcessRefund(src: ProcessRefund) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(929991442, 32);
        b_0.storeAddress(src.user);
        b_0.storeCoins(src.amount);
        b_0.storeCoins(src.fee);
        b_0.storeUint(src.currency, 8);
        b_0.storeUint(src.round_number, 32);
        b_0.storeCoins(src.usdt_equivalent_amount);
    };
}

export function loadProcessRefund(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 929991442) { throw Error('Invalid prefix'); }
    const _user = sc_0.loadAddress();
    const _amount = sc_0.loadCoins();
    const _fee = sc_0.loadCoins();
    const _currency = sc_0.loadUintBig(8);
    const _round_number = sc_0.loadUintBig(32);
    const _usdt_equivalent_amount = sc_0.loadCoins();
    return { $$type: 'ProcessRefund' as const, user: _user, amount: _amount, fee: _fee, currency: _currency, round_number: _round_number, usdt_equivalent_amount: _usdt_equivalent_amount };
}

export function loadTupleProcessRefund(source: TupleReader) {
    const _user = source.readAddress();
    const _amount = source.readBigNumber();
    const _fee = source.readBigNumber();
    const _currency = source.readBigNumber();
    const _round_number = source.readBigNumber();
    const _usdt_equivalent_amount = source.readBigNumber();
    return { $$type: 'ProcessRefund' as const, user: _user, amount: _amount, fee: _fee, currency: _currency, round_number: _round_number, usdt_equivalent_amount: _usdt_equivalent_amount };
}

export function loadGetterTupleProcessRefund(source: TupleReader) {
    const _user = source.readAddress();
    const _amount = source.readBigNumber();
    const _fee = source.readBigNumber();
    const _currency = source.readBigNumber();
    const _round_number = source.readBigNumber();
    const _usdt_equivalent_amount = source.readBigNumber();
    return { $$type: 'ProcessRefund' as const, user: _user, amount: _amount, fee: _fee, currency: _currency, round_number: _round_number, usdt_equivalent_amount: _usdt_equivalent_amount };
}

export function storeTupleProcessRefund(source: ProcessRefund) {
    const builder = new TupleBuilder();
    builder.writeAddress(source.user);
    builder.writeNumber(source.amount);
    builder.writeNumber(source.fee);
    builder.writeNumber(source.currency);
    builder.writeNumber(source.round_number);
    builder.writeNumber(source.usdt_equivalent_amount);
    return builder.build();
}

export function dictValueParserProcessRefund(): DictionaryValue<ProcessRefund> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeProcessRefund(src)).endCell());
        },
        parse: (src) => {
            return loadProcessRefund(src.loadRef().beginParse());
        }
    }
}

export type PurchaseRecord = {
    $$type: 'PurchaseRecord';
    id: bigint;
    user: Address;
    amount: bigint;
    tokens: bigint;
    timestamp: bigint;
    currency: bigint;
    purchase_method: bigint;
    nonce: bigint;
    round_number: bigint;
    usdt_equivalent_amount: bigint;
}

export function storePurchaseRecord(src: PurchaseRecord) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(src.id, 32);
        b_0.storeAddress(src.user);
        b_0.storeCoins(src.amount);
        b_0.storeCoins(src.tokens);
        b_0.storeUint(src.timestamp, 64);
        b_0.storeUint(src.currency, 8);
        b_0.storeUint(src.purchase_method, 8);
        b_0.storeUint(src.nonce, 64);
        b_0.storeUint(src.round_number, 32);
        b_0.storeCoins(src.usdt_equivalent_amount);
    };
}

export function loadPurchaseRecord(slice: Slice) {
    const sc_0 = slice;
    const _id = sc_0.loadUintBig(32);
    const _user = sc_0.loadAddress();
    const _amount = sc_0.loadCoins();
    const _tokens = sc_0.loadCoins();
    const _timestamp = sc_0.loadUintBig(64);
    const _currency = sc_0.loadUintBig(8);
    const _purchase_method = sc_0.loadUintBig(8);
    const _nonce = sc_0.loadUintBig(64);
    const _round_number = sc_0.loadUintBig(32);
    const _usdt_equivalent_amount = sc_0.loadCoins();
    return { $$type: 'PurchaseRecord' as const, id: _id, user: _user, amount: _amount, tokens: _tokens, timestamp: _timestamp, currency: _currency, purchase_method: _purchase_method, nonce: _nonce, round_number: _round_number, usdt_equivalent_amount: _usdt_equivalent_amount };
}

export function loadTuplePurchaseRecord(source: TupleReader) {
    const _id = source.readBigNumber();
    const _user = source.readAddress();
    const _amount = source.readBigNumber();
    const _tokens = source.readBigNumber();
    const _timestamp = source.readBigNumber();
    const _currency = source.readBigNumber();
    const _purchase_method = source.readBigNumber();
    const _nonce = source.readBigNumber();
    const _round_number = source.readBigNumber();
    const _usdt_equivalent_amount = source.readBigNumber();
    return { $$type: 'PurchaseRecord' as const, id: _id, user: _user, amount: _amount, tokens: _tokens, timestamp: _timestamp, currency: _currency, purchase_method: _purchase_method, nonce: _nonce, round_number: _round_number, usdt_equivalent_amount: _usdt_equivalent_amount };
}

export function loadGetterTuplePurchaseRecord(source: TupleReader) {
    const _id = source.readBigNumber();
    const _user = source.readAddress();
    const _amount = source.readBigNumber();
    const _tokens = source.readBigNumber();
    const _timestamp = source.readBigNumber();
    const _currency = source.readBigNumber();
    const _purchase_method = source.readBigNumber();
    const _nonce = source.readBigNumber();
    const _round_number = source.readBigNumber();
    const _usdt_equivalent_amount = source.readBigNumber();
    return { $$type: 'PurchaseRecord' as const, id: _id, user: _user, amount: _amount, tokens: _tokens, timestamp: _timestamp, currency: _currency, purchase_method: _purchase_method, nonce: _nonce, round_number: _round_number, usdt_equivalent_amount: _usdt_equivalent_amount };
}

export function storeTuplePurchaseRecord(source: PurchaseRecord) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.id);
    builder.writeAddress(source.user);
    builder.writeNumber(source.amount);
    builder.writeNumber(source.tokens);
    builder.writeNumber(source.timestamp);
    builder.writeNumber(source.currency);
    builder.writeNumber(source.purchase_method);
    builder.writeNumber(source.nonce);
    builder.writeNumber(source.round_number);
    builder.writeNumber(source.usdt_equivalent_amount);
    return builder.build();
}

export function dictValueParserPurchaseRecord(): DictionaryValue<PurchaseRecord> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storePurchaseRecord(src)).endCell());
        },
        parse: (src) => {
            return loadPurchaseRecord(src.loadRef().beginParse());
        }
    }
}

export type UserPurchase$Data = {
    $$type: 'UserPurchase$Data';
    owner: Address;
    auction_address: Address;
    user_address: Address;
    total_purchased: bigint;
    total_paid: bigint;
    purchase_history: Dictionary<bigint, PurchaseRecord>;
    refund_history: Dictionary<bigint, bigint>;
    purchase_id_counter: bigint;
    participated_rounds: Dictionary<bigint, boolean>;
}

export function storeUserPurchase$Data(src: UserPurchase$Data) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeAddress(src.owner);
        b_0.storeAddress(src.auction_address);
        b_0.storeAddress(src.user_address);
        b_0.storeCoins(src.total_purchased);
        const b_1 = new Builder();
        b_1.storeCoins(src.total_paid);
        b_1.storeDict(src.purchase_history, Dictionary.Keys.BigInt(257), dictValueParserPurchaseRecord());
        b_1.storeDict(src.refund_history, Dictionary.Keys.BigInt(257), Dictionary.Values.BigInt(257));
        b_1.storeUint(src.purchase_id_counter, 32);
        b_1.storeDict(src.participated_rounds, Dictionary.Keys.BigInt(257), Dictionary.Values.Bool());
        b_0.storeRef(b_1.endCell());
    };
}

export function loadUserPurchase$Data(slice: Slice) {
    const sc_0 = slice;
    const _owner = sc_0.loadAddress();
    const _auction_address = sc_0.loadAddress();
    const _user_address = sc_0.loadAddress();
    const _total_purchased = sc_0.loadCoins();
    const sc_1 = sc_0.loadRef().beginParse();
    const _total_paid = sc_1.loadCoins();
    const _purchase_history = Dictionary.load(Dictionary.Keys.BigInt(257), dictValueParserPurchaseRecord(), sc_1);
    const _refund_history = Dictionary.load(Dictionary.Keys.BigInt(257), Dictionary.Values.BigInt(257), sc_1);
    const _purchase_id_counter = sc_1.loadUintBig(32);
    const _participated_rounds = Dictionary.load(Dictionary.Keys.BigInt(257), Dictionary.Values.Bool(), sc_1);
    return { $$type: 'UserPurchase$Data' as const, owner: _owner, auction_address: _auction_address, user_address: _user_address, total_purchased: _total_purchased, total_paid: _total_paid, purchase_history: _purchase_history, refund_history: _refund_history, purchase_id_counter: _purchase_id_counter, participated_rounds: _participated_rounds };
}

export function loadTupleUserPurchase$Data(source: TupleReader) {
    const _owner = source.readAddress();
    const _auction_address = source.readAddress();
    const _user_address = source.readAddress();
    const _total_purchased = source.readBigNumber();
    const _total_paid = source.readBigNumber();
    const _purchase_history = Dictionary.loadDirect(Dictionary.Keys.BigInt(257), dictValueParserPurchaseRecord(), source.readCellOpt());
    const _refund_history = Dictionary.loadDirect(Dictionary.Keys.BigInt(257), Dictionary.Values.BigInt(257), source.readCellOpt());
    const _purchase_id_counter = source.readBigNumber();
    const _participated_rounds = Dictionary.loadDirect(Dictionary.Keys.BigInt(257), Dictionary.Values.Bool(), source.readCellOpt());
    return { $$type: 'UserPurchase$Data' as const, owner: _owner, auction_address: _auction_address, user_address: _user_address, total_purchased: _total_purchased, total_paid: _total_paid, purchase_history: _purchase_history, refund_history: _refund_history, purchase_id_counter: _purchase_id_counter, participated_rounds: _participated_rounds };
}

export function loadGetterTupleUserPurchase$Data(source: TupleReader) {
    const _owner = source.readAddress();
    const _auction_address = source.readAddress();
    const _user_address = source.readAddress();
    const _total_purchased = source.readBigNumber();
    const _total_paid = source.readBigNumber();
    const _purchase_history = Dictionary.loadDirect(Dictionary.Keys.BigInt(257), dictValueParserPurchaseRecord(), source.readCellOpt());
    const _refund_history = Dictionary.loadDirect(Dictionary.Keys.BigInt(257), Dictionary.Values.BigInt(257), source.readCellOpt());
    const _purchase_id_counter = source.readBigNumber();
    const _participated_rounds = Dictionary.loadDirect(Dictionary.Keys.BigInt(257), Dictionary.Values.Bool(), source.readCellOpt());
    return { $$type: 'UserPurchase$Data' as const, owner: _owner, auction_address: _auction_address, user_address: _user_address, total_purchased: _total_purchased, total_paid: _total_paid, purchase_history: _purchase_history, refund_history: _refund_history, purchase_id_counter: _purchase_id_counter, participated_rounds: _participated_rounds };
}

export function storeTupleUserPurchase$Data(source: UserPurchase$Data) {
    const builder = new TupleBuilder();
    builder.writeAddress(source.owner);
    builder.writeAddress(source.auction_address);
    builder.writeAddress(source.user_address);
    builder.writeNumber(source.total_purchased);
    builder.writeNumber(source.total_paid);
    builder.writeCell(source.purchase_history.size > 0 ? beginCell().storeDictDirect(source.purchase_history, Dictionary.Keys.BigInt(257), dictValueParserPurchaseRecord()).endCell() : null);
    builder.writeCell(source.refund_history.size > 0 ? beginCell().storeDictDirect(source.refund_history, Dictionary.Keys.BigInt(257), Dictionary.Values.BigInt(257)).endCell() : null);
    builder.writeNumber(source.purchase_id_counter);
    builder.writeCell(source.participated_rounds.size > 0 ? beginCell().storeDictDirect(source.participated_rounds, Dictionary.Keys.BigInt(257), Dictionary.Values.Bool()).endCell() : null);
    return builder.build();
}

export function dictValueParserUserPurchase$Data(): DictionaryValue<UserPurchase$Data> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeUserPurchase$Data(src)).endCell());
        },
        parse: (src) => {
            return loadUserPurchase$Data(src.loadRef().beginParse());
        }
    }
}

export type StartAuction = {
    $$type: 'StartAuction';
    start_time: bigint;
    end_time: bigint;
    soft_cap: bigint;
    hard_cap: bigint;
    initial_price: bigint;
}

export function storeStartAuction(src: StartAuction) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(1082886929, 32);
        b_0.storeUint(src.start_time, 64);
        b_0.storeUint(src.end_time, 64);
        b_0.storeCoins(src.soft_cap);
        b_0.storeCoins(src.hard_cap);
        b_0.storeCoins(src.initial_price);
    };
}

export function loadStartAuction(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 1082886929) { throw Error('Invalid prefix'); }
    const _start_time = sc_0.loadUintBig(64);
    const _end_time = sc_0.loadUintBig(64);
    const _soft_cap = sc_0.loadCoins();
    const _hard_cap = sc_0.loadCoins();
    const _initial_price = sc_0.loadCoins();
    return { $$type: 'StartAuction' as const, start_time: _start_time, end_time: _end_time, soft_cap: _soft_cap, hard_cap: _hard_cap, initial_price: _initial_price };
}

export function loadTupleStartAuction(source: TupleReader) {
    const _start_time = source.readBigNumber();
    const _end_time = source.readBigNumber();
    const _soft_cap = source.readBigNumber();
    const _hard_cap = source.readBigNumber();
    const _initial_price = source.readBigNumber();
    return { $$type: 'StartAuction' as const, start_time: _start_time, end_time: _end_time, soft_cap: _soft_cap, hard_cap: _hard_cap, initial_price: _initial_price };
}

export function loadGetterTupleStartAuction(source: TupleReader) {
    const _start_time = source.readBigNumber();
    const _end_time = source.readBigNumber();
    const _soft_cap = source.readBigNumber();
    const _hard_cap = source.readBigNumber();
    const _initial_price = source.readBigNumber();
    return { $$type: 'StartAuction' as const, start_time: _start_time, end_time: _end_time, soft_cap: _soft_cap, hard_cap: _hard_cap, initial_price: _initial_price };
}

export function storeTupleStartAuction(source: StartAuction) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.start_time);
    builder.writeNumber(source.end_time);
    builder.writeNumber(source.soft_cap);
    builder.writeNumber(source.hard_cap);
    builder.writeNumber(source.initial_price);
    return builder.build();
}

export function dictValueParserStartAuction(): DictionaryValue<StartAuction> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeStartAuction(src)).endCell());
        },
        parse: (src) => {
            return loadStartAuction(src.loadRef().beginParse());
        }
    }
}

export type UpdateRound = {
    $$type: 'UpdateRound';
    new_price: bigint;
    round_number: bigint;
}

export function storeUpdateRound(src: UpdateRound) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(2861271945, 32);
        b_0.storeCoins(src.new_price);
        b_0.storeUint(src.round_number, 32);
    };
}

export function loadUpdateRound(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 2861271945) { throw Error('Invalid prefix'); }
    const _new_price = sc_0.loadCoins();
    const _round_number = sc_0.loadUintBig(32);
    return { $$type: 'UpdateRound' as const, new_price: _new_price, round_number: _round_number };
}

export function loadTupleUpdateRound(source: TupleReader) {
    const _new_price = source.readBigNumber();
    const _round_number = source.readBigNumber();
    return { $$type: 'UpdateRound' as const, new_price: _new_price, round_number: _round_number };
}

export function loadGetterTupleUpdateRound(source: TupleReader) {
    const _new_price = source.readBigNumber();
    const _round_number = source.readBigNumber();
    return { $$type: 'UpdateRound' as const, new_price: _new_price, round_number: _round_number };
}

export function storeTupleUpdateRound(source: UpdateRound) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.new_price);
    builder.writeNumber(source.round_number);
    return builder.build();
}

export function dictValueParserUpdateRound(): DictionaryValue<UpdateRound> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeUpdateRound(src)).endCell());
        },
        parse: (src) => {
            return loadUpdateRound(src.loadRef().beginParse());
        }
    }
}

export type SetUSDTAddress = {
    $$type: 'SetUSDTAddress';
    usdt_master: Address;
    usdt_wallet: Address;
}

export function storeSetUSDTAddress(src: SetUSDTAddress) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(2703444734, 32);
        b_0.storeAddress(src.usdt_master);
        b_0.storeAddress(src.usdt_wallet);
    };
}

export function loadSetUSDTAddress(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 2703444734) { throw Error('Invalid prefix'); }
    const _usdt_master = sc_0.loadAddress();
    const _usdt_wallet = sc_0.loadAddress();
    return { $$type: 'SetUSDTAddress' as const, usdt_master: _usdt_master, usdt_wallet: _usdt_wallet };
}

export function loadTupleSetUSDTAddress(source: TupleReader) {
    const _usdt_master = source.readAddress();
    const _usdt_wallet = source.readAddress();
    return { $$type: 'SetUSDTAddress' as const, usdt_master: _usdt_master, usdt_wallet: _usdt_wallet };
}

export function loadGetterTupleSetUSDTAddress(source: TupleReader) {
    const _usdt_master = source.readAddress();
    const _usdt_wallet = source.readAddress();
    return { $$type: 'SetUSDTAddress' as const, usdt_master: _usdt_master, usdt_wallet: _usdt_wallet };
}

export function storeTupleSetUSDTAddress(source: SetUSDTAddress) {
    const builder = new TupleBuilder();
    builder.writeAddress(source.usdt_master);
    builder.writeAddress(source.usdt_wallet);
    return builder.build();
}

export function dictValueParserSetUSDTAddress(): DictionaryValue<SetUSDTAddress> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeSetUSDTAddress(src)).endCell());
        },
        parse: (src) => {
            return loadSetUSDTAddress(src.loadRef().beginParse());
        }
    }
}

export type PurchaseWithSignature = {
    $$type: 'PurchaseWithSignature';
    calculation: PurchaseCalculation;
    signature: Slice;
}

export function storePurchaseWithSignature(src: PurchaseWithSignature) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(1524770820, 32);
        b_0.store(storePurchaseCalculation(src.calculation));
        b_0.storeRef(src.signature.asCell());
    };
}

export function loadPurchaseWithSignature(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 1524770820) { throw Error('Invalid prefix'); }
    const _calculation = loadPurchaseCalculation(sc_0);
    const _signature = sc_0.loadRef().asSlice();
    return { $$type: 'PurchaseWithSignature' as const, calculation: _calculation, signature: _signature };
}

export function loadTuplePurchaseWithSignature(source: TupleReader) {
    const _calculation = loadTuplePurchaseCalculation(source);
    const _signature = source.readCell().asSlice();
    return { $$type: 'PurchaseWithSignature' as const, calculation: _calculation, signature: _signature };
}

export function loadGetterTuplePurchaseWithSignature(source: TupleReader) {
    const _calculation = loadGetterTuplePurchaseCalculation(source);
    const _signature = source.readCell().asSlice();
    return { $$type: 'PurchaseWithSignature' as const, calculation: _calculation, signature: _signature };
}

export function storeTuplePurchaseWithSignature(source: PurchaseWithSignature) {
    const builder = new TupleBuilder();
    builder.writeTuple(storeTuplePurchaseCalculation(source.calculation));
    builder.writeSlice(source.signature.asCell());
    return builder.build();
}

export function dictValueParserPurchaseWithSignature(): DictionaryValue<PurchaseWithSignature> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storePurchaseWithSignature(src)).endCell());
        },
        parse: (src) => {
            return loadPurchaseWithSignature(src.loadRef().beginParse());
        }
    }
}

export type SetSigningKey = {
    $$type: 'SetSigningKey';
    public_key: bigint;
}

export function storeSetSigningKey(src: SetSigningKey) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(3315975678, 32);
        b_0.storeUint(src.public_key, 256);
    };
}

export function loadSetSigningKey(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 3315975678) { throw Error('Invalid prefix'); }
    const _public_key = sc_0.loadUintBig(256);
    return { $$type: 'SetSigningKey' as const, public_key: _public_key };
}

export function loadTupleSetSigningKey(source: TupleReader) {
    const _public_key = source.readBigNumber();
    return { $$type: 'SetSigningKey' as const, public_key: _public_key };
}

export function loadGetterTupleSetSigningKey(source: TupleReader) {
    const _public_key = source.readBigNumber();
    return { $$type: 'SetSigningKey' as const, public_key: _public_key };
}

export function storeTupleSetSigningKey(source: SetSigningKey) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.public_key);
    return builder.build();
}

export function dictValueParserSetSigningKey(): DictionaryValue<SetSigningKey> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeSetSigningKey(src)).endCell());
        },
        parse: (src) => {
            return loadSetSigningKey(src.loadRef().beginParse());
        }
    }
}

export type SetMinPurchase = {
    $$type: 'SetMinPurchase';
    min_purchase: bigint;
}

export function storeSetMinPurchase(src: SetMinPurchase) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(3390874056, 32);
        b_0.storeCoins(src.min_purchase);
    };
}

export function loadSetMinPurchase(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 3390874056) { throw Error('Invalid prefix'); }
    const _min_purchase = sc_0.loadCoins();
    return { $$type: 'SetMinPurchase' as const, min_purchase: _min_purchase };
}

export function loadTupleSetMinPurchase(source: TupleReader) {
    const _min_purchase = source.readBigNumber();
    return { $$type: 'SetMinPurchase' as const, min_purchase: _min_purchase };
}

export function loadGetterTupleSetMinPurchase(source: TupleReader) {
    const _min_purchase = source.readBigNumber();
    return { $$type: 'SetMinPurchase' as const, min_purchase: _min_purchase };
}

export function storeTupleSetMinPurchase(source: SetMinPurchase) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.min_purchase);
    return builder.build();
}

export function dictValueParserSetMinPurchase(): DictionaryValue<SetMinPurchase> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeSetMinPurchase(src)).endCell());
        },
        parse: (src) => {
            return loadSetMinPurchase(src.loadRef().beginParse());
        }
    }
}

export type WithdrawTON = {
    $$type: 'WithdrawTON';
    amount: bigint;
    destination: Address;
}

export function storeWithdrawTON(src: WithdrawTON) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(3520188881, 32);
        b_0.storeCoins(src.amount);
        b_0.storeAddress(src.destination);
    };
}

export function loadWithdrawTON(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 3520188881) { throw Error('Invalid prefix'); }
    const _amount = sc_0.loadCoins();
    const _destination = sc_0.loadAddress();
    return { $$type: 'WithdrawTON' as const, amount: _amount, destination: _destination };
}

export function loadTupleWithdrawTON(source: TupleReader) {
    const _amount = source.readBigNumber();
    const _destination = source.readAddress();
    return { $$type: 'WithdrawTON' as const, amount: _amount, destination: _destination };
}

export function loadGetterTupleWithdrawTON(source: TupleReader) {
    const _amount = source.readBigNumber();
    const _destination = source.readAddress();
    return { $$type: 'WithdrawTON' as const, amount: _amount, destination: _destination };
}

export function storeTupleWithdrawTON(source: WithdrawTON) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.amount);
    builder.writeAddress(source.destination);
    return builder.build();
}

export function dictValueParserWithdrawTON(): DictionaryValue<WithdrawTON> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeWithdrawTON(src)).endCell());
        },
        parse: (src) => {
            return loadWithdrawTON(src.loadRef().beginParse());
        }
    }
}

export type WithdrawUSDT = {
    $$type: 'WithdrawUSDT';
    amount: bigint;
    destination: Address;
}

export function storeWithdrawUSDT(src: WithdrawUSDT) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(3537031890, 32);
        b_0.storeCoins(src.amount);
        b_0.storeAddress(src.destination);
    };
}

export function loadWithdrawUSDT(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 3537031890) { throw Error('Invalid prefix'); }
    const _amount = sc_0.loadCoins();
    const _destination = sc_0.loadAddress();
    return { $$type: 'WithdrawUSDT' as const, amount: _amount, destination: _destination };
}

export function loadTupleWithdrawUSDT(source: TupleReader) {
    const _amount = source.readBigNumber();
    const _destination = source.readAddress();
    return { $$type: 'WithdrawUSDT' as const, amount: _amount, destination: _destination };
}

export function loadGetterTupleWithdrawUSDT(source: TupleReader) {
    const _amount = source.readBigNumber();
    const _destination = source.readAddress();
    return { $$type: 'WithdrawUSDT' as const, amount: _amount, destination: _destination };
}

export function storeTupleWithdrawUSDT(source: WithdrawUSDT) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.amount);
    builder.writeAddress(source.destination);
    return builder.build();
}

export function dictValueParserWithdrawUSDT(): DictionaryValue<WithdrawUSDT> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeWithdrawUSDT(src)).endCell());
        },
        parse: (src) => {
            return loadWithdrawUSDT(src.loadRef().beginParse());
        }
    }
}

export type SetTreasury = {
    $$type: 'SetTreasury';
    treasury_address: Address;
}

export function storeSetTreasury(src: SetTreasury) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(3553874899, 32);
        b_0.storeAddress(src.treasury_address);
    };
}

export function loadSetTreasury(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 3553874899) { throw Error('Invalid prefix'); }
    const _treasury_address = sc_0.loadAddress();
    return { $$type: 'SetTreasury' as const, treasury_address: _treasury_address };
}

export function loadTupleSetTreasury(source: TupleReader) {
    const _treasury_address = source.readAddress();
    return { $$type: 'SetTreasury' as const, treasury_address: _treasury_address };
}

export function loadGetterTupleSetTreasury(source: TupleReader) {
    const _treasury_address = source.readAddress();
    return { $$type: 'SetTreasury' as const, treasury_address: _treasury_address };
}

export function storeTupleSetTreasury(source: SetTreasury) {
    const builder = new TupleBuilder();
    builder.writeAddress(source.treasury_address);
    return builder.build();
}

export function dictValueParserSetTreasury(): DictionaryValue<SetTreasury> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeSetTreasury(src)).endCell());
        },
        parse: (src) => {
            return loadSetTreasury(src.loadRef().beginParse());
        }
    }
}

export type AuctionStarted = {
    $$type: 'AuctionStarted';
    start_time: bigint;
    end_time: bigint;
    soft_cap: bigint;
    hard_cap: bigint;
    initial_price: bigint;
    total_supply: bigint;
}

export function storeAuctionStarted(src: AuctionStarted) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(2217068725, 32);
        b_0.storeUint(src.start_time, 64);
        b_0.storeUint(src.end_time, 64);
        b_0.storeCoins(src.soft_cap);
        b_0.storeCoins(src.hard_cap);
        b_0.storeCoins(src.initial_price);
        b_0.storeCoins(src.total_supply);
    };
}

export function loadAuctionStarted(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 2217068725) { throw Error('Invalid prefix'); }
    const _start_time = sc_0.loadUintBig(64);
    const _end_time = sc_0.loadUintBig(64);
    const _soft_cap = sc_0.loadCoins();
    const _hard_cap = sc_0.loadCoins();
    const _initial_price = sc_0.loadCoins();
    const _total_supply = sc_0.loadCoins();
    return { $$type: 'AuctionStarted' as const, start_time: _start_time, end_time: _end_time, soft_cap: _soft_cap, hard_cap: _hard_cap, initial_price: _initial_price, total_supply: _total_supply };
}

export function loadTupleAuctionStarted(source: TupleReader) {
    const _start_time = source.readBigNumber();
    const _end_time = source.readBigNumber();
    const _soft_cap = source.readBigNumber();
    const _hard_cap = source.readBigNumber();
    const _initial_price = source.readBigNumber();
    const _total_supply = source.readBigNumber();
    return { $$type: 'AuctionStarted' as const, start_time: _start_time, end_time: _end_time, soft_cap: _soft_cap, hard_cap: _hard_cap, initial_price: _initial_price, total_supply: _total_supply };
}

export function loadGetterTupleAuctionStarted(source: TupleReader) {
    const _start_time = source.readBigNumber();
    const _end_time = source.readBigNumber();
    const _soft_cap = source.readBigNumber();
    const _hard_cap = source.readBigNumber();
    const _initial_price = source.readBigNumber();
    const _total_supply = source.readBigNumber();
    return { $$type: 'AuctionStarted' as const, start_time: _start_time, end_time: _end_time, soft_cap: _soft_cap, hard_cap: _hard_cap, initial_price: _initial_price, total_supply: _total_supply };
}

export function storeTupleAuctionStarted(source: AuctionStarted) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.start_time);
    builder.writeNumber(source.end_time);
    builder.writeNumber(source.soft_cap);
    builder.writeNumber(source.hard_cap);
    builder.writeNumber(source.initial_price);
    builder.writeNumber(source.total_supply);
    return builder.build();
}

export function dictValueParserAuctionStarted(): DictionaryValue<AuctionStarted> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeAuctionStarted(src)).endCell());
        },
        parse: (src) => {
            return loadAuctionStarted(src.loadRef().beginParse());
        }
    }
}

export type PurchaseCompleted = {
    $$type: 'PurchaseCompleted';
    user: Address;
    amount: bigint;
    tokens_received: bigint;
    currency: bigint;
    purchase_method: bigint;
    round_number: bigint;
    nonce: bigint;
    total_raised: bigint;
    total_raised_usdt: bigint;
    total_tokens_sold: bigint;
}

export function storePurchaseCompleted(src: PurchaseCompleted) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(3975568033, 32);
        b_0.storeAddress(src.user);
        b_0.storeCoins(src.amount);
        b_0.storeCoins(src.tokens_received);
        b_0.storeUint(src.currency, 8);
        b_0.storeUint(src.purchase_method, 8);
        b_0.storeUint(src.round_number, 32);
        b_0.storeUint(src.nonce, 64);
        b_0.storeCoins(src.total_raised);
        b_0.storeCoins(src.total_raised_usdt);
        const b_1 = new Builder();
        b_1.storeCoins(src.total_tokens_sold);
        b_0.storeRef(b_1.endCell());
    };
}

export function loadPurchaseCompleted(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 3975568033) { throw Error('Invalid prefix'); }
    const _user = sc_0.loadAddress();
    const _amount = sc_0.loadCoins();
    const _tokens_received = sc_0.loadCoins();
    const _currency = sc_0.loadUintBig(8);
    const _purchase_method = sc_0.loadUintBig(8);
    const _round_number = sc_0.loadUintBig(32);
    const _nonce = sc_0.loadUintBig(64);
    const _total_raised = sc_0.loadCoins();
    const _total_raised_usdt = sc_0.loadCoins();
    const sc_1 = sc_0.loadRef().beginParse();
    const _total_tokens_sold = sc_1.loadCoins();
    return { $$type: 'PurchaseCompleted' as const, user: _user, amount: _amount, tokens_received: _tokens_received, currency: _currency, purchase_method: _purchase_method, round_number: _round_number, nonce: _nonce, total_raised: _total_raised, total_raised_usdt: _total_raised_usdt, total_tokens_sold: _total_tokens_sold };
}

export function loadTuplePurchaseCompleted(source: TupleReader) {
    const _user = source.readAddress();
    const _amount = source.readBigNumber();
    const _tokens_received = source.readBigNumber();
    const _currency = source.readBigNumber();
    const _purchase_method = source.readBigNumber();
    const _round_number = source.readBigNumber();
    const _nonce = source.readBigNumber();
    const _total_raised = source.readBigNumber();
    const _total_raised_usdt = source.readBigNumber();
    const _total_tokens_sold = source.readBigNumber();
    return { $$type: 'PurchaseCompleted' as const, user: _user, amount: _amount, tokens_received: _tokens_received, currency: _currency, purchase_method: _purchase_method, round_number: _round_number, nonce: _nonce, total_raised: _total_raised, total_raised_usdt: _total_raised_usdt, total_tokens_sold: _total_tokens_sold };
}

export function loadGetterTuplePurchaseCompleted(source: TupleReader) {
    const _user = source.readAddress();
    const _amount = source.readBigNumber();
    const _tokens_received = source.readBigNumber();
    const _currency = source.readBigNumber();
    const _purchase_method = source.readBigNumber();
    const _round_number = source.readBigNumber();
    const _nonce = source.readBigNumber();
    const _total_raised = source.readBigNumber();
    const _total_raised_usdt = source.readBigNumber();
    const _total_tokens_sold = source.readBigNumber();
    return { $$type: 'PurchaseCompleted' as const, user: _user, amount: _amount, tokens_received: _tokens_received, currency: _currency, purchase_method: _purchase_method, round_number: _round_number, nonce: _nonce, total_raised: _total_raised, total_raised_usdt: _total_raised_usdt, total_tokens_sold: _total_tokens_sold };
}

export function storeTuplePurchaseCompleted(source: PurchaseCompleted) {
    const builder = new TupleBuilder();
    builder.writeAddress(source.user);
    builder.writeNumber(source.amount);
    builder.writeNumber(source.tokens_received);
    builder.writeNumber(source.currency);
    builder.writeNumber(source.purchase_method);
    builder.writeNumber(source.round_number);
    builder.writeNumber(source.nonce);
    builder.writeNumber(source.total_raised);
    builder.writeNumber(source.total_raised_usdt);
    builder.writeNumber(source.total_tokens_sold);
    return builder.build();
}

export function dictValueParserPurchaseCompleted(): DictionaryValue<PurchaseCompleted> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storePurchaseCompleted(src)).endCell());
        },
        parse: (src) => {
            return loadPurchaseCompleted(src.loadRef().beginParse());
        }
    }
}

export type USDTConfigured = {
    $$type: 'USDTConfigured';
    usdt_master: Address;
    usdt_wallet: Address;
    configured_by: Address;
}

export function storeUSDTConfigured(src: USDTConfigured) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(1031232428, 32);
        b_0.storeAddress(src.usdt_master);
        b_0.storeAddress(src.usdt_wallet);
        b_0.storeAddress(src.configured_by);
    };
}

export function loadUSDTConfigured(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 1031232428) { throw Error('Invalid prefix'); }
    const _usdt_master = sc_0.loadAddress();
    const _usdt_wallet = sc_0.loadAddress();
    const _configured_by = sc_0.loadAddress();
    return { $$type: 'USDTConfigured' as const, usdt_master: _usdt_master, usdt_wallet: _usdt_wallet, configured_by: _configured_by };
}

export function loadTupleUSDTConfigured(source: TupleReader) {
    const _usdt_master = source.readAddress();
    const _usdt_wallet = source.readAddress();
    const _configured_by = source.readAddress();
    return { $$type: 'USDTConfigured' as const, usdt_master: _usdt_master, usdt_wallet: _usdt_wallet, configured_by: _configured_by };
}

export function loadGetterTupleUSDTConfigured(source: TupleReader) {
    const _usdt_master = source.readAddress();
    const _usdt_wallet = source.readAddress();
    const _configured_by = source.readAddress();
    return { $$type: 'USDTConfigured' as const, usdt_master: _usdt_master, usdt_wallet: _usdt_wallet, configured_by: _configured_by };
}

export function storeTupleUSDTConfigured(source: USDTConfigured) {
    const builder = new TupleBuilder();
    builder.writeAddress(source.usdt_master);
    builder.writeAddress(source.usdt_wallet);
    builder.writeAddress(source.configured_by);
    return builder.build();
}

export function dictValueParserUSDTConfigured(): DictionaryValue<USDTConfigured> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeUSDTConfigured(src)).endCell());
        },
        parse: (src) => {
            return loadUSDTConfigured(src.loadRef().beginParse());
        }
    }
}

export type SigningKeySet = {
    $$type: 'SigningKeySet';
    public_key: bigint;
    set_by: Address;
}

export function storeSigningKeySet(src: SigningKeySet) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(2441211440, 32);
        b_0.storeUint(src.public_key, 256);
        b_0.storeAddress(src.set_by);
    };
}

export function loadSigningKeySet(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 2441211440) { throw Error('Invalid prefix'); }
    const _public_key = sc_0.loadUintBig(256);
    const _set_by = sc_0.loadAddress();
    return { $$type: 'SigningKeySet' as const, public_key: _public_key, set_by: _set_by };
}

export function loadTupleSigningKeySet(source: TupleReader) {
    const _public_key = source.readBigNumber();
    const _set_by = source.readAddress();
    return { $$type: 'SigningKeySet' as const, public_key: _public_key, set_by: _set_by };
}

export function loadGetterTupleSigningKeySet(source: TupleReader) {
    const _public_key = source.readBigNumber();
    const _set_by = source.readAddress();
    return { $$type: 'SigningKeySet' as const, public_key: _public_key, set_by: _set_by };
}

export function storeTupleSigningKeySet(source: SigningKeySet) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.public_key);
    builder.writeAddress(source.set_by);
    return builder.build();
}

export function dictValueParserSigningKeySet(): DictionaryValue<SigningKeySet> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeSigningKeySet(src)).endCell());
        },
        parse: (src) => {
            return loadSigningKeySet(src.loadRef().beginParse());
        }
    }
}

export type MinPurchaseUpdated = {
    $$type: 'MinPurchaseUpdated';
    old_min_purchase: bigint;
    new_min_purchase: bigint;
    updated_by: Address;
}

export function storeMinPurchaseUpdated(src: MinPurchaseUpdated) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(760782792, 32);
        b_0.storeCoins(src.old_min_purchase);
        b_0.storeCoins(src.new_min_purchase);
        b_0.storeAddress(src.updated_by);
    };
}

export function loadMinPurchaseUpdated(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 760782792) { throw Error('Invalid prefix'); }
    const _old_min_purchase = sc_0.loadCoins();
    const _new_min_purchase = sc_0.loadCoins();
    const _updated_by = sc_0.loadAddress();
    return { $$type: 'MinPurchaseUpdated' as const, old_min_purchase: _old_min_purchase, new_min_purchase: _new_min_purchase, updated_by: _updated_by };
}

export function loadTupleMinPurchaseUpdated(source: TupleReader) {
    const _old_min_purchase = source.readBigNumber();
    const _new_min_purchase = source.readBigNumber();
    const _updated_by = source.readAddress();
    return { $$type: 'MinPurchaseUpdated' as const, old_min_purchase: _old_min_purchase, new_min_purchase: _new_min_purchase, updated_by: _updated_by };
}

export function loadGetterTupleMinPurchaseUpdated(source: TupleReader) {
    const _old_min_purchase = source.readBigNumber();
    const _new_min_purchase = source.readBigNumber();
    const _updated_by = source.readAddress();
    return { $$type: 'MinPurchaseUpdated' as const, old_min_purchase: _old_min_purchase, new_min_purchase: _new_min_purchase, updated_by: _updated_by };
}

export function storeTupleMinPurchaseUpdated(source: MinPurchaseUpdated) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.old_min_purchase);
    builder.writeNumber(source.new_min_purchase);
    builder.writeAddress(source.updated_by);
    return builder.build();
}

export function dictValueParserMinPurchaseUpdated(): DictionaryValue<MinPurchaseUpdated> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeMinPurchaseUpdated(src)).endCell());
        },
        parse: (src) => {
            return loadMinPurchaseUpdated(src.loadRef().beginParse());
        }
    }
}

export type TreasurySet = {
    $$type: 'TreasurySet';
    old_treasury: Address;
    new_treasury: Address;
    set_by: Address;
}

export function storeTreasurySet(src: TreasurySet) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(4013598566, 32);
        b_0.storeAddress(src.old_treasury);
        b_0.storeAddress(src.new_treasury);
        b_0.storeAddress(src.set_by);
    };
}

export function loadTreasurySet(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 4013598566) { throw Error('Invalid prefix'); }
    const _old_treasury = sc_0.loadAddress();
    const _new_treasury = sc_0.loadAddress();
    const _set_by = sc_0.loadAddress();
    return { $$type: 'TreasurySet' as const, old_treasury: _old_treasury, new_treasury: _new_treasury, set_by: _set_by };
}

export function loadTupleTreasurySet(source: TupleReader) {
    const _old_treasury = source.readAddress();
    const _new_treasury = source.readAddress();
    const _set_by = source.readAddress();
    return { $$type: 'TreasurySet' as const, old_treasury: _old_treasury, new_treasury: _new_treasury, set_by: _set_by };
}

export function loadGetterTupleTreasurySet(source: TupleReader) {
    const _old_treasury = source.readAddress();
    const _new_treasury = source.readAddress();
    const _set_by = source.readAddress();
    return { $$type: 'TreasurySet' as const, old_treasury: _old_treasury, new_treasury: _new_treasury, set_by: _set_by };
}

export function storeTupleTreasurySet(source: TreasurySet) {
    const builder = new TupleBuilder();
    builder.writeAddress(source.old_treasury);
    builder.writeAddress(source.new_treasury);
    builder.writeAddress(source.set_by);
    return builder.build();
}

export function dictValueParserTreasurySet(): DictionaryValue<TreasurySet> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeTreasurySet(src)).endCell());
        },
        parse: (src) => {
            return loadTreasurySet(src.loadRef().beginParse());
        }
    }
}

export type FundsWithdrawn = {
    $$type: 'FundsWithdrawn';
    currency: bigint;
    amount: bigint;
    destination: Address;
    withdrawn_by: Address;
    remaining_balance: bigint;
}

export function storeFundsWithdrawn(src: FundsWithdrawn) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(3674912463, 32);
        b_0.storeUint(src.currency, 8);
        b_0.storeCoins(src.amount);
        b_0.storeAddress(src.destination);
        b_0.storeAddress(src.withdrawn_by);
        b_0.storeCoins(src.remaining_balance);
    };
}

export function loadFundsWithdrawn(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 3674912463) { throw Error('Invalid prefix'); }
    const _currency = sc_0.loadUintBig(8);
    const _amount = sc_0.loadCoins();
    const _destination = sc_0.loadAddress();
    const _withdrawn_by = sc_0.loadAddress();
    const _remaining_balance = sc_0.loadCoins();
    return { $$type: 'FundsWithdrawn' as const, currency: _currency, amount: _amount, destination: _destination, withdrawn_by: _withdrawn_by, remaining_balance: _remaining_balance };
}

export function loadTupleFundsWithdrawn(source: TupleReader) {
    const _currency = source.readBigNumber();
    const _amount = source.readBigNumber();
    const _destination = source.readAddress();
    const _withdrawn_by = source.readAddress();
    const _remaining_balance = source.readBigNumber();
    return { $$type: 'FundsWithdrawn' as const, currency: _currency, amount: _amount, destination: _destination, withdrawn_by: _withdrawn_by, remaining_balance: _remaining_balance };
}

export function loadGetterTupleFundsWithdrawn(source: TupleReader) {
    const _currency = source.readBigNumber();
    const _amount = source.readBigNumber();
    const _destination = source.readAddress();
    const _withdrawn_by = source.readAddress();
    const _remaining_balance = source.readBigNumber();
    return { $$type: 'FundsWithdrawn' as const, currency: _currency, amount: _amount, destination: _destination, withdrawn_by: _withdrawn_by, remaining_balance: _remaining_balance };
}

export function storeTupleFundsWithdrawn(source: FundsWithdrawn) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.currency);
    builder.writeNumber(source.amount);
    builder.writeAddress(source.destination);
    builder.writeAddress(source.withdrawn_by);
    builder.writeNumber(source.remaining_balance);
    return builder.build();
}

export function dictValueParserFundsWithdrawn(): DictionaryValue<FundsWithdrawn> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeFundsWithdrawn(src)).endCell());
        },
        parse: (src) => {
            return loadFundsWithdrawn(src.loadRef().beginParse());
        }
    }
}

export type RoundUpdated = {
    $$type: 'RoundUpdated';
    old_round: bigint;
    new_round: bigint;
    old_price: bigint;
    new_price: bigint;
    updated_by: Address;
}

export function storeRoundUpdated(src: RoundUpdated) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(190065307, 32);
        b_0.storeUint(src.old_round, 32);
        b_0.storeUint(src.new_round, 32);
        b_0.storeCoins(src.old_price);
        b_0.storeCoins(src.new_price);
        b_0.storeAddress(src.updated_by);
    };
}

export function loadRoundUpdated(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 190065307) { throw Error('Invalid prefix'); }
    const _old_round = sc_0.loadUintBig(32);
    const _new_round = sc_0.loadUintBig(32);
    const _old_price = sc_0.loadCoins();
    const _new_price = sc_0.loadCoins();
    const _updated_by = sc_0.loadAddress();
    return { $$type: 'RoundUpdated' as const, old_round: _old_round, new_round: _new_round, old_price: _old_price, new_price: _new_price, updated_by: _updated_by };
}

export function loadTupleRoundUpdated(source: TupleReader) {
    const _old_round = source.readBigNumber();
    const _new_round = source.readBigNumber();
    const _old_price = source.readBigNumber();
    const _new_price = source.readBigNumber();
    const _updated_by = source.readAddress();
    return { $$type: 'RoundUpdated' as const, old_round: _old_round, new_round: _new_round, old_price: _old_price, new_price: _new_price, updated_by: _updated_by };
}

export function loadGetterTupleRoundUpdated(source: TupleReader) {
    const _old_round = source.readBigNumber();
    const _new_round = source.readBigNumber();
    const _old_price = source.readBigNumber();
    const _new_price = source.readBigNumber();
    const _updated_by = source.readAddress();
    return { $$type: 'RoundUpdated' as const, old_round: _old_round, new_round: _new_round, old_price: _old_price, new_price: _new_price, updated_by: _updated_by };
}

export function storeTupleRoundUpdated(source: RoundUpdated) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.old_round);
    builder.writeNumber(source.new_round);
    builder.writeNumber(source.old_price);
    builder.writeNumber(source.new_price);
    builder.writeAddress(source.updated_by);
    return builder.build();
}

export function dictValueParserRoundUpdated(): DictionaryValue<RoundUpdated> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeRoundUpdated(src)).endCell());
        },
        parse: (src) => {
            return loadRoundUpdated(src.loadRef().beginParse());
        }
    }
}

export type AuctionEnded = {
    $$type: 'AuctionEnded';
    end_reason: bigint;
    final_status: bigint;
    total_raised: bigint;
    total_raised_usdt: bigint;
    total_tokens_sold: bigint;
    end_time: bigint;
}

export function storeAuctionEnded(src: AuctionEnded) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(397814633, 32);
        b_0.storeUint(src.end_reason, 8);
        b_0.storeUint(src.final_status, 8);
        b_0.storeCoins(src.total_raised);
        b_0.storeCoins(src.total_raised_usdt);
        b_0.storeCoins(src.total_tokens_sold);
        b_0.storeUint(src.end_time, 64);
    };
}

export function loadAuctionEnded(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 397814633) { throw Error('Invalid prefix'); }
    const _end_reason = sc_0.loadUintBig(8);
    const _final_status = sc_0.loadUintBig(8);
    const _total_raised = sc_0.loadCoins();
    const _total_raised_usdt = sc_0.loadCoins();
    const _total_tokens_sold = sc_0.loadCoins();
    const _end_time = sc_0.loadUintBig(64);
    return { $$type: 'AuctionEnded' as const, end_reason: _end_reason, final_status: _final_status, total_raised: _total_raised, total_raised_usdt: _total_raised_usdt, total_tokens_sold: _total_tokens_sold, end_time: _end_time };
}

export function loadTupleAuctionEnded(source: TupleReader) {
    const _end_reason = source.readBigNumber();
    const _final_status = source.readBigNumber();
    const _total_raised = source.readBigNumber();
    const _total_raised_usdt = source.readBigNumber();
    const _total_tokens_sold = source.readBigNumber();
    const _end_time = source.readBigNumber();
    return { $$type: 'AuctionEnded' as const, end_reason: _end_reason, final_status: _final_status, total_raised: _total_raised, total_raised_usdt: _total_raised_usdt, total_tokens_sold: _total_tokens_sold, end_time: _end_time };
}

export function loadGetterTupleAuctionEnded(source: TupleReader) {
    const _end_reason = source.readBigNumber();
    const _final_status = source.readBigNumber();
    const _total_raised = source.readBigNumber();
    const _total_raised_usdt = source.readBigNumber();
    const _total_tokens_sold = source.readBigNumber();
    const _end_time = source.readBigNumber();
    return { $$type: 'AuctionEnded' as const, end_reason: _end_reason, final_status: _final_status, total_raised: _total_raised, total_raised_usdt: _total_raised_usdt, total_tokens_sold: _total_tokens_sold, end_time: _end_time };
}

export function storeTupleAuctionEnded(source: AuctionEnded) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.end_reason);
    builder.writeNumber(source.final_status);
    builder.writeNumber(source.total_raised);
    builder.writeNumber(source.total_raised_usdt);
    builder.writeNumber(source.total_tokens_sold);
    builder.writeNumber(source.end_time);
    return builder.build();
}

export function dictValueParserAuctionEnded(): DictionaryValue<AuctionEnded> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeAuctionEnded(src)).endCell());
        },
        parse: (src) => {
            return loadAuctionEnded(src.loadRef().beginParse());
        }
    }
}

export type AuctionConfig = {
    $$type: 'AuctionConfig';
    start_time: bigint;
    end_time: bigint;
    soft_cap: bigint;
    hard_cap: bigint;
    total_supply: bigint;
    refund_fee_percent: bigint;
}

export function storeAuctionConfig(src: AuctionConfig) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(src.start_time, 64);
        b_0.storeUint(src.end_time, 64);
        b_0.storeCoins(src.soft_cap);
        b_0.storeCoins(src.hard_cap);
        b_0.storeCoins(src.total_supply);
        b_0.storeUint(src.refund_fee_percent, 8);
    };
}

export function loadAuctionConfig(slice: Slice) {
    const sc_0 = slice;
    const _start_time = sc_0.loadUintBig(64);
    const _end_time = sc_0.loadUintBig(64);
    const _soft_cap = sc_0.loadCoins();
    const _hard_cap = sc_0.loadCoins();
    const _total_supply = sc_0.loadCoins();
    const _refund_fee_percent = sc_0.loadUintBig(8);
    return { $$type: 'AuctionConfig' as const, start_time: _start_time, end_time: _end_time, soft_cap: _soft_cap, hard_cap: _hard_cap, total_supply: _total_supply, refund_fee_percent: _refund_fee_percent };
}

export function loadTupleAuctionConfig(source: TupleReader) {
    const _start_time = source.readBigNumber();
    const _end_time = source.readBigNumber();
    const _soft_cap = source.readBigNumber();
    const _hard_cap = source.readBigNumber();
    const _total_supply = source.readBigNumber();
    const _refund_fee_percent = source.readBigNumber();
    return { $$type: 'AuctionConfig' as const, start_time: _start_time, end_time: _end_time, soft_cap: _soft_cap, hard_cap: _hard_cap, total_supply: _total_supply, refund_fee_percent: _refund_fee_percent };
}

export function loadGetterTupleAuctionConfig(source: TupleReader) {
    const _start_time = source.readBigNumber();
    const _end_time = source.readBigNumber();
    const _soft_cap = source.readBigNumber();
    const _hard_cap = source.readBigNumber();
    const _total_supply = source.readBigNumber();
    const _refund_fee_percent = source.readBigNumber();
    return { $$type: 'AuctionConfig' as const, start_time: _start_time, end_time: _end_time, soft_cap: _soft_cap, hard_cap: _hard_cap, total_supply: _total_supply, refund_fee_percent: _refund_fee_percent };
}

export function storeTupleAuctionConfig(source: AuctionConfig) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.start_time);
    builder.writeNumber(source.end_time);
    builder.writeNumber(source.soft_cap);
    builder.writeNumber(source.hard_cap);
    builder.writeNumber(source.total_supply);
    builder.writeNumber(source.refund_fee_percent);
    return builder.build();
}

export function dictValueParserAuctionConfig(): DictionaryValue<AuctionConfig> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeAuctionConfig(src)).endCell());
        },
        parse: (src) => {
            return loadAuctionConfig(src.loadRef().beginParse());
        }
    }
}

export type RoundStats = {
    $$type: 'RoundStats';
    round_number: bigint;
    start_time: bigint;
    end_time: bigint;
    price: bigint;
    total_raised_ton: bigint;
    total_raised_usdt: bigint;
    raised_usdt_equivalent: bigint;
    tokens_sold: bigint;
    purchase_count: bigint;
    unique_users: bigint;
    refund_count: bigint;
    refunded_amount_ton: bigint;
    refunded_amount_usdt: bigint;
    refunded_usdt_equivalent: bigint;
}

export function storeRoundStats(src: RoundStats) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(src.round_number, 32);
        b_0.storeUint(src.start_time, 64);
        b_0.storeUint(src.end_time, 64);
        b_0.storeCoins(src.price);
        b_0.storeCoins(src.total_raised_ton);
        b_0.storeCoins(src.total_raised_usdt);
        b_0.storeCoins(src.raised_usdt_equivalent);
        b_0.storeCoins(src.tokens_sold);
        b_0.storeUint(src.purchase_count, 32);
        b_0.storeUint(src.unique_users, 32);
        b_0.storeUint(src.refund_count, 32);
        b_0.storeCoins(src.refunded_amount_ton);
        const b_1 = new Builder();
        b_1.storeCoins(src.refunded_amount_usdt);
        b_1.storeCoins(src.refunded_usdt_equivalent);
        b_0.storeRef(b_1.endCell());
    };
}

export function loadRoundStats(slice: Slice) {
    const sc_0 = slice;
    const _round_number = sc_0.loadUintBig(32);
    const _start_time = sc_0.loadUintBig(64);
    const _end_time = sc_0.loadUintBig(64);
    const _price = sc_0.loadCoins();
    const _total_raised_ton = sc_0.loadCoins();
    const _total_raised_usdt = sc_0.loadCoins();
    const _raised_usdt_equivalent = sc_0.loadCoins();
    const _tokens_sold = sc_0.loadCoins();
    const _purchase_count = sc_0.loadUintBig(32);
    const _unique_users = sc_0.loadUintBig(32);
    const _refund_count = sc_0.loadUintBig(32);
    const _refunded_amount_ton = sc_0.loadCoins();
    const sc_1 = sc_0.loadRef().beginParse();
    const _refunded_amount_usdt = sc_1.loadCoins();
    const _refunded_usdt_equivalent = sc_1.loadCoins();
    return { $$type: 'RoundStats' as const, round_number: _round_number, start_time: _start_time, end_time: _end_time, price: _price, total_raised_ton: _total_raised_ton, total_raised_usdt: _total_raised_usdt, raised_usdt_equivalent: _raised_usdt_equivalent, tokens_sold: _tokens_sold, purchase_count: _purchase_count, unique_users: _unique_users, refund_count: _refund_count, refunded_amount_ton: _refunded_amount_ton, refunded_amount_usdt: _refunded_amount_usdt, refunded_usdt_equivalent: _refunded_usdt_equivalent };
}

export function loadTupleRoundStats(source: TupleReader) {
    const _round_number = source.readBigNumber();
    const _start_time = source.readBigNumber();
    const _end_time = source.readBigNumber();
    const _price = source.readBigNumber();
    const _total_raised_ton = source.readBigNumber();
    const _total_raised_usdt = source.readBigNumber();
    const _raised_usdt_equivalent = source.readBigNumber();
    const _tokens_sold = source.readBigNumber();
    const _purchase_count = source.readBigNumber();
    const _unique_users = source.readBigNumber();
    const _refund_count = source.readBigNumber();
    const _refunded_amount_ton = source.readBigNumber();
    const _refunded_amount_usdt = source.readBigNumber();
    const _refunded_usdt_equivalent = source.readBigNumber();
    return { $$type: 'RoundStats' as const, round_number: _round_number, start_time: _start_time, end_time: _end_time, price: _price, total_raised_ton: _total_raised_ton, total_raised_usdt: _total_raised_usdt, raised_usdt_equivalent: _raised_usdt_equivalent, tokens_sold: _tokens_sold, purchase_count: _purchase_count, unique_users: _unique_users, refund_count: _refund_count, refunded_amount_ton: _refunded_amount_ton, refunded_amount_usdt: _refunded_amount_usdt, refunded_usdt_equivalent: _refunded_usdt_equivalent };
}

export function loadGetterTupleRoundStats(source: TupleReader) {
    const _round_number = source.readBigNumber();
    const _start_time = source.readBigNumber();
    const _end_time = source.readBigNumber();
    const _price = source.readBigNumber();
    const _total_raised_ton = source.readBigNumber();
    const _total_raised_usdt = source.readBigNumber();
    const _raised_usdt_equivalent = source.readBigNumber();
    const _tokens_sold = source.readBigNumber();
    const _purchase_count = source.readBigNumber();
    const _unique_users = source.readBigNumber();
    const _refund_count = source.readBigNumber();
    const _refunded_amount_ton = source.readBigNumber();
    const _refunded_amount_usdt = source.readBigNumber();
    const _refunded_usdt_equivalent = source.readBigNumber();
    return { $$type: 'RoundStats' as const, round_number: _round_number, start_time: _start_time, end_time: _end_time, price: _price, total_raised_ton: _total_raised_ton, total_raised_usdt: _total_raised_usdt, raised_usdt_equivalent: _raised_usdt_equivalent, tokens_sold: _tokens_sold, purchase_count: _purchase_count, unique_users: _unique_users, refund_count: _refund_count, refunded_amount_ton: _refunded_amount_ton, refunded_amount_usdt: _refunded_amount_usdt, refunded_usdt_equivalent: _refunded_usdt_equivalent };
}

export function storeTupleRoundStats(source: RoundStats) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.round_number);
    builder.writeNumber(source.start_time);
    builder.writeNumber(source.end_time);
    builder.writeNumber(source.price);
    builder.writeNumber(source.total_raised_ton);
    builder.writeNumber(source.total_raised_usdt);
    builder.writeNumber(source.raised_usdt_equivalent);
    builder.writeNumber(source.tokens_sold);
    builder.writeNumber(source.purchase_count);
    builder.writeNumber(source.unique_users);
    builder.writeNumber(source.refund_count);
    builder.writeNumber(source.refunded_amount_ton);
    builder.writeNumber(source.refunded_amount_usdt);
    builder.writeNumber(source.refunded_usdt_equivalent);
    return builder.build();
}

export function dictValueParserRoundStats(): DictionaryValue<RoundStats> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeRoundStats(src)).endCell());
        },
        parse: (src) => {
            return loadRoundStats(src.loadRef().beginParse());
        }
    }
}

export type USDTConfig = {
    $$type: 'USDTConfig';
    master_address: Address;
    wallet_address: Address | null;
    decimals: bigint;
}

export function storeUSDTConfig(src: USDTConfig) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeAddress(src.master_address);
        b_0.storeAddress(src.wallet_address);
        b_0.storeUint(src.decimals, 8);
    };
}

export function loadUSDTConfig(slice: Slice) {
    const sc_0 = slice;
    const _master_address = sc_0.loadAddress();
    const _wallet_address = sc_0.loadMaybeAddress();
    const _decimals = sc_0.loadUintBig(8);
    return { $$type: 'USDTConfig' as const, master_address: _master_address, wallet_address: _wallet_address, decimals: _decimals };
}

export function loadTupleUSDTConfig(source: TupleReader) {
    const _master_address = source.readAddress();
    const _wallet_address = source.readAddressOpt();
    const _decimals = source.readBigNumber();
    return { $$type: 'USDTConfig' as const, master_address: _master_address, wallet_address: _wallet_address, decimals: _decimals };
}

export function loadGetterTupleUSDTConfig(source: TupleReader) {
    const _master_address = source.readAddress();
    const _wallet_address = source.readAddressOpt();
    const _decimals = source.readBigNumber();
    return { $$type: 'USDTConfig' as const, master_address: _master_address, wallet_address: _wallet_address, decimals: _decimals };
}

export function storeTupleUSDTConfig(source: USDTConfig) {
    const builder = new TupleBuilder();
    builder.writeAddress(source.master_address);
    builder.writeAddress(source.wallet_address);
    builder.writeNumber(source.decimals);
    return builder.build();
}

export function dictValueParserUSDTConfig(): DictionaryValue<USDTConfig> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeUSDTConfig(src)).endCell());
        },
        parse: (src) => {
            return loadUSDTConfig(src.loadRef().beginParse());
        }
    }
}

export type PurchaseCalculation = {
    $$type: 'PurchaseCalculation';
    user: Address;
    amount: bigint;
    currency: bigint;
    tokens_to_receive: bigint;
    current_price: bigint;
    current_round: bigint;
    timestamp: bigint;
    nonce: bigint;
    usdt_equivalent_amount: bigint;
}

export function storePurchaseCalculation(src: PurchaseCalculation) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeAddress(src.user);
        b_0.storeCoins(src.amount);
        b_0.storeUint(src.currency, 8);
        b_0.storeCoins(src.tokens_to_receive);
        b_0.storeCoins(src.current_price);
        b_0.storeUint(src.current_round, 32);
        b_0.storeUint(src.timestamp, 64);
        b_0.storeUint(src.nonce, 64);
        b_0.storeCoins(src.usdt_equivalent_amount);
    };
}

export function loadPurchaseCalculation(slice: Slice) {
    const sc_0 = slice;
    const _user = sc_0.loadAddress();
    const _amount = sc_0.loadCoins();
    const _currency = sc_0.loadUintBig(8);
    const _tokens_to_receive = sc_0.loadCoins();
    const _current_price = sc_0.loadCoins();
    const _current_round = sc_0.loadUintBig(32);
    const _timestamp = sc_0.loadUintBig(64);
    const _nonce = sc_0.loadUintBig(64);
    const _usdt_equivalent_amount = sc_0.loadCoins();
    return { $$type: 'PurchaseCalculation' as const, user: _user, amount: _amount, currency: _currency, tokens_to_receive: _tokens_to_receive, current_price: _current_price, current_round: _current_round, timestamp: _timestamp, nonce: _nonce, usdt_equivalent_amount: _usdt_equivalent_amount };
}

export function loadTuplePurchaseCalculation(source: TupleReader) {
    const _user = source.readAddress();
    const _amount = source.readBigNumber();
    const _currency = source.readBigNumber();
    const _tokens_to_receive = source.readBigNumber();
    const _current_price = source.readBigNumber();
    const _current_round = source.readBigNumber();
    const _timestamp = source.readBigNumber();
    const _nonce = source.readBigNumber();
    const _usdt_equivalent_amount = source.readBigNumber();
    return { $$type: 'PurchaseCalculation' as const, user: _user, amount: _amount, currency: _currency, tokens_to_receive: _tokens_to_receive, current_price: _current_price, current_round: _current_round, timestamp: _timestamp, nonce: _nonce, usdt_equivalent_amount: _usdt_equivalent_amount };
}

export function loadGetterTuplePurchaseCalculation(source: TupleReader) {
    const _user = source.readAddress();
    const _amount = source.readBigNumber();
    const _currency = source.readBigNumber();
    const _tokens_to_receive = source.readBigNumber();
    const _current_price = source.readBigNumber();
    const _current_round = source.readBigNumber();
    const _timestamp = source.readBigNumber();
    const _nonce = source.readBigNumber();
    const _usdt_equivalent_amount = source.readBigNumber();
    return { $$type: 'PurchaseCalculation' as const, user: _user, amount: _amount, currency: _currency, tokens_to_receive: _tokens_to_receive, current_price: _current_price, current_round: _current_round, timestamp: _timestamp, nonce: _nonce, usdt_equivalent_amount: _usdt_equivalent_amount };
}

export function storeTuplePurchaseCalculation(source: PurchaseCalculation) {
    const builder = new TupleBuilder();
    builder.writeAddress(source.user);
    builder.writeNumber(source.amount);
    builder.writeNumber(source.currency);
    builder.writeNumber(source.tokens_to_receive);
    builder.writeNumber(source.current_price);
    builder.writeNumber(source.current_round);
    builder.writeNumber(source.timestamp);
    builder.writeNumber(source.nonce);
    builder.writeNumber(source.usdt_equivalent_amount);
    return builder.build();
}

export function dictValueParserPurchaseCalculation(): DictionaryValue<PurchaseCalculation> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storePurchaseCalculation(src)).endCell());
        },
        parse: (src) => {
            return loadPurchaseCalculation(src.loadRef().beginParse());
        }
    }
}

export type ParsedPurchaseData = {
    $$type: 'ParsedPurchaseData';
    calculation: PurchaseCalculation;
    signature: Slice;
}

export function storeParsedPurchaseData(src: ParsedPurchaseData) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.store(storePurchaseCalculation(src.calculation));
        b_0.storeRef(src.signature.asCell());
    };
}

export function loadParsedPurchaseData(slice: Slice) {
    const sc_0 = slice;
    const _calculation = loadPurchaseCalculation(sc_0);
    const _signature = sc_0.loadRef().asSlice();
    return { $$type: 'ParsedPurchaseData' as const, calculation: _calculation, signature: _signature };
}

export function loadTupleParsedPurchaseData(source: TupleReader) {
    const _calculation = loadTuplePurchaseCalculation(source);
    const _signature = source.readCell().asSlice();
    return { $$type: 'ParsedPurchaseData' as const, calculation: _calculation, signature: _signature };
}

export function loadGetterTupleParsedPurchaseData(source: TupleReader) {
    const _calculation = loadGetterTuplePurchaseCalculation(source);
    const _signature = source.readCell().asSlice();
    return { $$type: 'ParsedPurchaseData' as const, calculation: _calculation, signature: _signature };
}

export function storeTupleParsedPurchaseData(source: ParsedPurchaseData) {
    const builder = new TupleBuilder();
    builder.writeTuple(storeTuplePurchaseCalculation(source.calculation));
    builder.writeSlice(source.signature.asCell());
    return builder.build();
}

export function dictValueParserParsedPurchaseData(): DictionaryValue<ParsedPurchaseData> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeParsedPurchaseData(src)).endCell());
        },
        parse: (src) => {
            return loadParsedPurchaseData(src.loadRef().beginParse());
        }
    }
}

export type OnionAuction$Data = {
    $$type: 'OnionAuction$Data';
    owner: Address;
    stopped: boolean;
    auction_config: AuctionConfig;
    current_round: bigint;
    current_price: bigint;
    total_raised: bigint;
    total_tokens_sold: bigint;
    auction_status: bigint;
    usdt_config: USDTConfig | null;
    total_raised_usdt: bigint;
    total_raised_usdt_equivalent: bigint;
    purchase_count: bigint;
    round_stats: Dictionary<bigint, RoundStats>;
    user_round_purchases: Dictionary<Address, bigint>;
    signing_public_key: bigint;
    used_nonces: Dictionary<bigint, boolean>;
    signature_timeout: bigint;
    min_purchase: bigint;
    treasury_address: Address | null;
}

export function storeOnionAuction$Data(src: OnionAuction$Data) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeAddress(src.owner);
        b_0.storeBit(src.stopped);
        b_0.store(storeAuctionConfig(src.auction_config));
        b_0.storeUint(src.current_round, 32);
        b_0.storeCoins(src.current_price);
        const b_1 = new Builder();
        b_1.storeCoins(src.total_raised);
        b_1.storeCoins(src.total_tokens_sold);
        b_1.storeUint(src.auction_status, 8);
        if (src.usdt_config !== null && src.usdt_config !== undefined) { b_1.storeBit(true); b_1.store(storeUSDTConfig(src.usdt_config)); } else { b_1.storeBit(false); }
        b_1.storeCoins(src.total_raised_usdt);
        const b_2 = new Builder();
        b_2.storeCoins(src.total_raised_usdt_equivalent);
        b_2.storeUint(src.purchase_count, 32);
        b_2.storeDict(src.round_stats, Dictionary.Keys.BigInt(257), dictValueParserRoundStats());
        b_2.storeDict(src.user_round_purchases, Dictionary.Keys.Address(), Dictionary.Values.BigInt(257));
        b_2.storeUint(src.signing_public_key, 256);
        b_2.storeDict(src.used_nonces, Dictionary.Keys.BigInt(257), Dictionary.Values.Bool());
        b_2.storeUint(src.signature_timeout, 64);
        b_2.storeCoins(src.min_purchase);
        b_2.storeAddress(src.treasury_address);
        b_1.storeRef(b_2.endCell());
        b_0.storeRef(b_1.endCell());
    };
}

export function loadOnionAuction$Data(slice: Slice) {
    const sc_0 = slice;
    const _owner = sc_0.loadAddress();
    const _stopped = sc_0.loadBit();
    const _auction_config = loadAuctionConfig(sc_0);
    const _current_round = sc_0.loadUintBig(32);
    const _current_price = sc_0.loadCoins();
    const sc_1 = sc_0.loadRef().beginParse();
    const _total_raised = sc_1.loadCoins();
    const _total_tokens_sold = sc_1.loadCoins();
    const _auction_status = sc_1.loadUintBig(8);
    const _usdt_config = sc_1.loadBit() ? loadUSDTConfig(sc_1) : null;
    const _total_raised_usdt = sc_1.loadCoins();
    const sc_2 = sc_1.loadRef().beginParse();
    const _total_raised_usdt_equivalent = sc_2.loadCoins();
    const _purchase_count = sc_2.loadUintBig(32);
    const _round_stats = Dictionary.load(Dictionary.Keys.BigInt(257), dictValueParserRoundStats(), sc_2);
    const _user_round_purchases = Dictionary.load(Dictionary.Keys.Address(), Dictionary.Values.BigInt(257), sc_2);
    const _signing_public_key = sc_2.loadUintBig(256);
    const _used_nonces = Dictionary.load(Dictionary.Keys.BigInt(257), Dictionary.Values.Bool(), sc_2);
    const _signature_timeout = sc_2.loadUintBig(64);
    const _min_purchase = sc_2.loadCoins();
    const _treasury_address = sc_2.loadMaybeAddress();
    return { $$type: 'OnionAuction$Data' as const, owner: _owner, stopped: _stopped, auction_config: _auction_config, current_round: _current_round, current_price: _current_price, total_raised: _total_raised, total_tokens_sold: _total_tokens_sold, auction_status: _auction_status, usdt_config: _usdt_config, total_raised_usdt: _total_raised_usdt, total_raised_usdt_equivalent: _total_raised_usdt_equivalent, purchase_count: _purchase_count, round_stats: _round_stats, user_round_purchases: _user_round_purchases, signing_public_key: _signing_public_key, used_nonces: _used_nonces, signature_timeout: _signature_timeout, min_purchase: _min_purchase, treasury_address: _treasury_address };
}

export function loadTupleOnionAuction$Data(source: TupleReader) {
    const _owner = source.readAddress();
    const _stopped = source.readBoolean();
    const _auction_config = loadTupleAuctionConfig(source);
    const _current_round = source.readBigNumber();
    const _current_price = source.readBigNumber();
    const _total_raised = source.readBigNumber();
    const _total_tokens_sold = source.readBigNumber();
    const _auction_status = source.readBigNumber();
    const _usdt_config_p = source.readTupleOpt();
    const _usdt_config = _usdt_config_p ? loadTupleUSDTConfig(_usdt_config_p) : null;
    const _total_raised_usdt = source.readBigNumber();
    const _total_raised_usdt_equivalent = source.readBigNumber();
    const _purchase_count = source.readBigNumber();
    const _round_stats = Dictionary.loadDirect(Dictionary.Keys.BigInt(257), dictValueParserRoundStats(), source.readCellOpt());
    const _user_round_purchases = Dictionary.loadDirect(Dictionary.Keys.Address(), Dictionary.Values.BigInt(257), source.readCellOpt());
    source = source.readTuple();
    const _signing_public_key = source.readBigNumber();
    const _used_nonces = Dictionary.loadDirect(Dictionary.Keys.BigInt(257), Dictionary.Values.Bool(), source.readCellOpt());
    const _signature_timeout = source.readBigNumber();
    const _min_purchase = source.readBigNumber();
    const _treasury_address = source.readAddressOpt();
    return { $$type: 'OnionAuction$Data' as const, owner: _owner, stopped: _stopped, auction_config: _auction_config, current_round: _current_round, current_price: _current_price, total_raised: _total_raised, total_tokens_sold: _total_tokens_sold, auction_status: _auction_status, usdt_config: _usdt_config, total_raised_usdt: _total_raised_usdt, total_raised_usdt_equivalent: _total_raised_usdt_equivalent, purchase_count: _purchase_count, round_stats: _round_stats, user_round_purchases: _user_round_purchases, signing_public_key: _signing_public_key, used_nonces: _used_nonces, signature_timeout: _signature_timeout, min_purchase: _min_purchase, treasury_address: _treasury_address };
}

export function loadGetterTupleOnionAuction$Data(source: TupleReader) {
    const _owner = source.readAddress();
    const _stopped = source.readBoolean();
    const _auction_config = loadGetterTupleAuctionConfig(source);
    const _current_round = source.readBigNumber();
    const _current_price = source.readBigNumber();
    const _total_raised = source.readBigNumber();
    const _total_tokens_sold = source.readBigNumber();
    const _auction_status = source.readBigNumber();
    const _usdt_config_p = source.readTupleOpt();
    const _usdt_config = _usdt_config_p ? loadTupleUSDTConfig(_usdt_config_p) : null;
    const _total_raised_usdt = source.readBigNumber();
    const _total_raised_usdt_equivalent = source.readBigNumber();
    const _purchase_count = source.readBigNumber();
    const _round_stats = Dictionary.loadDirect(Dictionary.Keys.BigInt(257), dictValueParserRoundStats(), source.readCellOpt());
    const _user_round_purchases = Dictionary.loadDirect(Dictionary.Keys.Address(), Dictionary.Values.BigInt(257), source.readCellOpt());
    const _signing_public_key = source.readBigNumber();
    const _used_nonces = Dictionary.loadDirect(Dictionary.Keys.BigInt(257), Dictionary.Values.Bool(), source.readCellOpt());
    const _signature_timeout = source.readBigNumber();
    const _min_purchase = source.readBigNumber();
    const _treasury_address = source.readAddressOpt();
    return { $$type: 'OnionAuction$Data' as const, owner: _owner, stopped: _stopped, auction_config: _auction_config, current_round: _current_round, current_price: _current_price, total_raised: _total_raised, total_tokens_sold: _total_tokens_sold, auction_status: _auction_status, usdt_config: _usdt_config, total_raised_usdt: _total_raised_usdt, total_raised_usdt_equivalent: _total_raised_usdt_equivalent, purchase_count: _purchase_count, round_stats: _round_stats, user_round_purchases: _user_round_purchases, signing_public_key: _signing_public_key, used_nonces: _used_nonces, signature_timeout: _signature_timeout, min_purchase: _min_purchase, treasury_address: _treasury_address };
}

export function storeTupleOnionAuction$Data(source: OnionAuction$Data) {
    const builder = new TupleBuilder();
    builder.writeAddress(source.owner);
    builder.writeBoolean(source.stopped);
    builder.writeTuple(storeTupleAuctionConfig(source.auction_config));
    builder.writeNumber(source.current_round);
    builder.writeNumber(source.current_price);
    builder.writeNumber(source.total_raised);
    builder.writeNumber(source.total_tokens_sold);
    builder.writeNumber(source.auction_status);
    if (source.usdt_config !== null && source.usdt_config !== undefined) {
        builder.writeTuple(storeTupleUSDTConfig(source.usdt_config));
    } else {
        builder.writeTuple(null);
    }
    builder.writeNumber(source.total_raised_usdt);
    builder.writeNumber(source.total_raised_usdt_equivalent);
    builder.writeNumber(source.purchase_count);
    builder.writeCell(source.round_stats.size > 0 ? beginCell().storeDictDirect(source.round_stats, Dictionary.Keys.BigInt(257), dictValueParserRoundStats()).endCell() : null);
    builder.writeCell(source.user_round_purchases.size > 0 ? beginCell().storeDictDirect(source.user_round_purchases, Dictionary.Keys.Address(), Dictionary.Values.BigInt(257)).endCell() : null);
    builder.writeNumber(source.signing_public_key);
    builder.writeCell(source.used_nonces.size > 0 ? beginCell().storeDictDirect(source.used_nonces, Dictionary.Keys.BigInt(257), Dictionary.Values.Bool()).endCell() : null);
    builder.writeNumber(source.signature_timeout);
    builder.writeNumber(source.min_purchase);
    builder.writeAddress(source.treasury_address);
    return builder.build();
}

export function dictValueParserOnionAuction$Data(): DictionaryValue<OnionAuction$Data> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeOnionAuction$Data(src)).endCell());
        },
        parse: (src) => {
            return loadOnionAuction$Data(src.loadRef().beginParse());
        }
    }
}

 type UserPurchase_init_args = {
    $$type: 'UserPurchase_init_args';
    auction_address: Address;
    user_address: Address;
}

function initUserPurchase_init_args(src: UserPurchase_init_args) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeAddress(src.auction_address);
        b_0.storeAddress(src.user_address);
    };
}

async function UserPurchase_init(auction_address: Address, user_address: Address) {
    const __code = Cell.fromHex('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');
    const builder = beginCell();
    builder.storeUint(0, 1);
    initUserPurchase_init_args({ $$type: 'UserPurchase_init_args', auction_address, user_address })(builder);
    const __data = builder.endCell();
    return { code: __code, data: __data };
}

export const UserPurchase_errors = {
    2: { message: "Stack underflow" },
    3: { message: "Stack overflow" },
    4: { message: "Integer overflow" },
    5: { message: "Integer out of expected range" },
    6: { message: "Invalid opcode" },
    7: { message: "Type check error" },
    8: { message: "Cell overflow" },
    9: { message: "Cell underflow" },
    10: { message: "Dictionary error" },
    11: { message: "'Unknown' error" },
    12: { message: "Fatal error" },
    13: { message: "Out of gas error" },
    14: { message: "Virtualization error" },
    32: { message: "Action list is invalid" },
    33: { message: "Action list is too long" },
    34: { message: "Action is invalid or not supported" },
    35: { message: "Invalid source address in outbound message" },
    36: { message: "Invalid destination address in outbound message" },
    37: { message: "Not enough Toncoin" },
    38: { message: "Not enough extra currencies" },
    39: { message: "Outbound message does not fit into a cell after rewriting" },
    40: { message: "Cannot process a message" },
    41: { message: "Library reference is null" },
    42: { message: "Library change action error" },
    43: { message: "Exceeded maximum number of cells in the library or the maximum depth of the Merkle tree" },
    50: { message: "Account state size exceeded limits" },
    128: { message: "Null reference exception" },
    129: { message: "Invalid serialization prefix" },
    130: { message: "Invalid incoming message" },
    131: { message: "Constraints error" },
    132: { message: "Access denied" },
    133: { message: "Contract stopped" },
    134: { message: "Invalid argument" },
    135: { message: "Code of a contract was not found" },
    136: { message: "Invalid standard address" },
    138: { message: "Not a basechain address" },
    2296: { message: "JettonWallet: Only Jetton master or Jetton wallet can call this function" },
    13105: { message: "JettonWallet: Not enough jettons to transfer" },
    22411: { message: "Already refunded" },
    27831: { message: "Only owner can call this function" },
    29133: { message: "JettonWallet: Not allow negative balance after internal transfer" },
    37185: { message: "Not enough funds to transfer" },
    39144: { message: "Purchase not found" },
    47048: { message: "JettonWallet: Only owner can burn tokens" },
    49729: { message: "Unauthorized" },
    53296: { message: "Contract not stopped" },
    60354: { message: "JettonWallet: Not enough balance to burn tokens" },
} as const

export const UserPurchase_errors_backward = {
    "Stack underflow": 2,
    "Stack overflow": 3,
    "Integer overflow": 4,
    "Integer out of expected range": 5,
    "Invalid opcode": 6,
    "Type check error": 7,
    "Cell overflow": 8,
    "Cell underflow": 9,
    "Dictionary error": 10,
    "'Unknown' error": 11,
    "Fatal error": 12,
    "Out of gas error": 13,
    "Virtualization error": 14,
    "Action list is invalid": 32,
    "Action list is too long": 33,
    "Action is invalid or not supported": 34,
    "Invalid source address in outbound message": 35,
    "Invalid destination address in outbound message": 36,
    "Not enough Toncoin": 37,
    "Not enough extra currencies": 38,
    "Outbound message does not fit into a cell after rewriting": 39,
    "Cannot process a message": 40,
    "Library reference is null": 41,
    "Library change action error": 42,
    "Exceeded maximum number of cells in the library or the maximum depth of the Merkle tree": 43,
    "Account state size exceeded limits": 50,
    "Null reference exception": 128,
    "Invalid serialization prefix": 129,
    "Invalid incoming message": 130,
    "Constraints error": 131,
    "Access denied": 132,
    "Contract stopped": 133,
    "Invalid argument": 134,
    "Code of a contract was not found": 135,
    "Invalid standard address": 136,
    "Not a basechain address": 138,
    "JettonWallet: Only Jetton master or Jetton wallet can call this function": 2296,
    "JettonWallet: Not enough jettons to transfer": 13105,
    "Already refunded": 22411,
    "Only owner can call this function": 27831,
    "JettonWallet: Not allow negative balance after internal transfer": 29133,
    "Not enough funds to transfer": 37185,
    "Purchase not found": 39144,
    "JettonWallet: Only owner can burn tokens": 47048,
    "Unauthorized": 49729,
    "Contract not stopped": 53296,
    "JettonWallet: Not enough balance to burn tokens": 60354,
} as const

const UserPurchase_types: ABIType[] = [
    {"name":"DataSize","header":null,"fields":[{"name":"cells","type":{"kind":"simple","type":"int","optional":false,"format":257}},{"name":"bits","type":{"kind":"simple","type":"int","optional":false,"format":257}},{"name":"refs","type":{"kind":"simple","type":"int","optional":false,"format":257}}]},
    {"name":"SignedBundle","header":null,"fields":[{"name":"signature","type":{"kind":"simple","type":"fixed-bytes","optional":false,"format":64}},{"name":"signedData","type":{"kind":"simple","type":"slice","optional":false,"format":"remainder"}}]},
    {"name":"StateInit","header":null,"fields":[{"name":"code","type":{"kind":"simple","type":"cell","optional":false}},{"name":"data","type":{"kind":"simple","type":"cell","optional":false}}]},
    {"name":"Context","header":null,"fields":[{"name":"bounceable","type":{"kind":"simple","type":"bool","optional":false}},{"name":"sender","type":{"kind":"simple","type":"address","optional":false}},{"name":"value","type":{"kind":"simple","type":"int","optional":false,"format":257}},{"name":"raw","type":{"kind":"simple","type":"slice","optional":false}}]},
    {"name":"SendParameters","header":null,"fields":[{"name":"mode","type":{"kind":"simple","type":"int","optional":false,"format":257}},{"name":"body","type":{"kind":"simple","type":"cell","optional":true}},{"name":"code","type":{"kind":"simple","type":"cell","optional":true}},{"name":"data","type":{"kind":"simple","type":"cell","optional":true}},{"name":"value","type":{"kind":"simple","type":"int","optional":false,"format":257}},{"name":"to","type":{"kind":"simple","type":"address","optional":false}},{"name":"bounce","type":{"kind":"simple","type":"bool","optional":false}}]},
    {"name":"MessageParameters","header":null,"fields":[{"name":"mode","type":{"kind":"simple","type":"int","optional":false,"format":257}},{"name":"body","type":{"kind":"simple","type":"cell","optional":true}},{"name":"value","type":{"kind":"simple","type":"int","optional":false,"format":257}},{"name":"to","type":{"kind":"simple","type":"address","optional":false}},{"name":"bounce","type":{"kind":"simple","type":"bool","optional":false}}]},
    {"name":"DeployParameters","header":null,"fields":[{"name":"mode","type":{"kind":"simple","type":"int","optional":false,"format":257}},{"name":"body","type":{"kind":"simple","type":"cell","optional":true}},{"name":"value","type":{"kind":"simple","type":"int","optional":false,"format":257}},{"name":"bounce","type":{"kind":"simple","type":"bool","optional":false}},{"name":"init","type":{"kind":"simple","type":"StateInit","optional":false}}]},
    {"name":"StdAddress","header":null,"fields":[{"name":"workchain","type":{"kind":"simple","type":"int","optional":false,"format":8}},{"name":"address","type":{"kind":"simple","type":"uint","optional":false,"format":256}}]},
    {"name":"VarAddress","header":null,"fields":[{"name":"workchain","type":{"kind":"simple","type":"int","optional":false,"format":32}},{"name":"address","type":{"kind":"simple","type":"slice","optional":false}}]},
    {"name":"BasechainAddress","header":null,"fields":[{"name":"hash","type":{"kind":"simple","type":"int","optional":true,"format":257}}]},
    {"name":"ChangeOwner","header":2174598809,"fields":[{"name":"queryId","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"newOwner","type":{"kind":"simple","type":"address","optional":false}}]},
    {"name":"ChangeOwnerOk","header":846932810,"fields":[{"name":"queryId","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"newOwner","type":{"kind":"simple","type":"address","optional":false}}]},
    {"name":"Deploy","header":2490013878,"fields":[{"name":"queryId","type":{"kind":"simple","type":"uint","optional":false,"format":64}}]},
    {"name":"DeployOk","header":2952335191,"fields":[{"name":"queryId","type":{"kind":"simple","type":"uint","optional":false,"format":64}}]},
    {"name":"FactoryDeploy","header":1829761339,"fields":[{"name":"queryId","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"cashback","type":{"kind":"simple","type":"address","optional":false}}]},
    {"name":"JettonTransfer","header":260734629,"fields":[{"name":"query_id","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"amount","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"destination","type":{"kind":"simple","type":"address","optional":false}},{"name":"response_destination","type":{"kind":"simple","type":"address","optional":false}},{"name":"custom_payload","type":{"kind":"simple","type":"cell","optional":true}},{"name":"forward_ton_amount","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"forward_payload","type":{"kind":"simple","type":"cell","optional":true}}]},
    {"name":"JettonTransferNotification","header":1935855772,"fields":[{"name":"query_id","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"amount","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"sender","type":{"kind":"simple","type":"address","optional":false}},{"name":"forward_payload","type":{"kind":"simple","type":"cell","optional":true}}]},
    {"name":"JettonBurn","header":1499400124,"fields":[{"name":"query_id","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"amount","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"response_destination","type":{"kind":"simple","type":"address","optional":false}},{"name":"custom_payload","type":{"kind":"simple","type":"cell","optional":true}}]},
    {"name":"JettonExcesses","header":3576854235,"fields":[{"name":"query_id","type":{"kind":"simple","type":"uint","optional":false,"format":64}}]},
    {"name":"JettonInternalTransfer","header":395134233,"fields":[{"name":"query_id","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"amount","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"from","type":{"kind":"simple","type":"address","optional":false}},{"name":"response_address","type":{"kind":"simple","type":"address","optional":false}},{"name":"forward_ton_amount","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"forward_payload","type":{"kind":"simple","type":"cell","optional":true}}]},
    {"name":"JettonBurnNotification","header":2078119902,"fields":[{"name":"query_id","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"amount","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"sender","type":{"kind":"simple","type":"address","optional":false}},{"name":"response_destination","type":{"kind":"simple","type":"address","optional":false}}]},
    {"name":"WalletData","header":null,"fields":[{"name":"balance","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"owner","type":{"kind":"simple","type":"address","optional":false}},{"name":"jetton","type":{"kind":"simple","type":"address","optional":false}},{"name":"jetton_wallet_code","type":{"kind":"simple","type":"cell","optional":false}}]},
    {"name":"CreateUserPurchase","header":1098075148,"fields":[{"name":"user","type":{"kind":"simple","type":"address","optional":false}},{"name":"amount","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"tokens","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"currency","type":{"kind":"simple","type":"uint","optional":false,"format":8}},{"name":"purchase_method","type":{"kind":"simple","type":"uint","optional":false,"format":8}},{"name":"nonce","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"round_number","type":{"kind":"simple","type":"uint","optional":false,"format":32}},{"name":"usdt_equivalent_amount","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}}]},
    {"name":"Refund","header":836089260,"fields":[{"name":"purchase_id","type":{"kind":"simple","type":"uint","optional":false,"format":32}}]},
    {"name":"ProcessRefund","header":929991442,"fields":[{"name":"user","type":{"kind":"simple","type":"address","optional":false}},{"name":"amount","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"fee","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"currency","type":{"kind":"simple","type":"uint","optional":false,"format":8}},{"name":"round_number","type":{"kind":"simple","type":"uint","optional":false,"format":32}},{"name":"usdt_equivalent_amount","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}}]},
    {"name":"PurchaseRecord","header":null,"fields":[{"name":"id","type":{"kind":"simple","type":"uint","optional":false,"format":32}},{"name":"user","type":{"kind":"simple","type":"address","optional":false}},{"name":"amount","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"tokens","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"timestamp","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"currency","type":{"kind":"simple","type":"uint","optional":false,"format":8}},{"name":"purchase_method","type":{"kind":"simple","type":"uint","optional":false,"format":8}},{"name":"nonce","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"round_number","type":{"kind":"simple","type":"uint","optional":false,"format":32}},{"name":"usdt_equivalent_amount","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}}]},
    {"name":"UserPurchase$Data","header":null,"fields":[{"name":"owner","type":{"kind":"simple","type":"address","optional":false}},{"name":"auction_address","type":{"kind":"simple","type":"address","optional":false}},{"name":"user_address","type":{"kind":"simple","type":"address","optional":false}},{"name":"total_purchased","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"total_paid","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"purchase_history","type":{"kind":"dict","key":"int","value":"PurchaseRecord","valueFormat":"ref"}},{"name":"refund_history","type":{"kind":"dict","key":"int","value":"int"}},{"name":"purchase_id_counter","type":{"kind":"simple","type":"uint","optional":false,"format":32}},{"name":"participated_rounds","type":{"kind":"dict","key":"int","value":"bool"}}]},
    {"name":"StartAuction","header":1082886929,"fields":[{"name":"start_time","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"end_time","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"soft_cap","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"hard_cap","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"initial_price","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}}]},
    {"name":"UpdateRound","header":2861271945,"fields":[{"name":"new_price","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"round_number","type":{"kind":"simple","type":"uint","optional":false,"format":32}}]},
    {"name":"SetUSDTAddress","header":2703444734,"fields":[{"name":"usdt_master","type":{"kind":"simple","type":"address","optional":false}},{"name":"usdt_wallet","type":{"kind":"simple","type":"address","optional":false}}]},
    {"name":"PurchaseWithSignature","header":1524770820,"fields":[{"name":"calculation","type":{"kind":"simple","type":"PurchaseCalculation","optional":false}},{"name":"signature","type":{"kind":"simple","type":"slice","optional":false}}]},
    {"name":"SetSigningKey","header":3315975678,"fields":[{"name":"public_key","type":{"kind":"simple","type":"uint","optional":false,"format":256}}]},
    {"name":"SetMinPurchase","header":3390874056,"fields":[{"name":"min_purchase","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}}]},
    {"name":"WithdrawTON","header":3520188881,"fields":[{"name":"amount","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"destination","type":{"kind":"simple","type":"address","optional":false}}]},
    {"name":"WithdrawUSDT","header":3537031890,"fields":[{"name":"amount","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"destination","type":{"kind":"simple","type":"address","optional":false}}]},
    {"name":"SetTreasury","header":3553874899,"fields":[{"name":"treasury_address","type":{"kind":"simple","type":"address","optional":false}}]},
    {"name":"AuctionStarted","header":2217068725,"fields":[{"name":"start_time","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"end_time","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"soft_cap","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"hard_cap","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"initial_price","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"total_supply","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}}]},
    {"name":"PurchaseCompleted","header":3975568033,"fields":[{"name":"user","type":{"kind":"simple","type":"address","optional":false}},{"name":"amount","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"tokens_received","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"currency","type":{"kind":"simple","type":"uint","optional":false,"format":8}},{"name":"purchase_method","type":{"kind":"simple","type":"uint","optional":false,"format":8}},{"name":"round_number","type":{"kind":"simple","type":"uint","optional":false,"format":32}},{"name":"nonce","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"total_raised","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"total_raised_usdt","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"total_tokens_sold","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}}]},
    {"name":"USDTConfigured","header":1031232428,"fields":[{"name":"usdt_master","type":{"kind":"simple","type":"address","optional":false}},{"name":"usdt_wallet","type":{"kind":"simple","type":"address","optional":false}},{"name":"configured_by","type":{"kind":"simple","type":"address","optional":false}}]},
    {"name":"SigningKeySet","header":2441211440,"fields":[{"name":"public_key","type":{"kind":"simple","type":"uint","optional":false,"format":256}},{"name":"set_by","type":{"kind":"simple","type":"address","optional":false}}]},
    {"name":"MinPurchaseUpdated","header":760782792,"fields":[{"name":"old_min_purchase","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"new_min_purchase","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"updated_by","type":{"kind":"simple","type":"address","optional":false}}]},
    {"name":"TreasurySet","header":4013598566,"fields":[{"name":"old_treasury","type":{"kind":"simple","type":"address","optional":false}},{"name":"new_treasury","type":{"kind":"simple","type":"address","optional":false}},{"name":"set_by","type":{"kind":"simple","type":"address","optional":false}}]},
    {"name":"FundsWithdrawn","header":3674912463,"fields":[{"name":"currency","type":{"kind":"simple","type":"uint","optional":false,"format":8}},{"name":"amount","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"destination","type":{"kind":"simple","type":"address","optional":false}},{"name":"withdrawn_by","type":{"kind":"simple","type":"address","optional":false}},{"name":"remaining_balance","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}}]},
    {"name":"RoundUpdated","header":190065307,"fields":[{"name":"old_round","type":{"kind":"simple","type":"uint","optional":false,"format":32}},{"name":"new_round","type":{"kind":"simple","type":"uint","optional":false,"format":32}},{"name":"old_price","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"new_price","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"updated_by","type":{"kind":"simple","type":"address","optional":false}}]},
    {"name":"AuctionEnded","header":397814633,"fields":[{"name":"end_reason","type":{"kind":"simple","type":"uint","optional":false,"format":8}},{"name":"final_status","type":{"kind":"simple","type":"uint","optional":false,"format":8}},{"name":"total_raised","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"total_raised_usdt","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"total_tokens_sold","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"end_time","type":{"kind":"simple","type":"uint","optional":false,"format":64}}]},
    {"name":"AuctionConfig","header":null,"fields":[{"name":"start_time","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"end_time","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"soft_cap","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"hard_cap","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"total_supply","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"refund_fee_percent","type":{"kind":"simple","type":"uint","optional":false,"format":8}}]},
    {"name":"RoundStats","header":null,"fields":[{"name":"round_number","type":{"kind":"simple","type":"uint","optional":false,"format":32}},{"name":"start_time","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"end_time","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"price","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"total_raised_ton","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"total_raised_usdt","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"raised_usdt_equivalent","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"tokens_sold","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"purchase_count","type":{"kind":"simple","type":"uint","optional":false,"format":32}},{"name":"unique_users","type":{"kind":"simple","type":"uint","optional":false,"format":32}},{"name":"refund_count","type":{"kind":"simple","type":"uint","optional":false,"format":32}},{"name":"refunded_amount_ton","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"refunded_amount_usdt","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"refunded_usdt_equivalent","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}}]},
    {"name":"USDTConfig","header":null,"fields":[{"name":"master_address","type":{"kind":"simple","type":"address","optional":false}},{"name":"wallet_address","type":{"kind":"simple","type":"address","optional":true}},{"name":"decimals","type":{"kind":"simple","type":"uint","optional":false,"format":8}}]},
    {"name":"PurchaseCalculation","header":null,"fields":[{"name":"user","type":{"kind":"simple","type":"address","optional":false}},{"name":"amount","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"currency","type":{"kind":"simple","type":"uint","optional":false,"format":8}},{"name":"tokens_to_receive","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"current_price","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"current_round","type":{"kind":"simple","type":"uint","optional":false,"format":32}},{"name":"timestamp","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"nonce","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"usdt_equivalent_amount","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}}]},
    {"name":"ParsedPurchaseData","header":null,"fields":[{"name":"calculation","type":{"kind":"simple","type":"PurchaseCalculation","optional":false}},{"name":"signature","type":{"kind":"simple","type":"slice","optional":false}}]},
    {"name":"OnionAuction$Data","header":null,"fields":[{"name":"owner","type":{"kind":"simple","type":"address","optional":false}},{"name":"stopped","type":{"kind":"simple","type":"bool","optional":false}},{"name":"auction_config","type":{"kind":"simple","type":"AuctionConfig","optional":false}},{"name":"current_round","type":{"kind":"simple","type":"uint","optional":false,"format":32}},{"name":"current_price","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"total_raised","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"total_tokens_sold","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"auction_status","type":{"kind":"simple","type":"uint","optional":false,"format":8}},{"name":"usdt_config","type":{"kind":"simple","type":"USDTConfig","optional":true}},{"name":"total_raised_usdt","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"total_raised_usdt_equivalent","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"purchase_count","type":{"kind":"simple","type":"uint","optional":false,"format":32}},{"name":"round_stats","type":{"kind":"dict","key":"int","value":"RoundStats","valueFormat":"ref"}},{"name":"user_round_purchases","type":{"kind":"dict","key":"address","value":"int"}},{"name":"signing_public_key","type":{"kind":"simple","type":"uint","optional":false,"format":256}},{"name":"used_nonces","type":{"kind":"dict","key":"int","value":"bool"}},{"name":"signature_timeout","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"min_purchase","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"treasury_address","type":{"kind":"simple","type":"address","optional":true}}]},
]

const UserPurchase_opcodes = {
    "ChangeOwner": 2174598809,
    "ChangeOwnerOk": 846932810,
    "Deploy": 2490013878,
    "DeployOk": 2952335191,
    "FactoryDeploy": 1829761339,
    "JettonTransfer": 260734629,
    "JettonTransferNotification": 1935855772,
    "JettonBurn": 1499400124,
    "JettonExcesses": 3576854235,
    "JettonInternalTransfer": 395134233,
    "JettonBurnNotification": 2078119902,
    "CreateUserPurchase": 1098075148,
    "Refund": 836089260,
    "ProcessRefund": 929991442,
    "StartAuction": 1082886929,
    "UpdateRound": 2861271945,
    "SetUSDTAddress": 2703444734,
    "PurchaseWithSignature": 1524770820,
    "SetSigningKey": 3315975678,
    "SetMinPurchase": 3390874056,
    "WithdrawTON": 3520188881,
    "WithdrawUSDT": 3537031890,
    "SetTreasury": 3553874899,
    "AuctionStarted": 2217068725,
    "PurchaseCompleted": 3975568033,
    "USDTConfigured": 1031232428,
    "SigningKeySet": 2441211440,
    "MinPurchaseUpdated": 760782792,
    "TreasurySet": 4013598566,
    "FundsWithdrawn": 3674912463,
    "RoundUpdated": 190065307,
    "AuctionEnded": 397814633,
}

const UserPurchase_getters: ABIGetter[] = [
    {"name":"total_purchased","methodId":97654,"arguments":[],"returnType":{"kind":"simple","type":"int","optional":false,"format":257}},
    {"name":"total_paid","methodId":127313,"arguments":[],"returnType":{"kind":"simple","type":"int","optional":false,"format":257}},
    {"name":"purchase_id_counter","methodId":90655,"arguments":[],"returnType":{"kind":"simple","type":"int","optional":false,"format":257}},
    {"name":"purchase_details","methodId":108146,"arguments":[{"name":"purchase_id","type":{"kind":"simple","type":"int","optional":false,"format":257}}],"returnType":{"kind":"simple","type":"PurchaseRecord","optional":true}},
    {"name":"is_refunded","methodId":70580,"arguments":[{"name":"purchase_id","type":{"kind":"simple","type":"int","optional":false,"format":257}}],"returnType":{"kind":"simple","type":"bool","optional":false}},
    {"name":"signature_verified_purchases","methodId":90079,"arguments":[],"returnType":{"kind":"simple","type":"int","optional":false,"format":257}},
    {"name":"purchase_method_stats","methodId":123538,"arguments":[],"returnType":{"kind":"dict","key":"int","value":"int"}},
    {"name":"purchases_by_round","methodId":98340,"arguments":[{"name":"round_number","type":{"kind":"simple","type":"int","optional":false,"format":257}}],"returnType":{"kind":"dict","key":"int","value":"PurchaseRecord","valueFormat":"ref"}},
    {"name":"round_total_amount","methodId":67459,"arguments":[{"name":"round_number","type":{"kind":"simple","type":"int","optional":false,"format":257}}],"returnType":{"kind":"simple","type":"int","optional":false,"format":257}},
    {"name":"round_total_tokens","methodId":123888,"arguments":[{"name":"round_number","type":{"kind":"simple","type":"int","optional":false,"format":257}}],"returnType":{"kind":"simple","type":"int","optional":false,"format":257}},
    {"name":"round_purchase_count","methodId":82786,"arguments":[{"name":"round_number","type":{"kind":"simple","type":"int","optional":false,"format":257}}],"returnType":{"kind":"simple","type":"int","optional":false,"format":257}},
    {"name":"participated_rounds","methodId":108842,"arguments":[],"returnType":{"kind":"dict","key":"int","value":"bool"}},
    {"name":"participated_in_round","methodId":124279,"arguments":[{"name":"round_number","type":{"kind":"simple","type":"int","optional":false,"format":257}}],"returnType":{"kind":"simple","type":"bool","optional":false}},
    {"name":"round_count","methodId":86414,"arguments":[],"returnType":{"kind":"simple","type":"int","optional":false,"format":257}},
    {"name":"all_records","methodId":96455,"arguments":[],"returnType":{"kind":"dict","key":"int","value":"PurchaseRecord","valueFormat":"ref"}},
    {"name":"owner","methodId":83229,"arguments":[],"returnType":{"kind":"simple","type":"address","optional":false}},
]

export const UserPurchase_getterMapping: { [key: string]: string } = {
    'total_purchased': 'getTotalPurchased',
    'total_paid': 'getTotalPaid',
    'purchase_id_counter': 'getPurchaseIdCounter',
    'purchase_details': 'getPurchaseDetails',
    'is_refunded': 'getIsRefunded',
    'signature_verified_purchases': 'getSignatureVerifiedPurchases',
    'purchase_method_stats': 'getPurchaseMethodStats',
    'purchases_by_round': 'getPurchasesByRound',
    'round_total_amount': 'getRoundTotalAmount',
    'round_total_tokens': 'getRoundTotalTokens',
    'round_purchase_count': 'getRoundPurchaseCount',
    'participated_rounds': 'getParticipatedRounds',
    'participated_in_round': 'getParticipatedInRound',
    'round_count': 'getRoundCount',
    'all_records': 'getAllRecords',
    'owner': 'getOwner',
}

const UserPurchase_receivers: ABIReceiver[] = [
    {"receiver":"internal","message":{"kind":"text","text":"Deploy"}},
    {"receiver":"internal","message":{"kind":"typed","type":"CreateUserPurchase"}},
    {"receiver":"internal","message":{"kind":"typed","type":"Refund"}},
]

export const ERROR_UNAUTHORIZED = 62001n;
export const ERROR_PURCHASE_NOT_FOUND = 62002n;
export const ERROR_ALREADY_REFUNDED = 62003n;
export const OP_CREATE_USER_PURCHASE = 1098075148n;
export const OP_REFUND = 836089260n;
export const OP_PROCESS_REFUND = 929991442n;
export const ERROR_AUCTION_NOT_ACTIVE = 51001n;
export const ERROR_AUCTION_NOT_STARTED = 51002n;
export const ERROR_AUCTION_ENDED = 51003n;
export const ERROR_INVALID_CURRENCY = 51004n;
export const ERROR_AMOUNT_BELOW_MINIMUM = 51005n;
export const ERROR_INSUFFICIENT_TOKENS = 51006n;
export const ERROR_USDT_NOT_CONFIGURED = 51007n;
export const ERROR_INVALID_USDT_WALLET = 51008n;
export const ERROR_SIGNING_KEY_NOT_SET = 51009n;
export const ERROR_SIGNATURE_EXPIRED = 51010n;
export const ERROR_FUTURE_TIMESTAMP = 51011n;
export const ERROR_NONCE_ALREADY_USED = 51012n;
export const ERROR_INVALID_SIGNATURE = 51013n;
export const ERROR_CURRENCY_MISMATCH = 51014n;
export const ERROR_AMOUNT_MISMATCH = 51015n;
export const ERROR_USER_MISMATCH = 51016n;
export const ERROR_UNAUTHORIZED_REFUND = 51017n;
export const ERROR_MIN_PURCHASE_INVALID = 51018n;
export const ERROR_WITHDRAWAL_NOT_ALLOWED = 51019n;
export const ERROR_INSUFFICIENT_BALANCE = 51020n;
export const ERROR_INVALID_WITHDRAWAL_ADDRESS = 51021n;
export const ERROR_ROUND_SOFT_CAP_EXCEEDED = 51022n;
export const OP_PURCHASE = 3952774220n;
export const OP_START_AUCTION = 1082886929n;
export const OP_UPDATE_ROUND = 2861271945n;
export const OP_SET_USDT_ADDRESS = 2703444734n;
export const OP_PURCHASE_WITH_SIGNATURE = 1524770820n;
export const OP_SET_SIGNING_KEY = 3315975678n;
export const OP_SET_MIN_PURCHASE = 3390874056n;
export const OP_WITHDRAW_TON = 3520188881n;
export const OP_WITHDRAW_USDT = 3537031890n;
export const OP_SET_TREASURY = 3553874899n;

export class UserPurchase implements Contract {
    
    public static readonly storageReserve = 0n;
    public static readonly ROUND_DURATION = 3600n;
    public static readonly PRICE_INCREMENT = 10000000n;
    public static readonly SIGNATURE_TIMEOUT = 300n;
    public static readonly errors = UserPurchase_errors_backward;
    public static readonly opcodes = UserPurchase_opcodes;
    
    static async init(auction_address: Address, user_address: Address) {
        return await UserPurchase_init(auction_address, user_address);
    }
    
    static async fromInit(auction_address: Address, user_address: Address) {
        const __gen_init = await UserPurchase_init(auction_address, user_address);
        const address = contractAddress(0, __gen_init);
        return new UserPurchase(address, __gen_init);
    }
    
    static fromAddress(address: Address) {
        return new UserPurchase(address);
    }
    
    readonly address: Address; 
    readonly init?: { code: Cell, data: Cell };
    readonly abi: ContractABI = {
        types:  UserPurchase_types,
        getters: UserPurchase_getters,
        receivers: UserPurchase_receivers,
        errors: UserPurchase_errors,
    };
    
    constructor(address: Address, init?: { code: Cell, data: Cell }) {
        this.address = address;
        this.init = init;
    }
    
    async send(provider: ContractProvider, via: Sender, args: { value: bigint, bounce?: boolean| null | undefined }, message: "Deploy" | CreateUserPurchase | Refund) {
        
        let body: Cell | null = null;
        if (message === "Deploy") {
            body = beginCell().storeUint(0, 32).storeStringTail(message).endCell();
        }
        if (message && typeof message === 'object' && !(message instanceof Slice) && message.$$type === 'CreateUserPurchase') {
            body = beginCell().store(storeCreateUserPurchase(message)).endCell();
        }
        if (message && typeof message === 'object' && !(message instanceof Slice) && message.$$type === 'Refund') {
            body = beginCell().store(storeRefund(message)).endCell();
        }
        if (body === null) { throw new Error('Invalid message type'); }
        
        await provider.internal(via, { ...args, body: body });
        
    }
    
    async getTotalPurchased(provider: ContractProvider) {
        const builder = new TupleBuilder();
        const source = (await provider.get('total_purchased', builder.build())).stack;
        const result = source.readBigNumber();
        return result;
    }
    
    async getTotalPaid(provider: ContractProvider) {
        const builder = new TupleBuilder();
        const source = (await provider.get('total_paid', builder.build())).stack;
        const result = source.readBigNumber();
        return result;
    }
    
    async getPurchaseIdCounter(provider: ContractProvider) {
        const builder = new TupleBuilder();
        const source = (await provider.get('purchase_id_counter', builder.build())).stack;
        const result = source.readBigNumber();
        return result;
    }
    
    async getPurchaseDetails(provider: ContractProvider, purchase_id: bigint) {
        const builder = new TupleBuilder();
        builder.writeNumber(purchase_id);
        const source = (await provider.get('purchase_details', builder.build())).stack;
        const result_p = source.readTupleOpt();
        const result = result_p ? loadTuplePurchaseRecord(result_p) : null;
        return result;
    }
    
    async getIsRefunded(provider: ContractProvider, purchase_id: bigint) {
        const builder = new TupleBuilder();
        builder.writeNumber(purchase_id);
        const source = (await provider.get('is_refunded', builder.build())).stack;
        const result = source.readBoolean();
        return result;
    }
    
    async getSignatureVerifiedPurchases(provider: ContractProvider) {
        const builder = new TupleBuilder();
        const source = (await provider.get('signature_verified_purchases', builder.build())).stack;
        const result = source.readBigNumber();
        return result;
    }
    
    async getPurchaseMethodStats(provider: ContractProvider) {
        const builder = new TupleBuilder();
        const source = (await provider.get('purchase_method_stats', builder.build())).stack;
        const result = Dictionary.loadDirect(Dictionary.Keys.BigInt(257), Dictionary.Values.BigInt(257), source.readCellOpt());
        return result;
    }
    
    async getPurchasesByRound(provider: ContractProvider, round_number: bigint) {
        const builder = new TupleBuilder();
        builder.writeNumber(round_number);
        const source = (await provider.get('purchases_by_round', builder.build())).stack;
        const result = Dictionary.loadDirect(Dictionary.Keys.BigInt(257), dictValueParserPurchaseRecord(), source.readCellOpt());
        return result;
    }
    
    async getRoundTotalAmount(provider: ContractProvider, round_number: bigint) {
        const builder = new TupleBuilder();
        builder.writeNumber(round_number);
        const source = (await provider.get('round_total_amount', builder.build())).stack;
        const result = source.readBigNumber();
        return result;
    }
    
    async getRoundTotalTokens(provider: ContractProvider, round_number: bigint) {
        const builder = new TupleBuilder();
        builder.writeNumber(round_number);
        const source = (await provider.get('round_total_tokens', builder.build())).stack;
        const result = source.readBigNumber();
        return result;
    }
    
    async getRoundPurchaseCount(provider: ContractProvider, round_number: bigint) {
        const builder = new TupleBuilder();
        builder.writeNumber(round_number);
        const source = (await provider.get('round_purchase_count', builder.build())).stack;
        const result = source.readBigNumber();
        return result;
    }
    
    async getParticipatedRounds(provider: ContractProvider) {
        const builder = new TupleBuilder();
        const source = (await provider.get('participated_rounds', builder.build())).stack;
        const result = Dictionary.loadDirect(Dictionary.Keys.BigInt(257), Dictionary.Values.Bool(), source.readCellOpt());
        return result;
    }
    
    async getParticipatedInRound(provider: ContractProvider, round_number: bigint) {
        const builder = new TupleBuilder();
        builder.writeNumber(round_number);
        const source = (await provider.get('participated_in_round', builder.build())).stack;
        const result = source.readBoolean();
        return result;
    }
    
    async getRoundCount(provider: ContractProvider) {
        const builder = new TupleBuilder();
        const source = (await provider.get('round_count', builder.build())).stack;
        const result = source.readBigNumber();
        return result;
    }
    
    async getAllRecords(provider: ContractProvider) {
        const builder = new TupleBuilder();
        const source = (await provider.get('all_records', builder.build())).stack;
        const result = Dictionary.loadDirect(Dictionary.Keys.BigInt(257), dictValueParserPurchaseRecord(), source.readCellOpt());
        return result;
    }
    
    async getOwner(provider: ContractProvider) {
        const builder = new TupleBuilder();
        const source = (await provider.get('owner', builder.build())).stack;
        const result = source.readAddress();
        return result;
    }
    
}