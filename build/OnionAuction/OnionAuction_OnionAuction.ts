import {
    Cell,
    Slice,
    Address,
    Builder,
    begin<PERSON>ell,
    ComputeError,
    <PERSON>pleItem,
    TupleReader,
    Dictionary,
    contractAddress,
    address,
    ContractProvider,
    Sender,
    Contract,
    ContractAB<PERSON>,
    ABIType,
    ABIGetter,
    ABIReceiver,
    TupleBuilder,
    DictionaryValue
} from '@ton/core';

export type DataSize = {
    $$type: 'DataSize';
    cells: bigint;
    bits: bigint;
    refs: bigint;
}

export function storeDataSize(src: DataSize) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeInt(src.cells, 257);
        b_0.storeInt(src.bits, 257);
        b_0.storeInt(src.refs, 257);
    };
}

export function loadDataSize(slice: Slice) {
    const sc_0 = slice;
    const _cells = sc_0.loadIntBig(257);
    const _bits = sc_0.loadIntBig(257);
    const _refs = sc_0.loadIntBig(257);
    return { $$type: 'DataSize' as const, cells: _cells, bits: _bits, refs: _refs };
}

export function loadTupleDataSize(source: <PERSON>pleReader) {
    const _cells = source.readBigNumber();
    const _bits = source.readBigNumber();
    const _refs = source.readBigNumber();
    return { $$type: 'DataSize' as const, cells: _cells, bits: _bits, refs: _refs };
}

export function loadGetterTupleDataSize(source: TupleReader) {
    const _cells = source.readBigNumber();
    const _bits = source.readBigNumber();
    const _refs = source.readBigNumber();
    return { $$type: 'DataSize' as const, cells: _cells, bits: _bits, refs: _refs };
}

export function storeTupleDataSize(source: DataSize) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.cells);
    builder.writeNumber(source.bits);
    builder.writeNumber(source.refs);
    return builder.build();
}

export function dictValueParserDataSize(): DictionaryValue<DataSize> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeDataSize(src)).endCell());
        },
        parse: (src) => {
            return loadDataSize(src.loadRef().beginParse());
        }
    }
}

export type SignedBundle = {
    $$type: 'SignedBundle';
    signature: Buffer;
    signedData: Slice;
}

export function storeSignedBundle(src: SignedBundle) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeBuffer(src.signature);
        b_0.storeBuilder(src.signedData.asBuilder());
    };
}

export function loadSignedBundle(slice: Slice) {
    const sc_0 = slice;
    const _signature = sc_0.loadBuffer(64);
    const _signedData = sc_0;
    return { $$type: 'SignedBundle' as const, signature: _signature, signedData: _signedData };
}

export function loadTupleSignedBundle(source: TupleReader) {
    const _signature = source.readBuffer();
    const _signedData = source.readCell().asSlice();
    return { $$type: 'SignedBundle' as const, signature: _signature, signedData: _signedData };
}

export function loadGetterTupleSignedBundle(source: TupleReader) {
    const _signature = source.readBuffer();
    const _signedData = source.readCell().asSlice();
    return { $$type: 'SignedBundle' as const, signature: _signature, signedData: _signedData };
}

export function storeTupleSignedBundle(source: SignedBundle) {
    const builder = new TupleBuilder();
    builder.writeBuffer(source.signature);
    builder.writeSlice(source.signedData.asCell());
    return builder.build();
}

export function dictValueParserSignedBundle(): DictionaryValue<SignedBundle> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeSignedBundle(src)).endCell());
        },
        parse: (src) => {
            return loadSignedBundle(src.loadRef().beginParse());
        }
    }
}

export type StateInit = {
    $$type: 'StateInit';
    code: Cell;
    data: Cell;
}

export function storeStateInit(src: StateInit) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeRef(src.code);
        b_0.storeRef(src.data);
    };
}

export function loadStateInit(slice: Slice) {
    const sc_0 = slice;
    const _code = sc_0.loadRef();
    const _data = sc_0.loadRef();
    return { $$type: 'StateInit' as const, code: _code, data: _data };
}

export function loadTupleStateInit(source: TupleReader) {
    const _code = source.readCell();
    const _data = source.readCell();
    return { $$type: 'StateInit' as const, code: _code, data: _data };
}

export function loadGetterTupleStateInit(source: TupleReader) {
    const _code = source.readCell();
    const _data = source.readCell();
    return { $$type: 'StateInit' as const, code: _code, data: _data };
}

export function storeTupleStateInit(source: StateInit) {
    const builder = new TupleBuilder();
    builder.writeCell(source.code);
    builder.writeCell(source.data);
    return builder.build();
}

export function dictValueParserStateInit(): DictionaryValue<StateInit> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeStateInit(src)).endCell());
        },
        parse: (src) => {
            return loadStateInit(src.loadRef().beginParse());
        }
    }
}

export type Context = {
    $$type: 'Context';
    bounceable: boolean;
    sender: Address;
    value: bigint;
    raw: Slice;
}

export function storeContext(src: Context) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeBit(src.bounceable);
        b_0.storeAddress(src.sender);
        b_0.storeInt(src.value, 257);
        b_0.storeRef(src.raw.asCell());
    };
}

export function loadContext(slice: Slice) {
    const sc_0 = slice;
    const _bounceable = sc_0.loadBit();
    const _sender = sc_0.loadAddress();
    const _value = sc_0.loadIntBig(257);
    const _raw = sc_0.loadRef().asSlice();
    return { $$type: 'Context' as const, bounceable: _bounceable, sender: _sender, value: _value, raw: _raw };
}

export function loadTupleContext(source: TupleReader) {
    const _bounceable = source.readBoolean();
    const _sender = source.readAddress();
    const _value = source.readBigNumber();
    const _raw = source.readCell().asSlice();
    return { $$type: 'Context' as const, bounceable: _bounceable, sender: _sender, value: _value, raw: _raw };
}

export function loadGetterTupleContext(source: TupleReader) {
    const _bounceable = source.readBoolean();
    const _sender = source.readAddress();
    const _value = source.readBigNumber();
    const _raw = source.readCell().asSlice();
    return { $$type: 'Context' as const, bounceable: _bounceable, sender: _sender, value: _value, raw: _raw };
}

export function storeTupleContext(source: Context) {
    const builder = new TupleBuilder();
    builder.writeBoolean(source.bounceable);
    builder.writeAddress(source.sender);
    builder.writeNumber(source.value);
    builder.writeSlice(source.raw.asCell());
    return builder.build();
}

export function dictValueParserContext(): DictionaryValue<Context> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeContext(src)).endCell());
        },
        parse: (src) => {
            return loadContext(src.loadRef().beginParse());
        }
    }
}

export type SendParameters = {
    $$type: 'SendParameters';
    mode: bigint;
    body: Cell | null;
    code: Cell | null;
    data: Cell | null;
    value: bigint;
    to: Address;
    bounce: boolean;
}

export function storeSendParameters(src: SendParameters) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeInt(src.mode, 257);
        if (src.body !== null && src.body !== undefined) { b_0.storeBit(true).storeRef(src.body); } else { b_0.storeBit(false); }
        if (src.code !== null && src.code !== undefined) { b_0.storeBit(true).storeRef(src.code); } else { b_0.storeBit(false); }
        if (src.data !== null && src.data !== undefined) { b_0.storeBit(true).storeRef(src.data); } else { b_0.storeBit(false); }
        b_0.storeInt(src.value, 257);
        b_0.storeAddress(src.to);
        b_0.storeBit(src.bounce);
    };
}

export function loadSendParameters(slice: Slice) {
    const sc_0 = slice;
    const _mode = sc_0.loadIntBig(257);
    const _body = sc_0.loadBit() ? sc_0.loadRef() : null;
    const _code = sc_0.loadBit() ? sc_0.loadRef() : null;
    const _data = sc_0.loadBit() ? sc_0.loadRef() : null;
    const _value = sc_0.loadIntBig(257);
    const _to = sc_0.loadAddress();
    const _bounce = sc_0.loadBit();
    return { $$type: 'SendParameters' as const, mode: _mode, body: _body, code: _code, data: _data, value: _value, to: _to, bounce: _bounce };
}

export function loadTupleSendParameters(source: TupleReader) {
    const _mode = source.readBigNumber();
    const _body = source.readCellOpt();
    const _code = source.readCellOpt();
    const _data = source.readCellOpt();
    const _value = source.readBigNumber();
    const _to = source.readAddress();
    const _bounce = source.readBoolean();
    return { $$type: 'SendParameters' as const, mode: _mode, body: _body, code: _code, data: _data, value: _value, to: _to, bounce: _bounce };
}

export function loadGetterTupleSendParameters(source: TupleReader) {
    const _mode = source.readBigNumber();
    const _body = source.readCellOpt();
    const _code = source.readCellOpt();
    const _data = source.readCellOpt();
    const _value = source.readBigNumber();
    const _to = source.readAddress();
    const _bounce = source.readBoolean();
    return { $$type: 'SendParameters' as const, mode: _mode, body: _body, code: _code, data: _data, value: _value, to: _to, bounce: _bounce };
}

export function storeTupleSendParameters(source: SendParameters) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.mode);
    builder.writeCell(source.body);
    builder.writeCell(source.code);
    builder.writeCell(source.data);
    builder.writeNumber(source.value);
    builder.writeAddress(source.to);
    builder.writeBoolean(source.bounce);
    return builder.build();
}

export function dictValueParserSendParameters(): DictionaryValue<SendParameters> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeSendParameters(src)).endCell());
        },
        parse: (src) => {
            return loadSendParameters(src.loadRef().beginParse());
        }
    }
}

export type MessageParameters = {
    $$type: 'MessageParameters';
    mode: bigint;
    body: Cell | null;
    value: bigint;
    to: Address;
    bounce: boolean;
}

export function storeMessageParameters(src: MessageParameters) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeInt(src.mode, 257);
        if (src.body !== null && src.body !== undefined) { b_0.storeBit(true).storeRef(src.body); } else { b_0.storeBit(false); }
        b_0.storeInt(src.value, 257);
        b_0.storeAddress(src.to);
        b_0.storeBit(src.bounce);
    };
}

export function loadMessageParameters(slice: Slice) {
    const sc_0 = slice;
    const _mode = sc_0.loadIntBig(257);
    const _body = sc_0.loadBit() ? sc_0.loadRef() : null;
    const _value = sc_0.loadIntBig(257);
    const _to = sc_0.loadAddress();
    const _bounce = sc_0.loadBit();
    return { $$type: 'MessageParameters' as const, mode: _mode, body: _body, value: _value, to: _to, bounce: _bounce };
}

export function loadTupleMessageParameters(source: TupleReader) {
    const _mode = source.readBigNumber();
    const _body = source.readCellOpt();
    const _value = source.readBigNumber();
    const _to = source.readAddress();
    const _bounce = source.readBoolean();
    return { $$type: 'MessageParameters' as const, mode: _mode, body: _body, value: _value, to: _to, bounce: _bounce };
}

export function loadGetterTupleMessageParameters(source: TupleReader) {
    const _mode = source.readBigNumber();
    const _body = source.readCellOpt();
    const _value = source.readBigNumber();
    const _to = source.readAddress();
    const _bounce = source.readBoolean();
    return { $$type: 'MessageParameters' as const, mode: _mode, body: _body, value: _value, to: _to, bounce: _bounce };
}

export function storeTupleMessageParameters(source: MessageParameters) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.mode);
    builder.writeCell(source.body);
    builder.writeNumber(source.value);
    builder.writeAddress(source.to);
    builder.writeBoolean(source.bounce);
    return builder.build();
}

export function dictValueParserMessageParameters(): DictionaryValue<MessageParameters> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeMessageParameters(src)).endCell());
        },
        parse: (src) => {
            return loadMessageParameters(src.loadRef().beginParse());
        }
    }
}

export type DeployParameters = {
    $$type: 'DeployParameters';
    mode: bigint;
    body: Cell | null;
    value: bigint;
    bounce: boolean;
    init: StateInit;
}

export function storeDeployParameters(src: DeployParameters) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeInt(src.mode, 257);
        if (src.body !== null && src.body !== undefined) { b_0.storeBit(true).storeRef(src.body); } else { b_0.storeBit(false); }
        b_0.storeInt(src.value, 257);
        b_0.storeBit(src.bounce);
        b_0.store(storeStateInit(src.init));
    };
}

export function loadDeployParameters(slice: Slice) {
    const sc_0 = slice;
    const _mode = sc_0.loadIntBig(257);
    const _body = sc_0.loadBit() ? sc_0.loadRef() : null;
    const _value = sc_0.loadIntBig(257);
    const _bounce = sc_0.loadBit();
    const _init = loadStateInit(sc_0);
    return { $$type: 'DeployParameters' as const, mode: _mode, body: _body, value: _value, bounce: _bounce, init: _init };
}

export function loadTupleDeployParameters(source: TupleReader) {
    const _mode = source.readBigNumber();
    const _body = source.readCellOpt();
    const _value = source.readBigNumber();
    const _bounce = source.readBoolean();
    const _init = loadTupleStateInit(source);
    return { $$type: 'DeployParameters' as const, mode: _mode, body: _body, value: _value, bounce: _bounce, init: _init };
}

export function loadGetterTupleDeployParameters(source: TupleReader) {
    const _mode = source.readBigNumber();
    const _body = source.readCellOpt();
    const _value = source.readBigNumber();
    const _bounce = source.readBoolean();
    const _init = loadGetterTupleStateInit(source);
    return { $$type: 'DeployParameters' as const, mode: _mode, body: _body, value: _value, bounce: _bounce, init: _init };
}

export function storeTupleDeployParameters(source: DeployParameters) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.mode);
    builder.writeCell(source.body);
    builder.writeNumber(source.value);
    builder.writeBoolean(source.bounce);
    builder.writeTuple(storeTupleStateInit(source.init));
    return builder.build();
}

export function dictValueParserDeployParameters(): DictionaryValue<DeployParameters> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeDeployParameters(src)).endCell());
        },
        parse: (src) => {
            return loadDeployParameters(src.loadRef().beginParse());
        }
    }
}

export type StdAddress = {
    $$type: 'StdAddress';
    workchain: bigint;
    address: bigint;
}

export function storeStdAddress(src: StdAddress) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeInt(src.workchain, 8);
        b_0.storeUint(src.address, 256);
    };
}

export function loadStdAddress(slice: Slice) {
    const sc_0 = slice;
    const _workchain = sc_0.loadIntBig(8);
    const _address = sc_0.loadUintBig(256);
    return { $$type: 'StdAddress' as const, workchain: _workchain, address: _address };
}

export function loadTupleStdAddress(source: TupleReader) {
    const _workchain = source.readBigNumber();
    const _address = source.readBigNumber();
    return { $$type: 'StdAddress' as const, workchain: _workchain, address: _address };
}

export function loadGetterTupleStdAddress(source: TupleReader) {
    const _workchain = source.readBigNumber();
    const _address = source.readBigNumber();
    return { $$type: 'StdAddress' as const, workchain: _workchain, address: _address };
}

export function storeTupleStdAddress(source: StdAddress) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.workchain);
    builder.writeNumber(source.address);
    return builder.build();
}

export function dictValueParserStdAddress(): DictionaryValue<StdAddress> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeStdAddress(src)).endCell());
        },
        parse: (src) => {
            return loadStdAddress(src.loadRef().beginParse());
        }
    }
}

export type VarAddress = {
    $$type: 'VarAddress';
    workchain: bigint;
    address: Slice;
}

export function storeVarAddress(src: VarAddress) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeInt(src.workchain, 32);
        b_0.storeRef(src.address.asCell());
    };
}

export function loadVarAddress(slice: Slice) {
    const sc_0 = slice;
    const _workchain = sc_0.loadIntBig(32);
    const _address = sc_0.loadRef().asSlice();
    return { $$type: 'VarAddress' as const, workchain: _workchain, address: _address };
}

export function loadTupleVarAddress(source: TupleReader) {
    const _workchain = source.readBigNumber();
    const _address = source.readCell().asSlice();
    return { $$type: 'VarAddress' as const, workchain: _workchain, address: _address };
}

export function loadGetterTupleVarAddress(source: TupleReader) {
    const _workchain = source.readBigNumber();
    const _address = source.readCell().asSlice();
    return { $$type: 'VarAddress' as const, workchain: _workchain, address: _address };
}

export function storeTupleVarAddress(source: VarAddress) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.workchain);
    builder.writeSlice(source.address.asCell());
    return builder.build();
}

export function dictValueParserVarAddress(): DictionaryValue<VarAddress> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeVarAddress(src)).endCell());
        },
        parse: (src) => {
            return loadVarAddress(src.loadRef().beginParse());
        }
    }
}

export type BasechainAddress = {
    $$type: 'BasechainAddress';
    hash: bigint | null;
}

export function storeBasechainAddress(src: BasechainAddress) {
    return (builder: Builder) => {
        const b_0 = builder;
        if (src.hash !== null && src.hash !== undefined) { b_0.storeBit(true).storeInt(src.hash, 257); } else { b_0.storeBit(false); }
    };
}

export function loadBasechainAddress(slice: Slice) {
    const sc_0 = slice;
    const _hash = sc_0.loadBit() ? sc_0.loadIntBig(257) : null;
    return { $$type: 'BasechainAddress' as const, hash: _hash };
}

export function loadTupleBasechainAddress(source: TupleReader) {
    const _hash = source.readBigNumberOpt();
    return { $$type: 'BasechainAddress' as const, hash: _hash };
}

export function loadGetterTupleBasechainAddress(source: TupleReader) {
    const _hash = source.readBigNumberOpt();
    return { $$type: 'BasechainAddress' as const, hash: _hash };
}

export function storeTupleBasechainAddress(source: BasechainAddress) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.hash);
    return builder.build();
}

export function dictValueParserBasechainAddress(): DictionaryValue<BasechainAddress> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeBasechainAddress(src)).endCell());
        },
        parse: (src) => {
            return loadBasechainAddress(src.loadRef().beginParse());
        }
    }
}

export type ChangeOwner = {
    $$type: 'ChangeOwner';
    queryId: bigint;
    newOwner: Address;
}

export function storeChangeOwner(src: ChangeOwner) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(2174598809, 32);
        b_0.storeUint(src.queryId, 64);
        b_0.storeAddress(src.newOwner);
    };
}

export function loadChangeOwner(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 2174598809) { throw Error('Invalid prefix'); }
    const _queryId = sc_0.loadUintBig(64);
    const _newOwner = sc_0.loadAddress();
    return { $$type: 'ChangeOwner' as const, queryId: _queryId, newOwner: _newOwner };
}

export function loadTupleChangeOwner(source: TupleReader) {
    const _queryId = source.readBigNumber();
    const _newOwner = source.readAddress();
    return { $$type: 'ChangeOwner' as const, queryId: _queryId, newOwner: _newOwner };
}

export function loadGetterTupleChangeOwner(source: TupleReader) {
    const _queryId = source.readBigNumber();
    const _newOwner = source.readAddress();
    return { $$type: 'ChangeOwner' as const, queryId: _queryId, newOwner: _newOwner };
}

export function storeTupleChangeOwner(source: ChangeOwner) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.queryId);
    builder.writeAddress(source.newOwner);
    return builder.build();
}

export function dictValueParserChangeOwner(): DictionaryValue<ChangeOwner> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeChangeOwner(src)).endCell());
        },
        parse: (src) => {
            return loadChangeOwner(src.loadRef().beginParse());
        }
    }
}

export type ChangeOwnerOk = {
    $$type: 'ChangeOwnerOk';
    queryId: bigint;
    newOwner: Address;
}

export function storeChangeOwnerOk(src: ChangeOwnerOk) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(846932810, 32);
        b_0.storeUint(src.queryId, 64);
        b_0.storeAddress(src.newOwner);
    };
}

export function loadChangeOwnerOk(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 846932810) { throw Error('Invalid prefix'); }
    const _queryId = sc_0.loadUintBig(64);
    const _newOwner = sc_0.loadAddress();
    return { $$type: 'ChangeOwnerOk' as const, queryId: _queryId, newOwner: _newOwner };
}

export function loadTupleChangeOwnerOk(source: TupleReader) {
    const _queryId = source.readBigNumber();
    const _newOwner = source.readAddress();
    return { $$type: 'ChangeOwnerOk' as const, queryId: _queryId, newOwner: _newOwner };
}

export function loadGetterTupleChangeOwnerOk(source: TupleReader) {
    const _queryId = source.readBigNumber();
    const _newOwner = source.readAddress();
    return { $$type: 'ChangeOwnerOk' as const, queryId: _queryId, newOwner: _newOwner };
}

export function storeTupleChangeOwnerOk(source: ChangeOwnerOk) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.queryId);
    builder.writeAddress(source.newOwner);
    return builder.build();
}

export function dictValueParserChangeOwnerOk(): DictionaryValue<ChangeOwnerOk> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeChangeOwnerOk(src)).endCell());
        },
        parse: (src) => {
            return loadChangeOwnerOk(src.loadRef().beginParse());
        }
    }
}

export type Deploy = {
    $$type: 'Deploy';
    queryId: bigint;
}

export function storeDeploy(src: Deploy) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(2490013878, 32);
        b_0.storeUint(src.queryId, 64);
    };
}

export function loadDeploy(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 2490013878) { throw Error('Invalid prefix'); }
    const _queryId = sc_0.loadUintBig(64);
    return { $$type: 'Deploy' as const, queryId: _queryId };
}

export function loadTupleDeploy(source: TupleReader) {
    const _queryId = source.readBigNumber();
    return { $$type: 'Deploy' as const, queryId: _queryId };
}

export function loadGetterTupleDeploy(source: TupleReader) {
    const _queryId = source.readBigNumber();
    return { $$type: 'Deploy' as const, queryId: _queryId };
}

export function storeTupleDeploy(source: Deploy) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.queryId);
    return builder.build();
}

export function dictValueParserDeploy(): DictionaryValue<Deploy> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeDeploy(src)).endCell());
        },
        parse: (src) => {
            return loadDeploy(src.loadRef().beginParse());
        }
    }
}

export type DeployOk = {
    $$type: 'DeployOk';
    queryId: bigint;
}

export function storeDeployOk(src: DeployOk) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(2952335191, 32);
        b_0.storeUint(src.queryId, 64);
    };
}

export function loadDeployOk(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 2952335191) { throw Error('Invalid prefix'); }
    const _queryId = sc_0.loadUintBig(64);
    return { $$type: 'DeployOk' as const, queryId: _queryId };
}

export function loadTupleDeployOk(source: TupleReader) {
    const _queryId = source.readBigNumber();
    return { $$type: 'DeployOk' as const, queryId: _queryId };
}

export function loadGetterTupleDeployOk(source: TupleReader) {
    const _queryId = source.readBigNumber();
    return { $$type: 'DeployOk' as const, queryId: _queryId };
}

export function storeTupleDeployOk(source: DeployOk) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.queryId);
    return builder.build();
}

export function dictValueParserDeployOk(): DictionaryValue<DeployOk> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeDeployOk(src)).endCell());
        },
        parse: (src) => {
            return loadDeployOk(src.loadRef().beginParse());
        }
    }
}

export type FactoryDeploy = {
    $$type: 'FactoryDeploy';
    queryId: bigint;
    cashback: Address;
}

export function storeFactoryDeploy(src: FactoryDeploy) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(1829761339, 32);
        b_0.storeUint(src.queryId, 64);
        b_0.storeAddress(src.cashback);
    };
}

export function loadFactoryDeploy(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 1829761339) { throw Error('Invalid prefix'); }
    const _queryId = sc_0.loadUintBig(64);
    const _cashback = sc_0.loadAddress();
    return { $$type: 'FactoryDeploy' as const, queryId: _queryId, cashback: _cashback };
}

export function loadTupleFactoryDeploy(source: TupleReader) {
    const _queryId = source.readBigNumber();
    const _cashback = source.readAddress();
    return { $$type: 'FactoryDeploy' as const, queryId: _queryId, cashback: _cashback };
}

export function loadGetterTupleFactoryDeploy(source: TupleReader) {
    const _queryId = source.readBigNumber();
    const _cashback = source.readAddress();
    return { $$type: 'FactoryDeploy' as const, queryId: _queryId, cashback: _cashback };
}

export function storeTupleFactoryDeploy(source: FactoryDeploy) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.queryId);
    builder.writeAddress(source.cashback);
    return builder.build();
}

export function dictValueParserFactoryDeploy(): DictionaryValue<FactoryDeploy> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeFactoryDeploy(src)).endCell());
        },
        parse: (src) => {
            return loadFactoryDeploy(src.loadRef().beginParse());
        }
    }
}

export type JettonTransfer = {
    $$type: 'JettonTransfer';
    query_id: bigint;
    amount: bigint;
    destination: Address;
    response_destination: Address;
    custom_payload: Cell | null;
    forward_ton_amount: bigint;
    forward_payload: Cell | null;
}

export function storeJettonTransfer(src: JettonTransfer) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(260734629, 32);
        b_0.storeUint(src.query_id, 64);
        b_0.storeCoins(src.amount);
        b_0.storeAddress(src.destination);
        b_0.storeAddress(src.response_destination);
        if (src.custom_payload !== null && src.custom_payload !== undefined) { b_0.storeBit(true).storeRef(src.custom_payload); } else { b_0.storeBit(false); }
        b_0.storeCoins(src.forward_ton_amount);
        if (src.forward_payload !== null && src.forward_payload !== undefined) { b_0.storeBit(true).storeRef(src.forward_payload); } else { b_0.storeBit(false); }
    };
}

export function loadJettonTransfer(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 260734629) { throw Error('Invalid prefix'); }
    const _query_id = sc_0.loadUintBig(64);
    const _amount = sc_0.loadCoins();
    const _destination = sc_0.loadAddress();
    const _response_destination = sc_0.loadAddress();
    const _custom_payload = sc_0.loadBit() ? sc_0.loadRef() : null;
    const _forward_ton_amount = sc_0.loadCoins();
    const _forward_payload = sc_0.loadBit() ? sc_0.loadRef() : null;
    return { $$type: 'JettonTransfer' as const, query_id: _query_id, amount: _amount, destination: _destination, response_destination: _response_destination, custom_payload: _custom_payload, forward_ton_amount: _forward_ton_amount, forward_payload: _forward_payload };
}

export function loadTupleJettonTransfer(source: TupleReader) {
    const _query_id = source.readBigNumber();
    const _amount = source.readBigNumber();
    const _destination = source.readAddress();
    const _response_destination = source.readAddress();
    const _custom_payload = source.readCellOpt();
    const _forward_ton_amount = source.readBigNumber();
    const _forward_payload = source.readCellOpt();
    return { $$type: 'JettonTransfer' as const, query_id: _query_id, amount: _amount, destination: _destination, response_destination: _response_destination, custom_payload: _custom_payload, forward_ton_amount: _forward_ton_amount, forward_payload: _forward_payload };
}

export function loadGetterTupleJettonTransfer(source: TupleReader) {
    const _query_id = source.readBigNumber();
    const _amount = source.readBigNumber();
    const _destination = source.readAddress();
    const _response_destination = source.readAddress();
    const _custom_payload = source.readCellOpt();
    const _forward_ton_amount = source.readBigNumber();
    const _forward_payload = source.readCellOpt();
    return { $$type: 'JettonTransfer' as const, query_id: _query_id, amount: _amount, destination: _destination, response_destination: _response_destination, custom_payload: _custom_payload, forward_ton_amount: _forward_ton_amount, forward_payload: _forward_payload };
}

export function storeTupleJettonTransfer(source: JettonTransfer) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.query_id);
    builder.writeNumber(source.amount);
    builder.writeAddress(source.destination);
    builder.writeAddress(source.response_destination);
    builder.writeCell(source.custom_payload);
    builder.writeNumber(source.forward_ton_amount);
    builder.writeCell(source.forward_payload);
    return builder.build();
}

export function dictValueParserJettonTransfer(): DictionaryValue<JettonTransfer> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeJettonTransfer(src)).endCell());
        },
        parse: (src) => {
            return loadJettonTransfer(src.loadRef().beginParse());
        }
    }
}

export type JettonTransferNotification = {
    $$type: 'JettonTransferNotification';
    query_id: bigint;
    amount: bigint;
    sender: Address;
    forward_payload: Cell | null;
}

export function storeJettonTransferNotification(src: JettonTransferNotification) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(1935855772, 32);
        b_0.storeUint(src.query_id, 64);
        b_0.storeCoins(src.amount);
        b_0.storeAddress(src.sender);
        if (src.forward_payload !== null && src.forward_payload !== undefined) { b_0.storeBit(true).storeRef(src.forward_payload); } else { b_0.storeBit(false); }
    };
}

export function loadJettonTransferNotification(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 1935855772) { throw Error('Invalid prefix'); }
    const _query_id = sc_0.loadUintBig(64);
    const _amount = sc_0.loadCoins();
    const _sender = sc_0.loadAddress();
    const _forward_payload = sc_0.loadBit() ? sc_0.loadRef() : null;
    return { $$type: 'JettonTransferNotification' as const, query_id: _query_id, amount: _amount, sender: _sender, forward_payload: _forward_payload };
}

export function loadTupleJettonTransferNotification(source: TupleReader) {
    const _query_id = source.readBigNumber();
    const _amount = source.readBigNumber();
    const _sender = source.readAddress();
    const _forward_payload = source.readCellOpt();
    return { $$type: 'JettonTransferNotification' as const, query_id: _query_id, amount: _amount, sender: _sender, forward_payload: _forward_payload };
}

export function loadGetterTupleJettonTransferNotification(source: TupleReader) {
    const _query_id = source.readBigNumber();
    const _amount = source.readBigNumber();
    const _sender = source.readAddress();
    const _forward_payload = source.readCellOpt();
    return { $$type: 'JettonTransferNotification' as const, query_id: _query_id, amount: _amount, sender: _sender, forward_payload: _forward_payload };
}

export function storeTupleJettonTransferNotification(source: JettonTransferNotification) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.query_id);
    builder.writeNumber(source.amount);
    builder.writeAddress(source.sender);
    builder.writeCell(source.forward_payload);
    return builder.build();
}

export function dictValueParserJettonTransferNotification(): DictionaryValue<JettonTransferNotification> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeJettonTransferNotification(src)).endCell());
        },
        parse: (src) => {
            return loadJettonTransferNotification(src.loadRef().beginParse());
        }
    }
}

export type JettonBurn = {
    $$type: 'JettonBurn';
    query_id: bigint;
    amount: bigint;
    response_destination: Address;
    custom_payload: Cell | null;
}

export function storeJettonBurn(src: JettonBurn) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(1499400124, 32);
        b_0.storeUint(src.query_id, 64);
        b_0.storeCoins(src.amount);
        b_0.storeAddress(src.response_destination);
        if (src.custom_payload !== null && src.custom_payload !== undefined) { b_0.storeBit(true).storeRef(src.custom_payload); } else { b_0.storeBit(false); }
    };
}

export function loadJettonBurn(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 1499400124) { throw Error('Invalid prefix'); }
    const _query_id = sc_0.loadUintBig(64);
    const _amount = sc_0.loadCoins();
    const _response_destination = sc_0.loadAddress();
    const _custom_payload = sc_0.loadBit() ? sc_0.loadRef() : null;
    return { $$type: 'JettonBurn' as const, query_id: _query_id, amount: _amount, response_destination: _response_destination, custom_payload: _custom_payload };
}

export function loadTupleJettonBurn(source: TupleReader) {
    const _query_id = source.readBigNumber();
    const _amount = source.readBigNumber();
    const _response_destination = source.readAddress();
    const _custom_payload = source.readCellOpt();
    return { $$type: 'JettonBurn' as const, query_id: _query_id, amount: _amount, response_destination: _response_destination, custom_payload: _custom_payload };
}

export function loadGetterTupleJettonBurn(source: TupleReader) {
    const _query_id = source.readBigNumber();
    const _amount = source.readBigNumber();
    const _response_destination = source.readAddress();
    const _custom_payload = source.readCellOpt();
    return { $$type: 'JettonBurn' as const, query_id: _query_id, amount: _amount, response_destination: _response_destination, custom_payload: _custom_payload };
}

export function storeTupleJettonBurn(source: JettonBurn) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.query_id);
    builder.writeNumber(source.amount);
    builder.writeAddress(source.response_destination);
    builder.writeCell(source.custom_payload);
    return builder.build();
}

export function dictValueParserJettonBurn(): DictionaryValue<JettonBurn> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeJettonBurn(src)).endCell());
        },
        parse: (src) => {
            return loadJettonBurn(src.loadRef().beginParse());
        }
    }
}

export type JettonExcesses = {
    $$type: 'JettonExcesses';
    query_id: bigint;
}

export function storeJettonExcesses(src: JettonExcesses) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(3576854235, 32);
        b_0.storeUint(src.query_id, 64);
    };
}

export function loadJettonExcesses(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 3576854235) { throw Error('Invalid prefix'); }
    const _query_id = sc_0.loadUintBig(64);
    return { $$type: 'JettonExcesses' as const, query_id: _query_id };
}

export function loadTupleJettonExcesses(source: TupleReader) {
    const _query_id = source.readBigNumber();
    return { $$type: 'JettonExcesses' as const, query_id: _query_id };
}

export function loadGetterTupleJettonExcesses(source: TupleReader) {
    const _query_id = source.readBigNumber();
    return { $$type: 'JettonExcesses' as const, query_id: _query_id };
}

export function storeTupleJettonExcesses(source: JettonExcesses) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.query_id);
    return builder.build();
}

export function dictValueParserJettonExcesses(): DictionaryValue<JettonExcesses> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeJettonExcesses(src)).endCell());
        },
        parse: (src) => {
            return loadJettonExcesses(src.loadRef().beginParse());
        }
    }
}

export type JettonInternalTransfer = {
    $$type: 'JettonInternalTransfer';
    query_id: bigint;
    amount: bigint;
    from: Address;
    response_address: Address;
    forward_ton_amount: bigint;
    forward_payload: Cell | null;
}

export function storeJettonInternalTransfer(src: JettonInternalTransfer) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(395134233, 32);
        b_0.storeUint(src.query_id, 64);
        b_0.storeCoins(src.amount);
        b_0.storeAddress(src.from);
        b_0.storeAddress(src.response_address);
        b_0.storeCoins(src.forward_ton_amount);
        if (src.forward_payload !== null && src.forward_payload !== undefined) { b_0.storeBit(true).storeRef(src.forward_payload); } else { b_0.storeBit(false); }
    };
}

export function loadJettonInternalTransfer(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 395134233) { throw Error('Invalid prefix'); }
    const _query_id = sc_0.loadUintBig(64);
    const _amount = sc_0.loadCoins();
    const _from = sc_0.loadAddress();
    const _response_address = sc_0.loadAddress();
    const _forward_ton_amount = sc_0.loadCoins();
    const _forward_payload = sc_0.loadBit() ? sc_0.loadRef() : null;
    return { $$type: 'JettonInternalTransfer' as const, query_id: _query_id, amount: _amount, from: _from, response_address: _response_address, forward_ton_amount: _forward_ton_amount, forward_payload: _forward_payload };
}

export function loadTupleJettonInternalTransfer(source: TupleReader) {
    const _query_id = source.readBigNumber();
    const _amount = source.readBigNumber();
    const _from = source.readAddress();
    const _response_address = source.readAddress();
    const _forward_ton_amount = source.readBigNumber();
    const _forward_payload = source.readCellOpt();
    return { $$type: 'JettonInternalTransfer' as const, query_id: _query_id, amount: _amount, from: _from, response_address: _response_address, forward_ton_amount: _forward_ton_amount, forward_payload: _forward_payload };
}

export function loadGetterTupleJettonInternalTransfer(source: TupleReader) {
    const _query_id = source.readBigNumber();
    const _amount = source.readBigNumber();
    const _from = source.readAddress();
    const _response_address = source.readAddress();
    const _forward_ton_amount = source.readBigNumber();
    const _forward_payload = source.readCellOpt();
    return { $$type: 'JettonInternalTransfer' as const, query_id: _query_id, amount: _amount, from: _from, response_address: _response_address, forward_ton_amount: _forward_ton_amount, forward_payload: _forward_payload };
}

export function storeTupleJettonInternalTransfer(source: JettonInternalTransfer) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.query_id);
    builder.writeNumber(source.amount);
    builder.writeAddress(source.from);
    builder.writeAddress(source.response_address);
    builder.writeNumber(source.forward_ton_amount);
    builder.writeCell(source.forward_payload);
    return builder.build();
}

export function dictValueParserJettonInternalTransfer(): DictionaryValue<JettonInternalTransfer> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeJettonInternalTransfer(src)).endCell());
        },
        parse: (src) => {
            return loadJettonInternalTransfer(src.loadRef().beginParse());
        }
    }
}

export type JettonBurnNotification = {
    $$type: 'JettonBurnNotification';
    query_id: bigint;
    amount: bigint;
    sender: Address;
    response_destination: Address;
}

export function storeJettonBurnNotification(src: JettonBurnNotification) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(2078119902, 32);
        b_0.storeUint(src.query_id, 64);
        b_0.storeCoins(src.amount);
        b_0.storeAddress(src.sender);
        b_0.storeAddress(src.response_destination);
    };
}

export function loadJettonBurnNotification(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 2078119902) { throw Error('Invalid prefix'); }
    const _query_id = sc_0.loadUintBig(64);
    const _amount = sc_0.loadCoins();
    const _sender = sc_0.loadAddress();
    const _response_destination = sc_0.loadAddress();
    return { $$type: 'JettonBurnNotification' as const, query_id: _query_id, amount: _amount, sender: _sender, response_destination: _response_destination };
}

export function loadTupleJettonBurnNotification(source: TupleReader) {
    const _query_id = source.readBigNumber();
    const _amount = source.readBigNumber();
    const _sender = source.readAddress();
    const _response_destination = source.readAddress();
    return { $$type: 'JettonBurnNotification' as const, query_id: _query_id, amount: _amount, sender: _sender, response_destination: _response_destination };
}

export function loadGetterTupleJettonBurnNotification(source: TupleReader) {
    const _query_id = source.readBigNumber();
    const _amount = source.readBigNumber();
    const _sender = source.readAddress();
    const _response_destination = source.readAddress();
    return { $$type: 'JettonBurnNotification' as const, query_id: _query_id, amount: _amount, sender: _sender, response_destination: _response_destination };
}

export function storeTupleJettonBurnNotification(source: JettonBurnNotification) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.query_id);
    builder.writeNumber(source.amount);
    builder.writeAddress(source.sender);
    builder.writeAddress(source.response_destination);
    return builder.build();
}

export function dictValueParserJettonBurnNotification(): DictionaryValue<JettonBurnNotification> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeJettonBurnNotification(src)).endCell());
        },
        parse: (src) => {
            return loadJettonBurnNotification(src.loadRef().beginParse());
        }
    }
}

export type WalletData = {
    $$type: 'WalletData';
    balance: bigint;
    owner: Address;
    jetton: Address;
    jetton_wallet_code: Cell;
}

export function storeWalletData(src: WalletData) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeCoins(src.balance);
        b_0.storeAddress(src.owner);
        b_0.storeAddress(src.jetton);
        b_0.storeRef(src.jetton_wallet_code);
    };
}

export function loadWalletData(slice: Slice) {
    const sc_0 = slice;
    const _balance = sc_0.loadCoins();
    const _owner = sc_0.loadAddress();
    const _jetton = sc_0.loadAddress();
    const _jetton_wallet_code = sc_0.loadRef();
    return { $$type: 'WalletData' as const, balance: _balance, owner: _owner, jetton: _jetton, jetton_wallet_code: _jetton_wallet_code };
}

export function loadTupleWalletData(source: TupleReader) {
    const _balance = source.readBigNumber();
    const _owner = source.readAddress();
    const _jetton = source.readAddress();
    const _jetton_wallet_code = source.readCell();
    return { $$type: 'WalletData' as const, balance: _balance, owner: _owner, jetton: _jetton, jetton_wallet_code: _jetton_wallet_code };
}

export function loadGetterTupleWalletData(source: TupleReader) {
    const _balance = source.readBigNumber();
    const _owner = source.readAddress();
    const _jetton = source.readAddress();
    const _jetton_wallet_code = source.readCell();
    return { $$type: 'WalletData' as const, balance: _balance, owner: _owner, jetton: _jetton, jetton_wallet_code: _jetton_wallet_code };
}

export function storeTupleWalletData(source: WalletData) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.balance);
    builder.writeAddress(source.owner);
    builder.writeAddress(source.jetton);
    builder.writeCell(source.jetton_wallet_code);
    return builder.build();
}

export function dictValueParserWalletData(): DictionaryValue<WalletData> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeWalletData(src)).endCell());
        },
        parse: (src) => {
            return loadWalletData(src.loadRef().beginParse());
        }
    }
}

export type CreateUserPurchase = {
    $$type: 'CreateUserPurchase';
    user: Address;
    amount: bigint;
    tokens: bigint;
    currency: bigint;
    purchase_method: bigint;
    nonce: bigint;
    round_number: bigint;
    usdt_equivalent_amount: bigint;
}

export function storeCreateUserPurchase(src: CreateUserPurchase) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(1098075148, 32);
        b_0.storeAddress(src.user);
        b_0.storeCoins(src.amount);
        b_0.storeCoins(src.tokens);
        b_0.storeUint(src.currency, 8);
        b_0.storeUint(src.purchase_method, 8);
        b_0.storeUint(src.nonce, 64);
        b_0.storeUint(src.round_number, 32);
        b_0.storeCoins(src.usdt_equivalent_amount);
    };
}

export function loadCreateUserPurchase(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 1098075148) { throw Error('Invalid prefix'); }
    const _user = sc_0.loadAddress();
    const _amount = sc_0.loadCoins();
    const _tokens = sc_0.loadCoins();
    const _currency = sc_0.loadUintBig(8);
    const _purchase_method = sc_0.loadUintBig(8);
    const _nonce = sc_0.loadUintBig(64);
    const _round_number = sc_0.loadUintBig(32);
    const _usdt_equivalent_amount = sc_0.loadCoins();
    return { $$type: 'CreateUserPurchase' as const, user: _user, amount: _amount, tokens: _tokens, currency: _currency, purchase_method: _purchase_method, nonce: _nonce, round_number: _round_number, usdt_equivalent_amount: _usdt_equivalent_amount };
}

export function loadTupleCreateUserPurchase(source: TupleReader) {
    const _user = source.readAddress();
    const _amount = source.readBigNumber();
    const _tokens = source.readBigNumber();
    const _currency = source.readBigNumber();
    const _purchase_method = source.readBigNumber();
    const _nonce = source.readBigNumber();
    const _round_number = source.readBigNumber();
    const _usdt_equivalent_amount = source.readBigNumber();
    return { $$type: 'CreateUserPurchase' as const, user: _user, amount: _amount, tokens: _tokens, currency: _currency, purchase_method: _purchase_method, nonce: _nonce, round_number: _round_number, usdt_equivalent_amount: _usdt_equivalent_amount };
}

export function loadGetterTupleCreateUserPurchase(source: TupleReader) {
    const _user = source.readAddress();
    const _amount = source.readBigNumber();
    const _tokens = source.readBigNumber();
    const _currency = source.readBigNumber();
    const _purchase_method = source.readBigNumber();
    const _nonce = source.readBigNumber();
    const _round_number = source.readBigNumber();
    const _usdt_equivalent_amount = source.readBigNumber();
    return { $$type: 'CreateUserPurchase' as const, user: _user, amount: _amount, tokens: _tokens, currency: _currency, purchase_method: _purchase_method, nonce: _nonce, round_number: _round_number, usdt_equivalent_amount: _usdt_equivalent_amount };
}

export function storeTupleCreateUserPurchase(source: CreateUserPurchase) {
    const builder = new TupleBuilder();
    builder.writeAddress(source.user);
    builder.writeNumber(source.amount);
    builder.writeNumber(source.tokens);
    builder.writeNumber(source.currency);
    builder.writeNumber(source.purchase_method);
    builder.writeNumber(source.nonce);
    builder.writeNumber(source.round_number);
    builder.writeNumber(source.usdt_equivalent_amount);
    return builder.build();
}

export function dictValueParserCreateUserPurchase(): DictionaryValue<CreateUserPurchase> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeCreateUserPurchase(src)).endCell());
        },
        parse: (src) => {
            return loadCreateUserPurchase(src.loadRef().beginParse());
        }
    }
}

export type Refund = {
    $$type: 'Refund';
    purchase_id: bigint;
}

export function storeRefund(src: Refund) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(836089260, 32);
        b_0.storeUint(src.purchase_id, 32);
    };
}

export function loadRefund(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 836089260) { throw Error('Invalid prefix'); }
    const _purchase_id = sc_0.loadUintBig(32);
    return { $$type: 'Refund' as const, purchase_id: _purchase_id };
}

export function loadTupleRefund(source: TupleReader) {
    const _purchase_id = source.readBigNumber();
    return { $$type: 'Refund' as const, purchase_id: _purchase_id };
}

export function loadGetterTupleRefund(source: TupleReader) {
    const _purchase_id = source.readBigNumber();
    return { $$type: 'Refund' as const, purchase_id: _purchase_id };
}

export function storeTupleRefund(source: Refund) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.purchase_id);
    return builder.build();
}

export function dictValueParserRefund(): DictionaryValue<Refund> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeRefund(src)).endCell());
        },
        parse: (src) => {
            return loadRefund(src.loadRef().beginParse());
        }
    }
}

export type ProcessRefund = {
    $$type: 'ProcessRefund';
    user: Address;
    amount: bigint;
    fee: bigint;
    currency: bigint;
    round_number: bigint;
    usdt_equivalent_amount: bigint;
}

export function storeProcessRefund(src: ProcessRefund) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(929991442, 32);
        b_0.storeAddress(src.user);
        b_0.storeCoins(src.amount);
        b_0.storeCoins(src.fee);
        b_0.storeUint(src.currency, 8);
        b_0.storeUint(src.round_number, 32);
        b_0.storeCoins(src.usdt_equivalent_amount);
    };
}

export function loadProcessRefund(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 929991442) { throw Error('Invalid prefix'); }
    const _user = sc_0.loadAddress();
    const _amount = sc_0.loadCoins();
    const _fee = sc_0.loadCoins();
    const _currency = sc_0.loadUintBig(8);
    const _round_number = sc_0.loadUintBig(32);
    const _usdt_equivalent_amount = sc_0.loadCoins();
    return { $$type: 'ProcessRefund' as const, user: _user, amount: _amount, fee: _fee, currency: _currency, round_number: _round_number, usdt_equivalent_amount: _usdt_equivalent_amount };
}

export function loadTupleProcessRefund(source: TupleReader) {
    const _user = source.readAddress();
    const _amount = source.readBigNumber();
    const _fee = source.readBigNumber();
    const _currency = source.readBigNumber();
    const _round_number = source.readBigNumber();
    const _usdt_equivalent_amount = source.readBigNumber();
    return { $$type: 'ProcessRefund' as const, user: _user, amount: _amount, fee: _fee, currency: _currency, round_number: _round_number, usdt_equivalent_amount: _usdt_equivalent_amount };
}

export function loadGetterTupleProcessRefund(source: TupleReader) {
    const _user = source.readAddress();
    const _amount = source.readBigNumber();
    const _fee = source.readBigNumber();
    const _currency = source.readBigNumber();
    const _round_number = source.readBigNumber();
    const _usdt_equivalent_amount = source.readBigNumber();
    return { $$type: 'ProcessRefund' as const, user: _user, amount: _amount, fee: _fee, currency: _currency, round_number: _round_number, usdt_equivalent_amount: _usdt_equivalent_amount };
}

export function storeTupleProcessRefund(source: ProcessRefund) {
    const builder = new TupleBuilder();
    builder.writeAddress(source.user);
    builder.writeNumber(source.amount);
    builder.writeNumber(source.fee);
    builder.writeNumber(source.currency);
    builder.writeNumber(source.round_number);
    builder.writeNumber(source.usdt_equivalent_amount);
    return builder.build();
}

export function dictValueParserProcessRefund(): DictionaryValue<ProcessRefund> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeProcessRefund(src)).endCell());
        },
        parse: (src) => {
            return loadProcessRefund(src.loadRef().beginParse());
        }
    }
}

export type PurchaseRecord = {
    $$type: 'PurchaseRecord';
    id: bigint;
    user: Address;
    amount: bigint;
    tokens: bigint;
    timestamp: bigint;
    currency: bigint;
    purchase_method: bigint;
    nonce: bigint;
    round_number: bigint;
    usdt_equivalent_amount: bigint;
}

export function storePurchaseRecord(src: PurchaseRecord) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(src.id, 32);
        b_0.storeAddress(src.user);
        b_0.storeCoins(src.amount);
        b_0.storeCoins(src.tokens);
        b_0.storeUint(src.timestamp, 64);
        b_0.storeUint(src.currency, 8);
        b_0.storeUint(src.purchase_method, 8);
        b_0.storeUint(src.nonce, 64);
        b_0.storeUint(src.round_number, 32);
        b_0.storeCoins(src.usdt_equivalent_amount);
    };
}

export function loadPurchaseRecord(slice: Slice) {
    const sc_0 = slice;
    const _id = sc_0.loadUintBig(32);
    const _user = sc_0.loadAddress();
    const _amount = sc_0.loadCoins();
    const _tokens = sc_0.loadCoins();
    const _timestamp = sc_0.loadUintBig(64);
    const _currency = sc_0.loadUintBig(8);
    const _purchase_method = sc_0.loadUintBig(8);
    const _nonce = sc_0.loadUintBig(64);
    const _round_number = sc_0.loadUintBig(32);
    const _usdt_equivalent_amount = sc_0.loadCoins();
    return { $$type: 'PurchaseRecord' as const, id: _id, user: _user, amount: _amount, tokens: _tokens, timestamp: _timestamp, currency: _currency, purchase_method: _purchase_method, nonce: _nonce, round_number: _round_number, usdt_equivalent_amount: _usdt_equivalent_amount };
}

export function loadTuplePurchaseRecord(source: TupleReader) {
    const _id = source.readBigNumber();
    const _user = source.readAddress();
    const _amount = source.readBigNumber();
    const _tokens = source.readBigNumber();
    const _timestamp = source.readBigNumber();
    const _currency = source.readBigNumber();
    const _purchase_method = source.readBigNumber();
    const _nonce = source.readBigNumber();
    const _round_number = source.readBigNumber();
    const _usdt_equivalent_amount = source.readBigNumber();
    return { $$type: 'PurchaseRecord' as const, id: _id, user: _user, amount: _amount, tokens: _tokens, timestamp: _timestamp, currency: _currency, purchase_method: _purchase_method, nonce: _nonce, round_number: _round_number, usdt_equivalent_amount: _usdt_equivalent_amount };
}

export function loadGetterTuplePurchaseRecord(source: TupleReader) {
    const _id = source.readBigNumber();
    const _user = source.readAddress();
    const _amount = source.readBigNumber();
    const _tokens = source.readBigNumber();
    const _timestamp = source.readBigNumber();
    const _currency = source.readBigNumber();
    const _purchase_method = source.readBigNumber();
    const _nonce = source.readBigNumber();
    const _round_number = source.readBigNumber();
    const _usdt_equivalent_amount = source.readBigNumber();
    return { $$type: 'PurchaseRecord' as const, id: _id, user: _user, amount: _amount, tokens: _tokens, timestamp: _timestamp, currency: _currency, purchase_method: _purchase_method, nonce: _nonce, round_number: _round_number, usdt_equivalent_amount: _usdt_equivalent_amount };
}

export function storeTuplePurchaseRecord(source: PurchaseRecord) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.id);
    builder.writeAddress(source.user);
    builder.writeNumber(source.amount);
    builder.writeNumber(source.tokens);
    builder.writeNumber(source.timestamp);
    builder.writeNumber(source.currency);
    builder.writeNumber(source.purchase_method);
    builder.writeNumber(source.nonce);
    builder.writeNumber(source.round_number);
    builder.writeNumber(source.usdt_equivalent_amount);
    return builder.build();
}

export function dictValueParserPurchaseRecord(): DictionaryValue<PurchaseRecord> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storePurchaseRecord(src)).endCell());
        },
        parse: (src) => {
            return loadPurchaseRecord(src.loadRef().beginParse());
        }
    }
}

export type UserPurchase$Data = {
    $$type: 'UserPurchase$Data';
    owner: Address;
    auction_address: Address;
    user_address: Address;
    total_purchased: bigint;
    total_paid: bigint;
    purchase_history: Dictionary<bigint, PurchaseRecord>;
    refund_history: Dictionary<bigint, bigint>;
    purchase_id_counter: bigint;
    participated_rounds: Dictionary<bigint, boolean>;
}

export function storeUserPurchase$Data(src: UserPurchase$Data) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeAddress(src.owner);
        b_0.storeAddress(src.auction_address);
        b_0.storeAddress(src.user_address);
        b_0.storeCoins(src.total_purchased);
        const b_1 = new Builder();
        b_1.storeCoins(src.total_paid);
        b_1.storeDict(src.purchase_history, Dictionary.Keys.BigInt(257), dictValueParserPurchaseRecord());
        b_1.storeDict(src.refund_history, Dictionary.Keys.BigInt(257), Dictionary.Values.BigInt(257));
        b_1.storeUint(src.purchase_id_counter, 32);
        b_1.storeDict(src.participated_rounds, Dictionary.Keys.BigInt(257), Dictionary.Values.Bool());
        b_0.storeRef(b_1.endCell());
    };
}

export function loadUserPurchase$Data(slice: Slice) {
    const sc_0 = slice;
    const _owner = sc_0.loadAddress();
    const _auction_address = sc_0.loadAddress();
    const _user_address = sc_0.loadAddress();
    const _total_purchased = sc_0.loadCoins();
    const sc_1 = sc_0.loadRef().beginParse();
    const _total_paid = sc_1.loadCoins();
    const _purchase_history = Dictionary.load(Dictionary.Keys.BigInt(257), dictValueParserPurchaseRecord(), sc_1);
    const _refund_history = Dictionary.load(Dictionary.Keys.BigInt(257), Dictionary.Values.BigInt(257), sc_1);
    const _purchase_id_counter = sc_1.loadUintBig(32);
    const _participated_rounds = Dictionary.load(Dictionary.Keys.BigInt(257), Dictionary.Values.Bool(), sc_1);
    return { $$type: 'UserPurchase$Data' as const, owner: _owner, auction_address: _auction_address, user_address: _user_address, total_purchased: _total_purchased, total_paid: _total_paid, purchase_history: _purchase_history, refund_history: _refund_history, purchase_id_counter: _purchase_id_counter, participated_rounds: _participated_rounds };
}

export function loadTupleUserPurchase$Data(source: TupleReader) {
    const _owner = source.readAddress();
    const _auction_address = source.readAddress();
    const _user_address = source.readAddress();
    const _total_purchased = source.readBigNumber();
    const _total_paid = source.readBigNumber();
    const _purchase_history = Dictionary.loadDirect(Dictionary.Keys.BigInt(257), dictValueParserPurchaseRecord(), source.readCellOpt());
    const _refund_history = Dictionary.loadDirect(Dictionary.Keys.BigInt(257), Dictionary.Values.BigInt(257), source.readCellOpt());
    const _purchase_id_counter = source.readBigNumber();
    const _participated_rounds = Dictionary.loadDirect(Dictionary.Keys.BigInt(257), Dictionary.Values.Bool(), source.readCellOpt());
    return { $$type: 'UserPurchase$Data' as const, owner: _owner, auction_address: _auction_address, user_address: _user_address, total_purchased: _total_purchased, total_paid: _total_paid, purchase_history: _purchase_history, refund_history: _refund_history, purchase_id_counter: _purchase_id_counter, participated_rounds: _participated_rounds };
}

export function loadGetterTupleUserPurchase$Data(source: TupleReader) {
    const _owner = source.readAddress();
    const _auction_address = source.readAddress();
    const _user_address = source.readAddress();
    const _total_purchased = source.readBigNumber();
    const _total_paid = source.readBigNumber();
    const _purchase_history = Dictionary.loadDirect(Dictionary.Keys.BigInt(257), dictValueParserPurchaseRecord(), source.readCellOpt());
    const _refund_history = Dictionary.loadDirect(Dictionary.Keys.BigInt(257), Dictionary.Values.BigInt(257), source.readCellOpt());
    const _purchase_id_counter = source.readBigNumber();
    const _participated_rounds = Dictionary.loadDirect(Dictionary.Keys.BigInt(257), Dictionary.Values.Bool(), source.readCellOpt());
    return { $$type: 'UserPurchase$Data' as const, owner: _owner, auction_address: _auction_address, user_address: _user_address, total_purchased: _total_purchased, total_paid: _total_paid, purchase_history: _purchase_history, refund_history: _refund_history, purchase_id_counter: _purchase_id_counter, participated_rounds: _participated_rounds };
}

export function storeTupleUserPurchase$Data(source: UserPurchase$Data) {
    const builder = new TupleBuilder();
    builder.writeAddress(source.owner);
    builder.writeAddress(source.auction_address);
    builder.writeAddress(source.user_address);
    builder.writeNumber(source.total_purchased);
    builder.writeNumber(source.total_paid);
    builder.writeCell(source.purchase_history.size > 0 ? beginCell().storeDictDirect(source.purchase_history, Dictionary.Keys.BigInt(257), dictValueParserPurchaseRecord()).endCell() : null);
    builder.writeCell(source.refund_history.size > 0 ? beginCell().storeDictDirect(source.refund_history, Dictionary.Keys.BigInt(257), Dictionary.Values.BigInt(257)).endCell() : null);
    builder.writeNumber(source.purchase_id_counter);
    builder.writeCell(source.participated_rounds.size > 0 ? beginCell().storeDictDirect(source.participated_rounds, Dictionary.Keys.BigInt(257), Dictionary.Values.Bool()).endCell() : null);
    return builder.build();
}

export function dictValueParserUserPurchase$Data(): DictionaryValue<UserPurchase$Data> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeUserPurchase$Data(src)).endCell());
        },
        parse: (src) => {
            return loadUserPurchase$Data(src.loadRef().beginParse());
        }
    }
}

export type StartAuction = {
    $$type: 'StartAuction';
    start_time: bigint;
    end_time: bigint;
    soft_cap: bigint;
    hard_cap: bigint;
    initial_price: bigint;
}

export function storeStartAuction(src: StartAuction) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(1082886929, 32);
        b_0.storeUint(src.start_time, 64);
        b_0.storeUint(src.end_time, 64);
        b_0.storeCoins(src.soft_cap);
        b_0.storeCoins(src.hard_cap);
        b_0.storeCoins(src.initial_price);
    };
}

export function loadStartAuction(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 1082886929) { throw Error('Invalid prefix'); }
    const _start_time = sc_0.loadUintBig(64);
    const _end_time = sc_0.loadUintBig(64);
    const _soft_cap = sc_0.loadCoins();
    const _hard_cap = sc_0.loadCoins();
    const _initial_price = sc_0.loadCoins();
    return { $$type: 'StartAuction' as const, start_time: _start_time, end_time: _end_time, soft_cap: _soft_cap, hard_cap: _hard_cap, initial_price: _initial_price };
}

export function loadTupleStartAuction(source: TupleReader) {
    const _start_time = source.readBigNumber();
    const _end_time = source.readBigNumber();
    const _soft_cap = source.readBigNumber();
    const _hard_cap = source.readBigNumber();
    const _initial_price = source.readBigNumber();
    return { $$type: 'StartAuction' as const, start_time: _start_time, end_time: _end_time, soft_cap: _soft_cap, hard_cap: _hard_cap, initial_price: _initial_price };
}

export function loadGetterTupleStartAuction(source: TupleReader) {
    const _start_time = source.readBigNumber();
    const _end_time = source.readBigNumber();
    const _soft_cap = source.readBigNumber();
    const _hard_cap = source.readBigNumber();
    const _initial_price = source.readBigNumber();
    return { $$type: 'StartAuction' as const, start_time: _start_time, end_time: _end_time, soft_cap: _soft_cap, hard_cap: _hard_cap, initial_price: _initial_price };
}

export function storeTupleStartAuction(source: StartAuction) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.start_time);
    builder.writeNumber(source.end_time);
    builder.writeNumber(source.soft_cap);
    builder.writeNumber(source.hard_cap);
    builder.writeNumber(source.initial_price);
    return builder.build();
}

export function dictValueParserStartAuction(): DictionaryValue<StartAuction> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeStartAuction(src)).endCell());
        },
        parse: (src) => {
            return loadStartAuction(src.loadRef().beginParse());
        }
    }
}

export type UpdateRound = {
    $$type: 'UpdateRound';
    new_price: bigint;
    round_number: bigint;
}

export function storeUpdateRound(src: UpdateRound) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(2861271945, 32);
        b_0.storeCoins(src.new_price);
        b_0.storeUint(src.round_number, 32);
    };
}

export function loadUpdateRound(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 2861271945) { throw Error('Invalid prefix'); }
    const _new_price = sc_0.loadCoins();
    const _round_number = sc_0.loadUintBig(32);
    return { $$type: 'UpdateRound' as const, new_price: _new_price, round_number: _round_number };
}

export function loadTupleUpdateRound(source: TupleReader) {
    const _new_price = source.readBigNumber();
    const _round_number = source.readBigNumber();
    return { $$type: 'UpdateRound' as const, new_price: _new_price, round_number: _round_number };
}

export function loadGetterTupleUpdateRound(source: TupleReader) {
    const _new_price = source.readBigNumber();
    const _round_number = source.readBigNumber();
    return { $$type: 'UpdateRound' as const, new_price: _new_price, round_number: _round_number };
}

export function storeTupleUpdateRound(source: UpdateRound) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.new_price);
    builder.writeNumber(source.round_number);
    return builder.build();
}

export function dictValueParserUpdateRound(): DictionaryValue<UpdateRound> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeUpdateRound(src)).endCell());
        },
        parse: (src) => {
            return loadUpdateRound(src.loadRef().beginParse());
        }
    }
}

export type SetUSDTAddress = {
    $$type: 'SetUSDTAddress';
    usdt_master: Address;
    usdt_wallet: Address;
}

export function storeSetUSDTAddress(src: SetUSDTAddress) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(2703444734, 32);
        b_0.storeAddress(src.usdt_master);
        b_0.storeAddress(src.usdt_wallet);
    };
}

export function loadSetUSDTAddress(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 2703444734) { throw Error('Invalid prefix'); }
    const _usdt_master = sc_0.loadAddress();
    const _usdt_wallet = sc_0.loadAddress();
    return { $$type: 'SetUSDTAddress' as const, usdt_master: _usdt_master, usdt_wallet: _usdt_wallet };
}

export function loadTupleSetUSDTAddress(source: TupleReader) {
    const _usdt_master = source.readAddress();
    const _usdt_wallet = source.readAddress();
    return { $$type: 'SetUSDTAddress' as const, usdt_master: _usdt_master, usdt_wallet: _usdt_wallet };
}

export function loadGetterTupleSetUSDTAddress(source: TupleReader) {
    const _usdt_master = source.readAddress();
    const _usdt_wallet = source.readAddress();
    return { $$type: 'SetUSDTAddress' as const, usdt_master: _usdt_master, usdt_wallet: _usdt_wallet };
}

export function storeTupleSetUSDTAddress(source: SetUSDTAddress) {
    const builder = new TupleBuilder();
    builder.writeAddress(source.usdt_master);
    builder.writeAddress(source.usdt_wallet);
    return builder.build();
}

export function dictValueParserSetUSDTAddress(): DictionaryValue<SetUSDTAddress> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeSetUSDTAddress(src)).endCell());
        },
        parse: (src) => {
            return loadSetUSDTAddress(src.loadRef().beginParse());
        }
    }
}

export type PurchaseWithSignature = {
    $$type: 'PurchaseWithSignature';
    calculation: PurchaseCalculation;
    signature: Slice;
}

export function storePurchaseWithSignature(src: PurchaseWithSignature) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(1524770820, 32);
        b_0.store(storePurchaseCalculation(src.calculation));
        b_0.storeRef(src.signature.asCell());
    };
}

export function loadPurchaseWithSignature(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 1524770820) { throw Error('Invalid prefix'); }
    const _calculation = loadPurchaseCalculation(sc_0);
    const _signature = sc_0.loadRef().asSlice();
    return { $$type: 'PurchaseWithSignature' as const, calculation: _calculation, signature: _signature };
}

export function loadTuplePurchaseWithSignature(source: TupleReader) {
    const _calculation = loadTuplePurchaseCalculation(source);
    const _signature = source.readCell().asSlice();
    return { $$type: 'PurchaseWithSignature' as const, calculation: _calculation, signature: _signature };
}

export function loadGetterTuplePurchaseWithSignature(source: TupleReader) {
    const _calculation = loadGetterTuplePurchaseCalculation(source);
    const _signature = source.readCell().asSlice();
    return { $$type: 'PurchaseWithSignature' as const, calculation: _calculation, signature: _signature };
}

export function storeTuplePurchaseWithSignature(source: PurchaseWithSignature) {
    const builder = new TupleBuilder();
    builder.writeTuple(storeTuplePurchaseCalculation(source.calculation));
    builder.writeSlice(source.signature.asCell());
    return builder.build();
}

export function dictValueParserPurchaseWithSignature(): DictionaryValue<PurchaseWithSignature> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storePurchaseWithSignature(src)).endCell());
        },
        parse: (src) => {
            return loadPurchaseWithSignature(src.loadRef().beginParse());
        }
    }
}

export type SetSigningKey = {
    $$type: 'SetSigningKey';
    public_key: bigint;
}

export function storeSetSigningKey(src: SetSigningKey) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(3315975678, 32);
        b_0.storeUint(src.public_key, 256);
    };
}

export function loadSetSigningKey(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 3315975678) { throw Error('Invalid prefix'); }
    const _public_key = sc_0.loadUintBig(256);
    return { $$type: 'SetSigningKey' as const, public_key: _public_key };
}

export function loadTupleSetSigningKey(source: TupleReader) {
    const _public_key = source.readBigNumber();
    return { $$type: 'SetSigningKey' as const, public_key: _public_key };
}

export function loadGetterTupleSetSigningKey(source: TupleReader) {
    const _public_key = source.readBigNumber();
    return { $$type: 'SetSigningKey' as const, public_key: _public_key };
}

export function storeTupleSetSigningKey(source: SetSigningKey) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.public_key);
    return builder.build();
}

export function dictValueParserSetSigningKey(): DictionaryValue<SetSigningKey> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeSetSigningKey(src)).endCell());
        },
        parse: (src) => {
            return loadSetSigningKey(src.loadRef().beginParse());
        }
    }
}

export type SetMinPurchase = {
    $$type: 'SetMinPurchase';
    min_purchase: bigint;
}

export function storeSetMinPurchase(src: SetMinPurchase) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(3390874056, 32);
        b_0.storeCoins(src.min_purchase);
    };
}

export function loadSetMinPurchase(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 3390874056) { throw Error('Invalid prefix'); }
    const _min_purchase = sc_0.loadCoins();
    return { $$type: 'SetMinPurchase' as const, min_purchase: _min_purchase };
}

export function loadTupleSetMinPurchase(source: TupleReader) {
    const _min_purchase = source.readBigNumber();
    return { $$type: 'SetMinPurchase' as const, min_purchase: _min_purchase };
}

export function loadGetterTupleSetMinPurchase(source: TupleReader) {
    const _min_purchase = source.readBigNumber();
    return { $$type: 'SetMinPurchase' as const, min_purchase: _min_purchase };
}

export function storeTupleSetMinPurchase(source: SetMinPurchase) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.min_purchase);
    return builder.build();
}

export function dictValueParserSetMinPurchase(): DictionaryValue<SetMinPurchase> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeSetMinPurchase(src)).endCell());
        },
        parse: (src) => {
            return loadSetMinPurchase(src.loadRef().beginParse());
        }
    }
}

export type WithdrawTON = {
    $$type: 'WithdrawTON';
    amount: bigint;
    destination: Address;
}

export function storeWithdrawTON(src: WithdrawTON) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(3520188881, 32);
        b_0.storeCoins(src.amount);
        b_0.storeAddress(src.destination);
    };
}

export function loadWithdrawTON(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 3520188881) { throw Error('Invalid prefix'); }
    const _amount = sc_0.loadCoins();
    const _destination = sc_0.loadAddress();
    return { $$type: 'WithdrawTON' as const, amount: _amount, destination: _destination };
}

export function loadTupleWithdrawTON(source: TupleReader) {
    const _amount = source.readBigNumber();
    const _destination = source.readAddress();
    return { $$type: 'WithdrawTON' as const, amount: _amount, destination: _destination };
}

export function loadGetterTupleWithdrawTON(source: TupleReader) {
    const _amount = source.readBigNumber();
    const _destination = source.readAddress();
    return { $$type: 'WithdrawTON' as const, amount: _amount, destination: _destination };
}

export function storeTupleWithdrawTON(source: WithdrawTON) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.amount);
    builder.writeAddress(source.destination);
    return builder.build();
}

export function dictValueParserWithdrawTON(): DictionaryValue<WithdrawTON> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeWithdrawTON(src)).endCell());
        },
        parse: (src) => {
            return loadWithdrawTON(src.loadRef().beginParse());
        }
    }
}

export type WithdrawUSDT = {
    $$type: 'WithdrawUSDT';
    amount: bigint;
    destination: Address;
}

export function storeWithdrawUSDT(src: WithdrawUSDT) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(3537031890, 32);
        b_0.storeCoins(src.amount);
        b_0.storeAddress(src.destination);
    };
}

export function loadWithdrawUSDT(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 3537031890) { throw Error('Invalid prefix'); }
    const _amount = sc_0.loadCoins();
    const _destination = sc_0.loadAddress();
    return { $$type: 'WithdrawUSDT' as const, amount: _amount, destination: _destination };
}

export function loadTupleWithdrawUSDT(source: TupleReader) {
    const _amount = source.readBigNumber();
    const _destination = source.readAddress();
    return { $$type: 'WithdrawUSDT' as const, amount: _amount, destination: _destination };
}

export function loadGetterTupleWithdrawUSDT(source: TupleReader) {
    const _amount = source.readBigNumber();
    const _destination = source.readAddress();
    return { $$type: 'WithdrawUSDT' as const, amount: _amount, destination: _destination };
}

export function storeTupleWithdrawUSDT(source: WithdrawUSDT) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.amount);
    builder.writeAddress(source.destination);
    return builder.build();
}

export function dictValueParserWithdrawUSDT(): DictionaryValue<WithdrawUSDT> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeWithdrawUSDT(src)).endCell());
        },
        parse: (src) => {
            return loadWithdrawUSDT(src.loadRef().beginParse());
        }
    }
}

export type SetTreasury = {
    $$type: 'SetTreasury';
    treasury_address: Address;
}

export function storeSetTreasury(src: SetTreasury) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(3553874899, 32);
        b_0.storeAddress(src.treasury_address);
    };
}

export function loadSetTreasury(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 3553874899) { throw Error('Invalid prefix'); }
    const _treasury_address = sc_0.loadAddress();
    return { $$type: 'SetTreasury' as const, treasury_address: _treasury_address };
}

export function loadTupleSetTreasury(source: TupleReader) {
    const _treasury_address = source.readAddress();
    return { $$type: 'SetTreasury' as const, treasury_address: _treasury_address };
}

export function loadGetterTupleSetTreasury(source: TupleReader) {
    const _treasury_address = source.readAddress();
    return { $$type: 'SetTreasury' as const, treasury_address: _treasury_address };
}

export function storeTupleSetTreasury(source: SetTreasury) {
    const builder = new TupleBuilder();
    builder.writeAddress(source.treasury_address);
    return builder.build();
}

export function dictValueParserSetTreasury(): DictionaryValue<SetTreasury> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeSetTreasury(src)).endCell());
        },
        parse: (src) => {
            return loadSetTreasury(src.loadRef().beginParse());
        }
    }
}

export type AuctionStarted = {
    $$type: 'AuctionStarted';
    start_time: bigint;
    end_time: bigint;
    soft_cap: bigint;
    hard_cap: bigint;
    initial_price: bigint;
    total_supply: bigint;
}

export function storeAuctionStarted(src: AuctionStarted) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(2217068725, 32);
        b_0.storeUint(src.start_time, 64);
        b_0.storeUint(src.end_time, 64);
        b_0.storeCoins(src.soft_cap);
        b_0.storeCoins(src.hard_cap);
        b_0.storeCoins(src.initial_price);
        b_0.storeCoins(src.total_supply);
    };
}

export function loadAuctionStarted(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 2217068725) { throw Error('Invalid prefix'); }
    const _start_time = sc_0.loadUintBig(64);
    const _end_time = sc_0.loadUintBig(64);
    const _soft_cap = sc_0.loadCoins();
    const _hard_cap = sc_0.loadCoins();
    const _initial_price = sc_0.loadCoins();
    const _total_supply = sc_0.loadCoins();
    return { $$type: 'AuctionStarted' as const, start_time: _start_time, end_time: _end_time, soft_cap: _soft_cap, hard_cap: _hard_cap, initial_price: _initial_price, total_supply: _total_supply };
}

export function loadTupleAuctionStarted(source: TupleReader) {
    const _start_time = source.readBigNumber();
    const _end_time = source.readBigNumber();
    const _soft_cap = source.readBigNumber();
    const _hard_cap = source.readBigNumber();
    const _initial_price = source.readBigNumber();
    const _total_supply = source.readBigNumber();
    return { $$type: 'AuctionStarted' as const, start_time: _start_time, end_time: _end_time, soft_cap: _soft_cap, hard_cap: _hard_cap, initial_price: _initial_price, total_supply: _total_supply };
}

export function loadGetterTupleAuctionStarted(source: TupleReader) {
    const _start_time = source.readBigNumber();
    const _end_time = source.readBigNumber();
    const _soft_cap = source.readBigNumber();
    const _hard_cap = source.readBigNumber();
    const _initial_price = source.readBigNumber();
    const _total_supply = source.readBigNumber();
    return { $$type: 'AuctionStarted' as const, start_time: _start_time, end_time: _end_time, soft_cap: _soft_cap, hard_cap: _hard_cap, initial_price: _initial_price, total_supply: _total_supply };
}

export function storeTupleAuctionStarted(source: AuctionStarted) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.start_time);
    builder.writeNumber(source.end_time);
    builder.writeNumber(source.soft_cap);
    builder.writeNumber(source.hard_cap);
    builder.writeNumber(source.initial_price);
    builder.writeNumber(source.total_supply);
    return builder.build();
}

export function dictValueParserAuctionStarted(): DictionaryValue<AuctionStarted> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeAuctionStarted(src)).endCell());
        },
        parse: (src) => {
            return loadAuctionStarted(src.loadRef().beginParse());
        }
    }
}

export type PurchaseCompleted = {
    $$type: 'PurchaseCompleted';
    user: Address;
    amount: bigint;
    tokens_received: bigint;
    currency: bigint;
    purchase_method: bigint;
    round_number: bigint;
    nonce: bigint;
    total_raised: bigint;
    total_raised_usdt: bigint;
    total_tokens_sold: bigint;
}

export function storePurchaseCompleted(src: PurchaseCompleted) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(3975568033, 32);
        b_0.storeAddress(src.user);
        b_0.storeCoins(src.amount);
        b_0.storeCoins(src.tokens_received);
        b_0.storeUint(src.currency, 8);
        b_0.storeUint(src.purchase_method, 8);
        b_0.storeUint(src.round_number, 32);
        b_0.storeUint(src.nonce, 64);
        b_0.storeCoins(src.total_raised);
        b_0.storeCoins(src.total_raised_usdt);
        const b_1 = new Builder();
        b_1.storeCoins(src.total_tokens_sold);
        b_0.storeRef(b_1.endCell());
    };
}

export function loadPurchaseCompleted(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 3975568033) { throw Error('Invalid prefix'); }
    const _user = sc_0.loadAddress();
    const _amount = sc_0.loadCoins();
    const _tokens_received = sc_0.loadCoins();
    const _currency = sc_0.loadUintBig(8);
    const _purchase_method = sc_0.loadUintBig(8);
    const _round_number = sc_0.loadUintBig(32);
    const _nonce = sc_0.loadUintBig(64);
    const _total_raised = sc_0.loadCoins();
    const _total_raised_usdt = sc_0.loadCoins();
    const sc_1 = sc_0.loadRef().beginParse();
    const _total_tokens_sold = sc_1.loadCoins();
    return { $$type: 'PurchaseCompleted' as const, user: _user, amount: _amount, tokens_received: _tokens_received, currency: _currency, purchase_method: _purchase_method, round_number: _round_number, nonce: _nonce, total_raised: _total_raised, total_raised_usdt: _total_raised_usdt, total_tokens_sold: _total_tokens_sold };
}

export function loadTuplePurchaseCompleted(source: TupleReader) {
    const _user = source.readAddress();
    const _amount = source.readBigNumber();
    const _tokens_received = source.readBigNumber();
    const _currency = source.readBigNumber();
    const _purchase_method = source.readBigNumber();
    const _round_number = source.readBigNumber();
    const _nonce = source.readBigNumber();
    const _total_raised = source.readBigNumber();
    const _total_raised_usdt = source.readBigNumber();
    const _total_tokens_sold = source.readBigNumber();
    return { $$type: 'PurchaseCompleted' as const, user: _user, amount: _amount, tokens_received: _tokens_received, currency: _currency, purchase_method: _purchase_method, round_number: _round_number, nonce: _nonce, total_raised: _total_raised, total_raised_usdt: _total_raised_usdt, total_tokens_sold: _total_tokens_sold };
}

export function loadGetterTuplePurchaseCompleted(source: TupleReader) {
    const _user = source.readAddress();
    const _amount = source.readBigNumber();
    const _tokens_received = source.readBigNumber();
    const _currency = source.readBigNumber();
    const _purchase_method = source.readBigNumber();
    const _round_number = source.readBigNumber();
    const _nonce = source.readBigNumber();
    const _total_raised = source.readBigNumber();
    const _total_raised_usdt = source.readBigNumber();
    const _total_tokens_sold = source.readBigNumber();
    return { $$type: 'PurchaseCompleted' as const, user: _user, amount: _amount, tokens_received: _tokens_received, currency: _currency, purchase_method: _purchase_method, round_number: _round_number, nonce: _nonce, total_raised: _total_raised, total_raised_usdt: _total_raised_usdt, total_tokens_sold: _total_tokens_sold };
}

export function storeTuplePurchaseCompleted(source: PurchaseCompleted) {
    const builder = new TupleBuilder();
    builder.writeAddress(source.user);
    builder.writeNumber(source.amount);
    builder.writeNumber(source.tokens_received);
    builder.writeNumber(source.currency);
    builder.writeNumber(source.purchase_method);
    builder.writeNumber(source.round_number);
    builder.writeNumber(source.nonce);
    builder.writeNumber(source.total_raised);
    builder.writeNumber(source.total_raised_usdt);
    builder.writeNumber(source.total_tokens_sold);
    return builder.build();
}

export function dictValueParserPurchaseCompleted(): DictionaryValue<PurchaseCompleted> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storePurchaseCompleted(src)).endCell());
        },
        parse: (src) => {
            return loadPurchaseCompleted(src.loadRef().beginParse());
        }
    }
}

export type USDTConfigured = {
    $$type: 'USDTConfigured';
    usdt_master: Address;
    usdt_wallet: Address;
    configured_by: Address;
}

export function storeUSDTConfigured(src: USDTConfigured) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(1031232428, 32);
        b_0.storeAddress(src.usdt_master);
        b_0.storeAddress(src.usdt_wallet);
        b_0.storeAddress(src.configured_by);
    };
}

export function loadUSDTConfigured(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 1031232428) { throw Error('Invalid prefix'); }
    const _usdt_master = sc_0.loadAddress();
    const _usdt_wallet = sc_0.loadAddress();
    const _configured_by = sc_0.loadAddress();
    return { $$type: 'USDTConfigured' as const, usdt_master: _usdt_master, usdt_wallet: _usdt_wallet, configured_by: _configured_by };
}

export function loadTupleUSDTConfigured(source: TupleReader) {
    const _usdt_master = source.readAddress();
    const _usdt_wallet = source.readAddress();
    const _configured_by = source.readAddress();
    return { $$type: 'USDTConfigured' as const, usdt_master: _usdt_master, usdt_wallet: _usdt_wallet, configured_by: _configured_by };
}

export function loadGetterTupleUSDTConfigured(source: TupleReader) {
    const _usdt_master = source.readAddress();
    const _usdt_wallet = source.readAddress();
    const _configured_by = source.readAddress();
    return { $$type: 'USDTConfigured' as const, usdt_master: _usdt_master, usdt_wallet: _usdt_wallet, configured_by: _configured_by };
}

export function storeTupleUSDTConfigured(source: USDTConfigured) {
    const builder = new TupleBuilder();
    builder.writeAddress(source.usdt_master);
    builder.writeAddress(source.usdt_wallet);
    builder.writeAddress(source.configured_by);
    return builder.build();
}

export function dictValueParserUSDTConfigured(): DictionaryValue<USDTConfigured> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeUSDTConfigured(src)).endCell());
        },
        parse: (src) => {
            return loadUSDTConfigured(src.loadRef().beginParse());
        }
    }
}

export type SigningKeySet = {
    $$type: 'SigningKeySet';
    public_key: bigint;
    set_by: Address;
}

export function storeSigningKeySet(src: SigningKeySet) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(2441211440, 32);
        b_0.storeUint(src.public_key, 256);
        b_0.storeAddress(src.set_by);
    };
}

export function loadSigningKeySet(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 2441211440) { throw Error('Invalid prefix'); }
    const _public_key = sc_0.loadUintBig(256);
    const _set_by = sc_0.loadAddress();
    return { $$type: 'SigningKeySet' as const, public_key: _public_key, set_by: _set_by };
}

export function loadTupleSigningKeySet(source: TupleReader) {
    const _public_key = source.readBigNumber();
    const _set_by = source.readAddress();
    return { $$type: 'SigningKeySet' as const, public_key: _public_key, set_by: _set_by };
}

export function loadGetterTupleSigningKeySet(source: TupleReader) {
    const _public_key = source.readBigNumber();
    const _set_by = source.readAddress();
    return { $$type: 'SigningKeySet' as const, public_key: _public_key, set_by: _set_by };
}

export function storeTupleSigningKeySet(source: SigningKeySet) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.public_key);
    builder.writeAddress(source.set_by);
    return builder.build();
}

export function dictValueParserSigningKeySet(): DictionaryValue<SigningKeySet> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeSigningKeySet(src)).endCell());
        },
        parse: (src) => {
            return loadSigningKeySet(src.loadRef().beginParse());
        }
    }
}

export type MinPurchaseUpdated = {
    $$type: 'MinPurchaseUpdated';
    old_min_purchase: bigint;
    new_min_purchase: bigint;
    updated_by: Address;
}

export function storeMinPurchaseUpdated(src: MinPurchaseUpdated) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(760782792, 32);
        b_0.storeCoins(src.old_min_purchase);
        b_0.storeCoins(src.new_min_purchase);
        b_0.storeAddress(src.updated_by);
    };
}

export function loadMinPurchaseUpdated(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 760782792) { throw Error('Invalid prefix'); }
    const _old_min_purchase = sc_0.loadCoins();
    const _new_min_purchase = sc_0.loadCoins();
    const _updated_by = sc_0.loadAddress();
    return { $$type: 'MinPurchaseUpdated' as const, old_min_purchase: _old_min_purchase, new_min_purchase: _new_min_purchase, updated_by: _updated_by };
}

export function loadTupleMinPurchaseUpdated(source: TupleReader) {
    const _old_min_purchase = source.readBigNumber();
    const _new_min_purchase = source.readBigNumber();
    const _updated_by = source.readAddress();
    return { $$type: 'MinPurchaseUpdated' as const, old_min_purchase: _old_min_purchase, new_min_purchase: _new_min_purchase, updated_by: _updated_by };
}

export function loadGetterTupleMinPurchaseUpdated(source: TupleReader) {
    const _old_min_purchase = source.readBigNumber();
    const _new_min_purchase = source.readBigNumber();
    const _updated_by = source.readAddress();
    return { $$type: 'MinPurchaseUpdated' as const, old_min_purchase: _old_min_purchase, new_min_purchase: _new_min_purchase, updated_by: _updated_by };
}

export function storeTupleMinPurchaseUpdated(source: MinPurchaseUpdated) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.old_min_purchase);
    builder.writeNumber(source.new_min_purchase);
    builder.writeAddress(source.updated_by);
    return builder.build();
}

export function dictValueParserMinPurchaseUpdated(): DictionaryValue<MinPurchaseUpdated> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeMinPurchaseUpdated(src)).endCell());
        },
        parse: (src) => {
            return loadMinPurchaseUpdated(src.loadRef().beginParse());
        }
    }
}

export type TreasurySet = {
    $$type: 'TreasurySet';
    old_treasury: Address;
    new_treasury: Address;
    set_by: Address;
}

export function storeTreasurySet(src: TreasurySet) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(4013598566, 32);
        b_0.storeAddress(src.old_treasury);
        b_0.storeAddress(src.new_treasury);
        b_0.storeAddress(src.set_by);
    };
}

export function loadTreasurySet(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 4013598566) { throw Error('Invalid prefix'); }
    const _old_treasury = sc_0.loadAddress();
    const _new_treasury = sc_0.loadAddress();
    const _set_by = sc_0.loadAddress();
    return { $$type: 'TreasurySet' as const, old_treasury: _old_treasury, new_treasury: _new_treasury, set_by: _set_by };
}

export function loadTupleTreasurySet(source: TupleReader) {
    const _old_treasury = source.readAddress();
    const _new_treasury = source.readAddress();
    const _set_by = source.readAddress();
    return { $$type: 'TreasurySet' as const, old_treasury: _old_treasury, new_treasury: _new_treasury, set_by: _set_by };
}

export function loadGetterTupleTreasurySet(source: TupleReader) {
    const _old_treasury = source.readAddress();
    const _new_treasury = source.readAddress();
    const _set_by = source.readAddress();
    return { $$type: 'TreasurySet' as const, old_treasury: _old_treasury, new_treasury: _new_treasury, set_by: _set_by };
}

export function storeTupleTreasurySet(source: TreasurySet) {
    const builder = new TupleBuilder();
    builder.writeAddress(source.old_treasury);
    builder.writeAddress(source.new_treasury);
    builder.writeAddress(source.set_by);
    return builder.build();
}

export function dictValueParserTreasurySet(): DictionaryValue<TreasurySet> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeTreasurySet(src)).endCell());
        },
        parse: (src) => {
            return loadTreasurySet(src.loadRef().beginParse());
        }
    }
}

export type FundsWithdrawn = {
    $$type: 'FundsWithdrawn';
    currency: bigint;
    amount: bigint;
    destination: Address;
    withdrawn_by: Address;
    remaining_balance: bigint;
}

export function storeFundsWithdrawn(src: FundsWithdrawn) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(3674912463, 32);
        b_0.storeUint(src.currency, 8);
        b_0.storeCoins(src.amount);
        b_0.storeAddress(src.destination);
        b_0.storeAddress(src.withdrawn_by);
        b_0.storeCoins(src.remaining_balance);
    };
}

export function loadFundsWithdrawn(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 3674912463) { throw Error('Invalid prefix'); }
    const _currency = sc_0.loadUintBig(8);
    const _amount = sc_0.loadCoins();
    const _destination = sc_0.loadAddress();
    const _withdrawn_by = sc_0.loadAddress();
    const _remaining_balance = sc_0.loadCoins();
    return { $$type: 'FundsWithdrawn' as const, currency: _currency, amount: _amount, destination: _destination, withdrawn_by: _withdrawn_by, remaining_balance: _remaining_balance };
}

export function loadTupleFundsWithdrawn(source: TupleReader) {
    const _currency = source.readBigNumber();
    const _amount = source.readBigNumber();
    const _destination = source.readAddress();
    const _withdrawn_by = source.readAddress();
    const _remaining_balance = source.readBigNumber();
    return { $$type: 'FundsWithdrawn' as const, currency: _currency, amount: _amount, destination: _destination, withdrawn_by: _withdrawn_by, remaining_balance: _remaining_balance };
}

export function loadGetterTupleFundsWithdrawn(source: TupleReader) {
    const _currency = source.readBigNumber();
    const _amount = source.readBigNumber();
    const _destination = source.readAddress();
    const _withdrawn_by = source.readAddress();
    const _remaining_balance = source.readBigNumber();
    return { $$type: 'FundsWithdrawn' as const, currency: _currency, amount: _amount, destination: _destination, withdrawn_by: _withdrawn_by, remaining_balance: _remaining_balance };
}

export function storeTupleFundsWithdrawn(source: FundsWithdrawn) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.currency);
    builder.writeNumber(source.amount);
    builder.writeAddress(source.destination);
    builder.writeAddress(source.withdrawn_by);
    builder.writeNumber(source.remaining_balance);
    return builder.build();
}

export function dictValueParserFundsWithdrawn(): DictionaryValue<FundsWithdrawn> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeFundsWithdrawn(src)).endCell());
        },
        parse: (src) => {
            return loadFundsWithdrawn(src.loadRef().beginParse());
        }
    }
}

export type RoundUpdated = {
    $$type: 'RoundUpdated';
    old_round: bigint;
    new_round: bigint;
    old_price: bigint;
    new_price: bigint;
    updated_by: Address;
}

export function storeRoundUpdated(src: RoundUpdated) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(190065307, 32);
        b_0.storeUint(src.old_round, 32);
        b_0.storeUint(src.new_round, 32);
        b_0.storeCoins(src.old_price);
        b_0.storeCoins(src.new_price);
        b_0.storeAddress(src.updated_by);
    };
}

export function loadRoundUpdated(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 190065307) { throw Error('Invalid prefix'); }
    const _old_round = sc_0.loadUintBig(32);
    const _new_round = sc_0.loadUintBig(32);
    const _old_price = sc_0.loadCoins();
    const _new_price = sc_0.loadCoins();
    const _updated_by = sc_0.loadAddress();
    return { $$type: 'RoundUpdated' as const, old_round: _old_round, new_round: _new_round, old_price: _old_price, new_price: _new_price, updated_by: _updated_by };
}

export function loadTupleRoundUpdated(source: TupleReader) {
    const _old_round = source.readBigNumber();
    const _new_round = source.readBigNumber();
    const _old_price = source.readBigNumber();
    const _new_price = source.readBigNumber();
    const _updated_by = source.readAddress();
    return { $$type: 'RoundUpdated' as const, old_round: _old_round, new_round: _new_round, old_price: _old_price, new_price: _new_price, updated_by: _updated_by };
}

export function loadGetterTupleRoundUpdated(source: TupleReader) {
    const _old_round = source.readBigNumber();
    const _new_round = source.readBigNumber();
    const _old_price = source.readBigNumber();
    const _new_price = source.readBigNumber();
    const _updated_by = source.readAddress();
    return { $$type: 'RoundUpdated' as const, old_round: _old_round, new_round: _new_round, old_price: _old_price, new_price: _new_price, updated_by: _updated_by };
}

export function storeTupleRoundUpdated(source: RoundUpdated) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.old_round);
    builder.writeNumber(source.new_round);
    builder.writeNumber(source.old_price);
    builder.writeNumber(source.new_price);
    builder.writeAddress(source.updated_by);
    return builder.build();
}

export function dictValueParserRoundUpdated(): DictionaryValue<RoundUpdated> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeRoundUpdated(src)).endCell());
        },
        parse: (src) => {
            return loadRoundUpdated(src.loadRef().beginParse());
        }
    }
}

export type AuctionEnded = {
    $$type: 'AuctionEnded';
    end_reason: bigint;
    final_status: bigint;
    total_raised: bigint;
    total_raised_usdt: bigint;
    total_tokens_sold: bigint;
    end_time: bigint;
}

export function storeAuctionEnded(src: AuctionEnded) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(397814633, 32);
        b_0.storeUint(src.end_reason, 8);
        b_0.storeUint(src.final_status, 8);
        b_0.storeCoins(src.total_raised);
        b_0.storeCoins(src.total_raised_usdt);
        b_0.storeCoins(src.total_tokens_sold);
        b_0.storeUint(src.end_time, 64);
    };
}

export function loadAuctionEnded(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 397814633) { throw Error('Invalid prefix'); }
    const _end_reason = sc_0.loadUintBig(8);
    const _final_status = sc_0.loadUintBig(8);
    const _total_raised = sc_0.loadCoins();
    const _total_raised_usdt = sc_0.loadCoins();
    const _total_tokens_sold = sc_0.loadCoins();
    const _end_time = sc_0.loadUintBig(64);
    return { $$type: 'AuctionEnded' as const, end_reason: _end_reason, final_status: _final_status, total_raised: _total_raised, total_raised_usdt: _total_raised_usdt, total_tokens_sold: _total_tokens_sold, end_time: _end_time };
}

export function loadTupleAuctionEnded(source: TupleReader) {
    const _end_reason = source.readBigNumber();
    const _final_status = source.readBigNumber();
    const _total_raised = source.readBigNumber();
    const _total_raised_usdt = source.readBigNumber();
    const _total_tokens_sold = source.readBigNumber();
    const _end_time = source.readBigNumber();
    return { $$type: 'AuctionEnded' as const, end_reason: _end_reason, final_status: _final_status, total_raised: _total_raised, total_raised_usdt: _total_raised_usdt, total_tokens_sold: _total_tokens_sold, end_time: _end_time };
}

export function loadGetterTupleAuctionEnded(source: TupleReader) {
    const _end_reason = source.readBigNumber();
    const _final_status = source.readBigNumber();
    const _total_raised = source.readBigNumber();
    const _total_raised_usdt = source.readBigNumber();
    const _total_tokens_sold = source.readBigNumber();
    const _end_time = source.readBigNumber();
    return { $$type: 'AuctionEnded' as const, end_reason: _end_reason, final_status: _final_status, total_raised: _total_raised, total_raised_usdt: _total_raised_usdt, total_tokens_sold: _total_tokens_sold, end_time: _end_time };
}

export function storeTupleAuctionEnded(source: AuctionEnded) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.end_reason);
    builder.writeNumber(source.final_status);
    builder.writeNumber(source.total_raised);
    builder.writeNumber(source.total_raised_usdt);
    builder.writeNumber(source.total_tokens_sold);
    builder.writeNumber(source.end_time);
    return builder.build();
}

export function dictValueParserAuctionEnded(): DictionaryValue<AuctionEnded> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeAuctionEnded(src)).endCell());
        },
        parse: (src) => {
            return loadAuctionEnded(src.loadRef().beginParse());
        }
    }
}

export type AuctionConfig = {
    $$type: 'AuctionConfig';
    start_time: bigint;
    end_time: bigint;
    soft_cap: bigint;
    hard_cap: bigint;
    total_supply: bigint;
    refund_fee_percent: bigint;
}

export function storeAuctionConfig(src: AuctionConfig) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(src.start_time, 64);
        b_0.storeUint(src.end_time, 64);
        b_0.storeCoins(src.soft_cap);
        b_0.storeCoins(src.hard_cap);
        b_0.storeCoins(src.total_supply);
        b_0.storeUint(src.refund_fee_percent, 8);
    };
}

export function loadAuctionConfig(slice: Slice) {
    const sc_0 = slice;
    const _start_time = sc_0.loadUintBig(64);
    const _end_time = sc_0.loadUintBig(64);
    const _soft_cap = sc_0.loadCoins();
    const _hard_cap = sc_0.loadCoins();
    const _total_supply = sc_0.loadCoins();
    const _refund_fee_percent = sc_0.loadUintBig(8);
    return { $$type: 'AuctionConfig' as const, start_time: _start_time, end_time: _end_time, soft_cap: _soft_cap, hard_cap: _hard_cap, total_supply: _total_supply, refund_fee_percent: _refund_fee_percent };
}

export function loadTupleAuctionConfig(source: TupleReader) {
    const _start_time = source.readBigNumber();
    const _end_time = source.readBigNumber();
    const _soft_cap = source.readBigNumber();
    const _hard_cap = source.readBigNumber();
    const _total_supply = source.readBigNumber();
    const _refund_fee_percent = source.readBigNumber();
    return { $$type: 'AuctionConfig' as const, start_time: _start_time, end_time: _end_time, soft_cap: _soft_cap, hard_cap: _hard_cap, total_supply: _total_supply, refund_fee_percent: _refund_fee_percent };
}

export function loadGetterTupleAuctionConfig(source: TupleReader) {
    const _start_time = source.readBigNumber();
    const _end_time = source.readBigNumber();
    const _soft_cap = source.readBigNumber();
    const _hard_cap = source.readBigNumber();
    const _total_supply = source.readBigNumber();
    const _refund_fee_percent = source.readBigNumber();
    return { $$type: 'AuctionConfig' as const, start_time: _start_time, end_time: _end_time, soft_cap: _soft_cap, hard_cap: _hard_cap, total_supply: _total_supply, refund_fee_percent: _refund_fee_percent };
}

export function storeTupleAuctionConfig(source: AuctionConfig) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.start_time);
    builder.writeNumber(source.end_time);
    builder.writeNumber(source.soft_cap);
    builder.writeNumber(source.hard_cap);
    builder.writeNumber(source.total_supply);
    builder.writeNumber(source.refund_fee_percent);
    return builder.build();
}

export function dictValueParserAuctionConfig(): DictionaryValue<AuctionConfig> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeAuctionConfig(src)).endCell());
        },
        parse: (src) => {
            return loadAuctionConfig(src.loadRef().beginParse());
        }
    }
}

export type RoundStats = {
    $$type: 'RoundStats';
    round_number: bigint;
    start_time: bigint;
    end_time: bigint;
    price: bigint;
    total_raised_ton: bigint;
    total_raised_usdt: bigint;
    raised_usdt_equivalent: bigint;
    tokens_sold: bigint;
    purchase_count: bigint;
    unique_users: bigint;
    refund_count: bigint;
    refunded_amount_ton: bigint;
    refunded_amount_usdt: bigint;
    refunded_usdt_equivalent: bigint;
}

export function storeRoundStats(src: RoundStats) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(src.round_number, 32);
        b_0.storeUint(src.start_time, 64);
        b_0.storeUint(src.end_time, 64);
        b_0.storeCoins(src.price);
        b_0.storeCoins(src.total_raised_ton);
        b_0.storeCoins(src.total_raised_usdt);
        b_0.storeCoins(src.raised_usdt_equivalent);
        b_0.storeCoins(src.tokens_sold);
        b_0.storeUint(src.purchase_count, 32);
        b_0.storeUint(src.unique_users, 32);
        b_0.storeUint(src.refund_count, 32);
        b_0.storeCoins(src.refunded_amount_ton);
        const b_1 = new Builder();
        b_1.storeCoins(src.refunded_amount_usdt);
        b_1.storeCoins(src.refunded_usdt_equivalent);
        b_0.storeRef(b_1.endCell());
    };
}

export function loadRoundStats(slice: Slice) {
    const sc_0 = slice;
    const _round_number = sc_0.loadUintBig(32);
    const _start_time = sc_0.loadUintBig(64);
    const _end_time = sc_0.loadUintBig(64);
    const _price = sc_0.loadCoins();
    const _total_raised_ton = sc_0.loadCoins();
    const _total_raised_usdt = sc_0.loadCoins();
    const _raised_usdt_equivalent = sc_0.loadCoins();
    const _tokens_sold = sc_0.loadCoins();
    const _purchase_count = sc_0.loadUintBig(32);
    const _unique_users = sc_0.loadUintBig(32);
    const _refund_count = sc_0.loadUintBig(32);
    const _refunded_amount_ton = sc_0.loadCoins();
    const sc_1 = sc_0.loadRef().beginParse();
    const _refunded_amount_usdt = sc_1.loadCoins();
    const _refunded_usdt_equivalent = sc_1.loadCoins();
    return { $$type: 'RoundStats' as const, round_number: _round_number, start_time: _start_time, end_time: _end_time, price: _price, total_raised_ton: _total_raised_ton, total_raised_usdt: _total_raised_usdt, raised_usdt_equivalent: _raised_usdt_equivalent, tokens_sold: _tokens_sold, purchase_count: _purchase_count, unique_users: _unique_users, refund_count: _refund_count, refunded_amount_ton: _refunded_amount_ton, refunded_amount_usdt: _refunded_amount_usdt, refunded_usdt_equivalent: _refunded_usdt_equivalent };
}

export function loadTupleRoundStats(source: TupleReader) {
    const _round_number = source.readBigNumber();
    const _start_time = source.readBigNumber();
    const _end_time = source.readBigNumber();
    const _price = source.readBigNumber();
    const _total_raised_ton = source.readBigNumber();
    const _total_raised_usdt = source.readBigNumber();
    const _raised_usdt_equivalent = source.readBigNumber();
    const _tokens_sold = source.readBigNumber();
    const _purchase_count = source.readBigNumber();
    const _unique_users = source.readBigNumber();
    const _refund_count = source.readBigNumber();
    const _refunded_amount_ton = source.readBigNumber();
    const _refunded_amount_usdt = source.readBigNumber();
    const _refunded_usdt_equivalent = source.readBigNumber();
    return { $$type: 'RoundStats' as const, round_number: _round_number, start_time: _start_time, end_time: _end_time, price: _price, total_raised_ton: _total_raised_ton, total_raised_usdt: _total_raised_usdt, raised_usdt_equivalent: _raised_usdt_equivalent, tokens_sold: _tokens_sold, purchase_count: _purchase_count, unique_users: _unique_users, refund_count: _refund_count, refunded_amount_ton: _refunded_amount_ton, refunded_amount_usdt: _refunded_amount_usdt, refunded_usdt_equivalent: _refunded_usdt_equivalent };
}

export function loadGetterTupleRoundStats(source: TupleReader) {
    const _round_number = source.readBigNumber();
    const _start_time = source.readBigNumber();
    const _end_time = source.readBigNumber();
    const _price = source.readBigNumber();
    const _total_raised_ton = source.readBigNumber();
    const _total_raised_usdt = source.readBigNumber();
    const _raised_usdt_equivalent = source.readBigNumber();
    const _tokens_sold = source.readBigNumber();
    const _purchase_count = source.readBigNumber();
    const _unique_users = source.readBigNumber();
    const _refund_count = source.readBigNumber();
    const _refunded_amount_ton = source.readBigNumber();
    const _refunded_amount_usdt = source.readBigNumber();
    const _refunded_usdt_equivalent = source.readBigNumber();
    return { $$type: 'RoundStats' as const, round_number: _round_number, start_time: _start_time, end_time: _end_time, price: _price, total_raised_ton: _total_raised_ton, total_raised_usdt: _total_raised_usdt, raised_usdt_equivalent: _raised_usdt_equivalent, tokens_sold: _tokens_sold, purchase_count: _purchase_count, unique_users: _unique_users, refund_count: _refund_count, refunded_amount_ton: _refunded_amount_ton, refunded_amount_usdt: _refunded_amount_usdt, refunded_usdt_equivalent: _refunded_usdt_equivalent };
}

export function storeTupleRoundStats(source: RoundStats) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.round_number);
    builder.writeNumber(source.start_time);
    builder.writeNumber(source.end_time);
    builder.writeNumber(source.price);
    builder.writeNumber(source.total_raised_ton);
    builder.writeNumber(source.total_raised_usdt);
    builder.writeNumber(source.raised_usdt_equivalent);
    builder.writeNumber(source.tokens_sold);
    builder.writeNumber(source.purchase_count);
    builder.writeNumber(source.unique_users);
    builder.writeNumber(source.refund_count);
    builder.writeNumber(source.refunded_amount_ton);
    builder.writeNumber(source.refunded_amount_usdt);
    builder.writeNumber(source.refunded_usdt_equivalent);
    return builder.build();
}

export function dictValueParserRoundStats(): DictionaryValue<RoundStats> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeRoundStats(src)).endCell());
        },
        parse: (src) => {
            return loadRoundStats(src.loadRef().beginParse());
        }
    }
}

export type USDTConfig = {
    $$type: 'USDTConfig';
    master_address: Address;
    wallet_address: Address | null;
    decimals: bigint;
}

export function storeUSDTConfig(src: USDTConfig) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeAddress(src.master_address);
        b_0.storeAddress(src.wallet_address);
        b_0.storeUint(src.decimals, 8);
    };
}

export function loadUSDTConfig(slice: Slice) {
    const sc_0 = slice;
    const _master_address = sc_0.loadAddress();
    const _wallet_address = sc_0.loadMaybeAddress();
    const _decimals = sc_0.loadUintBig(8);
    return { $$type: 'USDTConfig' as const, master_address: _master_address, wallet_address: _wallet_address, decimals: _decimals };
}

export function loadTupleUSDTConfig(source: TupleReader) {
    const _master_address = source.readAddress();
    const _wallet_address = source.readAddressOpt();
    const _decimals = source.readBigNumber();
    return { $$type: 'USDTConfig' as const, master_address: _master_address, wallet_address: _wallet_address, decimals: _decimals };
}

export function loadGetterTupleUSDTConfig(source: TupleReader) {
    const _master_address = source.readAddress();
    const _wallet_address = source.readAddressOpt();
    const _decimals = source.readBigNumber();
    return { $$type: 'USDTConfig' as const, master_address: _master_address, wallet_address: _wallet_address, decimals: _decimals };
}

export function storeTupleUSDTConfig(source: USDTConfig) {
    const builder = new TupleBuilder();
    builder.writeAddress(source.master_address);
    builder.writeAddress(source.wallet_address);
    builder.writeNumber(source.decimals);
    return builder.build();
}

export function dictValueParserUSDTConfig(): DictionaryValue<USDTConfig> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeUSDTConfig(src)).endCell());
        },
        parse: (src) => {
            return loadUSDTConfig(src.loadRef().beginParse());
        }
    }
}

export type PurchaseCalculation = {
    $$type: 'PurchaseCalculation';
    user: Address;
    amount: bigint;
    currency: bigint;
    tokens_to_receive: bigint;
    current_price: bigint;
    current_round: bigint;
    timestamp: bigint;
    nonce: bigint;
    usdt_equivalent_amount: bigint;
}

export function storePurchaseCalculation(src: PurchaseCalculation) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeAddress(src.user);
        b_0.storeCoins(src.amount);
        b_0.storeUint(src.currency, 8);
        b_0.storeCoins(src.tokens_to_receive);
        b_0.storeCoins(src.current_price);
        b_0.storeUint(src.current_round, 32);
        b_0.storeUint(src.timestamp, 64);
        b_0.storeUint(src.nonce, 64);
        b_0.storeCoins(src.usdt_equivalent_amount);
    };
}

export function loadPurchaseCalculation(slice: Slice) {
    const sc_0 = slice;
    const _user = sc_0.loadAddress();
    const _amount = sc_0.loadCoins();
    const _currency = sc_0.loadUintBig(8);
    const _tokens_to_receive = sc_0.loadCoins();
    const _current_price = sc_0.loadCoins();
    const _current_round = sc_0.loadUintBig(32);
    const _timestamp = sc_0.loadUintBig(64);
    const _nonce = sc_0.loadUintBig(64);
    const _usdt_equivalent_amount = sc_0.loadCoins();
    return { $$type: 'PurchaseCalculation' as const, user: _user, amount: _amount, currency: _currency, tokens_to_receive: _tokens_to_receive, current_price: _current_price, current_round: _current_round, timestamp: _timestamp, nonce: _nonce, usdt_equivalent_amount: _usdt_equivalent_amount };
}

export function loadTuplePurchaseCalculation(source: TupleReader) {
    const _user = source.readAddress();
    const _amount = source.readBigNumber();
    const _currency = source.readBigNumber();
    const _tokens_to_receive = source.readBigNumber();
    const _current_price = source.readBigNumber();
    const _current_round = source.readBigNumber();
    const _timestamp = source.readBigNumber();
    const _nonce = source.readBigNumber();
    const _usdt_equivalent_amount = source.readBigNumber();
    return { $$type: 'PurchaseCalculation' as const, user: _user, amount: _amount, currency: _currency, tokens_to_receive: _tokens_to_receive, current_price: _current_price, current_round: _current_round, timestamp: _timestamp, nonce: _nonce, usdt_equivalent_amount: _usdt_equivalent_amount };
}

export function loadGetterTuplePurchaseCalculation(source: TupleReader) {
    const _user = source.readAddress();
    const _amount = source.readBigNumber();
    const _currency = source.readBigNumber();
    const _tokens_to_receive = source.readBigNumber();
    const _current_price = source.readBigNumber();
    const _current_round = source.readBigNumber();
    const _timestamp = source.readBigNumber();
    const _nonce = source.readBigNumber();
    const _usdt_equivalent_amount = source.readBigNumber();
    return { $$type: 'PurchaseCalculation' as const, user: _user, amount: _amount, currency: _currency, tokens_to_receive: _tokens_to_receive, current_price: _current_price, current_round: _current_round, timestamp: _timestamp, nonce: _nonce, usdt_equivalent_amount: _usdt_equivalent_amount };
}

export function storeTuplePurchaseCalculation(source: PurchaseCalculation) {
    const builder = new TupleBuilder();
    builder.writeAddress(source.user);
    builder.writeNumber(source.amount);
    builder.writeNumber(source.currency);
    builder.writeNumber(source.tokens_to_receive);
    builder.writeNumber(source.current_price);
    builder.writeNumber(source.current_round);
    builder.writeNumber(source.timestamp);
    builder.writeNumber(source.nonce);
    builder.writeNumber(source.usdt_equivalent_amount);
    return builder.build();
}

export function dictValueParserPurchaseCalculation(): DictionaryValue<PurchaseCalculation> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storePurchaseCalculation(src)).endCell());
        },
        parse: (src) => {
            return loadPurchaseCalculation(src.loadRef().beginParse());
        }
    }
}

export type ParsedPurchaseData = {
    $$type: 'ParsedPurchaseData';
    calculation: PurchaseCalculation;
    signature: Slice;
}

export function storeParsedPurchaseData(src: ParsedPurchaseData) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.store(storePurchaseCalculation(src.calculation));
        b_0.storeRef(src.signature.asCell());
    };
}

export function loadParsedPurchaseData(slice: Slice) {
    const sc_0 = slice;
    const _calculation = loadPurchaseCalculation(sc_0);
    const _signature = sc_0.loadRef().asSlice();
    return { $$type: 'ParsedPurchaseData' as const, calculation: _calculation, signature: _signature };
}

export function loadTupleParsedPurchaseData(source: TupleReader) {
    const _calculation = loadTuplePurchaseCalculation(source);
    const _signature = source.readCell().asSlice();
    return { $$type: 'ParsedPurchaseData' as const, calculation: _calculation, signature: _signature };
}

export function loadGetterTupleParsedPurchaseData(source: TupleReader) {
    const _calculation = loadGetterTuplePurchaseCalculation(source);
    const _signature = source.readCell().asSlice();
    return { $$type: 'ParsedPurchaseData' as const, calculation: _calculation, signature: _signature };
}

export function storeTupleParsedPurchaseData(source: ParsedPurchaseData) {
    const builder = new TupleBuilder();
    builder.writeTuple(storeTuplePurchaseCalculation(source.calculation));
    builder.writeSlice(source.signature.asCell());
    return builder.build();
}

export function dictValueParserParsedPurchaseData(): DictionaryValue<ParsedPurchaseData> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeParsedPurchaseData(src)).endCell());
        },
        parse: (src) => {
            return loadParsedPurchaseData(src.loadRef().beginParse());
        }
    }
}

export type OnionAuction$Data = {
    $$type: 'OnionAuction$Data';
    owner: Address;
    stopped: boolean;
    auction_config: AuctionConfig;
    current_round: bigint;
    current_price: bigint;
    total_raised: bigint;
    total_tokens_sold: bigint;
    auction_status: bigint;
    usdt_config: USDTConfig | null;
    total_raised_usdt: bigint;
    total_raised_usdt_equivalent: bigint;
    purchase_count: bigint;
    round_stats: Dictionary<bigint, RoundStats>;
    user_round_purchases: Dictionary<Address, bigint>;
    signing_public_key: bigint;
    used_nonces: Dictionary<bigint, boolean>;
    signature_timeout: bigint;
    min_purchase: bigint;
    treasury_address: Address | null;
}

export function storeOnionAuction$Data(src: OnionAuction$Data) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeAddress(src.owner);
        b_0.storeBit(src.stopped);
        b_0.store(storeAuctionConfig(src.auction_config));
        b_0.storeUint(src.current_round, 32);
        b_0.storeCoins(src.current_price);
        const b_1 = new Builder();
        b_1.storeCoins(src.total_raised);
        b_1.storeCoins(src.total_tokens_sold);
        b_1.storeUint(src.auction_status, 8);
        if (src.usdt_config !== null && src.usdt_config !== undefined) { b_1.storeBit(true); b_1.store(storeUSDTConfig(src.usdt_config)); } else { b_1.storeBit(false); }
        b_1.storeCoins(src.total_raised_usdt);
        const b_2 = new Builder();
        b_2.storeCoins(src.total_raised_usdt_equivalent);
        b_2.storeUint(src.purchase_count, 32);
        b_2.storeDict(src.round_stats, Dictionary.Keys.BigInt(257), dictValueParserRoundStats());
        b_2.storeDict(src.user_round_purchases, Dictionary.Keys.Address(), Dictionary.Values.BigInt(257));
        b_2.storeUint(src.signing_public_key, 256);
        b_2.storeDict(src.used_nonces, Dictionary.Keys.BigInt(257), Dictionary.Values.Bool());
        b_2.storeUint(src.signature_timeout, 64);
        b_2.storeCoins(src.min_purchase);
        b_2.storeAddress(src.treasury_address);
        b_1.storeRef(b_2.endCell());
        b_0.storeRef(b_1.endCell());
    };
}

export function loadOnionAuction$Data(slice: Slice) {
    const sc_0 = slice;
    const _owner = sc_0.loadAddress();
    const _stopped = sc_0.loadBit();
    const _auction_config = loadAuctionConfig(sc_0);
    const _current_round = sc_0.loadUintBig(32);
    const _current_price = sc_0.loadCoins();
    const sc_1 = sc_0.loadRef().beginParse();
    const _total_raised = sc_1.loadCoins();
    const _total_tokens_sold = sc_1.loadCoins();
    const _auction_status = sc_1.loadUintBig(8);
    const _usdt_config = sc_1.loadBit() ? loadUSDTConfig(sc_1) : null;
    const _total_raised_usdt = sc_1.loadCoins();
    const sc_2 = sc_1.loadRef().beginParse();
    const _total_raised_usdt_equivalent = sc_2.loadCoins();
    const _purchase_count = sc_2.loadUintBig(32);
    const _round_stats = Dictionary.load(Dictionary.Keys.BigInt(257), dictValueParserRoundStats(), sc_2);
    const _user_round_purchases = Dictionary.load(Dictionary.Keys.Address(), Dictionary.Values.BigInt(257), sc_2);
    const _signing_public_key = sc_2.loadUintBig(256);
    const _used_nonces = Dictionary.load(Dictionary.Keys.BigInt(257), Dictionary.Values.Bool(), sc_2);
    const _signature_timeout = sc_2.loadUintBig(64);
    const _min_purchase = sc_2.loadCoins();
    const _treasury_address = sc_2.loadMaybeAddress();
    return { $$type: 'OnionAuction$Data' as const, owner: _owner, stopped: _stopped, auction_config: _auction_config, current_round: _current_round, current_price: _current_price, total_raised: _total_raised, total_tokens_sold: _total_tokens_sold, auction_status: _auction_status, usdt_config: _usdt_config, total_raised_usdt: _total_raised_usdt, total_raised_usdt_equivalent: _total_raised_usdt_equivalent, purchase_count: _purchase_count, round_stats: _round_stats, user_round_purchases: _user_round_purchases, signing_public_key: _signing_public_key, used_nonces: _used_nonces, signature_timeout: _signature_timeout, min_purchase: _min_purchase, treasury_address: _treasury_address };
}

export function loadTupleOnionAuction$Data(source: TupleReader) {
    const _owner = source.readAddress();
    const _stopped = source.readBoolean();
    const _auction_config = loadTupleAuctionConfig(source);
    const _current_round = source.readBigNumber();
    const _current_price = source.readBigNumber();
    const _total_raised = source.readBigNumber();
    const _total_tokens_sold = source.readBigNumber();
    const _auction_status = source.readBigNumber();
    const _usdt_config_p = source.readTupleOpt();
    const _usdt_config = _usdt_config_p ? loadTupleUSDTConfig(_usdt_config_p) : null;
    const _total_raised_usdt = source.readBigNumber();
    const _total_raised_usdt_equivalent = source.readBigNumber();
    const _purchase_count = source.readBigNumber();
    const _round_stats = Dictionary.loadDirect(Dictionary.Keys.BigInt(257), dictValueParserRoundStats(), source.readCellOpt());
    const _user_round_purchases = Dictionary.loadDirect(Dictionary.Keys.Address(), Dictionary.Values.BigInt(257), source.readCellOpt());
    source = source.readTuple();
    const _signing_public_key = source.readBigNumber();
    const _used_nonces = Dictionary.loadDirect(Dictionary.Keys.BigInt(257), Dictionary.Values.Bool(), source.readCellOpt());
    const _signature_timeout = source.readBigNumber();
    const _min_purchase = source.readBigNumber();
    const _treasury_address = source.readAddressOpt();
    return { $$type: 'OnionAuction$Data' as const, owner: _owner, stopped: _stopped, auction_config: _auction_config, current_round: _current_round, current_price: _current_price, total_raised: _total_raised, total_tokens_sold: _total_tokens_sold, auction_status: _auction_status, usdt_config: _usdt_config, total_raised_usdt: _total_raised_usdt, total_raised_usdt_equivalent: _total_raised_usdt_equivalent, purchase_count: _purchase_count, round_stats: _round_stats, user_round_purchases: _user_round_purchases, signing_public_key: _signing_public_key, used_nonces: _used_nonces, signature_timeout: _signature_timeout, min_purchase: _min_purchase, treasury_address: _treasury_address };
}

export function loadGetterTupleOnionAuction$Data(source: TupleReader) {
    const _owner = source.readAddress();
    const _stopped = source.readBoolean();
    const _auction_config = loadGetterTupleAuctionConfig(source);
    const _current_round = source.readBigNumber();
    const _current_price = source.readBigNumber();
    const _total_raised = source.readBigNumber();
    const _total_tokens_sold = source.readBigNumber();
    const _auction_status = source.readBigNumber();
    const _usdt_config_p = source.readTupleOpt();
    const _usdt_config = _usdt_config_p ? loadTupleUSDTConfig(_usdt_config_p) : null;
    const _total_raised_usdt = source.readBigNumber();
    const _total_raised_usdt_equivalent = source.readBigNumber();
    const _purchase_count = source.readBigNumber();
    const _round_stats = Dictionary.loadDirect(Dictionary.Keys.BigInt(257), dictValueParserRoundStats(), source.readCellOpt());
    const _user_round_purchases = Dictionary.loadDirect(Dictionary.Keys.Address(), Dictionary.Values.BigInt(257), source.readCellOpt());
    const _signing_public_key = source.readBigNumber();
    const _used_nonces = Dictionary.loadDirect(Dictionary.Keys.BigInt(257), Dictionary.Values.Bool(), source.readCellOpt());
    const _signature_timeout = source.readBigNumber();
    const _min_purchase = source.readBigNumber();
    const _treasury_address = source.readAddressOpt();
    return { $$type: 'OnionAuction$Data' as const, owner: _owner, stopped: _stopped, auction_config: _auction_config, current_round: _current_round, current_price: _current_price, total_raised: _total_raised, total_tokens_sold: _total_tokens_sold, auction_status: _auction_status, usdt_config: _usdt_config, total_raised_usdt: _total_raised_usdt, total_raised_usdt_equivalent: _total_raised_usdt_equivalent, purchase_count: _purchase_count, round_stats: _round_stats, user_round_purchases: _user_round_purchases, signing_public_key: _signing_public_key, used_nonces: _used_nonces, signature_timeout: _signature_timeout, min_purchase: _min_purchase, treasury_address: _treasury_address };
}

export function storeTupleOnionAuction$Data(source: OnionAuction$Data) {
    const builder = new TupleBuilder();
    builder.writeAddress(source.owner);
    builder.writeBoolean(source.stopped);
    builder.writeTuple(storeTupleAuctionConfig(source.auction_config));
    builder.writeNumber(source.current_round);
    builder.writeNumber(source.current_price);
    builder.writeNumber(source.total_raised);
    builder.writeNumber(source.total_tokens_sold);
    builder.writeNumber(source.auction_status);
    if (source.usdt_config !== null && source.usdt_config !== undefined) {
        builder.writeTuple(storeTupleUSDTConfig(source.usdt_config));
    } else {
        builder.writeTuple(null);
    }
    builder.writeNumber(source.total_raised_usdt);
    builder.writeNumber(source.total_raised_usdt_equivalent);
    builder.writeNumber(source.purchase_count);
    builder.writeCell(source.round_stats.size > 0 ? beginCell().storeDictDirect(source.round_stats, Dictionary.Keys.BigInt(257), dictValueParserRoundStats()).endCell() : null);
    builder.writeCell(source.user_round_purchases.size > 0 ? beginCell().storeDictDirect(source.user_round_purchases, Dictionary.Keys.Address(), Dictionary.Values.BigInt(257)).endCell() : null);
    builder.writeNumber(source.signing_public_key);
    builder.writeCell(source.used_nonces.size > 0 ? beginCell().storeDictDirect(source.used_nonces, Dictionary.Keys.BigInt(257), Dictionary.Values.Bool()).endCell() : null);
    builder.writeNumber(source.signature_timeout);
    builder.writeNumber(source.min_purchase);
    builder.writeAddress(source.treasury_address);
    return builder.build();
}

export function dictValueParserOnionAuction$Data(): DictionaryValue<OnionAuction$Data> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeOnionAuction$Data(src)).endCell());
        },
        parse: (src) => {
            return loadOnionAuction$Data(src.loadRef().beginParse());
        }
    }
}

 type OnionAuction_init_args = {
    $$type: 'OnionAuction_init_args';
    owner: Address;
    start_time: bigint;
    end_time: bigint;
    soft_cap: bigint;
    hard_cap: bigint;
    total_supply: bigint;
}

function initOnionAuction_init_args(src: OnionAuction_init_args) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeAddress(src.owner);
        b_0.storeInt(src.start_time, 257);
        b_0.storeInt(src.end_time, 257);
        const b_1 = new Builder();
        b_1.storeInt(src.soft_cap, 257);
        b_1.storeInt(src.hard_cap, 257);
        b_1.storeInt(src.total_supply, 257);
        b_0.storeRef(b_1.endCell());
    };
}

async function OnionAuction_init(owner: Address, start_time: bigint, end_time: bigint, soft_cap: bigint, hard_cap: bigint, total_supply: bigint) {
    const __code = Cell.fromHex('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');
    const builder = beginCell();
    builder.storeUint(0, 1);
    initOnionAuction_init_args({ $$type: 'OnionAuction_init_args', owner, start_time, end_time, soft_cap, hard_cap, total_supply })(builder);
    const __data = builder.endCell();
    return { code: __code, data: __data };
}

export const OnionAuction_errors = {
    2: { message: "Stack underflow" },
    3: { message: "Stack overflow" },
    4: { message: "Integer overflow" },
    5: { message: "Integer out of expected range" },
    6: { message: "Invalid opcode" },
    7: { message: "Type check error" },
    8: { message: "Cell overflow" },
    9: { message: "Cell underflow" },
    10: { message: "Dictionary error" },
    11: { message: "'Unknown' error" },
    12: { message: "Fatal error" },
    13: { message: "Out of gas error" },
    14: { message: "Virtualization error" },
    32: { message: "Action list is invalid" },
    33: { message: "Action list is too long" },
    34: { message: "Action is invalid or not supported" },
    35: { message: "Invalid source address in outbound message" },
    36: { message: "Invalid destination address in outbound message" },
    37: { message: "Not enough Toncoin" },
    38: { message: "Not enough extra currencies" },
    39: { message: "Outbound message does not fit into a cell after rewriting" },
    40: { message: "Cannot process a message" },
    41: { message: "Library reference is null" },
    42: { message: "Library change action error" },
    43: { message: "Exceeded maximum number of cells in the library or the maximum depth of the Merkle tree" },
    50: { message: "Account state size exceeded limits" },
    128: { message: "Null reference exception" },
    129: { message: "Invalid serialization prefix" },
    130: { message: "Invalid incoming message" },
    131: { message: "Constraints error" },
    132: { message: "Access denied" },
    133: { message: "Contract stopped" },
    134: { message: "Invalid argument" },
    135: { message: "Code of a contract was not found" },
    136: { message: "Invalid standard address" },
    138: { message: "Not a basechain address" },
    2296: { message: "JettonWallet: Only Jetton master or Jetton wallet can call this function" },
    13105: { message: "JettonWallet: Not enough jettons to transfer" },
    22411: { message: "Already refunded" },
    27831: { message: "Only owner can call this function" },
    29133: { message: "JettonWallet: Not allow negative balance after internal transfer" },
    37185: { message: "Not enough funds to transfer" },
    39144: { message: "Purchase not found" },
    47048: { message: "JettonWallet: Only owner can burn tokens" },
    49729: { message: "Unauthorized" },
    53296: { message: "Contract not stopped" },
    60354: { message: "JettonWallet: Not enough balance to burn tokens" },
} as const

export const OnionAuction_errors_backward = {
    "Stack underflow": 2,
    "Stack overflow": 3,
    "Integer overflow": 4,
    "Integer out of expected range": 5,
    "Invalid opcode": 6,
    "Type check error": 7,
    "Cell overflow": 8,
    "Cell underflow": 9,
    "Dictionary error": 10,
    "'Unknown' error": 11,
    "Fatal error": 12,
    "Out of gas error": 13,
    "Virtualization error": 14,
    "Action list is invalid": 32,
    "Action list is too long": 33,
    "Action is invalid or not supported": 34,
    "Invalid source address in outbound message": 35,
    "Invalid destination address in outbound message": 36,
    "Not enough Toncoin": 37,
    "Not enough extra currencies": 38,
    "Outbound message does not fit into a cell after rewriting": 39,
    "Cannot process a message": 40,
    "Library reference is null": 41,
    "Library change action error": 42,
    "Exceeded maximum number of cells in the library or the maximum depth of the Merkle tree": 43,
    "Account state size exceeded limits": 50,
    "Null reference exception": 128,
    "Invalid serialization prefix": 129,
    "Invalid incoming message": 130,
    "Constraints error": 131,
    "Access denied": 132,
    "Contract stopped": 133,
    "Invalid argument": 134,
    "Code of a contract was not found": 135,
    "Invalid standard address": 136,
    "Not a basechain address": 138,
    "JettonWallet: Only Jetton master or Jetton wallet can call this function": 2296,
    "JettonWallet: Not enough jettons to transfer": 13105,
    "Already refunded": 22411,
    "Only owner can call this function": 27831,
    "JettonWallet: Not allow negative balance after internal transfer": 29133,
    "Not enough funds to transfer": 37185,
    "Purchase not found": 39144,
    "JettonWallet: Only owner can burn tokens": 47048,
    "Unauthorized": 49729,
    "Contract not stopped": 53296,
    "JettonWallet: Not enough balance to burn tokens": 60354,
} as const

const OnionAuction_types: ABIType[] = [
    {"name":"DataSize","header":null,"fields":[{"name":"cells","type":{"kind":"simple","type":"int","optional":false,"format":257}},{"name":"bits","type":{"kind":"simple","type":"int","optional":false,"format":257}},{"name":"refs","type":{"kind":"simple","type":"int","optional":false,"format":257}}]},
    {"name":"SignedBundle","header":null,"fields":[{"name":"signature","type":{"kind":"simple","type":"fixed-bytes","optional":false,"format":64}},{"name":"signedData","type":{"kind":"simple","type":"slice","optional":false,"format":"remainder"}}]},
    {"name":"StateInit","header":null,"fields":[{"name":"code","type":{"kind":"simple","type":"cell","optional":false}},{"name":"data","type":{"kind":"simple","type":"cell","optional":false}}]},
    {"name":"Context","header":null,"fields":[{"name":"bounceable","type":{"kind":"simple","type":"bool","optional":false}},{"name":"sender","type":{"kind":"simple","type":"address","optional":false}},{"name":"value","type":{"kind":"simple","type":"int","optional":false,"format":257}},{"name":"raw","type":{"kind":"simple","type":"slice","optional":false}}]},
    {"name":"SendParameters","header":null,"fields":[{"name":"mode","type":{"kind":"simple","type":"int","optional":false,"format":257}},{"name":"body","type":{"kind":"simple","type":"cell","optional":true}},{"name":"code","type":{"kind":"simple","type":"cell","optional":true}},{"name":"data","type":{"kind":"simple","type":"cell","optional":true}},{"name":"value","type":{"kind":"simple","type":"int","optional":false,"format":257}},{"name":"to","type":{"kind":"simple","type":"address","optional":false}},{"name":"bounce","type":{"kind":"simple","type":"bool","optional":false}}]},
    {"name":"MessageParameters","header":null,"fields":[{"name":"mode","type":{"kind":"simple","type":"int","optional":false,"format":257}},{"name":"body","type":{"kind":"simple","type":"cell","optional":true}},{"name":"value","type":{"kind":"simple","type":"int","optional":false,"format":257}},{"name":"to","type":{"kind":"simple","type":"address","optional":false}},{"name":"bounce","type":{"kind":"simple","type":"bool","optional":false}}]},
    {"name":"DeployParameters","header":null,"fields":[{"name":"mode","type":{"kind":"simple","type":"int","optional":false,"format":257}},{"name":"body","type":{"kind":"simple","type":"cell","optional":true}},{"name":"value","type":{"kind":"simple","type":"int","optional":false,"format":257}},{"name":"bounce","type":{"kind":"simple","type":"bool","optional":false}},{"name":"init","type":{"kind":"simple","type":"StateInit","optional":false}}]},
    {"name":"StdAddress","header":null,"fields":[{"name":"workchain","type":{"kind":"simple","type":"int","optional":false,"format":8}},{"name":"address","type":{"kind":"simple","type":"uint","optional":false,"format":256}}]},
    {"name":"VarAddress","header":null,"fields":[{"name":"workchain","type":{"kind":"simple","type":"int","optional":false,"format":32}},{"name":"address","type":{"kind":"simple","type":"slice","optional":false}}]},
    {"name":"BasechainAddress","header":null,"fields":[{"name":"hash","type":{"kind":"simple","type":"int","optional":true,"format":257}}]},
    {"name":"ChangeOwner","header":2174598809,"fields":[{"name":"queryId","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"newOwner","type":{"kind":"simple","type":"address","optional":false}}]},
    {"name":"ChangeOwnerOk","header":846932810,"fields":[{"name":"queryId","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"newOwner","type":{"kind":"simple","type":"address","optional":false}}]},
    {"name":"Deploy","header":2490013878,"fields":[{"name":"queryId","type":{"kind":"simple","type":"uint","optional":false,"format":64}}]},
    {"name":"DeployOk","header":2952335191,"fields":[{"name":"queryId","type":{"kind":"simple","type":"uint","optional":false,"format":64}}]},
    {"name":"FactoryDeploy","header":1829761339,"fields":[{"name":"queryId","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"cashback","type":{"kind":"simple","type":"address","optional":false}}]},
    {"name":"JettonTransfer","header":260734629,"fields":[{"name":"query_id","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"amount","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"destination","type":{"kind":"simple","type":"address","optional":false}},{"name":"response_destination","type":{"kind":"simple","type":"address","optional":false}},{"name":"custom_payload","type":{"kind":"simple","type":"cell","optional":true}},{"name":"forward_ton_amount","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"forward_payload","type":{"kind":"simple","type":"cell","optional":true}}]},
    {"name":"JettonTransferNotification","header":1935855772,"fields":[{"name":"query_id","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"amount","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"sender","type":{"kind":"simple","type":"address","optional":false}},{"name":"forward_payload","type":{"kind":"simple","type":"cell","optional":true}}]},
    {"name":"JettonBurn","header":1499400124,"fields":[{"name":"query_id","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"amount","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"response_destination","type":{"kind":"simple","type":"address","optional":false}},{"name":"custom_payload","type":{"kind":"simple","type":"cell","optional":true}}]},
    {"name":"JettonExcesses","header":3576854235,"fields":[{"name":"query_id","type":{"kind":"simple","type":"uint","optional":false,"format":64}}]},
    {"name":"JettonInternalTransfer","header":395134233,"fields":[{"name":"query_id","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"amount","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"from","type":{"kind":"simple","type":"address","optional":false}},{"name":"response_address","type":{"kind":"simple","type":"address","optional":false}},{"name":"forward_ton_amount","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"forward_payload","type":{"kind":"simple","type":"cell","optional":true}}]},
    {"name":"JettonBurnNotification","header":2078119902,"fields":[{"name":"query_id","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"amount","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"sender","type":{"kind":"simple","type":"address","optional":false}},{"name":"response_destination","type":{"kind":"simple","type":"address","optional":false}}]},
    {"name":"WalletData","header":null,"fields":[{"name":"balance","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"owner","type":{"kind":"simple","type":"address","optional":false}},{"name":"jetton","type":{"kind":"simple","type":"address","optional":false}},{"name":"jetton_wallet_code","type":{"kind":"simple","type":"cell","optional":false}}]},
    {"name":"CreateUserPurchase","header":1098075148,"fields":[{"name":"user","type":{"kind":"simple","type":"address","optional":false}},{"name":"amount","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"tokens","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"currency","type":{"kind":"simple","type":"uint","optional":false,"format":8}},{"name":"purchase_method","type":{"kind":"simple","type":"uint","optional":false,"format":8}},{"name":"nonce","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"round_number","type":{"kind":"simple","type":"uint","optional":false,"format":32}},{"name":"usdt_equivalent_amount","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}}]},
    {"name":"Refund","header":836089260,"fields":[{"name":"purchase_id","type":{"kind":"simple","type":"uint","optional":false,"format":32}}]},
    {"name":"ProcessRefund","header":929991442,"fields":[{"name":"user","type":{"kind":"simple","type":"address","optional":false}},{"name":"amount","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"fee","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"currency","type":{"kind":"simple","type":"uint","optional":false,"format":8}},{"name":"round_number","type":{"kind":"simple","type":"uint","optional":false,"format":32}},{"name":"usdt_equivalent_amount","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}}]},
    {"name":"PurchaseRecord","header":null,"fields":[{"name":"id","type":{"kind":"simple","type":"uint","optional":false,"format":32}},{"name":"user","type":{"kind":"simple","type":"address","optional":false}},{"name":"amount","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"tokens","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"timestamp","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"currency","type":{"kind":"simple","type":"uint","optional":false,"format":8}},{"name":"purchase_method","type":{"kind":"simple","type":"uint","optional":false,"format":8}},{"name":"nonce","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"round_number","type":{"kind":"simple","type":"uint","optional":false,"format":32}},{"name":"usdt_equivalent_amount","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}}]},
    {"name":"UserPurchase$Data","header":null,"fields":[{"name":"owner","type":{"kind":"simple","type":"address","optional":false}},{"name":"auction_address","type":{"kind":"simple","type":"address","optional":false}},{"name":"user_address","type":{"kind":"simple","type":"address","optional":false}},{"name":"total_purchased","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"total_paid","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"purchase_history","type":{"kind":"dict","key":"int","value":"PurchaseRecord","valueFormat":"ref"}},{"name":"refund_history","type":{"kind":"dict","key":"int","value":"int"}},{"name":"purchase_id_counter","type":{"kind":"simple","type":"uint","optional":false,"format":32}},{"name":"participated_rounds","type":{"kind":"dict","key":"int","value":"bool"}}]},
    {"name":"StartAuction","header":1082886929,"fields":[{"name":"start_time","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"end_time","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"soft_cap","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"hard_cap","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"initial_price","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}}]},
    {"name":"UpdateRound","header":2861271945,"fields":[{"name":"new_price","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"round_number","type":{"kind":"simple","type":"uint","optional":false,"format":32}}]},
    {"name":"SetUSDTAddress","header":2703444734,"fields":[{"name":"usdt_master","type":{"kind":"simple","type":"address","optional":false}},{"name":"usdt_wallet","type":{"kind":"simple","type":"address","optional":false}}]},
    {"name":"PurchaseWithSignature","header":1524770820,"fields":[{"name":"calculation","type":{"kind":"simple","type":"PurchaseCalculation","optional":false}},{"name":"signature","type":{"kind":"simple","type":"slice","optional":false}}]},
    {"name":"SetSigningKey","header":3315975678,"fields":[{"name":"public_key","type":{"kind":"simple","type":"uint","optional":false,"format":256}}]},
    {"name":"SetMinPurchase","header":3390874056,"fields":[{"name":"min_purchase","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}}]},
    {"name":"WithdrawTON","header":3520188881,"fields":[{"name":"amount","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"destination","type":{"kind":"simple","type":"address","optional":false}}]},
    {"name":"WithdrawUSDT","header":3537031890,"fields":[{"name":"amount","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"destination","type":{"kind":"simple","type":"address","optional":false}}]},
    {"name":"SetTreasury","header":3553874899,"fields":[{"name":"treasury_address","type":{"kind":"simple","type":"address","optional":false}}]},
    {"name":"AuctionStarted","header":2217068725,"fields":[{"name":"start_time","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"end_time","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"soft_cap","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"hard_cap","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"initial_price","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"total_supply","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}}]},
    {"name":"PurchaseCompleted","header":3975568033,"fields":[{"name":"user","type":{"kind":"simple","type":"address","optional":false}},{"name":"amount","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"tokens_received","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"currency","type":{"kind":"simple","type":"uint","optional":false,"format":8}},{"name":"purchase_method","type":{"kind":"simple","type":"uint","optional":false,"format":8}},{"name":"round_number","type":{"kind":"simple","type":"uint","optional":false,"format":32}},{"name":"nonce","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"total_raised","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"total_raised_usdt","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"total_tokens_sold","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}}]},
    {"name":"USDTConfigured","header":1031232428,"fields":[{"name":"usdt_master","type":{"kind":"simple","type":"address","optional":false}},{"name":"usdt_wallet","type":{"kind":"simple","type":"address","optional":false}},{"name":"configured_by","type":{"kind":"simple","type":"address","optional":false}}]},
    {"name":"SigningKeySet","header":2441211440,"fields":[{"name":"public_key","type":{"kind":"simple","type":"uint","optional":false,"format":256}},{"name":"set_by","type":{"kind":"simple","type":"address","optional":false}}]},
    {"name":"MinPurchaseUpdated","header":760782792,"fields":[{"name":"old_min_purchase","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"new_min_purchase","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"updated_by","type":{"kind":"simple","type":"address","optional":false}}]},
    {"name":"TreasurySet","header":4013598566,"fields":[{"name":"old_treasury","type":{"kind":"simple","type":"address","optional":false}},{"name":"new_treasury","type":{"kind":"simple","type":"address","optional":false}},{"name":"set_by","type":{"kind":"simple","type":"address","optional":false}}]},
    {"name":"FundsWithdrawn","header":3674912463,"fields":[{"name":"currency","type":{"kind":"simple","type":"uint","optional":false,"format":8}},{"name":"amount","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"destination","type":{"kind":"simple","type":"address","optional":false}},{"name":"withdrawn_by","type":{"kind":"simple","type":"address","optional":false}},{"name":"remaining_balance","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}}]},
    {"name":"RoundUpdated","header":190065307,"fields":[{"name":"old_round","type":{"kind":"simple","type":"uint","optional":false,"format":32}},{"name":"new_round","type":{"kind":"simple","type":"uint","optional":false,"format":32}},{"name":"old_price","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"new_price","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"updated_by","type":{"kind":"simple","type":"address","optional":false}}]},
    {"name":"AuctionEnded","header":397814633,"fields":[{"name":"end_reason","type":{"kind":"simple","type":"uint","optional":false,"format":8}},{"name":"final_status","type":{"kind":"simple","type":"uint","optional":false,"format":8}},{"name":"total_raised","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"total_raised_usdt","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"total_tokens_sold","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"end_time","type":{"kind":"simple","type":"uint","optional":false,"format":64}}]},
    {"name":"AuctionConfig","header":null,"fields":[{"name":"start_time","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"end_time","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"soft_cap","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"hard_cap","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"total_supply","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"refund_fee_percent","type":{"kind":"simple","type":"uint","optional":false,"format":8}}]},
    {"name":"RoundStats","header":null,"fields":[{"name":"round_number","type":{"kind":"simple","type":"uint","optional":false,"format":32}},{"name":"start_time","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"end_time","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"price","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"total_raised_ton","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"total_raised_usdt","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"raised_usdt_equivalent","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"tokens_sold","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"purchase_count","type":{"kind":"simple","type":"uint","optional":false,"format":32}},{"name":"unique_users","type":{"kind":"simple","type":"uint","optional":false,"format":32}},{"name":"refund_count","type":{"kind":"simple","type":"uint","optional":false,"format":32}},{"name":"refunded_amount_ton","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"refunded_amount_usdt","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"refunded_usdt_equivalent","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}}]},
    {"name":"USDTConfig","header":null,"fields":[{"name":"master_address","type":{"kind":"simple","type":"address","optional":false}},{"name":"wallet_address","type":{"kind":"simple","type":"address","optional":true}},{"name":"decimals","type":{"kind":"simple","type":"uint","optional":false,"format":8}}]},
    {"name":"PurchaseCalculation","header":null,"fields":[{"name":"user","type":{"kind":"simple","type":"address","optional":false}},{"name":"amount","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"currency","type":{"kind":"simple","type":"uint","optional":false,"format":8}},{"name":"tokens_to_receive","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"current_price","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"current_round","type":{"kind":"simple","type":"uint","optional":false,"format":32}},{"name":"timestamp","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"nonce","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"usdt_equivalent_amount","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}}]},
    {"name":"ParsedPurchaseData","header":null,"fields":[{"name":"calculation","type":{"kind":"simple","type":"PurchaseCalculation","optional":false}},{"name":"signature","type":{"kind":"simple","type":"slice","optional":false}}]},
    {"name":"OnionAuction$Data","header":null,"fields":[{"name":"owner","type":{"kind":"simple","type":"address","optional":false}},{"name":"stopped","type":{"kind":"simple","type":"bool","optional":false}},{"name":"auction_config","type":{"kind":"simple","type":"AuctionConfig","optional":false}},{"name":"current_round","type":{"kind":"simple","type":"uint","optional":false,"format":32}},{"name":"current_price","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"total_raised","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"total_tokens_sold","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"auction_status","type":{"kind":"simple","type":"uint","optional":false,"format":8}},{"name":"usdt_config","type":{"kind":"simple","type":"USDTConfig","optional":true}},{"name":"total_raised_usdt","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"total_raised_usdt_equivalent","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"purchase_count","type":{"kind":"simple","type":"uint","optional":false,"format":32}},{"name":"round_stats","type":{"kind":"dict","key":"int","value":"RoundStats","valueFormat":"ref"}},{"name":"user_round_purchases","type":{"kind":"dict","key":"address","value":"int"}},{"name":"signing_public_key","type":{"kind":"simple","type":"uint","optional":false,"format":256}},{"name":"used_nonces","type":{"kind":"dict","key":"int","value":"bool"}},{"name":"signature_timeout","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"min_purchase","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"treasury_address","type":{"kind":"simple","type":"address","optional":true}}]},
]

const OnionAuction_opcodes = {
    "ChangeOwner": 2174598809,
    "ChangeOwnerOk": 846932810,
    "Deploy": 2490013878,
    "DeployOk": 2952335191,
    "FactoryDeploy": 1829761339,
    "JettonTransfer": 260734629,
    "JettonTransferNotification": 1935855772,
    "JettonBurn": 1499400124,
    "JettonExcesses": 3576854235,
    "JettonInternalTransfer": 395134233,
    "JettonBurnNotification": 2078119902,
    "CreateUserPurchase": 1098075148,
    "Refund": 836089260,
    "ProcessRefund": 929991442,
    "StartAuction": 1082886929,
    "UpdateRound": 2861271945,
    "SetUSDTAddress": 2703444734,
    "PurchaseWithSignature": 1524770820,
    "SetSigningKey": 3315975678,
    "SetMinPurchase": 3390874056,
    "WithdrawTON": 3520188881,
    "WithdrawUSDT": 3537031890,
    "SetTreasury": 3553874899,
    "AuctionStarted": 2217068725,
    "PurchaseCompleted": 3975568033,
    "USDTConfigured": 1031232428,
    "SigningKeySet": 2441211440,
    "MinPurchaseUpdated": 760782792,
    "TreasurySet": 4013598566,
    "FundsWithdrawn": 3674912463,
    "RoundUpdated": 190065307,
    "AuctionEnded": 397814633,
}

const OnionAuction_getters: ABIGetter[] = [
    {"name":"auction_info","methodId":72669,"arguments":[],"returnType":{"kind":"simple","type":"AuctionConfig","optional":false}},
    {"name":"current_round","methodId":108432,"arguments":[],"returnType":{"kind":"simple","type":"int","optional":false,"format":257}},
    {"name":"current_price","methodId":102733,"arguments":[],"returnType":{"kind":"simple","type":"int","optional":false,"format":257}},
    {"name":"total_raised","methodId":130922,"arguments":[],"returnType":{"kind":"simple","type":"int","optional":false,"format":257}},
    {"name":"total_tokens_sold","methodId":81273,"arguments":[],"returnType":{"kind":"simple","type":"int","optional":false,"format":257}},
    {"name":"auction_status","methodId":82124,"arguments":[],"returnType":{"kind":"simple","type":"int","optional":false,"format":257}},
    {"name":"purchase_count","methodId":69567,"arguments":[],"returnType":{"kind":"simple","type":"int","optional":false,"format":257}},
    {"name":"user_purchase_address","methodId":107761,"arguments":[{"name":"user","type":{"kind":"simple","type":"address","optional":false}}],"returnType":{"kind":"simple","type":"address","optional":false}},
    {"name":"remaining_tokens","methodId":95722,"arguments":[],"returnType":{"kind":"simple","type":"int","optional":false,"format":257}},
    {"name":"is_auction_active","methodId":121388,"arguments":[],"returnType":{"kind":"simple","type":"bool","optional":false}},
    {"name":"usdt_config","methodId":95594,"arguments":[],"returnType":{"kind":"simple","type":"USDTConfig","optional":true}},
    {"name":"total_raised_usdt","methodId":105864,"arguments":[],"returnType":{"kind":"simple","type":"int","optional":false,"format":257}},
    {"name":"total_raised_equivalent","methodId":89664,"arguments":[],"returnType":{"kind":"simple","type":"int","optional":false,"format":257}},
    {"name":"total_raised_usdt_equivalent","methodId":90613,"arguments":[],"returnType":{"kind":"simple","type":"int","optional":false,"format":257}},
    {"name":"is_usdt_enabled","methodId":123065,"arguments":[],"returnType":{"kind":"simple","type":"bool","optional":false}},
    {"name":"signing_public_key","methodId":126473,"arguments":[],"returnType":{"kind":"simple","type":"int","optional":false,"format":257}},
    {"name":"signature_timeout","methodId":91556,"arguments":[],"returnType":{"kind":"simple","type":"int","optional":false,"format":257}},
    {"name":"is_nonce_used","methodId":99217,"arguments":[{"name":"nonce","type":{"kind":"simple","type":"int","optional":false,"format":257}}],"returnType":{"kind":"simple","type":"bool","optional":false}},
    {"name":"is_signature_verification_enabled","methodId":111108,"arguments":[],"returnType":{"kind":"simple","type":"bool","optional":false}},
    {"name":"min_purchase","methodId":110922,"arguments":[],"returnType":{"kind":"simple","type":"int","optional":false,"format":257}},
    {"name":"treasury_address","methodId":71868,"arguments":[],"returnType":{"kind":"simple","type":"address","optional":true}},
    {"name":"withdrawable_ton","methodId":102777,"arguments":[],"returnType":{"kind":"simple","type":"int","optional":false,"format":257}},
    {"name":"withdrawable_usdt","methodId":97270,"arguments":[],"returnType":{"kind":"simple","type":"int","optional":false,"format":257}},
    {"name":"can_withdraw","methodId":115239,"arguments":[],"returnType":{"kind":"simple","type":"bool","optional":false}},
    {"name":"withdrawal_summary","methodId":126996,"arguments":[],"returnType":{"kind":"dict","key":"int","value":"int"}},
    {"name":"round_stats","methodId":86928,"arguments":[{"name":"round_number","type":{"kind":"simple","type":"int","optional":false,"format":257}}],"returnType":{"kind":"simple","type":"RoundStats","optional":true}},
    {"name":"current_round_stats","methodId":124822,"arguments":[],"returnType":{"kind":"simple","type":"RoundStats","optional":true}},
    {"name":"total_rounds","methodId":78978,"arguments":[],"returnType":{"kind":"simple","type":"int","optional":false,"format":257}},
    {"name":"round_summary","methodId":86664,"arguments":[{"name":"round_number","type":{"kind":"simple","type":"int","optional":false,"format":257}}],"returnType":{"kind":"simple","type":"RoundStats","optional":true}},
    {"name":"all_rounds_summary","methodId":83191,"arguments":[],"returnType":{"kind":"dict","key":"int","value":"RoundStats","valueFormat":"ref"}},
    {"name":"round_participants_count","methodId":85355,"arguments":[{"name":"round_number","type":{"kind":"simple","type":"int","optional":false,"format":257}}],"returnType":{"kind":"simple","type":"int","optional":false,"format":257}},
    {"name":"aggregated_stats","methodId":112308,"arguments":[],"returnType":{"kind":"simple","type":"RoundStats","optional":false}},
    {"name":"owner","methodId":83229,"arguments":[],"returnType":{"kind":"simple","type":"address","optional":false}},
    {"name":"stopped","methodId":74107,"arguments":[],"returnType":{"kind":"simple","type":"bool","optional":false}},
]

export const OnionAuction_getterMapping: { [key: string]: string } = {
    'auction_info': 'getAuctionInfo',
    'current_round': 'getCurrentRound',
    'current_price': 'getCurrentPrice',
    'total_raised': 'getTotalRaised',
    'total_tokens_sold': 'getTotalTokensSold',
    'auction_status': 'getAuctionStatus',
    'purchase_count': 'getPurchaseCount',
    'user_purchase_address': 'getUserPurchaseAddress',
    'remaining_tokens': 'getRemainingTokens',
    'is_auction_active': 'getIsAuctionActive',
    'usdt_config': 'getUsdtConfig',
    'total_raised_usdt': 'getTotalRaisedUsdt',
    'total_raised_equivalent': 'getTotalRaisedEquivalent',
    'total_raised_usdt_equivalent': 'getTotalRaisedUsdtEquivalent',
    'is_usdt_enabled': 'getIsUsdtEnabled',
    'signing_public_key': 'getSigningPublicKey',
    'signature_timeout': 'getSignatureTimeout',
    'is_nonce_used': 'getIsNonceUsed',
    'is_signature_verification_enabled': 'getIsSignatureVerificationEnabled',
    'min_purchase': 'getMinPurchase',
    'treasury_address': 'getTreasuryAddress',
    'withdrawable_ton': 'getWithdrawableTon',
    'withdrawable_usdt': 'getWithdrawableUsdt',
    'can_withdraw': 'getCanWithdraw',
    'withdrawal_summary': 'getWithdrawalSummary',
    'round_stats': 'getRoundStats',
    'current_round_stats': 'getCurrentRoundStats',
    'total_rounds': 'getTotalRounds',
    'round_summary': 'getRoundSummary',
    'all_rounds_summary': 'getAllRoundsSummary',
    'round_participants_count': 'getRoundParticipantsCount',
    'aggregated_stats': 'getAggregatedStats',
    'owner': 'getOwner',
    'stopped': 'getStopped',
}

const OnionAuction_receivers: ABIReceiver[] = [
    {"receiver":"internal","message":{"kind":"text","text":"Deploy"}},
    {"receiver":"internal","message":{"kind":"typed","type":"SetUSDTAddress"}},
    {"receiver":"internal","message":{"kind":"typed","type":"SetSigningKey"}},
    {"receiver":"internal","message":{"kind":"typed","type":"SetMinPurchase"}},
    {"receiver":"internal","message":{"kind":"typed","type":"SetTreasury"}},
    {"receiver":"internal","message":{"kind":"typed","type":"WithdrawTON"}},
    {"receiver":"internal","message":{"kind":"typed","type":"WithdrawUSDT"}},
    {"receiver":"internal","message":{"kind":"typed","type":"StartAuction"}},
    {"receiver":"internal","message":{"kind":"typed","type":"UpdateRound"}},
    {"receiver":"internal","message":{"kind":"typed","type":"JettonTransferNotification"}},
    {"receiver":"internal","message":{"kind":"typed","type":"PurchaseWithSignature"}},
    {"receiver":"internal","message":{"kind":"typed","type":"ProcessRefund"}},
    {"receiver":"internal","message":{"kind":"text","text":"end_auction"}},
    {"receiver":"internal","message":{"kind":"text","text":"Stop"}},
    {"receiver":"internal","message":{"kind":"typed","type":"Deploy"}},
]

export const ERROR_UNAUTHORIZED = 62001n;
export const ERROR_PURCHASE_NOT_FOUND = 62002n;
export const ERROR_ALREADY_REFUNDED = 62003n;
export const OP_CREATE_USER_PURCHASE = 1098075148n;
export const OP_REFUND = 836089260n;
export const OP_PROCESS_REFUND = 929991442n;
export const ERROR_AUCTION_NOT_ACTIVE = 51001n;
export const ERROR_AUCTION_NOT_STARTED = 51002n;
export const ERROR_AUCTION_ENDED = 51003n;
export const ERROR_INVALID_CURRENCY = 51004n;
export const ERROR_AMOUNT_BELOW_MINIMUM = 51005n;
export const ERROR_INSUFFICIENT_TOKENS = 51006n;
export const ERROR_USDT_NOT_CONFIGURED = 51007n;
export const ERROR_INVALID_USDT_WALLET = 51008n;
export const ERROR_SIGNING_KEY_NOT_SET = 51009n;
export const ERROR_SIGNATURE_EXPIRED = 51010n;
export const ERROR_FUTURE_TIMESTAMP = 51011n;
export const ERROR_NONCE_ALREADY_USED = 51012n;
export const ERROR_INVALID_SIGNATURE = 51013n;
export const ERROR_CURRENCY_MISMATCH = 51014n;
export const ERROR_AMOUNT_MISMATCH = 51015n;
export const ERROR_USER_MISMATCH = 51016n;
export const ERROR_UNAUTHORIZED_REFUND = 51017n;
export const ERROR_MIN_PURCHASE_INVALID = 51018n;
export const ERROR_WITHDRAWAL_NOT_ALLOWED = 51019n;
export const ERROR_INSUFFICIENT_BALANCE = 51020n;
export const ERROR_INVALID_WITHDRAWAL_ADDRESS = 51021n;
export const ERROR_ROUND_SOFT_CAP_EXCEEDED = 51022n;
export const OP_PURCHASE = 3952774220n;
export const OP_START_AUCTION = 1082886929n;
export const OP_UPDATE_ROUND = 2861271945n;
export const OP_SET_USDT_ADDRESS = 2703444734n;
export const OP_PURCHASE_WITH_SIGNATURE = 1524770820n;
export const OP_SET_SIGNING_KEY = 3315975678n;
export const OP_SET_MIN_PURCHASE = 3390874056n;
export const OP_WITHDRAW_TON = 3520188881n;
export const OP_WITHDRAW_USDT = 3537031890n;
export const OP_SET_TREASURY = 3553874899n;

export class OnionAuction implements Contract {
    
    public static readonly storageReserve = 0n;
    public static readonly ROUND_DURATION = 3600n;
    public static readonly PRICE_INCREMENT = 10000000n;
    public static readonly SIGNATURE_TIMEOUT = 300n;
    public static readonly errors = OnionAuction_errors_backward;
    public static readonly opcodes = OnionAuction_opcodes;
    
    static async init(owner: Address, start_time: bigint, end_time: bigint, soft_cap: bigint, hard_cap: bigint, total_supply: bigint) {
        return await OnionAuction_init(owner, start_time, end_time, soft_cap, hard_cap, total_supply);
    }
    
    static async fromInit(owner: Address, start_time: bigint, end_time: bigint, soft_cap: bigint, hard_cap: bigint, total_supply: bigint) {
        const __gen_init = await OnionAuction_init(owner, start_time, end_time, soft_cap, hard_cap, total_supply);
        const address = contractAddress(0, __gen_init);
        return new OnionAuction(address, __gen_init);
    }
    
    static fromAddress(address: Address) {
        return new OnionAuction(address);
    }
    
    readonly address: Address; 
    readonly init?: { code: Cell, data: Cell };
    readonly abi: ContractABI = {
        types:  OnionAuction_types,
        getters: OnionAuction_getters,
        receivers: OnionAuction_receivers,
        errors: OnionAuction_errors,
    };
    
    constructor(address: Address, init?: { code: Cell, data: Cell }) {
        this.address = address;
        this.init = init;
    }
    
    async send(provider: ContractProvider, via: Sender, args: { value: bigint, bounce?: boolean| null | undefined }, message: "Deploy" | SetUSDTAddress | SetSigningKey | SetMinPurchase | SetTreasury | WithdrawTON | WithdrawUSDT | StartAuction | UpdateRound | JettonTransferNotification | PurchaseWithSignature | ProcessRefund | "end_auction" | "Stop" | Deploy) {
        
        let body: Cell | null = null;
        if (message === "Deploy") {
            body = beginCell().storeUint(0, 32).storeStringTail(message).endCell();
        }
        if (message && typeof message === 'object' && !(message instanceof Slice) && message.$$type === 'SetUSDTAddress') {
            body = beginCell().store(storeSetUSDTAddress(message)).endCell();
        }
        if (message && typeof message === 'object' && !(message instanceof Slice) && message.$$type === 'SetSigningKey') {
            body = beginCell().store(storeSetSigningKey(message)).endCell();
        }
        if (message && typeof message === 'object' && !(message instanceof Slice) && message.$$type === 'SetMinPurchase') {
            body = beginCell().store(storeSetMinPurchase(message)).endCell();
        }
        if (message && typeof message === 'object' && !(message instanceof Slice) && message.$$type === 'SetTreasury') {
            body = beginCell().store(storeSetTreasury(message)).endCell();
        }
        if (message && typeof message === 'object' && !(message instanceof Slice) && message.$$type === 'WithdrawTON') {
            body = beginCell().store(storeWithdrawTON(message)).endCell();
        }
        if (message && typeof message === 'object' && !(message instanceof Slice) && message.$$type === 'WithdrawUSDT') {
            body = beginCell().store(storeWithdrawUSDT(message)).endCell();
        }
        if (message && typeof message === 'object' && !(message instanceof Slice) && message.$$type === 'StartAuction') {
            body = beginCell().store(storeStartAuction(message)).endCell();
        }
        if (message && typeof message === 'object' && !(message instanceof Slice) && message.$$type === 'UpdateRound') {
            body = beginCell().store(storeUpdateRound(message)).endCell();
        }
        if (message && typeof message === 'object' && !(message instanceof Slice) && message.$$type === 'JettonTransferNotification') {
            body = beginCell().store(storeJettonTransferNotification(message)).endCell();
        }
        if (message && typeof message === 'object' && !(message instanceof Slice) && message.$$type === 'PurchaseWithSignature') {
            body = beginCell().store(storePurchaseWithSignature(message)).endCell();
        }
        if (message && typeof message === 'object' && !(message instanceof Slice) && message.$$type === 'ProcessRefund') {
            body = beginCell().store(storeProcessRefund(message)).endCell();
        }
        if (message === "end_auction") {
            body = beginCell().storeUint(0, 32).storeStringTail(message).endCell();
        }
        if (message === "Stop") {
            body = beginCell().storeUint(0, 32).storeStringTail(message).endCell();
        }
        if (message && typeof message === 'object' && !(message instanceof Slice) && message.$$type === 'Deploy') {
            body = beginCell().store(storeDeploy(message)).endCell();
        }
        if (body === null) { throw new Error('Invalid message type'); }
        
        await provider.internal(via, { ...args, body: body });
        
    }
    
    async getAuctionInfo(provider: ContractProvider) {
        const builder = new TupleBuilder();
        const source = (await provider.get('auction_info', builder.build())).stack;
        const result = loadGetterTupleAuctionConfig(source);
        return result;
    }
    
    async getCurrentRound(provider: ContractProvider) {
        const builder = new TupleBuilder();
        const source = (await provider.get('current_round', builder.build())).stack;
        const result = source.readBigNumber();
        return result;
    }
    
    async getCurrentPrice(provider: ContractProvider) {
        const builder = new TupleBuilder();
        const source = (await provider.get('current_price', builder.build())).stack;
        const result = source.readBigNumber();
        return result;
    }
    
    async getTotalRaised(provider: ContractProvider) {
        const builder = new TupleBuilder();
        const source = (await provider.get('total_raised', builder.build())).stack;
        const result = source.readBigNumber();
        return result;
    }
    
    async getTotalTokensSold(provider: ContractProvider) {
        const builder = new TupleBuilder();
        const source = (await provider.get('total_tokens_sold', builder.build())).stack;
        const result = source.readBigNumber();
        return result;
    }
    
    async getAuctionStatus(provider: ContractProvider) {
        const builder = new TupleBuilder();
        const source = (await provider.get('auction_status', builder.build())).stack;
        const result = source.readBigNumber();
        return result;
    }
    
    async getPurchaseCount(provider: ContractProvider) {
        const builder = new TupleBuilder();
        const source = (await provider.get('purchase_count', builder.build())).stack;
        const result = source.readBigNumber();
        return result;
    }
    
    async getUserPurchaseAddress(provider: ContractProvider, user: Address) {
        const builder = new TupleBuilder();
        builder.writeAddress(user);
        const source = (await provider.get('user_purchase_address', builder.build())).stack;
        const result = source.readAddress();
        return result;
    }
    
    async getRemainingTokens(provider: ContractProvider) {
        const builder = new TupleBuilder();
        const source = (await provider.get('remaining_tokens', builder.build())).stack;
        const result = source.readBigNumber();
        return result;
    }
    
    async getIsAuctionActive(provider: ContractProvider) {
        const builder = new TupleBuilder();
        const source = (await provider.get('is_auction_active', builder.build())).stack;
        const result = source.readBoolean();
        return result;
    }
    
    async getUsdtConfig(provider: ContractProvider) {
        const builder = new TupleBuilder();
        const source = (await provider.get('usdt_config', builder.build())).stack;
        const result_p = source.readTupleOpt();
        const result = result_p ? loadTupleUSDTConfig(result_p) : null;
        return result;
    }
    
    async getTotalRaisedUsdt(provider: ContractProvider) {
        const builder = new TupleBuilder();
        const source = (await provider.get('total_raised_usdt', builder.build())).stack;
        const result = source.readBigNumber();
        return result;
    }
    
    async getTotalRaisedEquivalent(provider: ContractProvider) {
        const builder = new TupleBuilder();
        const source = (await provider.get('total_raised_equivalent', builder.build())).stack;
        const result = source.readBigNumber();
        return result;
    }
    
    async getTotalRaisedUsdtEquivalent(provider: ContractProvider) {
        const builder = new TupleBuilder();
        const source = (await provider.get('total_raised_usdt_equivalent', builder.build())).stack;
        const result = source.readBigNumber();
        return result;
    }
    
    async getIsUsdtEnabled(provider: ContractProvider) {
        const builder = new TupleBuilder();
        const source = (await provider.get('is_usdt_enabled', builder.build())).stack;
        const result = source.readBoolean();
        return result;
    }
    
    async getSigningPublicKey(provider: ContractProvider) {
        const builder = new TupleBuilder();
        const source = (await provider.get('signing_public_key', builder.build())).stack;
        const result = source.readBigNumber();
        return result;
    }
    
    async getSignatureTimeout(provider: ContractProvider) {
        const builder = new TupleBuilder();
        const source = (await provider.get('signature_timeout', builder.build())).stack;
        const result = source.readBigNumber();
        return result;
    }
    
    async getIsNonceUsed(provider: ContractProvider, nonce: bigint) {
        const builder = new TupleBuilder();
        builder.writeNumber(nonce);
        const source = (await provider.get('is_nonce_used', builder.build())).stack;
        const result = source.readBoolean();
        return result;
    }
    
    async getIsSignatureVerificationEnabled(provider: ContractProvider) {
        const builder = new TupleBuilder();
        const source = (await provider.get('is_signature_verification_enabled', builder.build())).stack;
        const result = source.readBoolean();
        return result;
    }
    
    async getMinPurchase(provider: ContractProvider) {
        const builder = new TupleBuilder();
        const source = (await provider.get('min_purchase', builder.build())).stack;
        const result = source.readBigNumber();
        return result;
    }
    
    async getTreasuryAddress(provider: ContractProvider) {
        const builder = new TupleBuilder();
        const source = (await provider.get('treasury_address', builder.build())).stack;
        const result = source.readAddressOpt();
        return result;
    }
    
    async getWithdrawableTon(provider: ContractProvider) {
        const builder = new TupleBuilder();
        const source = (await provider.get('withdrawable_ton', builder.build())).stack;
        const result = source.readBigNumber();
        return result;
    }
    
    async getWithdrawableUsdt(provider: ContractProvider) {
        const builder = new TupleBuilder();
        const source = (await provider.get('withdrawable_usdt', builder.build())).stack;
        const result = source.readBigNumber();
        return result;
    }
    
    async getCanWithdraw(provider: ContractProvider) {
        const builder = new TupleBuilder();
        const source = (await provider.get('can_withdraw', builder.build())).stack;
        const result = source.readBoolean();
        return result;
    }
    
    async getWithdrawalSummary(provider: ContractProvider) {
        const builder = new TupleBuilder();
        const source = (await provider.get('withdrawal_summary', builder.build())).stack;
        const result = Dictionary.loadDirect(Dictionary.Keys.BigInt(257), Dictionary.Values.BigInt(257), source.readCellOpt());
        return result;
    }
    
    async getRoundStats(provider: ContractProvider, round_number: bigint) {
        const builder = new TupleBuilder();
        builder.writeNumber(round_number);
        const source = (await provider.get('round_stats', builder.build())).stack;
        const result_p = source.readTupleOpt();
        const result = result_p ? loadTupleRoundStats(result_p) : null;
        return result;
    }
    
    async getCurrentRoundStats(provider: ContractProvider) {
        const builder = new TupleBuilder();
        const source = (await provider.get('current_round_stats', builder.build())).stack;
        const result_p = source.readTupleOpt();
        const result = result_p ? loadTupleRoundStats(result_p) : null;
        return result;
    }
    
    async getTotalRounds(provider: ContractProvider) {
        const builder = new TupleBuilder();
        const source = (await provider.get('total_rounds', builder.build())).stack;
        const result = source.readBigNumber();
        return result;
    }
    
    async getRoundSummary(provider: ContractProvider, round_number: bigint) {
        const builder = new TupleBuilder();
        builder.writeNumber(round_number);
        const source = (await provider.get('round_summary', builder.build())).stack;
        const result_p = source.readTupleOpt();
        const result = result_p ? loadTupleRoundStats(result_p) : null;
        return result;
    }
    
    async getAllRoundsSummary(provider: ContractProvider) {
        const builder = new TupleBuilder();
        const source = (await provider.get('all_rounds_summary', builder.build())).stack;
        const result = Dictionary.loadDirect(Dictionary.Keys.BigInt(257), dictValueParserRoundStats(), source.readCellOpt());
        return result;
    }
    
    async getRoundParticipantsCount(provider: ContractProvider, round_number: bigint) {
        const builder = new TupleBuilder();
        builder.writeNumber(round_number);
        const source = (await provider.get('round_participants_count', builder.build())).stack;
        const result = source.readBigNumber();
        return result;
    }
    
    async getAggregatedStats(provider: ContractProvider) {
        const builder = new TupleBuilder();
        const source = (await provider.get('aggregated_stats', builder.build())).stack;
        const result = loadGetterTupleRoundStats(source);
        return result;
    }
    
    async getOwner(provider: ContractProvider) {
        const builder = new TupleBuilder();
        const source = (await provider.get('owner', builder.build())).stack;
        const result = source.readAddress();
        return result;
    }
    
    async getStopped(provider: ContractProvider) {
        const builder = new TupleBuilder();
        const source = (await provider.get('stopped', builder.build())).stack;
        const result = source.readBoolean();
        return result;
    }
    
}