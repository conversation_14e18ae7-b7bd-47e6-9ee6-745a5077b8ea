# Tact compilation report
Contract: OnionAuction
BoC Size: 15609 bytes

## Structures (Structs and Messages)
Total structures: 51

### DataSize
TL-B: `_ cells:int257 bits:int257 refs:int257 = DataSize`
Signature: `DataSize{cells:int257,bits:int257,refs:int257}`

### SignedBundle
TL-B: `_ signature:fixed_bytes64 signedData:remainder<slice> = SignedBundle`
Signature: `SignedBundle{signature:fixed_bytes64,signedData:remainder<slice>}`

### StateInit
TL-B: `_ code:^cell data:^cell = StateInit`
Signature: `StateInit{code:^cell,data:^cell}`

### Context
TL-B: `_ bounceable:bool sender:address value:int257 raw:^slice = Context`
Signature: `Context{bounceable:bool,sender:address,value:int257,raw:^slice}`

### SendParameters
TL-B: `_ mode:int257 body:Maybe ^cell code:Maybe ^cell data:Maybe ^cell value:int257 to:address bounce:bool = SendParameters`
Signature: `SendParameters{mode:int257,body:Maybe ^cell,code:Maybe ^cell,data:Maybe ^cell,value:int257,to:address,bounce:bool}`

### MessageParameters
TL-B: `_ mode:int257 body:Maybe ^cell value:int257 to:address bounce:bool = MessageParameters`
Signature: `MessageParameters{mode:int257,body:Maybe ^cell,value:int257,to:address,bounce:bool}`

### DeployParameters
TL-B: `_ mode:int257 body:Maybe ^cell value:int257 bounce:bool init:StateInit{code:^cell,data:^cell} = DeployParameters`
Signature: `DeployParameters{mode:int257,body:Maybe ^cell,value:int257,bounce:bool,init:StateInit{code:^cell,data:^cell}}`

### StdAddress
TL-B: `_ workchain:int8 address:uint256 = StdAddress`
Signature: `StdAddress{workchain:int8,address:uint256}`

### VarAddress
TL-B: `_ workchain:int32 address:^slice = VarAddress`
Signature: `VarAddress{workchain:int32,address:^slice}`

### BasechainAddress
TL-B: `_ hash:Maybe int257 = BasechainAddress`
Signature: `BasechainAddress{hash:Maybe int257}`

### ChangeOwner
TL-B: `change_owner#819dbe99 queryId:uint64 newOwner:address = ChangeOwner`
Signature: `ChangeOwner{queryId:uint64,newOwner:address}`

### ChangeOwnerOk
TL-B: `change_owner_ok#327b2b4a queryId:uint64 newOwner:address = ChangeOwnerOk`
Signature: `ChangeOwnerOk{queryId:uint64,newOwner:address}`

### Deploy
TL-B: `deploy#946a98b6 queryId:uint64 = Deploy`
Signature: `Deploy{queryId:uint64}`

### DeployOk
TL-B: `deploy_ok#aff90f57 queryId:uint64 = DeployOk`
Signature: `DeployOk{queryId:uint64}`

### FactoryDeploy
TL-B: `factory_deploy#6d0ff13b queryId:uint64 cashback:address = FactoryDeploy`
Signature: `FactoryDeploy{queryId:uint64,cashback:address}`

### JettonTransfer
TL-B: `jetton_transfer#0f8a7ea5 query_id:uint64 amount:coins destination:address response_destination:address custom_payload:Maybe ^cell forward_ton_amount:coins forward_payload:Maybe ^cell = JettonTransfer`
Signature: `JettonTransfer{query_id:uint64,amount:coins,destination:address,response_destination:address,custom_payload:Maybe ^cell,forward_ton_amount:coins,forward_payload:Maybe ^cell}`

### JettonTransferNotification
TL-B: `jetton_transfer_notification#7362d09c query_id:uint64 amount:coins sender:address forward_payload:Maybe ^cell = JettonTransferNotification`
Signature: `JettonTransferNotification{query_id:uint64,amount:coins,sender:address,forward_payload:Maybe ^cell}`

### JettonBurn
TL-B: `jetton_burn#595f07bc query_id:uint64 amount:coins response_destination:address custom_payload:Maybe ^cell = JettonBurn`
Signature: `JettonBurn{query_id:uint64,amount:coins,response_destination:address,custom_payload:Maybe ^cell}`

### JettonExcesses
TL-B: `jetton_excesses#d53276db query_id:uint64 = JettonExcesses`
Signature: `JettonExcesses{query_id:uint64}`

### JettonInternalTransfer
TL-B: `jetton_internal_transfer#178d4519 query_id:uint64 amount:coins from:address response_address:address forward_ton_amount:coins forward_payload:Maybe ^cell = JettonInternalTransfer`
Signature: `JettonInternalTransfer{query_id:uint64,amount:coins,from:address,response_address:address,forward_ton_amount:coins,forward_payload:Maybe ^cell}`

### JettonBurnNotification
TL-B: `jetton_burn_notification#7bdd97de query_id:uint64 amount:coins sender:address response_destination:address = JettonBurnNotification`
Signature: `JettonBurnNotification{query_id:uint64,amount:coins,sender:address,response_destination:address}`

### WalletData
TL-B: `_ balance:coins owner:address jetton:address jetton_wallet_code:^cell = WalletData`
Signature: `WalletData{balance:coins,owner:address,jetton:address,jetton_wallet_code:^cell}`

### CreateUserPurchase
TL-B: `create_user_purchase#41734c0c user:address amount:coins tokens:coins currency:uint8 purchase_method:uint8 nonce:uint64 round_number:uint32 usdt_equivalent_amount:coins = CreateUserPurchase`
Signature: `CreateUserPurchase{user:address,amount:coins,tokens:coins,currency:uint8,purchase_method:uint8,nonce:uint64,round_number:uint32,usdt_equivalent_amount:coins}`

### Refund
TL-B: `refund#31d5b5ac purchase_id:uint32 = Refund`
Signature: `Refund{purchase_id:uint32}`

### ProcessRefund
TL-B: `process_refund#376e8b12 user:address amount:coins fee:coins currency:uint8 round_number:uint32 usdt_equivalent_amount:coins = ProcessRefund`
Signature: `ProcessRefund{user:address,amount:coins,fee:coins,currency:uint8,round_number:uint32,usdt_equivalent_amount:coins}`

### PurchaseRecord
TL-B: `_ id:uint32 user:address amount:coins tokens:coins timestamp:uint64 currency:uint8 purchase_method:uint8 nonce:uint64 round_number:uint32 usdt_equivalent_amount:coins = PurchaseRecord`
Signature: `PurchaseRecord{id:uint32,user:address,amount:coins,tokens:coins,timestamp:uint64,currency:uint8,purchase_method:uint8,nonce:uint64,round_number:uint32,usdt_equivalent_amount:coins}`

### UserPurchase$Data
TL-B: `_ owner:address auction_address:address user_address:address total_purchased:coins total_paid:coins purchase_history:dict<int, ^PurchaseRecord{id:uint32,user:address,amount:coins,tokens:coins,timestamp:uint64,currency:uint8,purchase_method:uint8,nonce:uint64,round_number:uint32,usdt_equivalent_amount:coins}> refund_history:dict<int, int> purchase_id_counter:uint32 participated_rounds:dict<int, bool> = UserPurchase`
Signature: `UserPurchase{owner:address,auction_address:address,user_address:address,total_purchased:coins,total_paid:coins,purchase_history:dict<int, ^PurchaseRecord{id:uint32,user:address,amount:coins,tokens:coins,timestamp:uint64,currency:uint8,purchase_method:uint8,nonce:uint64,round_number:uint32,usdt_equivalent_amount:coins}>,refund_history:dict<int, int>,purchase_id_counter:uint32,participated_rounds:dict<int, bool>}`

### StartAuction
TL-B: `start_auction#408b8b11 start_time:uint64 end_time:uint64 soft_cap:coins hard_cap:coins initial_price:coins = StartAuction`
Signature: `StartAuction{start_time:uint64,end_time:uint64,soft_cap:coins,hard_cap:coins,initial_price:coins}`

### UpdateRound
TL-B: `update_round#aa8b8b89 new_price:coins round_number:uint32 = UpdateRound`
Signature: `UpdateRound{new_price:coins,round_number:uint32}`

### SetUSDTAddress
TL-B: `set_usdt_address#a1234afe usdt_master:address usdt_wallet:address = SetUSDTAddress`
Signature: `SetUSDTAddress{usdt_master:address,usdt_wallet:address}`

### PurchaseWithSignature
TL-B: `purchase_with_signature#5ae22804 calculation:PurchaseCalculation{user:address,amount:coins,currency:uint8,tokens_to_receive:coins,current_price:coins,current_round:uint32,timestamp:uint64,nonce:uint64,usdt_equivalent_amount:coins} signature:^slice = PurchaseWithSignature`
Signature: `PurchaseWithSignature{calculation:PurchaseCalculation{user:address,amount:coins,currency:uint8,tokens_to_receive:coins,current_price:coins,current_round:uint32,timestamp:uint64,nonce:uint64,usdt_equivalent_amount:coins},signature:^slice}`

### SetSigningKey
TL-B: `set_signing_key#c5a5c5fe public_key:uint256 = SetSigningKey`
Signature: `SetSigningKey{public_key:uint256}`

### SetMinPurchase
TL-B: `set_min_purchase#ca1ca1c8 min_purchase:coins = SetMinPurchase`
Signature: `SetMinPurchase{min_purchase:coins}`

### WithdrawTON
TL-B: `withdraw_ton#d1d1d1d1 amount:coins destination:address = WithdrawTON`
Signature: `WithdrawTON{amount:coins,destination:address}`

### WithdrawUSDT
TL-B: `withdraw_usdt#d2d2d2d2 amount:coins destination:address = WithdrawUSDT`
Signature: `WithdrawUSDT{amount:coins,destination:address}`

### SetTreasury
TL-B: `set_treasury#d3d3d3d3 treasury_address:address = SetTreasury`
Signature: `SetTreasury{treasury_address:address}`

### AuctionStarted
TL-B: `auction_started#8425c8b5 start_time:uint64 end_time:uint64 soft_cap:coins hard_cap:coins initial_price:coins total_supply:coins = AuctionStarted`
Signature: `AuctionStarted{start_time:uint64,end_time:uint64,soft_cap:coins,hard_cap:coins,initial_price:coins,total_supply:coins}`

### PurchaseCompleted
TL-B: `purchase_completed#ecf65aa1 user:address amount:coins tokens_received:coins currency:uint8 purchase_method:uint8 round_number:uint32 nonce:uint64 total_raised:coins total_raised_usdt:coins total_tokens_sold:coins = PurchaseCompleted`
Signature: `PurchaseCompleted{user:address,amount:coins,tokens_received:coins,currency:uint8,purchase_method:uint8,round_number:uint32,nonce:uint64,total_raised:coins,total_raised_usdt:coins,total_tokens_sold:coins}`

### USDTConfigured
TL-B: `usdt_configured#3d775bac usdt_master:address usdt_wallet:address configured_by:address = USDTConfigured`
Signature: `USDTConfigured{usdt_master:address,usdt_wallet:address,configured_by:address}`

### SigningKeySet
TL-B: `signing_key_set#9181ee30 public_key:uint256 set_by:address = SigningKeySet`
Signature: `SigningKeySet{public_key:uint256,set_by:address}`

### MinPurchaseUpdated
TL-B: `min_purchase_updated#2d589fc8 old_min_purchase:coins new_min_purchase:coins updated_by:address = MinPurchaseUpdated`
Signature: `MinPurchaseUpdated{old_min_purchase:coins,new_min_purchase:coins,updated_by:address}`

### TreasurySet
TL-B: `treasury_set#ef3aa766 old_treasury:address new_treasury:address set_by:address = TreasurySet`
Signature: `TreasurySet{old_treasury:address,new_treasury:address,set_by:address}`

### FundsWithdrawn
TL-B: `funds_withdrawn#db0ab6cf currency:uint8 amount:coins destination:address withdrawn_by:address remaining_balance:coins = FundsWithdrawn`
Signature: `FundsWithdrawn{currency:uint8,amount:coins,destination:address,withdrawn_by:address,remaining_balance:coins}`

### RoundUpdated
TL-B: `round_updated#0b542a9b old_round:uint32 new_round:uint32 old_price:coins new_price:coins updated_by:address = RoundUpdated`
Signature: `RoundUpdated{old_round:uint32,new_round:uint32,old_price:coins,new_price:coins,updated_by:address}`

### AuctionEnded
TL-B: `auction_ended#17b62b69 end_reason:uint8 final_status:uint8 total_raised:coins total_raised_usdt:coins total_tokens_sold:coins end_time:uint64 = AuctionEnded`
Signature: `AuctionEnded{end_reason:uint8,final_status:uint8,total_raised:coins,total_raised_usdt:coins,total_tokens_sold:coins,end_time:uint64}`

### AuctionConfig
TL-B: `_ start_time:uint64 end_time:uint64 soft_cap:coins hard_cap:coins total_supply:coins refund_fee_percent:uint8 = AuctionConfig`
Signature: `AuctionConfig{start_time:uint64,end_time:uint64,soft_cap:coins,hard_cap:coins,total_supply:coins,refund_fee_percent:uint8}`

### RoundStats
TL-B: `_ round_number:uint32 start_time:uint64 end_time:uint64 price:coins total_raised_ton:coins total_raised_usdt:coins raised_usdt_equivalent:coins tokens_sold:coins purchase_count:uint32 unique_users:uint32 refund_count:uint32 refunded_amount_ton:coins refunded_amount_usdt:coins refunded_usdt_equivalent:coins = RoundStats`
Signature: `RoundStats{round_number:uint32,start_time:uint64,end_time:uint64,price:coins,total_raised_ton:coins,total_raised_usdt:coins,raised_usdt_equivalent:coins,tokens_sold:coins,purchase_count:uint32,unique_users:uint32,refund_count:uint32,refunded_amount_ton:coins,refunded_amount_usdt:coins,refunded_usdt_equivalent:coins}`

### USDTConfig
TL-B: `_ master_address:address wallet_address:address decimals:uint8 = USDTConfig`
Signature: `USDTConfig{master_address:address,wallet_address:address,decimals:uint8}`

### PurchaseCalculation
TL-B: `_ user:address amount:coins currency:uint8 tokens_to_receive:coins current_price:coins current_round:uint32 timestamp:uint64 nonce:uint64 usdt_equivalent_amount:coins = PurchaseCalculation`
Signature: `PurchaseCalculation{user:address,amount:coins,currency:uint8,tokens_to_receive:coins,current_price:coins,current_round:uint32,timestamp:uint64,nonce:uint64,usdt_equivalent_amount:coins}`

### ParsedPurchaseData
TL-B: `_ calculation:PurchaseCalculation{user:address,amount:coins,currency:uint8,tokens_to_receive:coins,current_price:coins,current_round:uint32,timestamp:uint64,nonce:uint64,usdt_equivalent_amount:coins} signature:^slice = ParsedPurchaseData`
Signature: `ParsedPurchaseData{calculation:PurchaseCalculation{user:address,amount:coins,currency:uint8,tokens_to_receive:coins,current_price:coins,current_round:uint32,timestamp:uint64,nonce:uint64,usdt_equivalent_amount:coins},signature:^slice}`

### OnionAuction$Data
TL-B: `_ owner:address stopped:bool auction_config:AuctionConfig{start_time:uint64,end_time:uint64,soft_cap:coins,hard_cap:coins,total_supply:coins,refund_fee_percent:uint8} current_round:uint32 current_price:coins total_raised:coins total_tokens_sold:coins auction_status:uint8 usdt_config:Maybe USDTConfig{master_address:address,wallet_address:address,decimals:uint8} total_raised_usdt:coins total_raised_usdt_equivalent:coins purchase_count:uint32 round_stats:dict<int, ^RoundStats{round_number:uint32,start_time:uint64,end_time:uint64,price:coins,total_raised_ton:coins,total_raised_usdt:coins,raised_usdt_equivalent:coins,tokens_sold:coins,purchase_count:uint32,unique_users:uint32,refund_count:uint32,refunded_amount_ton:coins,refunded_amount_usdt:coins,refunded_usdt_equivalent:coins}> user_round_purchases:dict<address, int> signing_public_key:uint256 used_nonces:dict<int, bool> signature_timeout:uint64 min_purchase:coins treasury_address:address = OnionAuction`
Signature: `OnionAuction{owner:address,stopped:bool,auction_config:AuctionConfig{start_time:uint64,end_time:uint64,soft_cap:coins,hard_cap:coins,total_supply:coins,refund_fee_percent:uint8},current_round:uint32,current_price:coins,total_raised:coins,total_tokens_sold:coins,auction_status:uint8,usdt_config:Maybe USDTConfig{master_address:address,wallet_address:address,decimals:uint8},total_raised_usdt:coins,total_raised_usdt_equivalent:coins,purchase_count:uint32,round_stats:dict<int, ^RoundStats{round_number:uint32,start_time:uint64,end_time:uint64,price:coins,total_raised_ton:coins,total_raised_usdt:coins,raised_usdt_equivalent:coins,tokens_sold:coins,purchase_count:uint32,unique_users:uint32,refund_count:uint32,refunded_amount_ton:coins,refunded_amount_usdt:coins,refunded_usdt_equivalent:coins}>,user_round_purchases:dict<address, int>,signing_public_key:uint256,used_nonces:dict<int, bool>,signature_timeout:uint64,min_purchase:coins,treasury_address:address}`

## Get methods
Total get methods: 34

## auction_info
No arguments

## current_round
No arguments

## current_price
No arguments

## total_raised
No arguments

## total_tokens_sold
No arguments

## auction_status
No arguments

## purchase_count
No arguments

## user_purchase_address
Argument: user

## remaining_tokens
No arguments

## is_auction_active
No arguments

## usdt_config
No arguments

## total_raised_usdt
No arguments

## total_raised_equivalent
No arguments

## total_raised_usdt_equivalent
No arguments

## is_usdt_enabled
No arguments

## signing_public_key
No arguments

## signature_timeout
No arguments

## is_nonce_used
Argument: nonce

## is_signature_verification_enabled
No arguments

## min_purchase
No arguments

## treasury_address
No arguments

## withdrawable_ton
No arguments

## withdrawable_usdt
No arguments

## can_withdraw
No arguments

## withdrawal_summary
No arguments

## round_stats
Argument: round_number

## current_round_stats
No arguments

## total_rounds
No arguments

## round_summary
Argument: round_number

## all_rounds_summary
No arguments

## round_participants_count
Argument: round_number

## aggregated_stats
No arguments

## owner
No arguments

## stopped
No arguments

## Exit codes
* 2: Stack underflow
* 3: Stack overflow
* 4: Integer overflow
* 5: Integer out of expected range
* 6: Invalid opcode
* 7: Type check error
* 8: Cell overflow
* 9: Cell underflow
* 10: Dictionary error
* 11: 'Unknown' error
* 12: Fatal error
* 13: Out of gas error
* 14: Virtualization error
* 32: Action list is invalid
* 33: Action list is too long
* 34: Action is invalid or not supported
* 35: Invalid source address in outbound message
* 36: Invalid destination address in outbound message
* 37: Not enough Toncoin
* 38: Not enough extra currencies
* 39: Outbound message does not fit into a cell after rewriting
* 40: Cannot process a message
* 41: Library reference is null
* 42: Library change action error
* 43: Exceeded maximum number of cells in the library or the maximum depth of the Merkle tree
* 50: Account state size exceeded limits
* 128: Null reference exception
* 129: Invalid serialization prefix
* 130: Invalid incoming message
* 131: Constraints error
* 132: Access denied
* 133: Contract stopped
* 134: Invalid argument
* 135: Code of a contract was not found
* 136: Invalid standard address
* 138: Not a basechain address
* 2296: JettonWallet: Only Jetton master or Jetton wallet can call this function
* 13105: JettonWallet: Not enough jettons to transfer
* 22411: Already refunded
* 27831: Only owner can call this function
* 29133: JettonWallet: Not allow negative balance after internal transfer
* 37185: Not enough funds to transfer
* 39144: Purchase not found
* 47048: JettonWallet: Only owner can burn tokens
* 49729: Unauthorized
* 53296: Contract not stopped
* 60354: JettonWallet: Not enough balance to burn tokens

## Trait inheritance diagram

```mermaid
graph TD
OnionAuction
OnionAuction --> BaseTrait
OnionAuction --> Ownable
Ownable --> BaseTrait
OnionAuction --> Stoppable
Stoppable --> Ownable
Stoppable --> BaseTrait
OnionAuction --> Deployable
Deployable --> BaseTrait
```

## Contract dependency diagram

```mermaid
graph TD
OnionAuction
OnionAuction --> UserPurchase
```