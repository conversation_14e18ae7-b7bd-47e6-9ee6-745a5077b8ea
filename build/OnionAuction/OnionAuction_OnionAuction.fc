#pragma version =0.4.6;
#pragma allow-post-modification;
#pragma compute-asm-ltr;

;; OnionAuction_OnionAuction.headers.fc
;;
;; Header files for OnionAuction
;; NOTE: declarations are sorted for optimal order
;;

;; __tact_store_address_opt
builder __tact_store_address_opt(builder b, slice address) inline;

;; __tact_not_null
forall X -> X __tact_not_null(X x) impure inline;

;; __tact_context_get
(int, slice, int, slice) __tact_context_get() inline;

;; __tact_context_get_sender
slice __tact_context_get_sender() inline;

;; __tact_dict_get_slice_int
int __tact_dict_get_slice_int(cell d, int kl, slice k, int vl) inline;

;; __tact_dict_set_slice_int
(cell, ()) __tact_dict_set_slice_int(cell d, int kl, slice k, int v, int vl) inline;

;; __tact_dict_get_int_int
int __tact_dict_get_int_int(cell d, int kl, int k, int vl) inline;

;; __tact_dict_set_int_int
(cell, ()) __tact_dict_set_int_int(cell d, int kl, int k, int v, int vl) inline;

;; __tact_dict_get_int_cell
cell __tact_dict_get_int_cell(cell d, int kl, int k) inline;

;; __tact_dict_set_int_cell
(cell, ()) __tact_dict_set_int_cell(cell d, int kl, int k, cell v) inline;

;; $DeployOk$_store
builder $DeployOk$_store(builder build_0, (int) v) inline;

;; $DeployOk$_store_cell
cell $DeployOk$_store_cell((int) v, builder b) inline;

;; $JettonTransfer$_store
builder $JettonTransfer$_store(builder build_0, (int, int, slice, slice, cell, int, cell) v) inline;

;; $JettonTransfer$_store_cell
cell $JettonTransfer$_store_cell((int, int, slice, slice, cell, int, cell) v, builder b) inline;

;; $CreateUserPurchase$_store
builder $CreateUserPurchase$_store(builder build_0, (slice, int, int, int, int, int, int, int) v) inline;

;; $CreateUserPurchase$_store_cell
cell $CreateUserPurchase$_store_cell((slice, int, int, int, int, int, int, int) v, builder b) inline;

;; $PurchaseCalculation$_load
(slice, ((slice, int, int, int, int, int, int, int, int))) $PurchaseCalculation$_load(slice sc_0) inline;

;; $AuctionStarted$_store
builder $AuctionStarted$_store(builder build_0, (int, int, int, int, int, int) v) inline;

;; $AuctionStarted$_store_cell
cell $AuctionStarted$_store_cell((int, int, int, int, int, int) v, builder b) inline;

;; $PurchaseCompleted$_store
builder $PurchaseCompleted$_store(builder build_0, (slice, int, int, int, int, int, int, int, int, int) v) inline;

;; $PurchaseCompleted$_store_cell
cell $PurchaseCompleted$_store_cell((slice, int, int, int, int, int, int, int, int, int) v, builder b) inline;

;; $USDTConfigured$_store
builder $USDTConfigured$_store(builder build_0, (slice, slice, slice) v) inline;

;; $USDTConfigured$_store_cell
cell $USDTConfigured$_store_cell((slice, slice, slice) v, builder b) inline;

;; $SigningKeySet$_store
builder $SigningKeySet$_store(builder build_0, (int, slice) v) inline;

;; $SigningKeySet$_store_cell
cell $SigningKeySet$_store_cell((int, slice) v, builder b) inline;

;; $MinPurchaseUpdated$_store
builder $MinPurchaseUpdated$_store(builder build_0, (int, int, slice) v) inline;

;; $MinPurchaseUpdated$_store_cell
cell $MinPurchaseUpdated$_store_cell((int, int, slice) v, builder b) inline;

;; $TreasurySet$_store
builder $TreasurySet$_store(builder build_0, (slice, slice, slice) v) inline;

;; $TreasurySet$_store_cell
cell $TreasurySet$_store_cell((slice, slice, slice) v, builder b) inline;

;; $FundsWithdrawn$_store
builder $FundsWithdrawn$_store(builder build_0, (int, int, slice, slice, int) v) inline;

;; $FundsWithdrawn$_store_cell
cell $FundsWithdrawn$_store_cell((int, int, slice, slice, int) v, builder b) inline;

;; $RoundUpdated$_store
builder $RoundUpdated$_store(builder build_0, (int, int, int, int, slice) v) inline;

;; $RoundUpdated$_store_cell
cell $RoundUpdated$_store_cell((int, int, int, int, slice) v, builder b) inline;

;; $AuctionEnded$_store
builder $AuctionEnded$_store(builder build_0, (int, int, int, int, int, int) v) inline;

;; $AuctionEnded$_store_cell
cell $AuctionEnded$_store_cell((int, int, int, int, int, int) v, builder b) inline;

;; $AuctionConfig$_store
builder $AuctionConfig$_store(builder build_0, (int, int, int, int, int, int) v) inline;

;; $AuctionConfig$_load
(slice, ((int, int, int, int, int, int))) $AuctionConfig$_load(slice sc_0) inline;

;; $RoundStats$_store
builder $RoundStats$_store(builder build_0, (int, int, int, int, int, int, int, int, int, int, int, int, int, int) v) inline_ref;

;; $RoundStats$_store_cell
cell $RoundStats$_store_cell((int, int, int, int, int, int, int, int, int, int, int, int, int, int) v, builder b) inline;

;; $RoundStats$_load
(slice, ((int, int, int, int, int, int, int, int, int, int, int, int, int, int))) $RoundStats$_load(slice sc_0) inline_ref;

;; $RoundStats$_as_optional
tuple $RoundStats$_as_optional((int, int, int, int, int, int, int, int, int, int, int, int, int, int) v) inline;

;; $RoundStats$_load_opt
tuple $RoundStats$_load_opt(cell cl) inline;

;; $USDTConfig$_store
builder $USDTConfig$_store(builder build_0, (slice, slice, int) v) inline;

;; $USDTConfig$_load
(slice, ((slice, slice, int))) $USDTConfig$_load(slice sc_0) inline;

;; $USDTConfig$_not_null
((slice, slice, int)) $USDTConfig$_not_null(tuple v) inline;

;; $OnionAuction$_store
builder $OnionAuction$_store(builder build_0, (slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) v) inline;

;; $USDTConfig$_as_optional
tuple $USDTConfig$_as_optional((slice, slice, int) v) inline;

;; $OnionAuction$_load
(slice, ((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice))) $OnionAuction$_load(slice sc_0) inline;

;; $Context$_get_value
_ $Context$_get_value((int, slice, int, slice) v) inline;

;; $AuctionConfig$_to_external
(int, int, int, int, int, int) $AuctionConfig$_to_external(((int, int, int, int, int, int)) v) inline;

;; $RoundStats$_get_tokens_sold
_ $RoundStats$_get_tokens_sold((int, int, int, int, int, int, int, int, int, int, int, int, int, int) v) inline;

;; $RoundStats$_get_unique_users
_ $RoundStats$_get_unique_users((int, int, int, int, int, int, int, int, int, int, int, int, int, int) v) inline;

;; $RoundStats$_not_null
((int, int, int, int, int, int, int, int, int, int, int, int, int, int)) $RoundStats$_not_null(tuple v) inline;

;; $RoundStats$_to_tuple
tuple $RoundStats$_to_tuple(((int, int, int, int, int, int, int, int, int, int, int, int, int, int)) v) inline;

;; $RoundStats$_to_opt_tuple
tuple $RoundStats$_to_opt_tuple(tuple v) inline;

;; $RoundStats$_to_external
(int, int, int, int, int, int, int, int, int, int, int, int, int, int) $RoundStats$_to_external(((int, int, int, int, int, int, int, int, int, int, int, int, int, int)) v) inline;

;; $RoundStats$_to_opt_external
tuple $RoundStats$_to_opt_external(tuple v) inline;

;; $USDTConfig$_to_tuple
tuple $USDTConfig$_to_tuple(((slice, slice, int)) v) inline;

;; $USDTConfig$_to_opt_tuple
tuple $USDTConfig$_to_opt_tuple(tuple v) inline;

;; $USDTConfig$_to_opt_external
tuple $USDTConfig$_to_opt_external(tuple v) inline;

;; $UserPurchase$init$_store
builder $UserPurchase$init$_store(builder build_0, (slice, slice) v) inline;

;; $OnionAuction$init$_load
(slice, ((slice, int, int, int, int, int))) $OnionAuction$init$_load(slice sc_0) inline;

;; $AuctionConfig$_constructor_start_time_end_time_soft_cap_hard_cap_total_supply_refund_fee_percent
((int, int, int, int, int, int)) $AuctionConfig$_constructor_start_time_end_time_soft_cap_hard_cap_total_supply_refund_fee_percent(int $start_time, int $end_time, int $soft_cap, int $hard_cap, int $total_supply, int $refund_fee_percent) inline;

;; $RoundStats$_constructor_round_number_start_time_end_time_price_total_raised_ton_total_raised_usdt_raised_usdt_equivalent_tokens_sold_purchase_count_unique_users_refund_count_refunded_amount_ton_refunded_amount_usdt_refunded_usdt_equivalent
((int, int, int, int, int, int, int, int, int, int, int, int, int, int)) $RoundStats$_constructor_round_number_start_time_end_time_price_total_raised_ton_total_raised_usdt_raised_usdt_equivalent_tokens_sold_purchase_count_unique_users_refund_count_refunded_amount_ton_refunded_amount_usdt_refunded_usdt_equivalent(int $round_number, int $start_time, int $end_time, int $price, int $total_raised_ton, int $total_raised_usdt, int $raised_usdt_equivalent, int $tokens_sold, int $purchase_count, int $unique_users, int $refund_count, int $refunded_amount_ton, int $refunded_amount_usdt, int $refunded_usdt_equivalent) inline;

;; $OnionAuction$_fun_initializeFirstRound
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), ()) $OnionAuction$_fun_initializeFirstRound((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref;

;; $OnionAuction$_contract_init
(slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $OnionAuction$_contract_init(slice $owner, int $start_time, int $end_time, int $soft_cap, int $hard_cap, int $total_supply) impure inline;

;; $OnionAuction$_contract_load
(slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $OnionAuction$_contract_load() impure inline;

;; $OnionAuction$_contract_store
() $OnionAuction$_contract_store((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) v) impure inline;

;; $Cell$_fun_asSlice
slice $Cell$_fun_asSlice(cell $self) impure inline;

;; $global_newAddress
slice $global_newAddress(int $chain, int $hash) impure inline;

;; $global_contractAddressExt
slice $global_contractAddressExt(int $chain, cell $code, cell $data) impure inline;

;; $global_contractAddress
slice $global_contractAddress((cell, cell) $s) impure inline;

;; $global_emit
() $global_emit(cell $body) impure inline;

;; $UserPurchase$_init_child
(cell, cell) $UserPurchase$_init_child(slice $auction_address, slice $user_address) inline_ref;

;; $MessageParameters$_constructor_bounce_to_value_mode_body
((int, cell, int, slice, int)) $MessageParameters$_constructor_bounce_to_value_mode_body(int $bounce, slice $to, int $value, int $mode, cell $body) inline;

;; $OnionAuction$_fun_initializeNewRound
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), ()) $OnionAuction$_fun_initializeNewRound((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self, int $round_number, int $start_time) impure inline_ref;

;; $OnionAuction$_fun_updateRoundStatsForPurchase
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), ()) $OnionAuction$_fun_updateRoundStatsForPurchase((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self, slice $user, int $amount, int $tokens, int $currency, int $usdt_equivalent_amount, int $round_number) impure inline_ref;

;; $OnionAuction$_fun_updateRoundStatsForRefund
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), ()) $OnionAuction$_fun_updateRoundStatsForRefund((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self, int $round_number, int $amount, int $currency, int $usdt_equivalent_amount) impure inline_ref;

;; $SendParameters$_constructor_to_value_bounce_body_code_data
((int, cell, cell, cell, int, slice, int)) $SendParameters$_constructor_to_value_bounce_body_code_data(slice $to, int $value, int $bounce, cell $body, cell $code, cell $data) inline;

;; $CreateUserPurchase$_constructor_user_amount_tokens_currency_purchase_method_nonce_round_number_usdt_equivalent_amount
((slice, int, int, int, int, int, int, int)) $CreateUserPurchase$_constructor_user_amount_tokens_currency_purchase_method_nonce_round_number_usdt_equivalent_amount(slice $user, int $amount, int $tokens, int $currency, int $purchase_method, int $nonce, int $round_number, int $usdt_equivalent_amount) inline;

;; $OnionAuction$_fun_getUserPurchaseInit
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), (cell, cell)) $OnionAuction$_fun_getUserPurchaseInit((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self, slice $user) impure inline_ref;

;; $OnionAuction$_fun_createOrUpdateUserPurchase
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), ()) $OnionAuction$_fun_createOrUpdateUserPurchase((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self, slice $user, int $amount, int $tokens, int $currency, int $purchase_method, int $nonce, int $usdt_equivalent_amount, int $round_number) impure inline_ref;

;; $AuctionEnded$_constructor_end_reason_final_status_total_raised_total_raised_usdt_total_tokens_sold_end_time
((int, int, int, int, int, int)) $AuctionEnded$_constructor_end_reason_final_status_total_raised_total_raised_usdt_total_tokens_sold_end_time(int $end_reason, int $final_status, int $total_raised, int $total_raised_usdt, int $total_tokens_sold, int $end_time) inline;

;; $PurchaseCompleted$_constructor_user_amount_tokens_received_currency_purchase_method_round_number_nonce_total_raised_total_raised_usdt_total_tokens_sold
((slice, int, int, int, int, int, int, int, int, int)) $PurchaseCompleted$_constructor_user_amount_tokens_received_currency_purchase_method_round_number_nonce_total_raised_total_raised_usdt_total_tokens_sold(slice $user, int $amount, int $tokens_received, int $currency, int $purchase_method, int $round_number, int $nonce, int $total_raised, int $total_raised_usdt, int $total_tokens_sold) inline;

;; $PurchaseCalculation$_constructor_user_amount_currency_tokens_to_receive_current_price_current_round_timestamp_nonce_usdt_equivalent_amount
((slice, int, int, int, int, int, int, int, int)) $PurchaseCalculation$_constructor_user_amount_currency_tokens_to_receive_current_price_current_round_timestamp_nonce_usdt_equivalent_amount(slice $user, int $amount, int $currency, int $tokens_to_receive, int $current_price, int $current_round, int $timestamp, int $nonce, int $usdt_equivalent_amount) inline;

;; $ParsedPurchaseData$_constructor_calculation_signature
(((slice, int, int, int, int, int, int, int, int), slice)) $ParsedPurchaseData$_constructor_calculation_signature((slice, int, int, int, int, int, int, int, int) $calculation, slice $signature) inline;

;; $OnionAuction$_fun_parsePurchaseFromPayload
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), ((slice, int, int, int, int, int, int, int, int), slice)) $OnionAuction$_fun_parsePurchaseFromPayload((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self, cell $payload) impure inline_ref;

;; $OnionAuction$_fun_hashPurchaseCalculation
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_hashPurchaseCalculation((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self, (slice, int, int, int, int, int, int, int, int) $calc) impure inline_ref;

;; $OnionAuction$_fun_getCurrentRoundTokensSold
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_getCurrentRoundTokensSold((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref;

;; $OnionAuction$_fun_handleUSDTWithSignature
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), ()) $OnionAuction$_fun_handleUSDTWithSignature((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self, (int, int, slice, cell) $msg) impure inline_ref;

;; $OnionAuction$_fun_getJettonWalletAddress
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), slice) $OnionAuction$_fun_getJettonWalletAddress((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self, slice $jetton_master, slice $owner) impure inline_ref;

;; $OnionAuction$_fun_auction_info
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), (int, int, int, int, int, int)) $OnionAuction$_fun_auction_info((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref;

;; $OnionAuction$_fun_current_round
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_current_round((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref;

;; $OnionAuction$_fun_current_price
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_current_price((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref;

;; $OnionAuction$_fun_total_raised
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_total_raised((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref;

;; $OnionAuction$_fun_total_tokens_sold
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_total_tokens_sold((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref;

;; $OnionAuction$_fun_auction_status
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_auction_status((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref;

;; $OnionAuction$_fun_purchase_count
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_purchase_count((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref;

;; $OnionAuction$_fun_user_purchase_address
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), slice) $OnionAuction$_fun_user_purchase_address((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self, slice $user) impure inline_ref;

;; $OnionAuction$_fun_remaining_tokens
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_remaining_tokens((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref;

;; $OnionAuction$_fun_is_auction_active
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_is_auction_active((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref;

;; $OnionAuction$_fun_usdt_config
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), tuple) $OnionAuction$_fun_usdt_config((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref;

;; $OnionAuction$_fun_total_raised_usdt
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_total_raised_usdt((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref;

;; $OnionAuction$_fun_total_raised_equivalent
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_total_raised_equivalent((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref;

;; $OnionAuction$_fun_total_raised_usdt_equivalent
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_total_raised_usdt_equivalent((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref;

;; $OnionAuction$_fun_is_usdt_enabled
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_is_usdt_enabled((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref;

;; $OnionAuction$_fun_signing_public_key
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_signing_public_key((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref;

;; $OnionAuction$_fun_signature_timeout
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_signature_timeout((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref;

;; $OnionAuction$_fun_is_nonce_used
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_is_nonce_used((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self, int $nonce) impure inline_ref;

;; $OnionAuction$_fun_is_signature_verification_enabled
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_is_signature_verification_enabled((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref;

;; $OnionAuction$_fun_min_purchase
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_min_purchase((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref;

;; $OnionAuction$_fun_treasury_address
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), slice) $OnionAuction$_fun_treasury_address((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref;

;; $OnionAuction$_fun_withdrawable_ton
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_withdrawable_ton((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref;

;; $OnionAuction$_fun_withdrawable_usdt
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_withdrawable_usdt((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref;

;; $OnionAuction$_fun_can_withdraw
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_can_withdraw((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref;

;; $OnionAuction$_fun_withdrawal_summary
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), cell) $OnionAuction$_fun_withdrawal_summary((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref;

;; $OnionAuction$_fun_round_stats
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), tuple) $OnionAuction$_fun_round_stats((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self, int $round_number) impure inline_ref;

;; $OnionAuction$_fun_current_round_stats
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), tuple) $OnionAuction$_fun_current_round_stats((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref;

;; $OnionAuction$_fun_total_rounds
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_total_rounds((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref;

;; $OnionAuction$_fun_round_summary
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), tuple) $OnionAuction$_fun_round_summary((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self, int $round_number) impure inline_ref;

;; $OnionAuction$_fun_all_rounds_summary
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), cell) $OnionAuction$_fun_all_rounds_summary((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref;

;; $OnionAuction$_fun_round_participants_count
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_round_participants_count((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self, int $round_number) impure inline_ref;

;; $OnionAuction$_fun_aggregated_stats
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), (int, int, int, int, int, int, int, int, int, int, int, int, int, int)) $OnionAuction$_fun_aggregated_stats((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref;

;; $OnionAuction$_fun_reply
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), ()) $OnionAuction$_fun_reply((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self, cell $body) impure inline;

;; $OnionAuction$_fun_notify
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), ()) $OnionAuction$_fun_notify((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self, cell $body) impure inline;

;; $OnionAuction$_fun_requireOwner
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), ()) $OnionAuction$_fun_requireOwner((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref;

;; $OnionAuction$_fun_owner
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), slice) $OnionAuction$_fun_owner((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref;

;; $OnionAuction$_fun_requireNotStopped
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), ()) $OnionAuction$_fun_requireNotStopped((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref;

;; $OnionAuction$_fun_stopped
((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_stopped((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref;

;; $USDTConfig$_constructor_master_address_wallet_address_decimals
((slice, slice, int)) $USDTConfig$_constructor_master_address_wallet_address_decimals(slice $master_address, slice $wallet_address, int $decimals) inline;

;; $USDTConfigured$_constructor_usdt_master_usdt_wallet_configured_by
((slice, slice, slice)) $USDTConfigured$_constructor_usdt_master_usdt_wallet_configured_by(slice $usdt_master, slice $usdt_wallet, slice $configured_by) inline;

;; $SigningKeySet$_constructor_public_key_set_by
((int, slice)) $SigningKeySet$_constructor_public_key_set_by(int $public_key, slice $set_by) inline;

;; $MinPurchaseUpdated$_constructor_old_min_purchase_new_min_purchase_updated_by
((int, int, slice)) $MinPurchaseUpdated$_constructor_old_min_purchase_new_min_purchase_updated_by(int $old_min_purchase, int $new_min_purchase, slice $updated_by) inline;

;; $TreasurySet$_constructor_old_treasury_new_treasury_set_by
((slice, slice, slice)) $TreasurySet$_constructor_old_treasury_new_treasury_set_by(slice $old_treasury, slice $new_treasury, slice $set_by) inline;

;; $SendParameters$_constructor_to_value_mode_bounce_body
((int, cell, cell, cell, int, slice, int)) $SendParameters$_constructor_to_value_mode_bounce_body(slice $to, int $value, int $mode, int $bounce, cell $body) inline;

;; $FundsWithdrawn$_constructor_currency_amount_destination_withdrawn_by_remaining_balance
((int, int, slice, slice, int)) $FundsWithdrawn$_constructor_currency_amount_destination_withdrawn_by_remaining_balance(int $currency, int $amount, slice $destination, slice $withdrawn_by, int $remaining_balance) inline;

;; $SendParameters$_constructor_to_value_bounce_body
((int, cell, cell, cell, int, slice, int)) $SendParameters$_constructor_to_value_bounce_body(slice $to, int $value, int $bounce, cell $body) inline;

;; $JettonTransfer$_constructor_query_id_amount_destination_response_destination_custom_payload_forward_ton_amount_forward_payload
((int, int, slice, slice, cell, int, cell)) $JettonTransfer$_constructor_query_id_amount_destination_response_destination_custom_payload_forward_ton_amount_forward_payload(int $query_id, int $amount, slice $destination, slice $response_destination, cell $custom_payload, int $forward_ton_amount, cell $forward_payload) inline;

;; $AuctionStarted$_constructor_start_time_end_time_soft_cap_hard_cap_initial_price_total_supply
((int, int, int, int, int, int)) $AuctionStarted$_constructor_start_time_end_time_soft_cap_hard_cap_initial_price_total_supply(int $start_time, int $end_time, int $soft_cap, int $hard_cap, int $initial_price, int $total_supply) inline;

;; $RoundUpdated$_constructor_old_round_new_round_old_price_new_price_updated_by
((int, int, int, int, slice)) $RoundUpdated$_constructor_old_round_new_round_old_price_new_price_updated_by(int $old_round, int $new_round, int $old_price, int $new_price, slice $updated_by) inline;

;; $DeployOk$_constructor_queryId
((int)) $DeployOk$_constructor_queryId(int $queryId) inline;


;; OnionAuction_OnionAuction.stdlib.fc
global (int, slice, int, slice) __tact_context;
global slice __tact_context_sender;
global cell __tact_child_contract_codes;
global int __tact_randomized;

(slice, slice) __tact_load_address_opt(slice cs) asm """

            b{00} SDBEGINSQ
            IF:<{
              PUSHNULL
            }>ELSE<{
              LDMSGADDR
              SWAP
            }>

""";

builder __tact_store_addr_none(builder b) asm "b{00} STSLICECONST";

builder __tact_store_address_opt(builder b, slice address) inline {
    if (null?(address)) {
        return __tact_store_addr_none(b);
    } else {
        return b.store_slice(address);
    }
}

forall X -> X __tact_not_null(X x) impure inline {
    throw_if(128, null?(x)); return x;
}

(cell, int) __tact_dict_delete(cell dict, int key_len, slice index) asm(index dict key_len) """
    DICTDEL
""";

(slice, int) __tact_dict_get(cell dict, int key_len, slice index) asm(index dict key_len) """
    DICTGET NULLSWAPIFNOT
""";

(int, slice, int, slice) __tact_context_get() inline {
    return __tact_context;
}

slice __tact_context_get_sender() inline {
    return __tact_context_sender;
}

forall X0, X1, X2 -> tuple __tact_tuple_create_3((X0, X1, X2) v) asm """
    3 TUPLE
""";

forall X0, X1, X2 -> (X0, X1, X2) __tact_tuple_destroy_3(tuple v) asm """
    3 UNTUPLE
""";

forall X0, X1, X2, X3, X4, X5, X6, X7, X8, X9, X10, X11, X12, X13 -> tuple __tact_tuple_create_14((X0, X1, X2, X3, X4, X5, X6, X7, X8, X9, X10, X11, X12, X13) v) asm """
    14 TUPLE
""";

forall X0, X1, X2, X3, X4, X5, X6, X7, X8, X9, X10, X11, X12, X13 -> (X0, X1, X2, X3, X4, X5, X6, X7, X8, X9, X10, X11, X12, X13) __tact_tuple_destroy_14(tuple v) asm """
    14 UNTUPLE
""";

int __tact_dict_get_slice_int(cell d, int kl, slice k, int vl) inline {
    var (r, ok) = __tact_dict_get(d, kl, k);
    if (ok) {
        return r~load_int(vl);
    } else {
        return null();
    }
}

(cell, ()) __tact_dict_set_slice_int(cell d, int kl, slice k, int v, int vl) inline {
    if (null?(v)) {
        var (r, ok) = __tact_dict_delete(d, kl, k);
        return (r, ());
    } else {
        return (dict_set_builder(d, kl, k, begin_cell().store_int(v, vl)), ());
    }
}

int __tact_dict_get_int_int(cell d, int kl, int k, int vl) inline {
    var (r, ok) = idict_get?(d, kl, k);
    if (ok) {
        return r~load_int(vl);
    } else {
        return null();
    }
}

(cell, ()) __tact_dict_set_int_int(cell d, int kl, int k, int v, int vl) inline {
    if (null?(v)) {
        var (r, ok) = idict_delete?(d, kl, k);
        return (r, ());
    } else {
        return (idict_set_builder(d, kl, k, begin_cell().store_int(v, vl)), ());
    }
}

cell __tact_dict_get_int_cell(cell d, int kl, int k) inline {
    var (r, ok) = idict_get_ref?(d, kl, k);
    if (ok) {
        return r;
    } else {
        return null();
    }
}

(cell, ()) __tact_dict_set_int_cell(cell d, int kl, int k, cell v) inline {
    if (null?(v)) {
        var (r, ok) = idict_delete?(d, kl, k);
        return (r, ());
    } else {
        return (idict_set_ref(d, kl, k, v), ());
    }
}

builder $global_beginCell() impure asm """
    NEWC
""";

int $global_checkSignature(int $hash, slice $signature, int $publicKey) impure asm """
    CHKSIGNU
""";

int $global_contractHash(cell $code, cell $data) impure asm """
    s0 PUSH HASHCU // `data` hash
    s2 PUSH HASHCU // `code` hash
    SWAP2
    CDEPTH         // `data` depth
    SWAP
    CDEPTH         // `code` depth
    131380 INT     // (2 << 16) | (1 << 8) | 0x34

    // Group 2: Composition of the Builder
    NEWC
    24 STU  // store refs_descriptor | bits_descriptor | data
    16 STU  // store depth_descriptor for `code`
    16 STU  // store depth_descriptor for `data`
    256 STU // store `code` hash
    256 STU // store `data` hash

    // Group 3: SHA256 hash of the resulting Builder
    ONE HASHEXT_SHA256
""";

slice $Slice$_fun_asAddressUnsafe(slice $self) impure asm "NOP";

slice $Cell$_fun_beginParse(cell $self) impure asm """
    CTOS
""";

slice $Cell$_fun_asSlice(cell $self) impure inline {
    var ($self) = $self;
    return $Cell$_fun_beginParse($self);
}

cell $Builder$_fun_endCell(builder $self) impure asm """
    ENDC
""";

slice $global_newAddress(int $chain, int $hash) impure inline {
    return $Slice$_fun_asAddressUnsafe($Cell$_fun_asSlice($Builder$_fun_endCell(store_uint(store_int(store_uint($global_beginCell(), 4, 3), $chain, 8), $hash, 256))));
}

slice $global_contractAddressExt(int $chain, cell $code, cell $data) impure inline {
    int $hash = $global_contractHash($code, $data);
    return $global_newAddress($chain, $hash);
}

slice $global_contractAddress((cell, cell) $s) impure inline {
    var (($s'code, $s'data)) = $s;
    return $global_contractAddressExt(0, $s'code, $s'data);
}

slice $global_myAddress() impure asm """
    MYADDR
""";

int $global_myBalance() impure asm """
    BALANCE FIRST
""";

() $global_nativeReserve(int $amount, int $mode) impure asm """
    RAWRESERVE
""";

() $global_message((int, cell, int, slice, int) $params) impure asm """
    NEWC
    b{01} STSLICECONST  // store tag = $0 and ihr_disabled = true
    1 STI               // store `bounce`
    b{000} STSLICECONST // store bounced = false and src = addr_none
    STSLICE             // store `to`
    SWAP
    STGRAMS             // store `value`
    106 PUSHINT         // 1 + 4 + 4 + 64 + 32 + 1
    STZEROES
    // → Stack state
    // s0: Builder
    // s1: `body`
    // s2: `mode`
    STDICT
    ENDC
    SWAP
    SENDRAWMSG
""";

() $global_send((int, cell, cell, cell, int, slice, int) $params) impure asm """
    NEWC
    b{01} STSLICECONST  // store tag = $0 and ihr_disabled = true
    1 STI               // store `bounce`
    b{000} STSLICECONST // store bounced = false and src = addr_none
    STSLICE             // store `to`
    SWAP
    STGRAMS             // store `value`
    105 PUSHINT         // 1 + 4 + 4 + 64 + 32
    STZEROES            // store currency_collection, ihr_fee, fwd_fee, created_lt and created_at
    // → Stack state
    // s0: Builder
    // s1: `data`
    // s2: `code`
    // s3: `body`
    // s4: `mode`

    // Group 2: Placing the Builder after code and data, then checking those for nullability
    s2 XCHG0
    DUP2
    ISNULL
    SWAP
    ISNULL
    AND
    // → Stack state
    // s0: -1 (true) if `data` and `code` are both null, 0 (false) otherwise
    // s1: `code`
    // s2: `data`
    // s3: Builder
    // s4: `body`
    // s5: `mode`

    // Group 3: Left branch of the IFELSE, executed if s0 is -1 (true)
    <{
        DROP2 // drop `data` and `code`, since either of those is null
        b{0} STSLICECONST
    }> PUSHCONT

    // Group 3: Right branch of the IFELSE, executed if s0 is 0 (false)
    <{
        // _ split_depth:(Maybe (## 5))
        //   special:(Maybe TickTock)
        //   code:(Maybe ^Cell)
        //   data:(Maybe ^Cell)
        //   library:(Maybe ^Cell)
        // = StateInit;
        ROT                // place message Builder on top
        b{10} STSLICECONST // store Maybe = true, Either = false
        // Start composing inlined StateInit
        b{00} STSLICECONST // store split_depth and special first
        STDICT             // store code
        STDICT             // store data
        b{0} STSLICECONST  // store library
    }> PUSHCONT

    // Group 3: IFELSE that does the branching shown above
    IFELSE
    // → Stack state
    // s0: Builder
    // s1: null or StateInit
    // s2: `body`
    // s3: `mode`

    // Group 4: Finalizing the message
    STDICT // store `body` as ref with an extra Maybe bit, since `body` might be null
    ENDC
    // → Stack state
    // s0: Cell
    // s1: `mode`

    // Group 5: Sending the message, with `mode` on top
    SWAP
    SENDRAWMSG // https://github.com/tact-lang/tact/issues/1558
""";

() $global_sendRawMessage(cell $msg, int $mode) impure asm """
    SENDRAWMSG
""";

builder $Builder$_fun_storeRef(cell $cell, builder $self) impure asm """
    STREF
""";

() $global_emit(cell $body) impure inline {
    cell $c = $Builder$_fun_endCell($Builder$_fun_storeRef($body, store_uint($global_beginCell(), 15211807202738752817960438464513, 104)));
    $global_sendRawMessage($c, 0);
}

int $global_now() impure asm """
    NOW
""";

builder $Builder$_fun_storeCoins(builder $self, int $value) impure asm """
    STVARUINT16
""";

builder $Builder$_fun_storeAddress(builder $self, slice $address) impure asm """
    STSLICER
""";

(slice, cell) $Slice$_fun_loadRef(slice $self) impure asm( -> 1 0) """
    LDREF
""";

(slice, int) $Slice$_fun_loadCoins(slice $self) impure asm( -> 1 0) """
    LDVARUINT16
""";

(slice, slice) $Slice$_fun_loadAddress(slice $self) impure asm( -> 1 0) """
    LDMSGADDR
""";

int $Cell$_fun_hash(cell $self) impure asm """
    HASHCU
""";

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), ()) $OnionAuction$_fun_reply((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self, cell $body) impure inline {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    slice $to = __tact_context_get_sender();
    int $bounce = true;
    if (false) {
        int $balance = $global_myBalance();
        int $balanceBeforeMessage = ($balance - $Context$_get_value(__tact_context_get()));
        if (($balanceBeforeMessage < 0)) {
            $global_nativeReserve(0, 0);
            $global_message($MessageParameters$_constructor_bounce_to_value_mode_body($bounce, $to, 0, 130, $body));
            return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), ());
        }
    }
    $global_message($MessageParameters$_constructor_bounce_to_value_mode_body($bounce, $to, 0, 66, $body));
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), ());
}

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), ()) $OnionAuction$_fun_notify((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self, cell $body) impure inline {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    slice $to = __tact_context_get_sender();
    int $bounce = false;
    if (false) {
        int $balance = $global_myBalance();
        int $balanceBeforeMessage = ($balance - $Context$_get_value(__tact_context_get()));
        if (($balanceBeforeMessage < 0)) {
            $global_nativeReserve(0, 0);
            $global_message($MessageParameters$_constructor_bounce_to_value_mode_body($bounce, $to, 0, 130, $body));
            return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), ());
        }
    }
    $global_message($MessageParameters$_constructor_bounce_to_value_mode_body($bounce, $to, 0, 66, $body));
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), ());
}

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), ()) $OnionAuction$_fun_requireOwner((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    throw_unless(132, ( equal_slices_bits(__tact_context_get_sender(), $self'owner) ));
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), ());
}

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), slice) $OnionAuction$_fun_owner((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    var $fresh$ret_138 = $self'owner;
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), $fresh$ret_138);
}

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), ()) $OnionAuction$_fun_requireNotStopped((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    throw_unless(133, (~ $self'stopped));
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), ());
}

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_stopped((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    var $fresh$ret_139 = $self'stopped;
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), $fresh$ret_139);
}

;; OnionAuction_OnionAuction.constants.fc
;; Cell 4UfNu2+Qd6yhfa02kS54aOUeNuIxSHmSH4f0u1BfkFY=
cell __gen_cell_cell_e147cdbb6f9077aca17dad36912e7868e51e36e2314879921f87f4bb505f9056() asm """
    B{b5ee9c7241010101002600004800000000544f4e207769746864726177616c2066726f6d204f6e696f6e41756374696f6e02f58f8e} B>boc PUSHREF
""";

;; Cell olLuc3MNw6VrTxv9tvaLoxMqyPi8dBuS3EKfyySgT7g=
cell __gen_cell_cell_a252ee73730dc3a56b4f1bfdb6f68ba3132ac8f8bc741b92dc429fcb24a04fb8() asm """
    B{b5ee9c7241010101002700004a0000000055534454207769746864726177616c2066726f6d204f6e696f6e41756374696f6e8b28d83e} B>boc PUSHREF
""";

;; Cell gldY9zP+pdgsoGTFbRb7BZXs6riA+Zr130ECIgKBdwg=
cell __gen_cell_cell_825758f733fea5d82ca064c56d16fb0595eceab880f99af5df41022202817708() asm """
    B{b5ee9c7241010101001a00003000000000544f4e20726566756e642070726f63657373656413a585a3} B>boc PUSHREF
""";

;; Cell 0OeTTgyxYwJffH3nsJ2weOrm9Y+6rF2VFqN1ZITp/cI=
cell __gen_cell_cell_d0e7934e0cb163025f7c7de7b09db078eae6f58fbaac5d9516a3756484e9fdc2() asm """
    B{b5ee9c7241010101001b000032000000005553445420726566756e642070726f6365737365647a5489f9} B>boc PUSHREF
""";

;; Cell j0tqFmVUtPKChV0DM8Yn1bWNKB6kuhHk3vkaEFMZOy0=
cell __gen_cell_cell_8f4b6a166554b4f282855d0333c627d5b58d281ea4ba11e4def91a1053193b2d() asm """
    B{b5ee9c7241010101000d0000160000000053746f707065646f94ddb2} B>boc PUSHREF
""";

;; OnionAuction_OnionAuction.storage.fc
;;
;; Type: Context
;; TLB: _ bounceable:bool sender:address value:int257 raw:^slice = Context
;;

_ $Context$_get_value((int, slice, int, slice) v) inline {
    var (v'bounceable, v'sender, v'value, v'raw) = v;
    return v'value;
}

;;
;; Type: SendParameters
;; TLB: _ mode:int257 body:Maybe ^cell code:Maybe ^cell data:Maybe ^cell value:int257 to:address bounce:bool = SendParameters
;;

((int, cell, cell, cell, int, slice, int)) $SendParameters$_constructor_to_value_bounce_body_code_data(slice $to, int $value, int $bounce, cell $body, cell $code, cell $data) inline {
    return (0, $body, $code, $data, $value, $to, $bounce);
}

((int, cell, cell, cell, int, slice, int)) $SendParameters$_constructor_to_value_mode_bounce_body(slice $to, int $value, int $mode, int $bounce, cell $body) inline {
    return ($mode, $body, null(), null(), $value, $to, $bounce);
}

((int, cell, cell, cell, int, slice, int)) $SendParameters$_constructor_to_value_bounce_body(slice $to, int $value, int $bounce, cell $body) inline {
    return (0, $body, null(), null(), $value, $to, $bounce);
}

;;
;; Type: MessageParameters
;; TLB: _ mode:int257 body:Maybe ^cell value:int257 to:address bounce:bool = MessageParameters
;;

((int, cell, int, slice, int)) $MessageParameters$_constructor_bounce_to_value_mode_body(int $bounce, slice $to, int $value, int $mode, cell $body) inline {
    return ($mode, $body, $value, $to, $bounce);
}

;;
;; Type: DeployOk
;; Header: 0xaff90f57
;; TLB: deploy_ok#aff90f57 queryId:uint64 = DeployOk
;;

builder $DeployOk$_store(builder build_0, (int) v) inline {
    var (v'queryId) = v;
    build_0 = store_uint(build_0, 2952335191, 32);
    build_0 = build_0.store_uint(v'queryId, 64);
    return build_0;
}

cell $DeployOk$_store_cell((int) v, builder b) inline {
    return $DeployOk$_store(b, v).end_cell();
}

((int)) $DeployOk$_constructor_queryId(int $queryId) inline {
    return ($queryId);
}

;;
;; Type: JettonTransfer
;; Header: 0x0f8a7ea5
;; TLB: jetton_transfer#0f8a7ea5 query_id:uint64 amount:coins destination:address response_destination:address custom_payload:Maybe ^cell forward_ton_amount:coins forward_payload:Maybe ^cell = JettonTransfer
;;

builder $JettonTransfer$_store(builder build_0, (int, int, slice, slice, cell, int, cell) v) inline {
    var (v'query_id, v'amount, v'destination, v'response_destination, v'custom_payload, v'forward_ton_amount, v'forward_payload) = v;
    build_0 = store_uint(build_0, 260734629, 32);
    build_0 = build_0.store_uint(v'query_id, 64);
    build_0 = build_0.store_varuint16(v'amount);
    build_0 = build_0.store_slice(v'destination);
    build_0 = build_0.store_slice(v'response_destination);
    build_0 = build_0.store_maybe_ref(v'custom_payload);
    build_0 = build_0.store_varuint16(v'forward_ton_amount);
    build_0 = build_0.store_maybe_ref(v'forward_payload);
    return build_0;
}

cell $JettonTransfer$_store_cell((int, int, slice, slice, cell, int, cell) v, builder b) inline {
    return $JettonTransfer$_store(b, v).end_cell();
}

((int, int, slice, slice, cell, int, cell)) $JettonTransfer$_constructor_query_id_amount_destination_response_destination_custom_payload_forward_ton_amount_forward_payload(int $query_id, int $amount, slice $destination, slice $response_destination, cell $custom_payload, int $forward_ton_amount, cell $forward_payload) inline {
    return ($query_id, $amount, $destination, $response_destination, $custom_payload, $forward_ton_amount, $forward_payload);
}

;;
;; Type: JettonTransferNotification
;; Header: 0x7362d09c
;; TLB: jetton_transfer_notification#7362d09c query_id:uint64 amount:coins sender:address forward_payload:Maybe ^cell = JettonTransferNotification
;;

((int, int, slice, cell)) $JettonTransferNotification$_tensor_cast((int, int, slice, cell) v) asm "NOP";

;;
;; Type: CreateUserPurchase
;; Header: 0x41734c0c
;; TLB: create_user_purchase#41734c0c user:address amount:coins tokens:coins currency:uint8 purchase_method:uint8 nonce:uint64 round_number:uint32 usdt_equivalent_amount:coins = CreateUserPurchase
;;

builder $CreateUserPurchase$_store(builder build_0, (slice, int, int, int, int, int, int, int) v) inline {
    var (v'user, v'amount, v'tokens, v'currency, v'purchase_method, v'nonce, v'round_number, v'usdt_equivalent_amount) = v;
    build_0 = store_uint(build_0, 1098075148, 32);
    build_0 = build_0.store_slice(v'user);
    build_0 = build_0.store_varuint16(v'amount);
    build_0 = build_0.store_varuint16(v'tokens);
    build_0 = build_0.store_uint(v'currency, 8);
    build_0 = build_0.store_uint(v'purchase_method, 8);
    build_0 = build_0.store_uint(v'nonce, 64);
    build_0 = build_0.store_uint(v'round_number, 32);
    build_0 = build_0.store_varuint16(v'usdt_equivalent_amount);
    return build_0;
}

cell $CreateUserPurchase$_store_cell((slice, int, int, int, int, int, int, int) v, builder b) inline {
    return $CreateUserPurchase$_store(b, v).end_cell();
}

((slice, int, int, int, int, int, int, int)) $CreateUserPurchase$_constructor_user_amount_tokens_currency_purchase_method_nonce_round_number_usdt_equivalent_amount(slice $user, int $amount, int $tokens, int $currency, int $purchase_method, int $nonce, int $round_number, int $usdt_equivalent_amount) inline {
    return ($user, $amount, $tokens, $currency, $purchase_method, $nonce, $round_number, $usdt_equivalent_amount);
}

;;
;; Type: PurchaseCalculation
;; TLB: _ user:address amount:coins currency:uint8 tokens_to_receive:coins current_price:coins current_round:uint32 timestamp:uint64 nonce:uint64 usdt_equivalent_amount:coins = PurchaseCalculation
;;

(slice, ((slice, int, int, int, int, int, int, int, int))) $PurchaseCalculation$_load(slice sc_0) inline {
    var v'user = sc_0~load_msg_addr();
    var v'amount = sc_0~load_varuint16();
    var v'currency = sc_0~load_uint(8);
    var v'tokens_to_receive = sc_0~load_varuint16();
    var v'current_price = sc_0~load_varuint16();
    var v'current_round = sc_0~load_uint(32);
    var v'timestamp = sc_0~load_uint(64);
    var v'nonce = sc_0~load_uint(64);
    var v'usdt_equivalent_amount = sc_0~load_varuint16();
    return (sc_0, (v'user, v'amount, v'currency, v'tokens_to_receive, v'current_price, v'current_round, v'timestamp, v'nonce, v'usdt_equivalent_amount));
}

((slice, int, int, int, int, int, int, int, int)) $PurchaseCalculation$_tensor_cast((slice, int, int, int, int, int, int, int, int) v) asm "NOP";

((slice, int, int, int, int, int, int, int, int)) $PurchaseCalculation$_constructor_user_amount_currency_tokens_to_receive_current_price_current_round_timestamp_nonce_usdt_equivalent_amount(slice $user, int $amount, int $currency, int $tokens_to_receive, int $current_price, int $current_round, int $timestamp, int $nonce, int $usdt_equivalent_amount) inline {
    return ($user, $amount, $currency, $tokens_to_receive, $current_price, $current_round, $timestamp, $nonce, $usdt_equivalent_amount);
}

;;
;; Type: AuctionStarted
;; Header: 0x8425c8b5
;; TLB: auction_started#8425c8b5 start_time:uint64 end_time:uint64 soft_cap:coins hard_cap:coins initial_price:coins total_supply:coins = AuctionStarted
;;

builder $AuctionStarted$_store(builder build_0, (int, int, int, int, int, int) v) inline {
    var (v'start_time, v'end_time, v'soft_cap, v'hard_cap, v'initial_price, v'total_supply) = v;
    build_0 = store_uint(build_0, 2217068725, 32);
    build_0 = build_0.store_uint(v'start_time, 64);
    build_0 = build_0.store_uint(v'end_time, 64);
    build_0 = build_0.store_varuint16(v'soft_cap);
    build_0 = build_0.store_varuint16(v'hard_cap);
    build_0 = build_0.store_varuint16(v'initial_price);
    build_0 = build_0.store_varuint16(v'total_supply);
    return build_0;
}

cell $AuctionStarted$_store_cell((int, int, int, int, int, int) v, builder b) inline {
    return $AuctionStarted$_store(b, v).end_cell();
}

((int, int, int, int, int, int)) $AuctionStarted$_constructor_start_time_end_time_soft_cap_hard_cap_initial_price_total_supply(int $start_time, int $end_time, int $soft_cap, int $hard_cap, int $initial_price, int $total_supply) inline {
    return ($start_time, $end_time, $soft_cap, $hard_cap, $initial_price, $total_supply);
}

;;
;; Type: PurchaseCompleted
;; Header: 0xecf65aa1
;; TLB: purchase_completed#ecf65aa1 user:address amount:coins tokens_received:coins currency:uint8 purchase_method:uint8 round_number:uint32 nonce:uint64 total_raised:coins total_raised_usdt:coins total_tokens_sold:coins = PurchaseCompleted
;;

builder $PurchaseCompleted$_store(builder build_0, (slice, int, int, int, int, int, int, int, int, int) v) inline {
    var (v'user, v'amount, v'tokens_received, v'currency, v'purchase_method, v'round_number, v'nonce, v'total_raised, v'total_raised_usdt, v'total_tokens_sold) = v;
    build_0 = store_uint(build_0, 3975568033, 32);
    build_0 = build_0.store_slice(v'user);
    build_0 = build_0.store_varuint16(v'amount);
    build_0 = build_0.store_varuint16(v'tokens_received);
    build_0 = build_0.store_uint(v'currency, 8);
    build_0 = build_0.store_uint(v'purchase_method, 8);
    build_0 = build_0.store_uint(v'round_number, 32);
    build_0 = build_0.store_uint(v'nonce, 64);
    build_0 = build_0.store_varuint16(v'total_raised);
    build_0 = build_0.store_varuint16(v'total_raised_usdt);
    var build_1 = begin_cell();
    build_1 = build_1.store_varuint16(v'total_tokens_sold);
    build_0 = store_builder_ref(build_0, build_1);
    return build_0;
}

cell $PurchaseCompleted$_store_cell((slice, int, int, int, int, int, int, int, int, int) v, builder b) inline {
    return $PurchaseCompleted$_store(b, v).end_cell();
}

((slice, int, int, int, int, int, int, int, int, int)) $PurchaseCompleted$_constructor_user_amount_tokens_received_currency_purchase_method_round_number_nonce_total_raised_total_raised_usdt_total_tokens_sold(slice $user, int $amount, int $tokens_received, int $currency, int $purchase_method, int $round_number, int $nonce, int $total_raised, int $total_raised_usdt, int $total_tokens_sold) inline {
    return ($user, $amount, $tokens_received, $currency, $purchase_method, $round_number, $nonce, $total_raised, $total_raised_usdt, $total_tokens_sold);
}

;;
;; Type: USDTConfigured
;; Header: 0x3d775bac
;; TLB: usdt_configured#3d775bac usdt_master:address usdt_wallet:address configured_by:address = USDTConfigured
;;

builder $USDTConfigured$_store(builder build_0, (slice, slice, slice) v) inline {
    var (v'usdt_master, v'usdt_wallet, v'configured_by) = v;
    build_0 = store_uint(build_0, 1031232428, 32);
    build_0 = build_0.store_slice(v'usdt_master);
    build_0 = build_0.store_slice(v'usdt_wallet);
    build_0 = build_0.store_slice(v'configured_by);
    return build_0;
}

cell $USDTConfigured$_store_cell((slice, slice, slice) v, builder b) inline {
    return $USDTConfigured$_store(b, v).end_cell();
}

((slice, slice, slice)) $USDTConfigured$_constructor_usdt_master_usdt_wallet_configured_by(slice $usdt_master, slice $usdt_wallet, slice $configured_by) inline {
    return ($usdt_master, $usdt_wallet, $configured_by);
}

;;
;; Type: SigningKeySet
;; Header: 0x9181ee30
;; TLB: signing_key_set#9181ee30 public_key:uint256 set_by:address = SigningKeySet
;;

builder $SigningKeySet$_store(builder build_0, (int, slice) v) inline {
    var (v'public_key, v'set_by) = v;
    build_0 = store_uint(build_0, 2441211440, 32);
    build_0 = build_0.store_uint(v'public_key, 256);
    build_0 = build_0.store_slice(v'set_by);
    return build_0;
}

cell $SigningKeySet$_store_cell((int, slice) v, builder b) inline {
    return $SigningKeySet$_store(b, v).end_cell();
}

((int, slice)) $SigningKeySet$_constructor_public_key_set_by(int $public_key, slice $set_by) inline {
    return ($public_key, $set_by);
}

;;
;; Type: MinPurchaseUpdated
;; Header: 0x2d589fc8
;; TLB: min_purchase_updated#2d589fc8 old_min_purchase:coins new_min_purchase:coins updated_by:address = MinPurchaseUpdated
;;

builder $MinPurchaseUpdated$_store(builder build_0, (int, int, slice) v) inline {
    var (v'old_min_purchase, v'new_min_purchase, v'updated_by) = v;
    build_0 = store_uint(build_0, 760782792, 32);
    build_0 = build_0.store_varuint16(v'old_min_purchase);
    build_0 = build_0.store_varuint16(v'new_min_purchase);
    build_0 = build_0.store_slice(v'updated_by);
    return build_0;
}

cell $MinPurchaseUpdated$_store_cell((int, int, slice) v, builder b) inline {
    return $MinPurchaseUpdated$_store(b, v).end_cell();
}

((int, int, slice)) $MinPurchaseUpdated$_constructor_old_min_purchase_new_min_purchase_updated_by(int $old_min_purchase, int $new_min_purchase, slice $updated_by) inline {
    return ($old_min_purchase, $new_min_purchase, $updated_by);
}

;;
;; Type: TreasurySet
;; Header: 0xef3aa766
;; TLB: treasury_set#ef3aa766 old_treasury:address new_treasury:address set_by:address = TreasurySet
;;

builder $TreasurySet$_store(builder build_0, (slice, slice, slice) v) inline {
    var (v'old_treasury, v'new_treasury, v'set_by) = v;
    build_0 = store_uint(build_0, 4013598566, 32);
    build_0 = build_0.store_slice(v'old_treasury);
    build_0 = build_0.store_slice(v'new_treasury);
    build_0 = build_0.store_slice(v'set_by);
    return build_0;
}

cell $TreasurySet$_store_cell((slice, slice, slice) v, builder b) inline {
    return $TreasurySet$_store(b, v).end_cell();
}

((slice, slice, slice)) $TreasurySet$_constructor_old_treasury_new_treasury_set_by(slice $old_treasury, slice $new_treasury, slice $set_by) inline {
    return ($old_treasury, $new_treasury, $set_by);
}

;;
;; Type: FundsWithdrawn
;; Header: 0xdb0ab6cf
;; TLB: funds_withdrawn#db0ab6cf currency:uint8 amount:coins destination:address withdrawn_by:address remaining_balance:coins = FundsWithdrawn
;;

builder $FundsWithdrawn$_store(builder build_0, (int, int, slice, slice, int) v) inline {
    var (v'currency, v'amount, v'destination, v'withdrawn_by, v'remaining_balance) = v;
    build_0 = store_uint(build_0, 3674912463, 32);
    build_0 = build_0.store_uint(v'currency, 8);
    build_0 = build_0.store_varuint16(v'amount);
    build_0 = build_0.store_slice(v'destination);
    build_0 = build_0.store_slice(v'withdrawn_by);
    build_0 = build_0.store_varuint16(v'remaining_balance);
    return build_0;
}

cell $FundsWithdrawn$_store_cell((int, int, slice, slice, int) v, builder b) inline {
    return $FundsWithdrawn$_store(b, v).end_cell();
}

((int, int, slice, slice, int)) $FundsWithdrawn$_constructor_currency_amount_destination_withdrawn_by_remaining_balance(int $currency, int $amount, slice $destination, slice $withdrawn_by, int $remaining_balance) inline {
    return ($currency, $amount, $destination, $withdrawn_by, $remaining_balance);
}

;;
;; Type: RoundUpdated
;; Header: 0x0b542a9b
;; TLB: round_updated#0b542a9b old_round:uint32 new_round:uint32 old_price:coins new_price:coins updated_by:address = RoundUpdated
;;

builder $RoundUpdated$_store(builder build_0, (int, int, int, int, slice) v) inline {
    var (v'old_round, v'new_round, v'old_price, v'new_price, v'updated_by) = v;
    build_0 = store_uint(build_0, 190065307, 32);
    build_0 = build_0.store_uint(v'old_round, 32);
    build_0 = build_0.store_uint(v'new_round, 32);
    build_0 = build_0.store_varuint16(v'old_price);
    build_0 = build_0.store_varuint16(v'new_price);
    build_0 = build_0.store_slice(v'updated_by);
    return build_0;
}

cell $RoundUpdated$_store_cell((int, int, int, int, slice) v, builder b) inline {
    return $RoundUpdated$_store(b, v).end_cell();
}

((int, int, int, int, slice)) $RoundUpdated$_constructor_old_round_new_round_old_price_new_price_updated_by(int $old_round, int $new_round, int $old_price, int $new_price, slice $updated_by) inline {
    return ($old_round, $new_round, $old_price, $new_price, $updated_by);
}

;;
;; Type: AuctionEnded
;; Header: 0x17b62b69
;; TLB: auction_ended#17b62b69 end_reason:uint8 final_status:uint8 total_raised:coins total_raised_usdt:coins total_tokens_sold:coins end_time:uint64 = AuctionEnded
;;

builder $AuctionEnded$_store(builder build_0, (int, int, int, int, int, int) v) inline {
    var (v'end_reason, v'final_status, v'total_raised, v'total_raised_usdt, v'total_tokens_sold, v'end_time) = v;
    build_0 = store_uint(build_0, 397814633, 32);
    build_0 = build_0.store_uint(v'end_reason, 8);
    build_0 = build_0.store_uint(v'final_status, 8);
    build_0 = build_0.store_varuint16(v'total_raised);
    build_0 = build_0.store_varuint16(v'total_raised_usdt);
    build_0 = build_0.store_varuint16(v'total_tokens_sold);
    build_0 = build_0.store_uint(v'end_time, 64);
    return build_0;
}

cell $AuctionEnded$_store_cell((int, int, int, int, int, int) v, builder b) inline {
    return $AuctionEnded$_store(b, v).end_cell();
}

((int, int, int, int, int, int)) $AuctionEnded$_constructor_end_reason_final_status_total_raised_total_raised_usdt_total_tokens_sold_end_time(int $end_reason, int $final_status, int $total_raised, int $total_raised_usdt, int $total_tokens_sold, int $end_time) inline {
    return ($end_reason, $final_status, $total_raised, $total_raised_usdt, $total_tokens_sold, $end_time);
}

;;
;; Type: AuctionConfig
;; TLB: _ start_time:uint64 end_time:uint64 soft_cap:coins hard_cap:coins total_supply:coins refund_fee_percent:uint8 = AuctionConfig
;;

builder $AuctionConfig$_store(builder build_0, (int, int, int, int, int, int) v) inline {
    var (v'start_time, v'end_time, v'soft_cap, v'hard_cap, v'total_supply, v'refund_fee_percent) = v;
    build_0 = build_0.store_uint(v'start_time, 64);
    build_0 = build_0.store_uint(v'end_time, 64);
    build_0 = build_0.store_varuint16(v'soft_cap);
    build_0 = build_0.store_varuint16(v'hard_cap);
    build_0 = build_0.store_varuint16(v'total_supply);
    build_0 = build_0.store_uint(v'refund_fee_percent, 8);
    return build_0;
}

(slice, ((int, int, int, int, int, int))) $AuctionConfig$_load(slice sc_0) inline {
    var v'start_time = sc_0~load_uint(64);
    var v'end_time = sc_0~load_uint(64);
    var v'soft_cap = sc_0~load_varuint16();
    var v'hard_cap = sc_0~load_varuint16();
    var v'total_supply = sc_0~load_varuint16();
    var v'refund_fee_percent = sc_0~load_uint(8);
    return (sc_0, (v'start_time, v'end_time, v'soft_cap, v'hard_cap, v'total_supply, v'refund_fee_percent));
}

(int, int, int, int, int, int) $AuctionConfig$_to_external(((int, int, int, int, int, int)) v) inline {
    var (v'start_time, v'end_time, v'soft_cap, v'hard_cap, v'total_supply, v'refund_fee_percent) = v; 
    return (v'start_time, v'end_time, v'soft_cap, v'hard_cap, v'total_supply, v'refund_fee_percent);
}

((int, int, int, int, int, int)) $AuctionConfig$_constructor_start_time_end_time_soft_cap_hard_cap_total_supply_refund_fee_percent(int $start_time, int $end_time, int $soft_cap, int $hard_cap, int $total_supply, int $refund_fee_percent) inline {
    return ($start_time, $end_time, $soft_cap, $hard_cap, $total_supply, $refund_fee_percent);
}

;;
;; Type: RoundStats
;; TLB: _ round_number:uint32 start_time:uint64 end_time:uint64 price:coins total_raised_ton:coins total_raised_usdt:coins raised_usdt_equivalent:coins tokens_sold:coins purchase_count:uint32 unique_users:uint32 refund_count:uint32 refunded_amount_ton:coins refunded_amount_usdt:coins refunded_usdt_equivalent:coins = RoundStats
;;

builder $RoundStats$_store(builder build_0, (int, int, int, int, int, int, int, int, int, int, int, int, int, int) v) inline_ref {
    var (v'round_number, v'start_time, v'end_time, v'price, v'total_raised_ton, v'total_raised_usdt, v'raised_usdt_equivalent, v'tokens_sold, v'purchase_count, v'unique_users, v'refund_count, v'refunded_amount_ton, v'refunded_amount_usdt, v'refunded_usdt_equivalent) = v;
    build_0 = build_0.store_uint(v'round_number, 32);
    build_0 = build_0.store_uint(v'start_time, 64);
    build_0 = build_0.store_uint(v'end_time, 64);
    build_0 = build_0.store_varuint16(v'price);
    build_0 = build_0.store_varuint16(v'total_raised_ton);
    build_0 = build_0.store_varuint16(v'total_raised_usdt);
    build_0 = build_0.store_varuint16(v'raised_usdt_equivalent);
    build_0 = build_0.store_varuint16(v'tokens_sold);
    build_0 = build_0.store_uint(v'purchase_count, 32);
    build_0 = build_0.store_uint(v'unique_users, 32);
    build_0 = build_0.store_uint(v'refund_count, 32);
    build_0 = build_0.store_varuint16(v'refunded_amount_ton);
    var build_1 = begin_cell();
    build_1 = build_1.store_varuint16(v'refunded_amount_usdt);
    build_1 = build_1.store_varuint16(v'refunded_usdt_equivalent);
    build_0 = store_builder_ref(build_0, build_1);
    return build_0;
}

cell $RoundStats$_store_cell((int, int, int, int, int, int, int, int, int, int, int, int, int, int) v, builder b) inline {
    return $RoundStats$_store(b, v).end_cell();
}

(slice, ((int, int, int, int, int, int, int, int, int, int, int, int, int, int))) $RoundStats$_load(slice sc_0) inline_ref {
    var v'round_number = sc_0~load_uint(32);
    var v'start_time = sc_0~load_uint(64);
    var v'end_time = sc_0~load_uint(64);
    var v'price = sc_0~load_varuint16();
    var v'total_raised_ton = sc_0~load_varuint16();
    var v'total_raised_usdt = sc_0~load_varuint16();
    var v'raised_usdt_equivalent = sc_0~load_varuint16();
    var v'tokens_sold = sc_0~load_varuint16();
    var v'purchase_count = sc_0~load_uint(32);
    var v'unique_users = sc_0~load_uint(32);
    var v'refund_count = sc_0~load_uint(32);
    var v'refunded_amount_ton = sc_0~load_varuint16();
    slice sc_1 = sc_0~load_ref().begin_parse();
    var v'refunded_amount_usdt = sc_1~load_varuint16();
    var v'refunded_usdt_equivalent = sc_1~load_varuint16();
    return (sc_0, (v'round_number, v'start_time, v'end_time, v'price, v'total_raised_ton, v'total_raised_usdt, v'raised_usdt_equivalent, v'tokens_sold, v'purchase_count, v'unique_users, v'refund_count, v'refunded_amount_ton, v'refunded_amount_usdt, v'refunded_usdt_equivalent));
}

tuple $RoundStats$_as_optional((int, int, int, int, int, int, int, int, int, int, int, int, int, int) v) inline {
    var (v'round_number, v'start_time, v'end_time, v'price, v'total_raised_ton, v'total_raised_usdt, v'raised_usdt_equivalent, v'tokens_sold, v'purchase_count, v'unique_users, v'refund_count, v'refunded_amount_ton, v'refunded_amount_usdt, v'refunded_usdt_equivalent) = v;
    return __tact_tuple_create_14(v'round_number, v'start_time, v'end_time, v'price, v'total_raised_ton, v'total_raised_usdt, v'raised_usdt_equivalent, v'tokens_sold, v'purchase_count, v'unique_users, v'refund_count, v'refunded_amount_ton, v'refunded_amount_usdt, v'refunded_usdt_equivalent);
}

tuple $RoundStats$_load_opt(cell cl) inline {
    if (null?(cl)) {
        return null();
    }
    var sc = cl.begin_parse();
    return $RoundStats$_as_optional(sc~$RoundStats$_load());
}

_ $RoundStats$_get_tokens_sold((int, int, int, int, int, int, int, int, int, int, int, int, int, int) v) inline {
    var (v'round_number, v'start_time, v'end_time, v'price, v'total_raised_ton, v'total_raised_usdt, v'raised_usdt_equivalent, v'tokens_sold, v'purchase_count, v'unique_users, v'refund_count, v'refunded_amount_ton, v'refunded_amount_usdt, v'refunded_usdt_equivalent) = v;
    return v'tokens_sold;
}

_ $RoundStats$_get_unique_users((int, int, int, int, int, int, int, int, int, int, int, int, int, int) v) inline {
    var (v'round_number, v'start_time, v'end_time, v'price, v'total_raised_ton, v'total_raised_usdt, v'raised_usdt_equivalent, v'tokens_sold, v'purchase_count, v'unique_users, v'refund_count, v'refunded_amount_ton, v'refunded_amount_usdt, v'refunded_usdt_equivalent) = v;
    return v'unique_users;
}

((int, int, int, int, int, int, int, int, int, int, int, int, int, int)) $RoundStats$_not_null(tuple v) inline {
    throw_if(128, null?(v));
    var (int vvv'round_number, int vvv'start_time, int vvv'end_time, int vvv'price, int vvv'total_raised_ton, int vvv'total_raised_usdt, int vvv'raised_usdt_equivalent, int vvv'tokens_sold, int vvv'purchase_count, int vvv'unique_users, int vvv'refund_count, int vvv'refunded_amount_ton, int vvv'refunded_amount_usdt, int vvv'refunded_usdt_equivalent) = __tact_tuple_destroy_14(v);
    return (vvv'round_number, vvv'start_time, vvv'end_time, vvv'price, vvv'total_raised_ton, vvv'total_raised_usdt, vvv'raised_usdt_equivalent, vvv'tokens_sold, vvv'purchase_count, vvv'unique_users, vvv'refund_count, vvv'refunded_amount_ton, vvv'refunded_amount_usdt, vvv'refunded_usdt_equivalent);
}

tuple $RoundStats$_to_tuple(((int, int, int, int, int, int, int, int, int, int, int, int, int, int)) v) inline {
    var (v'round_number, v'start_time, v'end_time, v'price, v'total_raised_ton, v'total_raised_usdt, v'raised_usdt_equivalent, v'tokens_sold, v'purchase_count, v'unique_users, v'refund_count, v'refunded_amount_ton, v'refunded_amount_usdt, v'refunded_usdt_equivalent) = v;
    return __tact_tuple_create_14(v'round_number, v'start_time, v'end_time, v'price, v'total_raised_ton, v'total_raised_usdt, v'raised_usdt_equivalent, v'tokens_sold, v'purchase_count, v'unique_users, v'refund_count, v'refunded_amount_ton, v'refunded_amount_usdt, v'refunded_usdt_equivalent);
}

tuple $RoundStats$_to_opt_tuple(tuple v) inline {
    if (null?(v)) { return null(); } 
    return $RoundStats$_to_tuple($RoundStats$_not_null(v)); 
}

(int, int, int, int, int, int, int, int, int, int, int, int, int, int) $RoundStats$_to_external(((int, int, int, int, int, int, int, int, int, int, int, int, int, int)) v) inline {
    var (v'round_number, v'start_time, v'end_time, v'price, v'total_raised_ton, v'total_raised_usdt, v'raised_usdt_equivalent, v'tokens_sold, v'purchase_count, v'unique_users, v'refund_count, v'refunded_amount_ton, v'refunded_amount_usdt, v'refunded_usdt_equivalent) = v; 
    return (v'round_number, v'start_time, v'end_time, v'price, v'total_raised_ton, v'total_raised_usdt, v'raised_usdt_equivalent, v'tokens_sold, v'purchase_count, v'unique_users, v'refund_count, v'refunded_amount_ton, v'refunded_amount_usdt, v'refunded_usdt_equivalent);
}

tuple $RoundStats$_to_opt_external(tuple v) inline {
    var loaded = $RoundStats$_to_opt_tuple(v);
    if (null?(loaded)) {
        return null();
    } else {
        return (loaded);
    }
}

((int, int, int, int, int, int, int, int, int, int, int, int, int, int)) $RoundStats$_constructor_round_number_start_time_end_time_price_total_raised_ton_total_raised_usdt_raised_usdt_equivalent_tokens_sold_purchase_count_unique_users_refund_count_refunded_amount_ton_refunded_amount_usdt_refunded_usdt_equivalent(int $round_number, int $start_time, int $end_time, int $price, int $total_raised_ton, int $total_raised_usdt, int $raised_usdt_equivalent, int $tokens_sold, int $purchase_count, int $unique_users, int $refund_count, int $refunded_amount_ton, int $refunded_amount_usdt, int $refunded_usdt_equivalent) inline {
    return ($round_number, $start_time, $end_time, $price, $total_raised_ton, $total_raised_usdt, $raised_usdt_equivalent, $tokens_sold, $purchase_count, $unique_users, $refund_count, $refunded_amount_ton, $refunded_amount_usdt, $refunded_usdt_equivalent);
}

;;
;; Type: USDTConfig
;; TLB: _ master_address:address wallet_address:address decimals:uint8 = USDTConfig
;;

builder $USDTConfig$_store(builder build_0, (slice, slice, int) v) inline {
    var (v'master_address, v'wallet_address, v'decimals) = v;
    build_0 = build_0.store_slice(v'master_address);
    build_0 = __tact_store_address_opt(build_0, v'wallet_address);
    build_0 = build_0.store_uint(v'decimals, 8);
    return build_0;
}

(slice, ((slice, slice, int))) $USDTConfig$_load(slice sc_0) inline {
    var v'master_address = sc_0~load_msg_addr();
    var v'wallet_address = sc_0~__tact_load_address_opt();
    var v'decimals = sc_0~load_uint(8);
    return (sc_0, (v'master_address, v'wallet_address, v'decimals));
}

((slice, slice, int)) $USDTConfig$_not_null(tuple v) inline {
    throw_if(128, null?(v));
    var (slice vvv'master_address, slice vvv'wallet_address, int vvv'decimals) = __tact_tuple_destroy_3(v);
    return (vvv'master_address, vvv'wallet_address, vvv'decimals);
}

tuple $USDTConfig$_as_optional((slice, slice, int) v) inline {
    var (v'master_address, v'wallet_address, v'decimals) = v;
    return __tact_tuple_create_3(v'master_address, v'wallet_address, v'decimals);
}

tuple $USDTConfig$_to_tuple(((slice, slice, int)) v) inline {
    var (v'master_address, v'wallet_address, v'decimals) = v;
    return __tact_tuple_create_3(v'master_address, v'wallet_address, v'decimals);
}

tuple $USDTConfig$_to_opt_tuple(tuple v) inline {
    if (null?(v)) { return null(); } 
    return $USDTConfig$_to_tuple($USDTConfig$_not_null(v)); 
}

tuple $USDTConfig$_to_opt_external(tuple v) inline {
    var loaded = $USDTConfig$_to_opt_tuple(v);
    if (null?(loaded)) {
        return null();
    } else {
        return (loaded);
    }
}

((slice, slice, int)) $USDTConfig$_constructor_master_address_wallet_address_decimals(slice $master_address, slice $wallet_address, int $decimals) inline {
    return ($master_address, $wallet_address, $decimals);
}

;;
;; Type: ParsedPurchaseData
;; TLB: _ calculation:PurchaseCalculation{user:address,amount:coins,currency:uint8,tokens_to_receive:coins,current_price:coins,current_round:uint32,timestamp:uint64,nonce:uint64,usdt_equivalent_amount:coins} signature:^slice = ParsedPurchaseData
;;

(((slice, int, int, int, int, int, int, int, int), slice)) $ParsedPurchaseData$_constructor_calculation_signature((slice, int, int, int, int, int, int, int, int) $calculation, slice $signature) inline {
    return ($calculation, $signature);
}

;;
;; Type: UserPurchase
;; TLB: _ owner:address auction_address:address user_address:address total_purchased:coins total_paid:coins purchase_history:dict<int, ^PurchaseRecord{id:uint32,user:address,amount:coins,tokens:coins,timestamp:uint64,currency:uint8,purchase_method:uint8,nonce:uint64,round_number:uint32,usdt_equivalent_amount:coins}> refund_history:dict<int, int> purchase_id_counter:uint32 participated_rounds:dict<int, bool> = UserPurchase
;;

builder $UserPurchase$init$_store(builder build_0, (slice, slice) v) inline {
    var (v'auction_address, v'user_address) = v;
    build_0 = build_0.store_slice(v'auction_address);
    build_0 = build_0.store_slice(v'user_address);
    return build_0;
}

cell $UserPurchase$_child_get_code() impure asm """
    B{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} B>boc PUSHREF
""";

(cell, cell) $UserPurchase$_init_child(slice $auction_address, slice $user_address) inline_ref {
    ;; Build init code cell

    ;; Contract Code: UserPurchase
    cell init_code = $UserPurchase$_child_get_code();


    builder b = begin_cell();
    b = b.store_int(false, 1);
    b = $UserPurchase$init$_store(b, ($auction_address, $user_address));
    return (init_code, b.end_cell());
}

;;
;; Type: OnionAuction
;; TLB: _ owner:address stopped:bool auction_config:AuctionConfig{start_time:uint64,end_time:uint64,soft_cap:coins,hard_cap:coins,total_supply:coins,refund_fee_percent:uint8} current_round:uint32 current_price:coins total_raised:coins total_tokens_sold:coins auction_status:uint8 usdt_config:Maybe USDTConfig{master_address:address,wallet_address:address,decimals:uint8} total_raised_usdt:coins total_raised_usdt_equivalent:coins purchase_count:uint32 round_stats:dict<int, ^RoundStats{round_number:uint32,start_time:uint64,end_time:uint64,price:coins,total_raised_ton:coins,total_raised_usdt:coins,raised_usdt_equivalent:coins,tokens_sold:coins,purchase_count:uint32,unique_users:uint32,refund_count:uint32,refunded_amount_ton:coins,refunded_amount_usdt:coins,refunded_usdt_equivalent:coins}> user_round_purchases:dict<address, int> signing_public_key:uint256 used_nonces:dict<int, bool> signature_timeout:uint64 min_purchase:coins treasury_address:address = OnionAuction
;;

builder $OnionAuction$_store(builder build_0, (slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) v) inline {
    var (v'owner, v'stopped, (v'auction_config'start_time, v'auction_config'end_time, v'auction_config'soft_cap, v'auction_config'hard_cap, v'auction_config'total_supply, v'auction_config'refund_fee_percent), v'current_round, v'current_price, v'total_raised, v'total_tokens_sold, v'auction_status, v'usdt_config, v'total_raised_usdt, v'total_raised_usdt_equivalent, v'purchase_count, v'round_stats, v'user_round_purchases, v'signing_public_key, v'used_nonces, v'signature_timeout, v'min_purchase, v'treasury_address) = v;
    build_0 = build_0.store_slice(v'owner);
    build_0 = build_0.store_int(v'stopped, 1);
    build_0 = $AuctionConfig$_store(build_0, (v'auction_config'start_time, v'auction_config'end_time, v'auction_config'soft_cap, v'auction_config'hard_cap, v'auction_config'total_supply, v'auction_config'refund_fee_percent));
    build_0 = build_0.store_uint(v'current_round, 32);
    build_0 = build_0.store_varuint16(v'current_price);
    var build_1 = begin_cell();
    build_1 = build_1.store_varuint16(v'total_raised);
    build_1 = build_1.store_varuint16(v'total_tokens_sold);
    build_1 = build_1.store_uint(v'auction_status, 8);
    build_1 = ~ null?(v'usdt_config) ? build_1.store_int(true, 1).$USDTConfig$_store($USDTConfig$_not_null(v'usdt_config)) : build_1.store_int(false, 1);
    build_1 = build_1.store_varuint16(v'total_raised_usdt);
    var build_2 = begin_cell();
    build_2 = build_2.store_varuint16(v'total_raised_usdt_equivalent);
    build_2 = build_2.store_uint(v'purchase_count, 32);
    build_2 = build_2.store_dict(v'round_stats);
    build_2 = build_2.store_dict(v'user_round_purchases);
    build_2 = build_2.store_uint(v'signing_public_key, 256);
    build_2 = build_2.store_dict(v'used_nonces);
    build_2 = build_2.store_uint(v'signature_timeout, 64);
    build_2 = build_2.store_varuint16(v'min_purchase);
    build_2 = __tact_store_address_opt(build_2, v'treasury_address);
    build_1 = store_builder_ref(build_1, build_2);
    build_0 = store_builder_ref(build_0, build_1);
    return build_0;
}

(slice, ((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice))) $OnionAuction$_load(slice sc_0) inline {
    var v'owner = sc_0~load_msg_addr();
    var v'stopped = sc_0~load_int(1);
    var v'auction_config = sc_0~$AuctionConfig$_load();
    var v'current_round = sc_0~load_uint(32);
    var v'current_price = sc_0~load_varuint16();
    slice sc_1 = sc_0~load_ref().begin_parse();
    var v'total_raised = sc_1~load_varuint16();
    var v'total_tokens_sold = sc_1~load_varuint16();
    var v'auction_status = sc_1~load_uint(8);
    var v'usdt_config = sc_1~load_int(1) ? $USDTConfig$_as_optional(sc_1~$USDTConfig$_load()) : null();
    var v'total_raised_usdt = sc_1~load_varuint16();
    slice sc_2 = sc_1~load_ref().begin_parse();
    var v'total_raised_usdt_equivalent = sc_2~load_varuint16();
    var v'purchase_count = sc_2~load_uint(32);
    var v'round_stats = sc_2~load_dict();
    var v'user_round_purchases = sc_2~load_dict();
    var v'signing_public_key = sc_2~load_uint(256);
    var v'used_nonces = sc_2~load_dict();
    var v'signature_timeout = sc_2~load_uint(64);
    var v'min_purchase = sc_2~load_varuint16();
    var v'treasury_address = sc_2~__tact_load_address_opt();
    return (sc_0, (v'owner, v'stopped, v'auction_config, v'current_round, v'current_price, v'total_raised, v'total_tokens_sold, v'auction_status, v'usdt_config, v'total_raised_usdt, v'total_raised_usdt_equivalent, v'purchase_count, v'round_stats, v'user_round_purchases, v'signing_public_key, v'used_nonces, v'signature_timeout, v'min_purchase, v'treasury_address));
}

(slice, ((slice, int, int, int, int, int))) $OnionAuction$init$_load(slice sc_0) inline {
    var v'owner = sc_0~load_msg_addr();
    var v'start_time = sc_0~load_int(257);
    var v'end_time = sc_0~load_int(257);
    slice sc_1 = sc_0~load_ref().begin_parse();
    var v'soft_cap = sc_1~load_int(257);
    var v'hard_cap = sc_1~load_int(257);
    var v'total_supply = sc_1~load_int(257);
    return (sc_0, (v'owner, v'start_time, v'end_time, v'soft_cap, v'hard_cap, v'total_supply));
}

(slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $OnionAuction$_contract_load() impure inline {
    slice $sc = get_data().begin_parse();
    int $loaded = $sc~load_int(1);
    if ($loaded) {
        return $sc~$OnionAuction$_load();
    }
    else {
        (slice $owner, int $start_time, int $end_time, int $soft_cap, int $hard_cap, int $total_supply) = $sc~$OnionAuction$init$_load();
        $sc.end_parse();
        return $OnionAuction$_contract_init($owner, $start_time, $end_time, $soft_cap, $hard_cap, $total_supply);
    }
}

() $OnionAuction$_contract_store((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) v) impure inline {
    builder b = begin_cell();
    b = b.store_int(true, 1);
    b = $OnionAuction$_store(b, v);
    set_data(b.end_cell());
}

;;
;; Contract OnionAuction functions
;;

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), ()) $OnionAuction$_fun_initializeFirstRound((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    var ($first_round'round_number, $first_round'start_time, $first_round'end_time, $first_round'price, $first_round'total_raised_ton, $first_round'total_raised_usdt, $first_round'raised_usdt_equivalent, $first_round'tokens_sold, $first_round'purchase_count, $first_round'unique_users, $first_round'refund_count, $first_round'refunded_amount_ton, $first_round'refunded_amount_usdt, $first_round'refunded_usdt_equivalent) = $RoundStats$_constructor_round_number_start_time_end_time_price_total_raised_ton_total_raised_usdt_raised_usdt_equivalent_tokens_sold_purchase_count_unique_users_refund_count_refunded_amount_ton_refunded_amount_usdt_refunded_usdt_equivalent(1, 0, 0, $self'current_price, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
    $self'round_stats~__tact_dict_set_int_cell(257, 1, $RoundStats$_store_cell(($first_round'round_number, $first_round'start_time, $first_round'end_time, $first_round'price, $first_round'total_raised_ton, $first_round'total_raised_usdt, $first_round'raised_usdt_equivalent, $first_round'tokens_sold, $first_round'purchase_count, $first_round'unique_users, $first_round'refund_count, $first_round'refunded_amount_ton, $first_round'refunded_amount_usdt, $first_round'refunded_usdt_equivalent), begin_cell()));
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), ());
}

(slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $OnionAuction$_contract_init(slice $owner, int $start_time, int $end_time, int $soft_cap, int $hard_cap, int $total_supply) impure inline {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = (null(), null(), null(), null(), null(), null(), null(), null(), null(), null(), null(), null(), null(), null(), null(), null(), null(), null(), null());
    $self'owner = $owner;
    $self'stopped = false;
    ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent) = $AuctionConfig$_constructor_start_time_end_time_soft_cap_hard_cap_total_supply_refund_fee_percent($start_time, $end_time, $soft_cap, $hard_cap, $total_supply, 5);
    $self'current_round = 1;
    $self'current_price = 100000000;
    $self'total_raised = 0;
    $self'total_tokens_sold = 0;
    $self'auction_status = 0;
    $self'purchase_count = 0;
    $self'usdt_config = null();
    $self'total_raised_usdt = 0;
    $self'total_raised_usdt_equivalent = 0;
    $self'signing_public_key = 0;
    $self'signature_timeout = 300;
    $self'min_purchase = 100000000;
    $self'treasury_address = $owner;
    ($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)~$OnionAuction$_fun_initializeFirstRound();
    return ($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address);
}

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), ()) $OnionAuction$_fun_initializeNewRound((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self, int $round_number, int $start_time) impure inline_ref {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    var ($new_round'round_number, $new_round'start_time, $new_round'end_time, $new_round'price, $new_round'total_raised_ton, $new_round'total_raised_usdt, $new_round'raised_usdt_equivalent, $new_round'tokens_sold, $new_round'purchase_count, $new_round'unique_users, $new_round'refund_count, $new_round'refunded_amount_ton, $new_round'refunded_amount_usdt, $new_round'refunded_usdt_equivalent) = $RoundStats$_constructor_round_number_start_time_end_time_price_total_raised_ton_total_raised_usdt_raised_usdt_equivalent_tokens_sold_purchase_count_unique_users_refund_count_refunded_amount_ton_refunded_amount_usdt_refunded_usdt_equivalent($round_number, $start_time, 0, $self'current_price, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
    $self'round_stats~__tact_dict_set_int_cell(257, $round_number, $RoundStats$_store_cell(($new_round'round_number, $new_round'start_time, $new_round'end_time, $new_round'price, $new_round'total_raised_ton, $new_round'total_raised_usdt, $new_round'raised_usdt_equivalent, $new_round'tokens_sold, $new_round'purchase_count, $new_round'unique_users, $new_round'refund_count, $new_round'refunded_amount_ton, $new_round'refunded_amount_usdt, $new_round'refunded_usdt_equivalent), begin_cell()));
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), ());
}

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), ()) $OnionAuction$_fun_updateRoundStatsForPurchase((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self, slice $user, int $amount, int $tokens, int $currency, int $usdt_equivalent_amount, int $round_number) impure inline_ref {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    tuple $current_stats = $RoundStats$_load_opt(__tact_dict_get_int_cell($self'round_stats, 257, $round_number));
    if (null?($current_stats)) {
        ($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)~$OnionAuction$_fun_initializeNewRound($round_number, $global_now());
        $current_stats = $RoundStats$_load_opt(__tact_dict_get_int_cell($self'round_stats, 257, $round_number));
    }
    if ((~ null?($current_stats))) {
        var ($stats'round_number, $stats'start_time, $stats'end_time, $stats'price, $stats'total_raised_ton, $stats'total_raised_usdt, $stats'raised_usdt_equivalent, $stats'tokens_sold, $stats'purchase_count, $stats'unique_users, $stats'refund_count, $stats'refunded_amount_ton, $stats'refunded_amount_usdt, $stats'refunded_usdt_equivalent) = $RoundStats$_not_null($current_stats);
        int $last_round_purchased = __tact_dict_get_slice_int($self'user_round_purchases, 267, $user, 257);
        int $is_new_user = false;
        if (( (null?($last_round_purchased)) ? (true) : ((__tact_not_null($last_round_purchased) < $round_number)) )) {
            $is_new_user = true;
            $self'user_round_purchases~__tact_dict_set_slice_int(267, $user, $round_number, 257);
        }
        var ($updated_stats'round_number, $updated_stats'start_time, $updated_stats'end_time, $updated_stats'price, $updated_stats'total_raised_ton, $updated_stats'total_raised_usdt, $updated_stats'raised_usdt_equivalent, $updated_stats'tokens_sold, $updated_stats'purchase_count, $updated_stats'unique_users, $updated_stats'refund_count, $updated_stats'refunded_amount_ton, $updated_stats'refunded_amount_usdt, $updated_stats'refunded_usdt_equivalent) = $RoundStats$_constructor_round_number_start_time_end_time_price_total_raised_ton_total_raised_usdt_raised_usdt_equivalent_tokens_sold_purchase_count_unique_users_refund_count_refunded_amount_ton_refunded_amount_usdt_refunded_usdt_equivalent($stats'round_number, $stats'start_time, $stats'end_time, $stats'price, ($stats'total_raised_ton + (($currency == 0) ? $amount : 0)), ($stats'total_raised_usdt + (($currency == 1) ? $amount : 0)), ($stats'raised_usdt_equivalent + $usdt_equivalent_amount), ($stats'tokens_sold + $tokens), ($stats'purchase_count + 1), ($stats'unique_users + ($is_new_user ? 1 : 0)), $stats'refund_count, $stats'refunded_amount_ton, $stats'refunded_amount_usdt, $stats'refunded_usdt_equivalent);
        $self'round_stats~__tact_dict_set_int_cell(257, $round_number, $RoundStats$_store_cell(($updated_stats'round_number, $updated_stats'start_time, $updated_stats'end_time, $updated_stats'price, $updated_stats'total_raised_ton, $updated_stats'total_raised_usdt, $updated_stats'raised_usdt_equivalent, $updated_stats'tokens_sold, $updated_stats'purchase_count, $updated_stats'unique_users, $updated_stats'refund_count, $updated_stats'refunded_amount_ton, $updated_stats'refunded_amount_usdt, $updated_stats'refunded_usdt_equivalent), begin_cell()));
    }
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), ());
}

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), ()) $OnionAuction$_fun_updateRoundStatsForRefund((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self, int $round_number, int $amount, int $currency, int $usdt_equivalent_amount) impure inline_ref {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    tuple $current_stats = $RoundStats$_load_opt(__tact_dict_get_int_cell($self'round_stats, 257, $round_number));
    if ((~ null?($current_stats))) {
        var ($stats'round_number, $stats'start_time, $stats'end_time, $stats'price, $stats'total_raised_ton, $stats'total_raised_usdt, $stats'raised_usdt_equivalent, $stats'tokens_sold, $stats'purchase_count, $stats'unique_users, $stats'refund_count, $stats'refunded_amount_ton, $stats'refunded_amount_usdt, $stats'refunded_usdt_equivalent) = $RoundStats$_not_null($current_stats);
        var ($updated_stats'round_number, $updated_stats'start_time, $updated_stats'end_time, $updated_stats'price, $updated_stats'total_raised_ton, $updated_stats'total_raised_usdt, $updated_stats'raised_usdt_equivalent, $updated_stats'tokens_sold, $updated_stats'purchase_count, $updated_stats'unique_users, $updated_stats'refund_count, $updated_stats'refunded_amount_ton, $updated_stats'refunded_amount_usdt, $updated_stats'refunded_usdt_equivalent) = $RoundStats$_constructor_round_number_start_time_end_time_price_total_raised_ton_total_raised_usdt_raised_usdt_equivalent_tokens_sold_purchase_count_unique_users_refund_count_refunded_amount_ton_refunded_amount_usdt_refunded_usdt_equivalent($stats'round_number, $stats'start_time, $stats'end_time, $stats'price, $stats'total_raised_ton, $stats'total_raised_usdt, $stats'raised_usdt_equivalent, $stats'tokens_sold, $stats'purchase_count, $stats'unique_users, ($stats'refund_count + 1), ($stats'refunded_amount_ton + (($currency == 0) ? $amount : 0)), ($stats'refunded_amount_usdt + (($currency == 1) ? $amount : 0)), ($stats'refunded_usdt_equivalent + $usdt_equivalent_amount));
        $self'round_stats~__tact_dict_set_int_cell(257, $round_number, $RoundStats$_store_cell(($updated_stats'round_number, $updated_stats'start_time, $updated_stats'end_time, $updated_stats'price, $updated_stats'total_raised_ton, $updated_stats'total_raised_usdt, $updated_stats'raised_usdt_equivalent, $updated_stats'tokens_sold, $updated_stats'purchase_count, $updated_stats'unique_users, $updated_stats'refund_count, $updated_stats'refunded_amount_ton, $updated_stats'refunded_amount_usdt, $updated_stats'refunded_usdt_equivalent), begin_cell()));
    }
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), ());
}

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), (cell, cell)) $OnionAuction$_fun_getUserPurchaseInit((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self, slice $user) impure inline_ref {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    var $fresh$ret_96 = $UserPurchase$_init_child($global_myAddress(), $user);
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), $fresh$ret_96);
}

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), ()) $OnionAuction$_fun_createOrUpdateUserPurchase((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self, slice $user, int $amount, int $tokens, int $currency, int $purchase_method, int $nonce, int $usdt_equivalent_amount, int $round_number) impure inline_ref {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    var ($init_code'code, $init_code'data) = ($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)~$OnionAuction$_fun_getUserPurchaseInit($user);
    slice $user_purchase_addr = $global_contractAddress(($init_code'code, $init_code'data));
    $global_send($SendParameters$_constructor_to_value_bounce_body_code_data($user_purchase_addr, 100000000, false, $CreateUserPurchase$_store_cell($CreateUserPurchase$_constructor_user_amount_tokens_currency_purchase_method_nonce_round_number_usdt_equivalent_amount($user, $amount, $tokens, $currency, $purchase_method, $nonce, $round_number, $usdt_equivalent_amount), begin_cell()), $init_code'code, $init_code'data));
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), ());
}

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), ((slice, int, int, int, int, int, int, int, int), slice)) $OnionAuction$_fun_parsePurchaseFromPayload((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self, cell $payload) impure inline_ref {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    slice $payload_slice = $Cell$_fun_beginParse($payload);
    var ($calc'user, $calc'amount, $calc'currency, $calc'tokens_to_receive, $calc'current_price, $calc'current_round, $calc'timestamp, $calc'nonce, $calc'usdt_equivalent_amount) = $PurchaseCalculation$_constructor_user_amount_currency_tokens_to_receive_current_price_current_round_timestamp_nonce_usdt_equivalent_amount($payload_slice~$Slice$_fun_loadAddress(), $payload_slice~$Slice$_fun_loadCoins(), $payload_slice~load_uint(8), $payload_slice~$Slice$_fun_loadCoins(), $payload_slice~$Slice$_fun_loadCoins(), $payload_slice~load_uint(32), $payload_slice~load_uint(64), $payload_slice~load_uint(64), $payload_slice~$Slice$_fun_loadCoins());
    slice $signature = $Cell$_fun_beginParse($payload_slice~$Slice$_fun_loadRef());
    var $fresh$ret_94 = $ParsedPurchaseData$_constructor_calculation_signature(($calc'user, $calc'amount, $calc'currency, $calc'tokens_to_receive, $calc'current_price, $calc'current_round, $calc'timestamp, $calc'nonce, $calc'usdt_equivalent_amount), $signature);
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), $fresh$ret_94);
}

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_hashPurchaseCalculation((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self, (slice, int, int, int, int, int, int, int, int) $calc) impure inline_ref {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    var (($calc'user, $calc'amount, $calc'currency, $calc'tokens_to_receive, $calc'current_price, $calc'current_round, $calc'timestamp, $calc'nonce, $calc'usdt_equivalent_amount)) = $calc;
    cell $data_cell = $Builder$_fun_endCell($Builder$_fun_storeCoins(store_uint(store_uint(store_uint($Builder$_fun_storeCoins($Builder$_fun_storeCoins(store_uint($Builder$_fun_storeCoins($Builder$_fun_storeAddress($global_beginCell(), $calc'user), $calc'amount), $calc'currency, 8), $calc'tokens_to_receive), $calc'current_price), $calc'current_round, 32), $calc'timestamp, 64), $calc'nonce, 64), $calc'usdt_equivalent_amount));
    var $fresh$ret_95 = $Cell$_fun_hash($data_cell);
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), $fresh$ret_95);
}

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_getCurrentRoundTokensSold((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    tuple $current_stats = $RoundStats$_load_opt(__tact_dict_get_int_cell($self'round_stats, 257, $self'current_round));
    if ((~ null?($current_stats))) {
        var $fresh$ret_99 = $RoundStats$_get_tokens_sold($RoundStats$_not_null($current_stats));
        return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), $fresh$ret_99);
    }
    var $fresh$ret_100 = 0;
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), $fresh$ret_100);
}

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), ()) $OnionAuction$_fun_handleUSDTWithSignature((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self, (int, int, slice, cell) $msg) impure inline_ref {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    var (($msg'query_id, $msg'amount, $msg'sender, $msg'forward_payload)) = $msg;
    throw_unless(51009, ($self'signing_public_key != 0));
    var (($parsed_data'calculation'user, $parsed_data'calculation'amount, $parsed_data'calculation'currency, $parsed_data'calculation'tokens_to_receive, $parsed_data'calculation'current_price, $parsed_data'calculation'current_round, $parsed_data'calculation'timestamp, $parsed_data'calculation'nonce, $parsed_data'calculation'usdt_equivalent_amount), $parsed_data'signature) = ($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)~$OnionAuction$_fun_parsePurchaseFromPayload(__tact_not_null($msg'forward_payload));
    var ($calc'user, $calc'amount, $calc'currency, $calc'tokens_to_receive, $calc'current_price, $calc'current_round, $calc'timestamp, $calc'nonce, $calc'usdt_equivalent_amount) = ($parsed_data'calculation'user, $parsed_data'calculation'amount, $parsed_data'calculation'currency, $parsed_data'calculation'tokens_to_receive, $parsed_data'calculation'current_price, $parsed_data'calculation'current_round, $parsed_data'calculation'timestamp, $parsed_data'calculation'nonce, $parsed_data'calculation'usdt_equivalent_amount);
    slice $signature = $parsed_data'signature;
    throw_unless(51014, ($calc'currency == 1));
    throw_unless(51015, ($calc'amount == $msg'amount));
    throw_unless(51016, ( equal_slices_bits($calc'user, $msg'sender) ));
    int $current_time = $global_now();
    throw_unless(51010, (($current_time - $calc'timestamp) <= $self'signature_timeout));
    throw_unless(51011, ($calc'timestamp <= $current_time));
    throw_unless(51012, null?(__tact_dict_get_int_int($self'used_nonces, 257, $calc'nonce, 1)));
    int $data_hash = ($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)~$OnionAuction$_fun_hashPurchaseCalculation($PurchaseCalculation$_tensor_cast(($calc'user, $calc'amount, $calc'currency, $calc'tokens_to_receive, $calc'current_price, $calc'current_round, $calc'timestamp, $calc'nonce, $calc'usdt_equivalent_amount)));
    throw_unless(51013, $global_checkSignature($data_hash, $signature, $self'signing_public_key));
    $self'used_nonces~__tact_dict_set_int_int(257, $calc'nonce, true, 1);
    throw_unless(51006, (($self'total_tokens_sold + $calc'tokens_to_receive) <= $self'auction_config'total_supply));
    int $current_round_tokens_sold = ($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)~$OnionAuction$_fun_getCurrentRoundTokensSold();
    throw_unless(51022, (($current_round_tokens_sold + $calc'tokens_to_receive) <= $self'auction_config'soft_cap));
    $self'total_raised_usdt = $self'total_raised_usdt + $calc'amount;
    $self'total_raised_usdt_equivalent = $self'total_raised_usdt_equivalent + $calc'usdt_equivalent_amount;
    $self'total_tokens_sold = $self'total_tokens_sold + $calc'tokens_to_receive;
    $self'purchase_count = $self'purchase_count + 1;
    ($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)~$OnionAuction$_fun_updateRoundStatsForPurchase($calc'user, $calc'amount, $calc'tokens_to_receive, 1, $calc'usdt_equivalent_amount, $calc'current_round);
    if (($self'total_tokens_sold >= $self'auction_config'hard_cap)) {
        $self'auction_status = 2;
        $global_emit($AuctionEnded$_store_cell($AuctionEnded$_constructor_end_reason_final_status_total_raised_total_raised_usdt_total_tokens_sold_end_time(2, $self'auction_status, $self'total_raised, $self'total_raised_usdt, $self'total_tokens_sold, $global_now()), begin_cell()));
    }
    ($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)~$OnionAuction$_fun_createOrUpdateUserPurchase($calc'user, $calc'amount, $calc'tokens_to_receive, 1, 1, $calc'nonce, $calc'usdt_equivalent_amount, $calc'current_round);
    $global_emit($PurchaseCompleted$_store_cell($PurchaseCompleted$_constructor_user_amount_tokens_received_currency_purchase_method_round_number_nonce_total_raised_total_raised_usdt_total_tokens_sold($calc'user, $calc'amount, $calc'tokens_to_receive, 1, 1, $calc'current_round, $calc'nonce, $self'total_raised, $self'total_raised_usdt, $self'total_tokens_sold), begin_cell()));
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), ());
}

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), slice) $OnionAuction$_fun_getJettonWalletAddress((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self, slice $jetton_master, slice $owner) impure inline_ref {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    if ((~ null?($self'usdt_config))) {
        var ($config'master_address, $config'wallet_address, $config'decimals) = $USDTConfig$_not_null($self'usdt_config);
        if ((~ null?($config'wallet_address))) {
            var $fresh$ret_97 = __tact_not_null($config'wallet_address);
            return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), $fresh$ret_97);
        }
    }
    var $fresh$ret_98 = $jetton_master;
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), $fresh$ret_98);
}

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), (int, int, int, int, int, int)) $OnionAuction$_fun_auction_info((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    var $fresh$ret_101 = ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent);
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), $fresh$ret_101);
}

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_current_round((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    var $fresh$ret_102 = $self'current_round;
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), $fresh$ret_102);
}

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_current_price((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    var $fresh$ret_103 = $self'current_price;
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), $fresh$ret_103);
}

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_total_raised((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    var $fresh$ret_104 = $self'total_raised;
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), $fresh$ret_104);
}

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_total_tokens_sold((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    var $fresh$ret_105 = $self'total_tokens_sold;
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), $fresh$ret_105);
}

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_auction_status((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    var $fresh$ret_106 = $self'auction_status;
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), $fresh$ret_106);
}

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_purchase_count((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    var $fresh$ret_107 = $self'purchase_count;
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), $fresh$ret_107);
}

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), slice) $OnionAuction$_fun_user_purchase_address((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self, slice $user) impure inline_ref {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    var $fresh$ret_108 = $global_contractAddress(($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)~$OnionAuction$_fun_getUserPurchaseInit($user));
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), $fresh$ret_108);
}

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_remaining_tokens((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    var $fresh$ret_109 = ($self'auction_config'total_supply - $self'total_tokens_sold);
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), $fresh$ret_109);
}

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_is_auction_active((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    int $current_time = $global_now();
    var $fresh$ret_110 = ( (( (($self'auction_status == 1)) ? (($current_time >= $self'auction_config'start_time)) : (false) )) ? (($current_time <= $self'auction_config'end_time)) : (false) );
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), $fresh$ret_110);
}

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), tuple) $OnionAuction$_fun_usdt_config((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    var $fresh$ret_111 = $self'usdt_config;
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), $fresh$ret_111);
}

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_total_raised_usdt((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    var $fresh$ret_112 = $self'total_raised_usdt;
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), $fresh$ret_112);
}

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_total_raised_equivalent((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    var $fresh$ret_113 = ($self'total_raised + ($self'total_raised_usdt * 1000));
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), $fresh$ret_113);
}

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_total_raised_usdt_equivalent((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    var $fresh$ret_114 = $self'total_raised_usdt_equivalent;
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), $fresh$ret_114);
}

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_is_usdt_enabled((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    var $fresh$ret_115 = (~ null?($self'usdt_config));
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), $fresh$ret_115);
}

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_signing_public_key((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    var $fresh$ret_116 = $self'signing_public_key;
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), $fresh$ret_116);
}

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_signature_timeout((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    var $fresh$ret_117 = $self'signature_timeout;
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), $fresh$ret_117);
}

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_is_nonce_used((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self, int $nonce) impure inline_ref {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    var $fresh$ret_118 = (~ null?(__tact_dict_get_int_int($self'used_nonces, 257, $nonce, 1)));
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), $fresh$ret_118);
}

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_is_signature_verification_enabled((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    var $fresh$ret_119 = ($self'signing_public_key != 0);
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), $fresh$ret_119);
}

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_min_purchase((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    var $fresh$ret_120 = $self'min_purchase;
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), $fresh$ret_120);
}

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), slice) $OnionAuction$_fun_treasury_address((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    var $fresh$ret_121 = $self'treasury_address;
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), $fresh$ret_121);
}

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_withdrawable_ton((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    if (($self'auction_status == 2)) {
        var $fresh$ret_122 = $self'total_raised;
        return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), $fresh$ret_122);
    }
    var $fresh$ret_123 = 0;
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), $fresh$ret_123);
}

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_withdrawable_usdt((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    var $fresh$ret_124 = $self'total_raised_usdt;
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), $fresh$ret_124);
}

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_can_withdraw((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    var $fresh$ret_125 = true;
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), $fresh$ret_125);
}

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), cell) $OnionAuction$_fun_withdrawal_summary((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    cell $summary = null();
    $summary~__tact_dict_set_int_int(257, 1, $self'auction_status, 257);
    $summary~__tact_dict_set_int_int(257, 2, ($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)~$OnionAuction$_fun_withdrawable_ton(), 257);
    $summary~__tact_dict_set_int_int(257, 3, ($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)~$OnionAuction$_fun_withdrawable_usdt(), 257);
    var $fresh$ret_126 = $summary;
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), $fresh$ret_126);
}

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), tuple) $OnionAuction$_fun_round_stats((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self, int $round_number) impure inline_ref {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    var $fresh$ret_127 = $RoundStats$_load_opt(__tact_dict_get_int_cell($self'round_stats, 257, $round_number));
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), $fresh$ret_127);
}

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), tuple) $OnionAuction$_fun_current_round_stats((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    var $fresh$ret_128 = $RoundStats$_load_opt(__tact_dict_get_int_cell($self'round_stats, 257, $self'current_round));
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), $fresh$ret_128);
}

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_total_rounds((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    int $current_time = $global_now();
    if (( (($self'auction_status != 1)) ? (true) : (($current_time < $self'auction_config'start_time)) )) {
        var $fresh$ret_129 = 0;
        return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), $fresh$ret_129);
    }
    int $elapsed_time = ($current_time - $self'auction_config'start_time);
    var $fresh$ret_130 = (($elapsed_time / 3600) + 1);
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), $fresh$ret_130);
}

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), tuple) $OnionAuction$_fun_round_summary((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self, int $round_number) impure inline_ref {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    tuple $stats = $RoundStats$_load_opt(__tact_dict_get_int_cell($self'round_stats, 257, $round_number));
    if (null?($stats)) {
        var $fresh$ret_131 = null();
        return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), $fresh$ret_131);
    }
    if (( (($round_number == $self'current_round)) ? (($self'auction_status == 1)) : (false) )) {
        var ($current_stats'round_number, $current_stats'start_time, $current_stats'end_time, $current_stats'price, $current_stats'total_raised_ton, $current_stats'total_raised_usdt, $current_stats'raised_usdt_equivalent, $current_stats'tokens_sold, $current_stats'purchase_count, $current_stats'unique_users, $current_stats'refund_count, $current_stats'refunded_amount_ton, $current_stats'refunded_amount_usdt, $current_stats'refunded_usdt_equivalent) = $RoundStats$_not_null($stats);
        int $current_time = $global_now();
        var $fresh$ret_132 = $RoundStats$_as_optional($RoundStats$_constructor_round_number_start_time_end_time_price_total_raised_ton_total_raised_usdt_raised_usdt_equivalent_tokens_sold_purchase_count_unique_users_refund_count_refunded_amount_ton_refunded_amount_usdt_refunded_usdt_equivalent($current_stats'round_number, $current_stats'start_time, $current_time, $current_stats'price, $current_stats'total_raised_ton, $current_stats'total_raised_usdt, $current_stats'raised_usdt_equivalent, $current_stats'tokens_sold, $current_stats'purchase_count, $current_stats'unique_users, $current_stats'refund_count, $current_stats'refunded_amount_ton, $current_stats'refunded_amount_usdt, $current_stats'refunded_usdt_equivalent));
        return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), $fresh$ret_132);
    }
    var $fresh$ret_133 = $stats;
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), $fresh$ret_133);
}

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), cell) $OnionAuction$_fun_all_rounds_summary((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    cell $summary = null();
    int $max_rounds = ($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)~$OnionAuction$_fun_total_rounds();
    int $i = 1;
    while (($i <= $max_rounds)) {
        tuple $round_stats = ($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)~$OnionAuction$_fun_round_summary($i);
        if ((~ null?($round_stats))) {
            $summary~__tact_dict_set_int_cell(257, $i, $RoundStats$_store_cell($RoundStats$_not_null($round_stats), begin_cell()));
        }
        $i = $i + 1;
    }
    var $fresh$ret_134 = $summary;
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), $fresh$ret_134);
}

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), int) $OnionAuction$_fun_round_participants_count((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self, int $round_number) impure inline_ref {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    tuple $round_stats = $RoundStats$_load_opt(__tact_dict_get_int_cell($self'round_stats, 257, $round_number));
    if ((~ null?($round_stats))) {
        var $fresh$ret_135 = $RoundStats$_get_unique_users($RoundStats$_not_null($round_stats));
        return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), $fresh$ret_135);
    }
    var $fresh$ret_136 = 0;
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), $fresh$ret_136);
}

((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice), (int, int, int, int, int, int, int, int, int, int, int, int, int, int)) $OnionAuction$_fun_aggregated_stats((slice, int, (int, int, int, int, int, int), int, int, int, int, int, tuple, int, int, int, cell, cell, int, cell, int, int, slice) $self) impure inline_ref {
    var (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)) = $self;
    int $total_raised_ton = 0;
    int $total_raised_usdt = 0;
    int $total_raised_usdt_equivalent = 0;
    int $total_tokens_sold = 0;
    int $total_purchases = 0;
    int $total_unique_users = 0;
    int $total_refunds = 0;
    int $total_refunded_ton = 0;
    int $total_refunded_usdt = 0;
    int $total_refunded_usdt_equivalent = 0;
    int $max_rounds = ($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)~$OnionAuction$_fun_total_rounds();
    int $i = 1;
    while (($i <= $max_rounds)) {
        tuple $round_stats = $RoundStats$_load_opt(__tact_dict_get_int_cell($self'round_stats, 257, $i));
        if ((~ null?($round_stats))) {
            var ($stats'round_number, $stats'start_time, $stats'end_time, $stats'price, $stats'total_raised_ton, $stats'total_raised_usdt, $stats'raised_usdt_equivalent, $stats'tokens_sold, $stats'purchase_count, $stats'unique_users, $stats'refund_count, $stats'refunded_amount_ton, $stats'refunded_amount_usdt, $stats'refunded_usdt_equivalent) = $RoundStats$_not_null($round_stats);
            $total_raised_ton = $total_raised_ton + $stats'total_raised_ton;
            $total_raised_usdt = $total_raised_usdt + $stats'total_raised_usdt;
            $total_raised_usdt_equivalent = $total_raised_usdt_equivalent + $stats'raised_usdt_equivalent;
            $total_tokens_sold = $total_tokens_sold + $stats'tokens_sold;
            $total_purchases = $total_purchases + $stats'purchase_count;
            $total_refunds = $total_refunds + $stats'refund_count;
            $total_refunded_ton = $total_refunded_ton + $stats'refunded_amount_ton;
            $total_refunded_usdt = $total_refunded_usdt + $stats'refunded_amount_usdt;
            $total_refunded_usdt_equivalent = $total_refunded_usdt_equivalent + $stats'refunded_usdt_equivalent;
            $total_unique_users = $total_unique_users + $stats'unique_users;
        }
        $i = $i + 1;
    }
    var $fresh$ret_137 = $RoundStats$_constructor_round_number_start_time_end_time_price_total_raised_ton_total_raised_usdt_raised_usdt_equivalent_tokens_sold_purchase_count_unique_users_refund_count_refunded_amount_ton_refunded_amount_usdt_refunded_usdt_equivalent(0, $self'auction_config'start_time, $global_now(), 0, $total_raised_ton, $total_raised_usdt, $total_raised_usdt_equivalent, $total_tokens_sold, $total_purchases, $total_unique_users, $total_refunds, $total_refunded_ton, $total_refunded_usdt, $total_refunded_usdt_equivalent);
    return (($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address), $fresh$ret_137);
}

;;
;; Get methods of a Contract OnionAuction
;;

_ %auction_info() method_id(72669) {
    var self = $OnionAuction$_contract_load();
    var res = self~$OnionAuction$_fun_auction_info();
    return $AuctionConfig$_to_external(res);
}

_ %current_round() method_id(108432) {
    var self = $OnionAuction$_contract_load();
    var res = self~$OnionAuction$_fun_current_round();
    return res;
}

_ %current_price() method_id(102733) {
    var self = $OnionAuction$_contract_load();
    var res = self~$OnionAuction$_fun_current_price();
    return res;
}

_ %total_raised() method_id(130922) {
    var self = $OnionAuction$_contract_load();
    var res = self~$OnionAuction$_fun_total_raised();
    return res;
}

_ %total_tokens_sold() method_id(81273) {
    var self = $OnionAuction$_contract_load();
    var res = self~$OnionAuction$_fun_total_tokens_sold();
    return res;
}

_ %auction_status() method_id(82124) {
    var self = $OnionAuction$_contract_load();
    var res = self~$OnionAuction$_fun_auction_status();
    return res;
}

_ %purchase_count() method_id(69567) {
    var self = $OnionAuction$_contract_load();
    var res = self~$OnionAuction$_fun_purchase_count();
    return res;
}

_ %user_purchase_address(slice $user) method_id(107761) {
    slice $user = $user;
    var self = $OnionAuction$_contract_load();
    var res = self~$OnionAuction$_fun_user_purchase_address($user);
    return res;
}

_ %remaining_tokens() method_id(95722) {
    var self = $OnionAuction$_contract_load();
    var res = self~$OnionAuction$_fun_remaining_tokens();
    return res;
}

_ %is_auction_active() method_id(121388) {
    var self = $OnionAuction$_contract_load();
    var res = self~$OnionAuction$_fun_is_auction_active();
    return res;
}

_ %usdt_config() method_id(95594) {
    var self = $OnionAuction$_contract_load();
    var res = self~$OnionAuction$_fun_usdt_config();
    return $USDTConfig$_to_opt_external(res);
}

_ %total_raised_usdt() method_id(105864) {
    var self = $OnionAuction$_contract_load();
    var res = self~$OnionAuction$_fun_total_raised_usdt();
    return res;
}

_ %total_raised_equivalent() method_id(89664) {
    var self = $OnionAuction$_contract_load();
    var res = self~$OnionAuction$_fun_total_raised_equivalent();
    return res;
}

_ %total_raised_usdt_equivalent() method_id(90613) {
    var self = $OnionAuction$_contract_load();
    var res = self~$OnionAuction$_fun_total_raised_usdt_equivalent();
    return res;
}

_ %is_usdt_enabled() method_id(123065) {
    var self = $OnionAuction$_contract_load();
    var res = self~$OnionAuction$_fun_is_usdt_enabled();
    return res;
}

_ %signing_public_key() method_id(126473) {
    var self = $OnionAuction$_contract_load();
    var res = self~$OnionAuction$_fun_signing_public_key();
    return res;
}

_ %signature_timeout() method_id(91556) {
    var self = $OnionAuction$_contract_load();
    var res = self~$OnionAuction$_fun_signature_timeout();
    return res;
}

_ %is_nonce_used(int $nonce) method_id(99217) {
    int $nonce = $nonce;
    var self = $OnionAuction$_contract_load();
    var res = self~$OnionAuction$_fun_is_nonce_used($nonce);
    return res;
}

_ %is_signature_verification_enabled() method_id(111108) {
    var self = $OnionAuction$_contract_load();
    var res = self~$OnionAuction$_fun_is_signature_verification_enabled();
    return res;
}

_ %min_purchase() method_id(110922) {
    var self = $OnionAuction$_contract_load();
    var res = self~$OnionAuction$_fun_min_purchase();
    return res;
}

_ %treasury_address() method_id(71868) {
    var self = $OnionAuction$_contract_load();
    var res = self~$OnionAuction$_fun_treasury_address();
    return res;
}

_ %withdrawable_ton() method_id(102777) {
    var self = $OnionAuction$_contract_load();
    var res = self~$OnionAuction$_fun_withdrawable_ton();
    return res;
}

_ %withdrawable_usdt() method_id(97270) {
    var self = $OnionAuction$_contract_load();
    var res = self~$OnionAuction$_fun_withdrawable_usdt();
    return res;
}

_ %can_withdraw() method_id(115239) {
    var self = $OnionAuction$_contract_load();
    var res = self~$OnionAuction$_fun_can_withdraw();
    return res;
}

_ %withdrawal_summary() method_id(126996) {
    var self = $OnionAuction$_contract_load();
    var res = self~$OnionAuction$_fun_withdrawal_summary();
    return res;
}

_ %round_stats(int $round_number) method_id(86928) {
    int $round_number = $round_number;
    var self = $OnionAuction$_contract_load();
    var res = self~$OnionAuction$_fun_round_stats($round_number);
    return $RoundStats$_to_opt_external(res);
}

_ %current_round_stats() method_id(124822) {
    var self = $OnionAuction$_contract_load();
    var res = self~$OnionAuction$_fun_current_round_stats();
    return $RoundStats$_to_opt_external(res);
}

_ %total_rounds() method_id(78978) {
    var self = $OnionAuction$_contract_load();
    var res = self~$OnionAuction$_fun_total_rounds();
    return res;
}

_ %round_summary(int $round_number) method_id(86664) {
    int $round_number = $round_number;
    var self = $OnionAuction$_contract_load();
    var res = self~$OnionAuction$_fun_round_summary($round_number);
    return $RoundStats$_to_opt_external(res);
}

_ %all_rounds_summary() method_id(83191) {
    var self = $OnionAuction$_contract_load();
    var res = self~$OnionAuction$_fun_all_rounds_summary();
    return res;
}

_ %round_participants_count(int $round_number) method_id(85355) {
    int $round_number = $round_number;
    var self = $OnionAuction$_contract_load();
    var res = self~$OnionAuction$_fun_round_participants_count($round_number);
    return res;
}

_ %aggregated_stats() method_id(112308) {
    var self = $OnionAuction$_contract_load();
    var res = self~$OnionAuction$_fun_aggregated_stats();
    return $RoundStats$_to_external(res);
}

_ %owner() method_id(83229) {
    var self = $OnionAuction$_contract_load();
    var res = self~$OnionAuction$_fun_owner();
    return res;
}

_ %stopped() method_id(74107) {
    var self = $OnionAuction$_contract_load();
    var res = self~$OnionAuction$_fun_stopped();
    return res;
}

;;
;; Routing of a Contract OnionAuction
;;

;; message opcode reader utility: only binary receivers
;; Returns 32 bit message opcode, otherwise throws the "Invalid incoming message" exit code
(slice, int) ~load_opcode_internal(slice s) asm( -> 1 0) "32 LDUQ 130 THROWIFNOT";

() recv_internal(int msg_value, cell in_msg_cell, slice in_msg) impure {
    
    ;; Context
    var cs = in_msg_cell.begin_parse();
    cs~skip_bits(2);
    var msg_bounceable = cs~load_int(1);
    var msg_bounced = cs~load_int(1);
    slice msg_sender_addr = cs~load_msg_addr();
    __tact_context = (msg_bounceable, msg_sender_addr, msg_value, cs);
    __tact_context_sender = msg_sender_addr;
    
    ;; Load contract data
    var ($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address) = $OnionAuction$_contract_load();
    
    ;; Handle bounced messages
    if (msg_bounced) { return (); }
    int op = 0;
    int in_msg_length = slice_bits(in_msg);
    if (in_msg_length >= 32) {
        op = in_msg~load_uint(32);
        ;; Receive SetUSDTAddress message
        if (op == 2703444734) {
            var $msg'usdt_master = in_msg~load_msg_addr();
            var $msg'usdt_wallet = in_msg~load_msg_addr();
            ($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)~$OnionAuction$_fun_requireOwner();
            $self'usdt_config = $USDTConfig$_as_optional($USDTConfig$_constructor_master_address_wallet_address_decimals($msg'usdt_master, $msg'usdt_wallet, 6));
            $global_emit($USDTConfigured$_store_cell($USDTConfigured$_constructor_usdt_master_usdt_wallet_configured_by($msg'usdt_master, $msg'usdt_wallet, __tact_context_get_sender()), begin_cell()));
            $OnionAuction$_contract_store(($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address));
            return ();
        }
        
        ;; Receive SetSigningKey message
        if (op == 3315975678) {
            var $msg'public_key = in_msg~load_uint(256);
            ($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)~$OnionAuction$_fun_requireOwner();
            $self'signing_public_key = $msg'public_key;
            $global_emit($SigningKeySet$_store_cell($SigningKeySet$_constructor_public_key_set_by($msg'public_key, __tact_context_get_sender()), begin_cell()));
            $OnionAuction$_contract_store(($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address));
            return ();
        }
        
        ;; Receive SetMinPurchase message
        if (op == 3390874056) {
            var $msg'min_purchase = in_msg~load_varuint16();
            ($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)~$OnionAuction$_fun_requireOwner();
            throw_unless(51018, ($msg'min_purchase > 0));
            int $old_min_purchase = $self'min_purchase;
            $self'min_purchase = $msg'min_purchase;
            $global_emit($MinPurchaseUpdated$_store_cell($MinPurchaseUpdated$_constructor_old_min_purchase_new_min_purchase_updated_by($old_min_purchase, $msg'min_purchase, __tact_context_get_sender()), begin_cell()));
            $OnionAuction$_contract_store(($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address));
            return ();
        }
        
        ;; Receive SetTreasury message
        if (op == 3553874899) {
            var $msg'treasury_address = in_msg~load_msg_addr();
            ($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)~$OnionAuction$_fun_requireOwner();
            slice $old_treasury = $self'treasury_address;
            slice $old_treasury_addr = $global_myAddress();
            if ((~ null?($old_treasury))) {
                $old_treasury_addr = __tact_not_null($old_treasury);
            }
            $self'treasury_address = $msg'treasury_address;
            $global_emit($TreasurySet$_store_cell($TreasurySet$_constructor_old_treasury_new_treasury_set_by($old_treasury_addr, $msg'treasury_address, __tact_context_get_sender()), begin_cell()));
            $OnionAuction$_contract_store(($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address));
            return ();
        }
        
        ;; Receive WithdrawTON message
        if (op == 3520188881) {
            var $msg'amount = in_msg~load_varuint16();
            var $msg'destination = in_msg~load_msg_addr();
            ($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)~$OnionAuction$_fun_requireOwner();
            int $withdrawal_amount = $msg'amount;
            ifnot ($withdrawal_amount) {
                $withdrawal_amount = $self'total_raised;
            }
            throw_unless(51020, ($withdrawal_amount <= $self'total_raised));
            throw_unless(51020, ($withdrawal_amount > 0));
            $self'total_raised = $self'total_raised - $withdrawal_amount;
            $global_send($SendParameters$_constructor_to_value_mode_bounce_body($msg'destination, $withdrawal_amount, 2, false, __gen_cell_cell_e147cdbb6f9077aca17dad36912e7868e51e36e2314879921f87f4bb505f9056()));
            $global_emit($FundsWithdrawn$_store_cell($FundsWithdrawn$_constructor_currency_amount_destination_withdrawn_by_remaining_balance(0, $withdrawal_amount, $msg'destination, __tact_context_get_sender(), $self'total_raised), begin_cell()));
            $OnionAuction$_contract_store(($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address));
            return ();
        }
        
        ;; Receive WithdrawUSDT message
        if (op == 3537031890) {
            var $msg'amount = in_msg~load_varuint16();
            var $msg'destination = in_msg~load_msg_addr();
            ($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)~$OnionAuction$_fun_requireOwner();
            throw_unless(51007, (~ null?($self'usdt_config)));
            var ($usdt_config'master_address, $usdt_config'wallet_address, $usdt_config'decimals) = $USDTConfig$_not_null($self'usdt_config);
            throw_unless(51007, (~ null?($usdt_config'wallet_address)));
            int $withdrawal_amount = $msg'amount;
            ifnot ($withdrawal_amount) {
                $withdrawal_amount = $self'total_raised_usdt;
            }
            throw_unless(51020, ($withdrawal_amount <= $self'total_raised_usdt));
            throw_unless(51020, ($withdrawal_amount > 0));
            $self'total_raised_usdt = $self'total_raised_usdt - $withdrawal_amount;
            $global_send($SendParameters$_constructor_to_value_bounce_body(__tact_not_null($usdt_config'wallet_address), 200000000, false, $JettonTransfer$_store_cell($JettonTransfer$_constructor_query_id_amount_destination_response_destination_custom_payload_forward_ton_amount_forward_payload(0, $withdrawal_amount, $msg'destination, $global_myAddress(), null(), 1, __gen_cell_cell_a252ee73730dc3a56b4f1bfdb6f68ba3132ac8f8bc741b92dc429fcb24a04fb8()), begin_cell())));
            $global_emit($FundsWithdrawn$_store_cell($FundsWithdrawn$_constructor_currency_amount_destination_withdrawn_by_remaining_balance(1, $withdrawal_amount, $msg'destination, __tact_context_get_sender(), $self'total_raised_usdt), begin_cell()));
            $OnionAuction$_contract_store(($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address));
            return ();
        }
        
        ;; Receive StartAuction message
        if (op == 1082886929) {
            var $msg'start_time = in_msg~load_uint(64);
            var $msg'end_time = in_msg~load_uint(64);
            var $msg'soft_cap = in_msg~load_varuint16();
            var $msg'hard_cap = in_msg~load_varuint16();
            var $msg'initial_price = in_msg~load_varuint16();
            ($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)~$OnionAuction$_fun_requireOwner();
            $self'auction_config'start_time = $msg'start_time;
            $self'auction_config'end_time = $msg'end_time;
            $self'auction_config'soft_cap = $msg'soft_cap;
            $self'auction_config'hard_cap = $msg'hard_cap;
            $self'current_price = $msg'initial_price;
            $self'auction_status = 1;
            tuple $first_round = $RoundStats$_load_opt(__tact_dict_get_int_cell($self'round_stats, 257, 1));
            if ((~ null?($first_round))) {
                var ($stats'round_number, $stats'start_time, $stats'end_time, $stats'price, $stats'total_raised_ton, $stats'total_raised_usdt, $stats'raised_usdt_equivalent, $stats'tokens_sold, $stats'purchase_count, $stats'unique_users, $stats'refund_count, $stats'refunded_amount_ton, $stats'refunded_amount_usdt, $stats'refunded_usdt_equivalent) = $RoundStats$_not_null($first_round);
                var ($updated_stats'round_number, $updated_stats'start_time, $updated_stats'end_time, $updated_stats'price, $updated_stats'total_raised_ton, $updated_stats'total_raised_usdt, $updated_stats'raised_usdt_equivalent, $updated_stats'tokens_sold, $updated_stats'purchase_count, $updated_stats'unique_users, $updated_stats'refund_count, $updated_stats'refunded_amount_ton, $updated_stats'refunded_amount_usdt, $updated_stats'refunded_usdt_equivalent) = $RoundStats$_constructor_round_number_start_time_end_time_price_total_raised_ton_total_raised_usdt_raised_usdt_equivalent_tokens_sold_purchase_count_unique_users_refund_count_refunded_amount_ton_refunded_amount_usdt_refunded_usdt_equivalent($stats'round_number, $msg'start_time, ($msg'start_time + 3600), $msg'initial_price, $stats'total_raised_ton, $stats'total_raised_usdt, $stats'raised_usdt_equivalent, $stats'tokens_sold, $stats'purchase_count, $stats'unique_users, $stats'refund_count, $stats'refunded_amount_ton, $stats'refunded_amount_usdt, $stats'refunded_usdt_equivalent);
                $self'round_stats~__tact_dict_set_int_cell(257, 1, $RoundStats$_store_cell(($updated_stats'round_number, $updated_stats'start_time, $updated_stats'end_time, $updated_stats'price, $updated_stats'total_raised_ton, $updated_stats'total_raised_usdt, $updated_stats'raised_usdt_equivalent, $updated_stats'tokens_sold, $updated_stats'purchase_count, $updated_stats'unique_users, $updated_stats'refund_count, $updated_stats'refunded_amount_ton, $updated_stats'refunded_amount_usdt, $updated_stats'refunded_usdt_equivalent), begin_cell()));
            }
            $global_emit($AuctionStarted$_store_cell($AuctionStarted$_constructor_start_time_end_time_soft_cap_hard_cap_initial_price_total_supply($msg'start_time, $msg'end_time, $msg'soft_cap, $msg'hard_cap, $msg'initial_price, $self'auction_config'total_supply), begin_cell()));
            $OnionAuction$_contract_store(($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address));
            return ();
        }
        
        ;; Receive UpdateRound message
        if (op == 2861271945) {
            var $msg'new_price = in_msg~load_varuint16();
            var $msg'round_number = in_msg~load_uint(32);
            ($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)~$OnionAuction$_fun_requireOwner();
            throw_unless(51001, ($self'auction_status == 1));
            int $old_round = $self'current_round;
            int $old_price = $self'current_price;
            $self'current_round = $msg'round_number;
            $self'current_price = $msg'new_price;
            tuple $existing_round = $RoundStats$_load_opt(__tact_dict_get_int_cell($self'round_stats, 257, $msg'round_number));
            if (null?($existing_round)) {
                ($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)~$OnionAuction$_fun_initializeNewRound($msg'round_number, $global_now());
            }
            $global_emit($RoundUpdated$_store_cell($RoundUpdated$_constructor_old_round_new_round_old_price_new_price_updated_by($old_round, $msg'round_number, $old_price, $msg'new_price, __tact_context_get_sender()), begin_cell()));
            $OnionAuction$_contract_store(($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address));
            return ();
        }
        
        ;; Receive JettonTransferNotification message
        if (op == 0x7362d09c) {
            var $msg'query_id = in_msg~load_uint(64);
            var $msg'amount = in_msg~load_varuint16();
            var $msg'sender = in_msg~load_msg_addr();
            var $msg'forward_payload = in_msg~load_maybe_ref();
            ($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)~$OnionAuction$_fun_requireNotStopped();
            throw_unless(51001, ($self'auction_status == 1));
            throw_unless(51002, ($global_now() >= $self'auction_config'start_time));
            throw_unless(51003, ($global_now() <= $self'auction_config'end_time));
            throw_unless(51007, (~ null?($self'usdt_config)));
            var ($usdt_config'master_address, $usdt_config'wallet_address, $usdt_config'decimals) = $USDTConfig$_not_null($self'usdt_config);
            slice $expected_wallet = ($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)~$OnionAuction$_fun_getJettonWalletAddress($usdt_config'master_address, $global_myAddress());
            throw_unless(51008, ( equal_slices_bits(__tact_context_get_sender(), $expected_wallet) ));
            throw_unless(51009, (~ null?($msg'forward_payload)));
            ($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)~$OnionAuction$_fun_handleUSDTWithSignature($JettonTransferNotification$_tensor_cast(($msg'query_id, $msg'amount, $msg'sender, $msg'forward_payload)));
            $OnionAuction$_contract_store(($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address));
            return ();
        }
        
        ;; Receive PurchaseWithSignature message
        if (op == 1524770820) {
            var ($msg'calculation'user, $msg'calculation'amount, $msg'calculation'currency, $msg'calculation'tokens_to_receive, $msg'calculation'current_price, $msg'calculation'current_round, $msg'calculation'timestamp, $msg'calculation'nonce, $msg'calculation'usdt_equivalent_amount) = in_msg~$PurchaseCalculation$_load();
            var $msg'signature = in_msg~load_ref().begin_parse();
            ($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)~$OnionAuction$_fun_requireNotStopped();
            throw_unless(51001, ($self'auction_status == 1));
            throw_unless(51002, ($global_now() >= $self'auction_config'start_time));
            throw_unless(51003, ($global_now() <= $self'auction_config'end_time));
            throw_unless(51009, ($self'signing_public_key != 0));
            var ($calc'user, $calc'amount, $calc'currency, $calc'tokens_to_receive, $calc'current_price, $calc'current_round, $calc'timestamp, $calc'nonce, $calc'usdt_equivalent_amount) = ($msg'calculation'user, $msg'calculation'amount, $msg'calculation'currency, $msg'calculation'tokens_to_receive, $msg'calculation'current_price, $msg'calculation'current_round, $msg'calculation'timestamp, $msg'calculation'nonce, $msg'calculation'usdt_equivalent_amount);
            int $current_time = $global_now();
            throw_unless(51010, (($current_time - $calc'timestamp) <= $self'signature_timeout));
            throw_unless(51011, ($calc'timestamp <= $current_time));
            throw_unless(51012, null?(__tact_dict_get_int_int($self'used_nonces, 257, $calc'nonce, 1)));
            int $data_hash = ($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)~$OnionAuction$_fun_hashPurchaseCalculation($PurchaseCalculation$_tensor_cast(($calc'user, $calc'amount, $calc'currency, $calc'tokens_to_receive, $calc'current_price, $calc'current_round, $calc'timestamp, $calc'nonce, $calc'usdt_equivalent_amount)));
            throw_unless(51013, $global_checkSignature($data_hash, $msg'signature, $self'signing_public_key));
            $self'used_nonces~__tact_dict_set_int_int(257, $calc'nonce, true, 1);
            throw_unless(51016, ( equal_slices_bits($calc'user, __tact_context_get_sender()) ));
            throw_unless(51006, (($self'total_tokens_sold + $calc'tokens_to_receive) <= $self'auction_config'total_supply));
            int $current_round_tokens_sold = ($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)~$OnionAuction$_fun_getCurrentRoundTokensSold();
            throw_unless(51022, (($current_round_tokens_sold + $calc'tokens_to_receive) <= $self'auction_config'soft_cap));
            ifnot ($calc'currency) {
                int $ton_received = $Context$_get_value(__tact_context_get());
                int $max_gas_fee = 100000000;
                int $actual_payment = ($ton_received - $max_gas_fee);
                throw_unless(51015, ($actual_payment >= $calc'amount));
                $self'total_raised = $self'total_raised + $calc'amount;
            } elseif (($calc'currency == 1)) {
                throw_unless(51007, (~ null?($self'usdt_config)));
                $self'total_raised_usdt = $self'total_raised_usdt + $calc'amount;
            } else {
                throw_unless(51004, false);
            }
            $self'total_raised_usdt_equivalent = $self'total_raised_usdt_equivalent + $calc'usdt_equivalent_amount;
            $self'total_tokens_sold = $self'total_tokens_sold + $calc'tokens_to_receive;
            $self'purchase_count = $self'purchase_count + 1;
            ($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)~$OnionAuction$_fun_updateRoundStatsForPurchase($calc'user, $calc'amount, $calc'tokens_to_receive, $calc'currency, $calc'usdt_equivalent_amount, $calc'current_round);
            if (($self'total_tokens_sold >= $self'auction_config'hard_cap)) {
                $self'auction_status = 2;
                $global_emit($AuctionEnded$_store_cell($AuctionEnded$_constructor_end_reason_final_status_total_raised_total_raised_usdt_total_tokens_sold_end_time(2, $self'auction_status, $self'total_raised, $self'total_raised_usdt, $self'total_tokens_sold, $global_now()), begin_cell()));
            }
            ($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)~$OnionAuction$_fun_createOrUpdateUserPurchase($calc'user, $calc'amount, $calc'tokens_to_receive, $calc'currency, 1, $calc'nonce, $calc'usdt_equivalent_amount, $calc'current_round);
            $global_emit($PurchaseCompleted$_store_cell($PurchaseCompleted$_constructor_user_amount_tokens_received_currency_purchase_method_round_number_nonce_total_raised_total_raised_usdt_total_tokens_sold($calc'user, $calc'amount, $calc'tokens_to_receive, $calc'currency, 1, $calc'current_round, $calc'nonce, $self'total_raised, $self'total_raised_usdt, $self'total_tokens_sold), begin_cell()));
            $OnionAuction$_contract_store(($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address));
            return ();
        }
        
        ;; Receive ProcessRefund message
        if (op == 0x376e8b12) {
            var $msg'user = in_msg~load_msg_addr();
            var $msg'amount = in_msg~load_varuint16();
            var $msg'fee = in_msg~load_varuint16();
            var $msg'currency = in_msg~load_uint(8);
            var $msg'round_number = in_msg~load_uint(32);
            var $msg'usdt_equivalent_amount = in_msg~load_varuint16();
            ($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)~$OnionAuction$_fun_requireNotStopped();
            throw_unless(51001, ($self'auction_status == 1));
            throw_unless(51003, ($global_now() <= $self'auction_config'end_time));
            slice $expected_user_purchase_addr = $global_contractAddress(($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)~$OnionAuction$_fun_getUserPurchaseInit($msg'user));
            throw_unless(51017, ( equal_slices_bits($expected_user_purchase_addr, __tact_context_get_sender()) ));
            int $fee = (($msg'amount * $self'auction_config'refund_fee_percent) / 100);
            int $refund_amount = ($msg'amount - $fee);
            ($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)~$OnionAuction$_fun_updateRoundStatsForRefund($msg'round_number, $msg'amount, $msg'currency, $msg'usdt_equivalent_amount);
            $self'total_raised_usdt_equivalent = $self'total_raised_usdt_equivalent - $msg'usdt_equivalent_amount;
            ifnot ($msg'currency) {
                $self'total_raised = $self'total_raised - $msg'amount;
                $global_send($SendParameters$_constructor_to_value_mode_bounce_body($msg'user, $refund_amount, 2, false, __gen_cell_cell_825758f733fea5d82ca064c56d16fb0595eceab880f99af5df41022202817708()));
            } elseif (($msg'currency == 1)) {
                throw_unless(51007, (~ null?($self'usdt_config)));
                $self'total_raised_usdt = $self'total_raised_usdt - $msg'amount;
                var ($usdt_config'master_address, $usdt_config'wallet_address, $usdt_config'decimals) = $USDTConfig$_not_null($self'usdt_config);
                throw_unless(51007, (~ null?($usdt_config'wallet_address)));
                $global_send($SendParameters$_constructor_to_value_bounce_body(__tact_not_null($usdt_config'wallet_address), 100000000, false, $JettonTransfer$_store_cell($JettonTransfer$_constructor_query_id_amount_destination_response_destination_custom_payload_forward_ton_amount_forward_payload(0, $refund_amount, $msg'user, $global_myAddress(), null(), 1, __gen_cell_cell_d0e7934e0cb163025f7c7de7b09db078eae6f58fbaac5d9516a3756484e9fdc2()), begin_cell())));
            }
            $OnionAuction$_contract_store(($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address));
            return ();
        }
        
        ;; Receive Deploy message
        if (op == 0x946a98b6) {
            var $deploy'queryId = in_msg~load_uint(64);
            ($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)~$OnionAuction$_fun_notify($DeployOk$_store_cell($DeployOk$_constructor_queryId($deploy'queryId), begin_cell()));
            $OnionAuction$_contract_store(($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address));
            return ();
        }
        
    }
    ;; Empty Receiver and Text Receivers
    var text_op = slice_hash(in_msg);
    ;; Receive "Deploy" message
    if (text_op == 0xc1c8ebe8e42f1458f2693e8bef345c9c08db8c56d2ca637be9b436ea1f68976f) {
        $OnionAuction$_contract_store(($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address));
        return ();
    }
    ;; Receive "end_auction" message
    if (text_op == 0xd3d827ac7e90e33b514279694e8c8b17dee75b92013c46c69b017320fe9f3526) {
        ($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)~$OnionAuction$_fun_requireOwner();
        throw_unless(51001, ($self'auction_status == 1));
        int $end_reason = 3;
        if (($global_now() > $self'auction_config'end_time)) {
            $end_reason = 1;
        } elseif (($self'total_tokens_sold >= $self'auction_config'hard_cap)) {
            $end_reason = 2;
        }
        int $final_status = 3;
        if (($self'total_tokens_sold > 0)) {
            $final_status = 2;
        }
        $self'auction_status = $final_status;
        $global_emit($AuctionEnded$_store_cell($AuctionEnded$_constructor_end_reason_final_status_total_raised_total_raised_usdt_total_tokens_sold_end_time($end_reason, $final_status, $self'total_raised, $self'total_raised_usdt, $self'total_tokens_sold, $global_now()), begin_cell()));
        $OnionAuction$_contract_store(($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address));
        return ();
    }
    ;; Receive "Stop" message
    if (text_op == 0xe2766386272bddd6151b177e581ac9f613131b45e13cdc7af80e4091130d73e8) {
        ($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)~$OnionAuction$_fun_requireOwner();
        ($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)~$OnionAuction$_fun_requireNotStopped();
        $self'stopped = true;
        ($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address)~$OnionAuction$_fun_reply(__gen_cell_cell_8f4b6a166554b4f282855d0333c627d5b58d281ea4ba11e4def91a1053193b2d());
        $OnionAuction$_contract_store(($self'owner, $self'stopped, ($self'auction_config'start_time, $self'auction_config'end_time, $self'auction_config'soft_cap, $self'auction_config'hard_cap, $self'auction_config'total_supply, $self'auction_config'refund_fee_percent), $self'current_round, $self'current_price, $self'total_raised, $self'total_tokens_sold, $self'auction_status, $self'usdt_config, $self'total_raised_usdt, $self'total_raised_usdt_equivalent, $self'purchase_count, $self'round_stats, $self'user_round_purchases, $self'signing_public_key, $self'used_nonces, $self'signature_timeout, $self'min_purchase, $self'treasury_address));
        return ();
    }
    ;; Throw if not handled
    throw(130);
}


() __tact_selector_hack_asm() impure asm """
@atend @ 1 {
        execute current@ context@ current!
        {
            // The core idea of this function is to save gas by avoiding unnecessary dict jump, when recv_internal/recv_external is called
            // We want to extract recv_internal/recv_external from the dict and select needed function
            // not by jumping to the needed function by it's index, but by using usual IF statements.

            }END> b> // Close previous builder, now we have a cell of previous code on top of the stack

            <{ // Start of the new code builder
                SETCP0
                // Swap the new code builder with the previous code, now we have previous code on top of the stack
                swap
                // Transform cell to slice and load first ref from the previous code, now we have the dict on top of the stack
                <s ref@

                // Extract the recv_internal from the dict
                dup 0 swap @procdictkeylen idict@ { "internal shortcut error" abort } ifnot
                swap

                // Delete the recv_internal from the dict
                0 swap @procdictkeylen idict- drop
                // Delete the recv_external from the dict (it's okay if it's not there)
                -1 swap @procdictkeylen idict- drop
                // Delete the __tact_selector_hack from the dict
                65535 swap @procdictkeylen idict- drop

                // Bring the code builder from the bottom of the stack
                // because if recv_external extraction is optional, and the number of elements on the stack is not fixed
                depth 1- roll
                // Swap with the dict from which we extracted recv_internal and (maybe) recv_external
                swap

                // Check if the dict is empty
                dup null?
                // Store a copy of this flag in the bottom of the stack
                dup depth 1- -roll
                {
                    // If the dict is empty, just drop it (it will be null if it's empty)
                    drop
                }
                {
                    // If the dict is not empty, prepare continuation to be stored in c3
                    <{
                        // Save this dict as first ref in this continuation, it will be pushed in runtime by DICTPUSHCONST
                        swap @procdictkeylen DICTPUSHCONST
                        // Jump to the needed function by it's index
                        DICTIGETJMPZ
                        // If such key is not found, throw 11 along with the key as an argument
                        11 THROWARG
                    }> PUSHCONT
                    // Store the continuation in c3
                    c3 POP
                } cond

                // Function id is on top of the (runtime) stack
                DUP IFNOTJMP:<{
                    // place recv_internal here
                    DROP swap @addop
                }>

                // Bring back the flag, indicating if the dict is empty or not from the bottom of the stack
                depth 1- roll
                {
                    // If the dict is empty, throw 11
                    11 THROWARG
                }
                {
                    // If the dict is not empty, jump to continuation from c3
                    c3 PUSH JMPX
                } cond
            }> b>
        } : }END>c
        current@ context! current!
    } does @atend !
""";

() __tact_selector_hack() method_id(65535) {
    return __tact_selector_hack_asm();
}