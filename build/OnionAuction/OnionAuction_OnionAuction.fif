// automatically generated from `@stdlib/std/stdlib.fc` `/Users/<USER>/code/tbook/onton_cc/onion-launch/build/OnionAuction/OnionAuction_OnionAuction.fc` 
PROGRAM{
  DECLPROC __tact_store_address_opt
  DECLPROC __tact_not_null
  DECLPROC __tact_context_get
  DECLPROC __tact_context_get_sender
  DECLPROC __tact_dict_get_slice_int
  DECLPROC __tact_dict_set_slice_int
  DECLPROC __tact_dict_get_int_int
  DECLPROC __tact_dict_set_int_int
  DECLPROC __tact_dict_get_int_cell
  DECLPROC __tact_dict_set_int_cell
  DECLPROC $DeployOk$_store
  DECLPROC $DeployOk$_store_cell
  DECLPROC $JettonTransfer$_store
  DECLPROC $JettonTransfer$_store_cell
  DECLPROC $CreateUserPurchase$_store
  DECLPROC $CreateUserPurchase$_store_cell
  DECLPROC $PurchaseCalculation$_load
  DECLPROC $AuctionStarted$_store
  DECLPROC $AuctionStarted$_store_cell
  DECLPROC $PurchaseCompleted$_store
  DECLPROC $PurchaseCompleted$_store_cell
  DECLPROC $USDTConfigured$_store
  DECLPROC $USDTConfigured$_store_cell
  DECLPROC $SigningKeySet$_store
  DECLPROC $SigningKeySet$_store_cell
  DECLPROC $MinPurchaseUpdated$_store
  DECLPROC $MinPurchaseUpdated$_store_cell
  DECLPROC $TreasurySet$_store
  DECLPROC $TreasurySet$_store_cell
  DECLPROC $FundsWithdrawn$_store
  DECLPROC $FundsWithdrawn$_store_cell
  DECLPROC $RoundUpdated$_store
  DECLPROC $RoundUpdated$_store_cell
  DECLPROC $AuctionEnded$_store
  DECLPROC $AuctionEnded$_store_cell
  DECLPROC $AuctionConfig$_store
  DECLPROC $AuctionConfig$_load
  DECLPROC $RoundStats$_store
  DECLPROC $RoundStats$_store_cell
  DECLPROC $RoundStats$_load
  DECLPROC $RoundStats$_as_optional
  DECLPROC $RoundStats$_load_opt
  DECLPROC $USDTConfig$_store
  DECLPROC $USDTConfig$_load
  DECLPROC $USDTConfig$_not_null
  DECLPROC $OnionAuction$_store
  DECLPROC $USDTConfig$_as_optional
  DECLPROC $OnionAuction$_load
  DECLPROC $Context$_get_value
  DECLPROC $AuctionConfig$_to_external
  DECLPROC $RoundStats$_get_tokens_sold
  DECLPROC $RoundStats$_get_unique_users
  DECLPROC $RoundStats$_not_null
  DECLPROC $RoundStats$_to_tuple
  DECLPROC $RoundStats$_to_opt_tuple
  DECLPROC $RoundStats$_to_external
  DECLPROC $RoundStats$_to_opt_external
  DECLPROC $USDTConfig$_to_tuple
  DECLPROC $USDTConfig$_to_opt_tuple
  DECLPROC $USDTConfig$_to_opt_external
  DECLPROC $UserPurchase$init$_store
  DECLPROC $OnionAuction$init$_load
  DECLPROC $AuctionConfig$_constructor_start_time_end_time_soft_cap_hard_cap_total_supply_refund_fee_percent
  DECLPROC $RoundStats$_constructor_round_number_start_time_end_time_price_total_raised_ton_total_raised_usdt_raised_usdt_equivalent_tokens_sold_purchase_count_unique_users_refund_count_refunded_amount_ton_refunded_amount_usdt_refunded_usdt_equivalent
  DECLPROC $OnionAuction$_fun_initializeFirstRound
  DECLPROC $OnionAuction$_contract_init
  DECLPROC $OnionAuction$_contract_load
  DECLPROC $OnionAuction$_contract_store
  DECLPROC $Cell$_fun_asSlice
  DECLPROC $global_newAddress
  DECLPROC $global_contractAddressExt
  DECLPROC $global_contractAddress
  DECLPROC $global_emit
  DECLPROC $UserPurchase$_init_child
  DECLPROC $MessageParameters$_constructor_bounce_to_value_mode_body
  DECLPROC $OnionAuction$_fun_initializeNewRound
  DECLPROC $OnionAuction$_fun_updateRoundStatsForPurchase
  DECLPROC $OnionAuction$_fun_updateRoundStatsForRefund
  DECLPROC $SendParameters$_constructor_to_value_bounce_body_code_data
  DECLPROC $CreateUserPurchase$_constructor_user_amount_tokens_currency_purchase_method_nonce_round_number_usdt_equivalent_amount
  DECLPROC $OnionAuction$_fun_getUserPurchaseInit
  DECLPROC $OnionAuction$_fun_createOrUpdateUserPurchase
  DECLPROC $AuctionEnded$_constructor_end_reason_final_status_total_raised_total_raised_usdt_total_tokens_sold_end_time
  DECLPROC $PurchaseCompleted$_constructor_user_amount_tokens_received_currency_purchase_method_round_number_nonce_total_raised_total_raised_usdt_total_tokens_sold
  DECLPROC $PurchaseCalculation$_constructor_user_amount_currency_tokens_to_receive_current_price_current_round_timestamp_nonce_usdt_equivalent_amount
  DECLPROC $ParsedPurchaseData$_constructor_calculation_signature
  DECLPROC $OnionAuction$_fun_parsePurchaseFromPayload
  DECLPROC $OnionAuction$_fun_hashPurchaseCalculation
  DECLPROC $OnionAuction$_fun_getCurrentRoundTokensSold
  DECLPROC $OnionAuction$_fun_handleUSDTWithSignature
  DECLPROC $OnionAuction$_fun_getJettonWalletAddress
  DECLPROC $OnionAuction$_fun_auction_info
  DECLPROC $OnionAuction$_fun_current_round
  DECLPROC $OnionAuction$_fun_current_price
  DECLPROC $OnionAuction$_fun_total_raised
  DECLPROC $OnionAuction$_fun_total_tokens_sold
  DECLPROC $OnionAuction$_fun_auction_status
  DECLPROC $OnionAuction$_fun_purchase_count
  DECLPROC $OnionAuction$_fun_user_purchase_address
  DECLPROC $OnionAuction$_fun_remaining_tokens
  DECLPROC $OnionAuction$_fun_is_auction_active
  DECLPROC $OnionAuction$_fun_usdt_config
  DECLPROC $OnionAuction$_fun_total_raised_usdt
  DECLPROC $OnionAuction$_fun_total_raised_equivalent
  DECLPROC $OnionAuction$_fun_total_raised_usdt_equivalent
  DECLPROC $OnionAuction$_fun_is_usdt_enabled
  DECLPROC $OnionAuction$_fun_signing_public_key
  DECLPROC $OnionAuction$_fun_signature_timeout
  DECLPROC $OnionAuction$_fun_is_nonce_used
  DECLPROC $OnionAuction$_fun_is_signature_verification_enabled
  DECLPROC $OnionAuction$_fun_min_purchase
  DECLPROC $OnionAuction$_fun_treasury_address
  DECLPROC $OnionAuction$_fun_withdrawable_ton
  DECLPROC $OnionAuction$_fun_withdrawable_usdt
  DECLPROC $OnionAuction$_fun_can_withdraw
  DECLPROC $OnionAuction$_fun_withdrawal_summary
  DECLPROC $OnionAuction$_fun_round_stats
  DECLPROC $OnionAuction$_fun_current_round_stats
  DECLPROC $OnionAuction$_fun_total_rounds
  DECLPROC $OnionAuction$_fun_round_summary
  DECLPROC $OnionAuction$_fun_all_rounds_summary
  DECLPROC $OnionAuction$_fun_round_participants_count
  DECLPROC $OnionAuction$_fun_aggregated_stats
  DECLPROC $OnionAuction$_fun_reply
  DECLPROC $OnionAuction$_fun_notify
  DECLPROC $OnionAuction$_fun_requireOwner
  DECLPROC $OnionAuction$_fun_owner
  DECLPROC $OnionAuction$_fun_requireNotStopped
  DECLPROC $OnionAuction$_fun_stopped
  DECLPROC $USDTConfig$_constructor_master_address_wallet_address_decimals
  DECLPROC $USDTConfigured$_constructor_usdt_master_usdt_wallet_configured_by
  DECLPROC $SigningKeySet$_constructor_public_key_set_by
  DECLPROC $MinPurchaseUpdated$_constructor_old_min_purchase_new_min_purchase_updated_by
  DECLPROC $TreasurySet$_constructor_old_treasury_new_treasury_set_by
  DECLPROC $SendParameters$_constructor_to_value_mode_bounce_body
  DECLPROC $FundsWithdrawn$_constructor_currency_amount_destination_withdrawn_by_remaining_balance
  DECLPROC $SendParameters$_constructor_to_value_bounce_body
  DECLPROC $JettonTransfer$_constructor_query_id_amount_destination_response_destination_custom_payload_forward_ton_amount_forward_payload
  DECLPROC $AuctionStarted$_constructor_start_time_end_time_soft_cap_hard_cap_initial_price_total_supply
  DECLPROC $RoundUpdated$_constructor_old_round_new_round_old_price_new_price_updated_by
  DECLPROC $DeployOk$_constructor_queryId
  72669 DECLMETHOD %auction_info
  108432 DECLMETHOD %current_round
  102733 DECLMETHOD %current_price
  130922 DECLMETHOD %total_raised
  81273 DECLMETHOD %total_tokens_sold
  82124 DECLMETHOD %auction_status
  69567 DECLMETHOD %purchase_count
  107761 DECLMETHOD %user_purchase_address
  95722 DECLMETHOD %remaining_tokens
  121388 DECLMETHOD %is_auction_active
  95594 DECLMETHOD %usdt_config
  105864 DECLMETHOD %total_raised_usdt
  89664 DECLMETHOD %total_raised_equivalent
  90613 DECLMETHOD %total_raised_usdt_equivalent
  123065 DECLMETHOD %is_usdt_enabled
  126473 DECLMETHOD %signing_public_key
  91556 DECLMETHOD %signature_timeout
  99217 DECLMETHOD %is_nonce_used
  111108 DECLMETHOD %is_signature_verification_enabled
  110922 DECLMETHOD %min_purchase
  71868 DECLMETHOD %treasury_address
  102777 DECLMETHOD %withdrawable_ton
  97270 DECLMETHOD %withdrawable_usdt
  115239 DECLMETHOD %can_withdraw
  126996 DECLMETHOD %withdrawal_summary
  86928 DECLMETHOD %round_stats
  124822 DECLMETHOD %current_round_stats
  78978 DECLMETHOD %total_rounds
  86664 DECLMETHOD %round_summary
  83191 DECLMETHOD %all_rounds_summary
  85355 DECLMETHOD %round_participants_count
  112308 DECLMETHOD %aggregated_stats
  83229 DECLMETHOD %owner
  74107 DECLMETHOD %stopped
  DECLPROC recv_internal
  65535 DECLMETHOD __tact_selector_hack
  DECLGLOBVAR __tact_context
  DECLGLOBVAR __tact_context_sender
  DECLGLOBVAR __tact_child_contract_codes
  DECLGLOBVAR __tact_randomized
  __tact_store_address_opt PROCINLINE:<{
    DUP
    ISNULL
    IF:<{
      DROP
      b{00} STSLICECONST
    }>ELSE<{
      SWAP
      STSLICE
    }>
  }>
  __tact_not_null PROCINLINE:<{
    DUP
    ISNULL
    128 THROWIF
  }>
  __tact_context_get PROCINLINE:<{
    __tact_context GETGLOB
    4 UNTUPLE
  }>
  __tact_context_get_sender PROCINLINE:<{
    __tact_context_sender GETGLOB
  }>
  __tact_dict_get_slice_int PROCINLINE:<{
    s1 s3 s3 XCHG3
        DICTGET NULLSWAPIFNOT
    IF:<{
      SWAP
      LDIX
      DROP
    }>ELSE<{
      2DROP
      PUSHNULL
    }>
  }>
  __tact_dict_set_slice_int PROCINLINE:<{
    OVER
    ISNULL
    IF:<{
      2DROP
      -ROT
          DICTDEL
      DROP
    }>ELSE<{
      NEWC
      SWAP
      STIX
      s1 s3 s3 XCHG3
      DICTSETB
    }>
  }>
  __tact_dict_get_int_int PROCINLINE:<{
    s1 s3 s3 XCHG3
    DICTIGET
    NULLSWAPIFNOT
    IF:<{
      SWAP
      LDIX
      DROP
    }>ELSE<{
      2DROP
      PUSHNULL
    }>
  }>
  __tact_dict_set_int_int PROCINLINE:<{
    OVER
    ISNULL
    IF:<{
      2DROP
      -ROT
      DICTIDEL
      DROP
    }>ELSE<{
      NEWC
      SWAP
      STIX
      s1 s3 s3 XCHG3
      DICTISETB
    }>
  }>
  __tact_dict_get_int_cell PROCINLINE:<{
    -ROT
    DICTIGETREF
    NULLSWAPIFNOT
    IF:<{
    }>ELSE<{
      DROP
      PUSHNULL
    }>
  }>
  __tact_dict_set_int_cell PROCINLINE:<{
    DUP
    ISNULL
    IF:<{
      DROP
      -ROT
      DICTIDEL
      DROP
    }>ELSE<{
      s1 s3 s3 XCHG3
      DICTISETREF
    }>
  }>
  $DeployOk$_store PROCINLINE:<{
    2952335191 PUSHINT
    ROT
    32 STU
    64 STU
  }>
  $DeployOk$_store_cell PROCINLINE:<{
    SWAP
    $DeployOk$_store INLINECALLDICT
    ENDC
  }>
  $JettonTransfer$_store PROCINLINE:<{
    260734629 PUSHINT
    s0 s8 XCHG2
    32 STU
    s1 s6 XCHG
    64 STU
    s0 s4 XCHG2
    STVARUINT16
    s1 s2 XCHG
    STSLICE
    STSLICE
    STOPTREF
    SWAP
    STVARUINT16
    STOPTREF
  }>
  $JettonTransfer$_store_cell PROCINLINE:<{
    7 -ROLL
    $JettonTransfer$_store INLINECALLDICT
    ENDC
  }>
  $CreateUserPurchase$_store PROCINLINE:<{
    1098075148 PUSHINT
    s0 s9 XCHG2
    32 STU
    s1 s7 XCHG
    STSLICE
    s0 s5 XCHG2
    STVARUINT16
    s0 s3 XCHG2
    STVARUINT16
    8 STU
    8 STU
    64 STU
    32 STU
    SWAP
    STVARUINT16
  }>
  $CreateUserPurchase$_store_cell PROCINLINE:<{
    8 -ROLL
    $CreateUserPurchase$_store INLINECALLDICT
    ENDC
  }>
  $PurchaseCalculation$_load PROCINLINE:<{
    LDMSGADDR
    LDVARUINT16
    8 LDU
    LDVARUINT16
    LDVARUINT16
    32 LDU
    64 LDU
    64 LDU
    LDVARUINT16
    9 -ROLL
  }>
  $AuctionStarted$_store PROCINLINE:<{
    2217068725 PUSHINT
    s0 s7 XCHG2
    32 STU
    s1 s5 XCHG
    64 STU
    s1 s3 XCHG
    64 STU
    SWAP
    STVARUINT16
    SWAP
    STVARUINT16
    SWAP
    STVARUINT16
    SWAP
    STVARUINT16
  }>
  $AuctionStarted$_store_cell PROCINLINE:<{
    6 -ROLL
    $AuctionStarted$_store INLINECALLDICT
    ENDC
  }>
  $PurchaseCompleted$_store PROCINLINE:<{
    3975568033 PUSHINT
    s0 s11 XCHG2
    32 STU
    s1 s9 XCHG
    STSLICE
    s0 s7 XCHG2
    STVARUINT16
    s0 s5 XCHG2
    STVARUINT16
    s1 s3 XCHG
    8 STU
    8 STU
    32 STU
    64 STU
    SWAP
    STVARUINT16
    SWAP
    STVARUINT16
    NEWC
    ROT
    STVARUINT16
    STBREFR
  }>
  $PurchaseCompleted$_store_cell PROCINLINE:<{
    10 -ROLL
    $PurchaseCompleted$_store INLINECALLDICT
    ENDC
  }>
  $USDTConfigured$_store PROCINLINE:<{
    1031232428 PUSHINT
    s0 s4 XCHG2
    32 STU
    s1 s2 XCHG
    STSLICE
    STSLICE
    STSLICE
  }>
  $USDTConfigured$_store_cell PROCINLINE:<{
    3 -ROLL
    $USDTConfigured$_store INLINECALLDICT
    ENDC
  }>
  $SigningKeySet$_store PROCINLINE:<{
    2441211440 PUSHINT
    s0 s3 XCHG2
    32 STU
    256 STU
    STSLICE
  }>
  $SigningKeySet$_store_cell PROCINLINE:<{
    -ROT
    $SigningKeySet$_store INLINECALLDICT
    ENDC
  }>
  $MinPurchaseUpdated$_store PROCINLINE:<{
    760782792 PUSHINT
    s0 s4 XCHG2
    32 STU
    ROT
    STVARUINT16
    SWAP
    STVARUINT16
    STSLICE
  }>
  $MinPurchaseUpdated$_store_cell PROCINLINE:<{
    3 -ROLL
    $MinPurchaseUpdated$_store INLINECALLDICT
    ENDC
  }>
  $TreasurySet$_store PROCINLINE:<{
    4013598566 PUSHINT
    s0 s4 XCHG2
    32 STU
    s1 s2 XCHG
    STSLICE
    STSLICE
    STSLICE
  }>
  $TreasurySet$_store_cell PROCINLINE:<{
    3 -ROLL
    $TreasurySet$_store INLINECALLDICT
    ENDC
  }>
  $FundsWithdrawn$_store PROCINLINE:<{
    3674912463 PUSHINT
    s0 s6 XCHG2
    32 STU
    s1 s4 XCHG
    8 STU
    ROT
    STVARUINT16
    STSLICE
    STSLICE
    SWAP
    STVARUINT16
  }>
  $FundsWithdrawn$_store_cell PROCINLINE:<{
    5 -ROLL
    $FundsWithdrawn$_store INLINECALLDICT
    ENDC
  }>
  $RoundUpdated$_store PROCINLINE:<{
    190065307 PUSHINT
    s0 s6 XCHG2
    32 STU
    s1 s4 XCHG
    32 STU
    s1 s2 XCHG
    32 STU
    SWAP
    STVARUINT16
    SWAP
    STVARUINT16
    STSLICE
  }>
  $RoundUpdated$_store_cell PROCINLINE:<{
    5 -ROLL
    $RoundUpdated$_store INLINECALLDICT
    ENDC
  }>
  $AuctionEnded$_store PROCINLINE:<{
    397814633 PUSHINT
    s0 s7 XCHG2
    32 STU
    s1 s5 XCHG
    8 STU
    s1 s3 XCHG
    8 STU
    SWAP
    STVARUINT16
    SWAP
    STVARUINT16
    SWAP
    STVARUINT16
    64 STU
  }>
  $AuctionEnded$_store_cell PROCINLINE:<{
    6 -ROLL
    $AuctionEnded$_store INLINECALLDICT
    ENDC
  }>
  $AuctionConfig$_store PROCINLINE:<{
    s5 s6 XCHG2
    64 STU
    s1 s3 XCHG
    64 STU
    SWAP
    STVARUINT16
    SWAP
    STVARUINT16
    SWAP
    STVARUINT16
    8 STU
  }>
  $AuctionConfig$_load PROCINLINE:<{
    64 LDU
    64 LDU
    LDVARUINT16
    LDVARUINT16
    LDVARUINT16
    8 LDU
    6 -ROLL
  }>
  $RoundStats$_store PROCREF:<{
    s13 s14 XCHG2
    32 STU
    s1 s11 XCHG
    64 STU
    s1 s9 XCHG
    64 STU
    s0 s7 XCHG2
    STVARUINT16
    s0 s5 XCHG2
    STVARUINT16
    s0 s3 XCHG2
    STVARUINT16
    SWAP
    STVARUINT16
    SWAP
    STVARUINT16
    32 STU
    32 STU
    32 STU
    SWAP
    STVARUINT16
    NEWC
    ROT
    STVARUINT16
    ROT
    STVARUINT16
    STBREFR
  }>
  $RoundStats$_store_cell PROCINLINE:<{
    14 -ROLL
    $RoundStats$_store INLINECALLDICT
    ENDC
  }>
  $RoundStats$_load PROCREF:<{
    32 LDU
    64 LDU
    64 LDU
    LDVARUINT16
    LDVARUINT16
    LDVARUINT16
    LDVARUINT16
    LDVARUINT16
    32 LDU
    32 LDU
    32 LDU
    LDVARUINT16
    LDREF
    SWAP
    CTOS
    LDVARUINT16
    LDVARUINT16
    DROP
    s2 s14 XCHG
    s2 s13 XCHG
    s2 s12 XCHG
    s2 s11 XCHG
    s2 s10 XCHG
    s2 s9 XCHG
    s2 s8 XCHG
    s2 s7 XCHG
    s2 s6 XCHG
    s2 s5 XCHG
    s2 s4 XCHG
    s2 s3 XCHG
  }>
  $RoundStats$_as_optional PROCINLINE:<{
        14 TUPLE
  }>
  $RoundStats$_load_opt PROCINLINE:<{
    DUP
    ISNULL
    IF:<{
      DROP
      PUSHNULL
    }>ELSE<{
      CTOS
      $RoundStats$_load INLINECALLDICT
      1 14 BLKDROP2
      $RoundStats$_as_optional INLINECALLDICT
    }>
  }>
  $USDTConfig$_store PROCINLINE:<{
    s2 s3 XCHG2
    STSLICE
    SWAP
    __tact_store_address_opt INLINECALLDICT
    8 STU
  }>
  $USDTConfig$_load PROCINLINE:<{
    LDMSGADDR
                b{00} SDBEGINSQ
                IF:<{
                  PUSHNULL
                }>ELSE<{
                  LDMSGADDR
                  SWAP
                }>
    SWAP
    8 LDU
    3 -ROLL
  }>
  $USDTConfig$_not_null PROCINLINE:<{
    DUP
    ISNULL
    128 THROWIF
        3 UNTUPLE
  }>
  $OnionAuction$_store PROCINLINE:<{
    s1 23 s() XCHG
    s0 24 s() XCHG
    STSLICE
    s1 21 s() XCHG
    1 STI
    s0 s6 XCHG
    s5 19 s() XCHG
    s4 18 s() XCHG
    s3 17 s() XCHG
    s2 16 s() XCHG
    s15 s14 XCHG2
    $AuctionConfig$_store INLINECALLDICT
    s1 s7 XCHG
    32 STU
    s0 s5 XCHG2
    STVARUINT16
    NEWC
    s0 s4 XCHG2
    STVARUINT16
    ROT
    STVARUINT16
    8 STU
    s2 PUSH
    ISNULL
    NOT
    IF:<{
      TRUE
      SWAP
      1 STI
      s0 s2 XCHG
      $USDTConfig$_not_null INLINECALLDICT
      s3 s4 XCHG
      $USDTConfig$_store INLINECALLDICT
    }>ELSE<{
      s2 POP
      FALSE
      ROT
      1 STI
    }>
    ROT
    STVARUINT16
    NEWC
    s0 s3 XCHG2
    STVARUINT16
    s1 s7 XCHG
    32 STU
    s1 s5 XCHG
    STDICT
    s1 s3 XCHG
    STDICT
    256 STU
    STDICT
    s1 s3 XCHG
    64 STU
    s0 s3 XCHG2
    STVARUINT16
    s0 s3 XCHG2
    __tact_store_address_opt INLINECALLDICT
    s1 s2 XCHG
    STBREFR
    STBREFR
  }>
  $USDTConfig$_as_optional PROCINLINE:<{
        3 TUPLE
  }>
  $OnionAuction$_load PROCINLINE:<{
    LDMSGADDR
    1 LDI
    $AuctionConfig$_load INLINECALLDICT
    s0 s6 XCHG
    32 LDU
    LDVARUINT16
    LDREF
    SWAP
    CTOS
    LDVARUINT16
    LDVARUINT16
    8 LDU
    1 LDI
    SWAP
    IF:<{
      $USDTConfig$_load INLINECALLDICT
      $USDTConfig$_as_optional INLINECALLDICT
    }>ELSE<{
      PUSHNULL
    }>
    SWAP
    LDVARUINT16
    LDREF
    DROP
    CTOS
    LDVARUINT16
    32 LDU
    LDDICT
    LDDICT
    256 LDU
    LDDICT
    64 LDU
    LDVARUINT16
                b{00} SDBEGINSQ
                IF:<{
                  PUSHNULL
                }>ELSE<{
                  LDMSGADDR
                  SWAP
                }>
    NIP
    s14 24 s() XCHG
    s14 23 s() XCHG
    s14 22 s() XCHG
    s14 16 s() XCHG
    s14 s15 XCHG
  }>
  $Context$_get_value PROCINLINE:<{
    s1 s3 XCHG
    3 BLKDROP
  }>
  $AuctionConfig$_to_external PROCINLINE:<{
  }>
  $RoundStats$_get_tokens_sold PROCINLINE:<{
    s6 s13 XCHG
    13 BLKDROP
  }>
  $RoundStats$_get_unique_users PROCINLINE:<{
    s4 s13 XCHG
    13 BLKDROP
  }>
  $RoundStats$_not_null PROCINLINE:<{
    DUP
    ISNULL
    128 THROWIF
        14 UNTUPLE
  }>
  $RoundStats$_to_tuple PROCINLINE:<{
        14 TUPLE
  }>
  $RoundStats$_to_opt_tuple PROCINLINE:<{
    DUP
    ISNULL
    IF:<{
      DROP
      PUSHNULL
    }>ELSE<{
      $RoundStats$_not_null INLINECALLDICT
      $RoundStats$_to_tuple INLINECALLDICT
    }>
  }>
  $RoundStats$_to_external PROCINLINE:<{
  }>
  $RoundStats$_to_opt_external PROCINLINE:<{
    $RoundStats$_to_opt_tuple INLINECALLDICT
    DUP
    ISNULL
    IF:<{
      DROP
      PUSHNULL
    }>ELSE<{
    }>
  }>
  $USDTConfig$_to_tuple PROCINLINE:<{
        3 TUPLE
  }>
  $USDTConfig$_to_opt_tuple PROCINLINE:<{
    DUP
    ISNULL
    IF:<{
      DROP
      PUSHNULL
    }>ELSE<{
      $USDTConfig$_not_null INLINECALLDICT
      $USDTConfig$_to_tuple INLINECALLDICT
    }>
  }>
  $USDTConfig$_to_opt_external PROCINLINE:<{
    $USDTConfig$_to_opt_tuple INLINECALLDICT
    DUP
    ISNULL
    IF:<{
      DROP
      PUSHNULL
    }>ELSE<{
    }>
  }>
  $UserPurchase$init$_store PROCINLINE:<{
    s0 s2 XCHG
    STSLICE
    STSLICE
  }>
  $OnionAuction$init$_load PROCINLINE:<{
    LDMSGADDR
    257 PUSHINT
    LDIX
    257 PUSHINT
    LDIX
    LDREF
    SWAP
    CTOS
    257 PUSHINT
    LDIX
    257 PUSHINT
    LDIX
    257 PUSHINT
    LDIX
    DROP
    s3 s6 XCHG
    s3 s5 XCHG
    s3 s4 XCHG
  }>
  $AuctionConfig$_constructor_start_time_end_time_soft_cap_hard_cap_total_supply_refund_fee_percent PROCINLINE:<{
  }>
  $RoundStats$_constructor_round_number_start_time_end_time_price_total_raised_ton_total_raised_usdt_raised_usdt_equivalent_tokens_sold_purchase_count_unique_users_refund_count_refunded_amount_ton_refunded_amount_usdt_refunded_usdt_equivalent PROCINLINE:<{
  }>
  $OnionAuction$_fun_initializeFirstRound PROCREF:<{
    1 PUSHINT
    0 PUSHINT
    s0 s0 s0 PUSH3
    s0 s0 s0 PUSH3
    s0 s0 s0 PUSH3
    s0 s0 PUSH2
    27 s() PUSH
    10 -ROLL
    $RoundStats$_constructor_round_number_start_time_end_time_price_total_raised_ton_total_raised_usdt_raised_usdt_equivalent_tokens_sold_purchase_count_unique_users_refund_count_refunded_amount_ton_refunded_amount_usdt_refunded_usdt_equivalent INLINECALLDICT
    s11 s13 XCHG
    5 9 REVERSE
    s8 s12 XCHG
    s7 s13 XCHG
    s6 s12 XCHG
    s5 s13 XCHG
    s4 s12 XCHG
    s3 s13 XCHG
    s12 s13 s0 XCHG3
    257 PUSHINT
    s13 s14 XCHG2
    1 PUSHINT
    s0 s14 XCHG
    NEWC
    $RoundStats$_store_cell INLINECALLDICT
    s3 s9 XCHG
    __tact_dict_set_int_cell INLINECALLDICT
    s0 s6 XCHG
  }>
  $OnionAuction$_contract_init PROCINLINE:<{
    PUSHNULL
    PUSHNULL
    PUSHNULL
    s8 PUSH
    s3 s8 XCHG
    s7 s6 s0 XCHG3
    FALSE
    s6 s5 XCHG2
    5 PUSHINT
    $AuctionConfig$_constructor_start_time_end_time_soft_cap_hard_cap_total_supply_refund_fee_percent INLINECALLDICT
    1 PUSHINT
    100000000 PUSHINT
    0 PUSHINT
    s0 s0 s0 PUSH3
    PUSHNULL
    s1 s1 s1 PUSH3
    300 PUSHINT
    s9 PUSH
    19 s() 23 s() XCHG
    18 s() 22 s() XCHG
    17 s() 21 s() XCHG
    16 s() 20 s() XCHG
    s15 19 s() XCHG
    s14 18 s() XCHG
    s13 17 s() XCHG
    s12 16 s() XCHG
    s11 s15 XCHG
    s10 s14 XCHG
    s9 s13 XCHG
    s8 s12 XCHG
    s7 s11 XCHG
    s5 s10 XCHG
    s4 s9 XCHG
    s3 s8 XCHG
    s6 s7 XCHG
    s5 s6 XCHG
    s4 s5 XCHG
    s1 s0 s4 XCHG3
    $OnionAuction$_fun_initializeFirstRound INLINECALLDICT
  }>
  $OnionAuction$_contract_load PROCINLINE:<{
    c4 PUSH
    CTOS
    1 LDI
    SWAP
    IF:<{
      $OnionAuction$_load INLINECALLDICT
      24 s() POP
      22 s() 23 s() XCHG
      21 s() 22 s() XCHG
      20 s() 21 s() XCHG
      19 s() 20 s() XCHG
      18 s() 19 s() XCHG
      17 s() 18 s() XCHG
      16 s() 17 s() XCHG
      s15 16 s() XCHG
      15 ROLL
    }>ELSE<{
      $OnionAuction$init$_load INLINECALLDICT
      s0 s6 XCHG
      ENDS
      5 ROLL
      $OnionAuction$_contract_init INLINECALLDICT
    }>
  }>
  $OnionAuction$_contract_store PROCINLINE:<{
    NEWC
    TRUE
    SWAP
    1 STI
    s0 24 s() XCHG
    s0 23 s() XCHG
    s0 22 s() XCHG
    s0 21 s() XCHG
    s0 20 s() XCHG
    s0 19 s() XCHG
    s0 18 s() XCHG
    s0 17 s() XCHG
    s0 16 s() XCHG
    15 -ROLL
    $OnionAuction$_store INLINECALLDICT
    ENDC
    c4 POP
  }>
  $Cell$_fun_asSlice PROCINLINE:<{
        CTOS
  }>
  $global_newAddress PROCINLINE:<{
        NEWC
    4 PUSHINT
    SWAP
    3 STU
    s1 s2 XCHG
    8 STI
    256 STU
        ENDC
    $Cell$_fun_asSlice INLINECALLDICT
  }>
  $global_contractAddressExt PROCINLINE:<{
        s0 PUSH HASHCU // `data` hash
        s2 PUSH HASHCU // `code` hash
        SWAP2
        CDEPTH         // `data` depth
        SWAP
        CDEPTH         // `code` depth
        131380 INT     // (2 << 16) | (1 << 8) | 0x34
        // Group 2: Composition of the Builder
        NEWC
        24 STU  // store refs_descriptor | bits_descriptor | data
        16 STU  // store depth_descriptor for `code`
        16 STU  // store depth_descriptor for `data`
        256 STU // store `code` hash
        256 STU // store `data` hash
        // Group 3: SHA256 hash of the resulting Builder
        ONE HASHEXT_SHA256
    $global_newAddress INLINECALLDICT
  }>
  $global_contractAddress PROCINLINE:<{
    0 PUSHINT
    -ROT
    $global_contractAddressExt INLINECALLDICT
  }>
  $global_emit PROCINLINE:<{
        NEWC
    15211807202738752817960438464513 PUSHINT
    SWAP
    104 STU
        STREF
        ENDC
    0 PUSHINT
        SENDRAWMSG
  }>
  $UserPurchase$_init_child PROCREF:<{
        B{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} B>boc PUSHREF
    NEWC
    FALSE
    SWAP
    1 STI
    2SWAP
    $UserPurchase$init$_store INLINECALLDICT
    ENDC
  }>
  $MessageParameters$_constructor_bounce_to_value_mode_body PROCINLINE:<{
    s3 s3 XCHG2
    s0 s4 XCHG
  }>
  $OnionAuction$_fun_initializeNewRound PROCREF:<{
    0 PUSHINT
    s0 s0 s0 PUSH3
    s0 s0 s0 PUSH3
    s0 s0 s0 PUSH3
    s0 s11 PUSH2
    s0 s12 XCHG
    s0 s11 XCHG
    s0 s10 XCHG
    28 s() PUSH
    s0 s10 XCHG
    8 2 BLKSWAP
    $RoundStats$_constructor_round_number_start_time_end_time_price_total_raised_ton_total_raised_usdt_raised_usdt_equivalent_tokens_sold_purchase_count_unique_users_refund_count_refunded_amount_ton_refunded_amount_usdt_refunded_usdt_equivalent INLINECALLDICT
    s12 s13 XCHG
    s11 s13 XCHG
    s10 s13 XCHG
    s9 s13 XCHG
    s0 s13 XCHG
    s0 s8 XCHG
    s0 s7 XCHG
    s0 s6 XCHG
    s0 s5 XCHG
    s0 s4 XCHG
    s3 s1 s3 XCHG3
    257 PUSHINT
    s2 s14 XCHG2
    NEWC
    $RoundStats$_store_cell INLINECALLDICT
    s3 s9 XCHG
    s1 s2 XCHG
    __tact_dict_set_int_cell INLINECALLDICT
    s0 s6 XCHG
  }>
  $OnionAuction$_fun_updateRoundStatsForPurchase PROCREF:<{
    s12 PUSH
    257 PUSHINT
    s2 PUSH
    __tact_dict_get_int_cell INLINECALLDICT
    $RoundStats$_load_opt INLINECALLDICT
    DUP
    ISNULL
    IF:<{
      DROP
          NOW
      24 s() 30 s() XCHG
      23 s() 29 s() XCHG
      22 s() 28 s() XCHG
      21 s() 27 s() XCHG
      20 s() 26 s() XCHG
      19 s() 25 s() XCHG
      18 s() 30 s() XCHG
      17 s() 29 s() XCHG
      16 s() 28 s() XCHG
      s15 27 s() XCHG
      s14 26 s() XCHG
      s13 25 s() XCHG
      s12 30 s() XCHG
      s11 29 s() XCHG
      s10 28 s() XCHG
      s9 27 s() XCHG
      s8 26 s() XCHG
      s7 25 s() XCHG
      s6 30 s() XCHG
      s5 29 s() XCHG
      s4 28 s() XCHG
      s3 27 s() XCHG
      s2 26 s() XCHG
      s1 25 s() XCHG
      25 s() PUSH
      SWAP
      $OnionAuction$_fun_initializeNewRound INLINECALLDICT
      s6 PUSH
      257 PUSHINT
      26 s() PUSH
      __tact_dict_get_int_cell INLINECALLDICT
      $RoundStats$_load_opt INLINECALLDICT
      24 s() 30 s() XCHG
      23 s() 29 s() XCHG
      22 s() 28 s() XCHG
      21 s() 27 s() XCHG
      20 s() 26 s() XCHG
      19 s() 25 s() XCHG
      18 s() 24 s() XCHG
      17 s() 23 s() XCHG
      16 s() 22 s() XCHG
      s15 21 s() XCHG
      s14 20 s() XCHG
      s13 19 s() XCHG
      s12 18 s() XCHG
      s11 17 s() XCHG
      s10 16 s() XCHG
      s9 s15 XCHG
      s8 s14 XCHG
      s7 s13 XCHG
      s6 s12 XCHG
      s5 s11 XCHG
      s4 s10 XCHG
      s3 s9 XCHG
      s8 s7 s0 XCHG3
    }>
    DUP
    ISNULL
    NOT
    IF:<{
      $RoundStats$_not_null INLINECALLDICT
      25 s() PUSH
      267 PUSHINT
      21 s() PUSH
      257 PUSHINT
      __tact_dict_get_slice_int INLINECALLDICT
      FALSE
      OVER
      ISNULL
      IF:<{
        NIP
        TRUE
      }>ELSE<{
        SWAP
        __tact_not_null INLINECALLDICT
        16 s() PUSH
        LESS
      }>
      IF:<{
        DROP
        s0 25 s() XCHG
        TRUE
        267 PUSHINT
        SWAP
        s0 21 s() XCHG
        16 s() PUSH
        257 PUSHINT
        __tact_dict_set_slice_int INLINECALLDICT
        s0 25 s() XCHG
      }>ELSE<{
        20 s() POP
      }>
      16 s() PUSH
      0 EQINT
      IF:<{
        18 s() PUSH
      }>ELSE<{
        0 PUSHINT
      }>
      s1 s10 XCHG
      ADD
      s0 16 s() XCHG
      1 EQINT
      IF:<{
      }>ELSE<{
        0 PUSHINT
        18 s() POP
      }>
      s0 17 s() XCHG
      s1 s7 XCHG
      ADD
      s5 s13 XCHG2
      ADD
      s3 s14 XCHG2
      ADD
      SWAP
      INC
      s0 s15 XCHG
      IF:<{
        1 PUSHINT
      }>ELSE<{
        0 PUSHINT
      }>
      s1 s2 XCHG
      ADD
      s8 s13 XCHG
      s7 s12 XCHG
      s6 s11 XCHG
      s5 s10 XCHG
      s6 s9 XCHG
      s5 s8 XCHG
      s1 s6 XCHG
      s5 s14 XCHG
      s3 s14 s4 XCHG3
      $RoundStats$_constructor_round_number_start_time_end_time_price_total_raised_ton_total_raised_usdt_raised_usdt_equivalent_tokens_sold_purchase_count_unique_users_refund_count_refunded_amount_ton_refunded_amount_usdt_refunded_usdt_equivalent INLINECALLDICT
      s12 s13 XCHG
      s11 s13 XCHG
      s10 s13 XCHG
      s9 s13 XCHG
      s0 s13 XCHG
      s0 s8 XCHG
      s0 s7 XCHG
      s0 s6 XCHG
      s0 s5 XCHG
      s0 s4 XCHG
      s3 s1 s3 XCHG3
      257 PUSHINT
      s2 s14 XCHG2
      NEWC
      $RoundStats$_store_cell INLINECALLDICT
      s3 s9 XCHG
      s1 s2 XCHG
      __tact_dict_set_int_cell INLINECALLDICT
      s0 s6 XCHG
    }>ELSE<{
      7 BLKDROP
    }>
  }>
  $OnionAuction$_fun_updateRoundStatsForRefund PROCREF:<{
    s10 PUSH
    257 PUSHINT
    s5 PUSH
    __tact_dict_get_int_cell INLINECALLDICT
    $RoundStats$_load_opt INLINECALLDICT
    DUP
    ISNULL
    NOT
    IF:<{
      $RoundStats$_not_null INLINECALLDICT
      s0 s3 XCHG
      INC
      s15 PUSH
      0 EQINT
      IF:<{
        16 s() PUSH
      }>ELSE<{
        0 PUSHINT
      }>
      s1 s3 XCHG
      ADD
      s0 s15 XCHG
      1 EQINT
      IF:<{
      }>ELSE<{
        0 PUSHINT
        16 s() POP
      }>
      s0 s15 XCHG2
      ADD
      s0 s12 XCHG
      ADD
      s10 s13 XCHG
      s9 s12 XCHG
      s8 s11 XCHG
      s7 s10 XCHG
      s6 s9 XCHG
      s5 s8 XCHG
      s4 s7 XCHG
      s3 s6 XCHG
      s5 s4 s0 XCHG3
      s3 s3 s0 XCHG3
      $RoundStats$_constructor_round_number_start_time_end_time_price_total_raised_ton_total_raised_usdt_raised_usdt_equivalent_tokens_sold_purchase_count_unique_users_refund_count_refunded_amount_ton_refunded_amount_usdt_refunded_usdt_equivalent INLINECALLDICT
      s12 s13 XCHG
      s11 s13 XCHG
      s10 s13 XCHG
      s9 s13 XCHG
      s0 s13 XCHG
      s0 s8 XCHG
      s0 s7 XCHG
      s0 s6 XCHG
      s0 s5 XCHG
      s0 s4 XCHG
      s3 s1 s3 XCHG3
      257 PUSHINT
      s2 s14 XCHG2
      NEWC
      $RoundStats$_store_cell INLINECALLDICT
      s3 s9 XCHG
      s1 s2 XCHG
      __tact_dict_set_int_cell INLINECALLDICT
      s0 s6 XCHG
    }>ELSE<{
      5 BLKDROP
    }>
  }>
  $SendParameters$_constructor_to_value_bounce_body_code_data PROCINLINE:<{
    0 PUSHINT
    s0 s6 XCHG
    s3 s5 XCHG
    s4 s0 s3 XCHG3
    s0 s2 XCHG
  }>
  $CreateUserPurchase$_constructor_user_amount_tokens_currency_purchase_method_nonce_round_number_usdt_equivalent_amount PROCINLINE:<{
  }>
  $OnionAuction$_fun_getUserPurchaseInit PROCREF:<{
        MYADDR
    SWAP
    $UserPurchase$_init_child INLINECALLDICT
  }>
  $OnionAuction$_fun_createOrUpdateUserPurchase PROCREF:<{
    23 s() 31 s() XCHG
    22 s() 30 s() XCHG
    21 s() 29 s() XCHG
    20 s() 28 s() XCHG
    19 s() 27 s() XCHG
    18 s() 26 s() XCHG
    17 s() 25 s() XCHG
    16 s() 24 s() XCHG
    s15 31 s() XCHG
    s14 30 s() XCHG
    s13 29 s() XCHG
    s12 28 s() XCHG
    s11 27 s() XCHG
    s10 26 s() XCHG
    s9 25 s() XCHG
    s8 24 s() XCHG
    s7 31 s() XCHG
    s6 30 s() XCHG
    s5 29 s() XCHG
    s4 28 s() XCHG
    s3 27 s() XCHG
    s2 26 s() XCHG
    s1 25 s() XCHG
    s0 24 s() XCHG
    31 s() PUSH
    $OnionAuction$_fun_getUserPurchaseInit INLINECALLDICT
    2DUP
    $global_contractAddress INLINECALLDICT
    s5 34 s() XCHG
    s4 33 s() XCHG
    s3 32 s() XCHG
    s2 31 s() XCHG
    s1 30 s() XCHG
    s0 29 s() XCHG
    100000000 PUSHINT
    s0 28 s() XCHG
    FALSE
    s0 30 s() XCHG
    $CreateUserPurchase$_constructor_user_amount_tokens_currency_purchase_method_nonce_round_number_usdt_equivalent_amount INLINECALLDICT
    NEWC
    $CreateUserPurchase$_store_cell INLINECALLDICT
    s5 24 s() XCHG
    s4 22 s() XCHG
    s3 23 s() XCHG
    s0 s2 XCHG
    s1 26 s() XCHG
    s0 25 s() XCHG
    $SendParameters$_constructor_to_value_bounce_body_code_data INLINECALLDICT
        NEWC
        b{01} STSLICECONST  // store tag = $0 and ihr_disabled = true
        1 STI               // store `bounce`
        b{000} STSLICECONST // store bounced = false and src = addr_none
        STSLICE             // store `to`
        SWAP
        STGRAMS             // store `value`
        105 PUSHINT         // 1 + 4 + 4 + 64 + 32
        STZEROES            // store currency_collection, ihr_fee, fwd_fee, created_lt and created_at
        // → Stack state
        // s0: Builder
        // s1: `data`
        // s2: `code`
        // s3: `body`
        // s4: `mode`
        // Group 2: Placing the Builder after code and data, then checking those for nullability
        s2 XCHG0
        DUP2
        ISNULL
        SWAP
        ISNULL
        AND
        // → Stack state
        // s0: -1 (true) if `data` and `code` are both null, 0 (false) otherwise
        // s1: `code`
        // s2: `data`
        // s3: Builder
        // s4: `body`
        // s5: `mode`
        // Group 3: Left branch of the IFELSE, executed if s0 is -1 (true)
        <{
            DROP2 // drop `data` and `code`, since either of those is null
            b{0} STSLICECONST
        }> PUSHCONT
        // Group 3: Right branch of the IFELSE, executed if s0 is 0 (false)
        <{
            // _ split_depth:(Maybe (## 5))
            //   special:(Maybe TickTock)
            //   code:(Maybe ^Cell)
            //   data:(Maybe ^Cell)
            //   library:(Maybe ^Cell)
            // = StateInit;
            ROT                // place message Builder on top
            b{10} STSLICECONST // store Maybe = true, Either = false
            // Start composing inlined StateInit
            b{00} STSLICECONST // store split_depth and special first
            STDICT             // store code
            STDICT             // store data
            b{0} STSLICECONST  // store library
        }> PUSHCONT
        // Group 3: IFELSE that does the branching shown above
        IFELSE
        // → Stack state
        // s0: Builder
        // s1: null or StateInit
        // s2: `body`
        // s3: `mode`
        // Group 4: Finalizing the message
        STDICT // store `body` as ref with an extra Maybe bit, since `body` might be null
        ENDC
        // → Stack state
        // s0: Cell
        // s1: `mode`
        // Group 5: Sending the message, with `mode` on top
        SWAP
        SENDRAWMSG // https://github.com/tact-lang/tact/issues/1558
    s15 23 s() XCHG
    s14 22 s() XCHG
    s13 21 s() XCHG
    s12 20 s() XCHG
    s11 19 s() XCHG
    s10 18 s() XCHG
    s9 17 s() XCHG
    s8 16 s() XCHG
    8 8 BLKSWAP
    s7 s5 s6 XCHG3
    s3 s4 XCHG
    SWAP
  }>
  $AuctionEnded$_constructor_end_reason_final_status_total_raised_total_raised_usdt_total_tokens_sold_end_time PROCINLINE:<{
  }>
  $PurchaseCompleted$_constructor_user_amount_tokens_received_currency_purchase_method_round_number_nonce_total_raised_total_raised_usdt_total_tokens_sold PROCINLINE:<{
  }>
  $PurchaseCalculation$_constructor_user_amount_currency_tokens_to_receive_current_price_current_round_timestamp_nonce_usdt_equivalent_amount PROCINLINE:<{
  }>
  $ParsedPurchaseData$_constructor_calculation_signature PROCINLINE:<{
  }>
  $OnionAuction$_fun_parsePurchaseFromPayload PROCREF:<{
        CTOS
        LDMSGADDR
        LDVARUINT16
    8 LDU
        LDVARUINT16
        LDVARUINT16
    32 LDU
    64 LDU
    64 LDU
        LDVARUINT16
    9 -ROLL
    $PurchaseCalculation$_constructor_user_amount_currency_tokens_to_receive_current_price_current_round_timestamp_nonce_usdt_equivalent_amount INLINECALLDICT
    s0 s9 XCHG
        LDREF
    DROP
        CTOS
    s8 s9 XCHG
    s7 s8 XCHG
    s6 s7 XCHG
    s5 s6 XCHG
    s4 s5 XCHG
    s3 s4 XCHG
    s1 s3 s0 XCHG3
    $ParsedPurchaseData$_constructor_calculation_signature INLINECALLDICT
  }>
  $OnionAuction$_fun_hashPurchaseCalculation PROCREF:<{
        NEWC
    s0 s9 XCHG2
        STSLICER
    s0 s7 XCHG2
        STVARUINT16
    s1 s5 XCHG
    8 STU
    s0 s3 XCHG2
        STVARUINT16
    SWAP
        STVARUINT16
    32 STU
    64 STU
    64 STU
    SWAP
        STVARUINT16
        ENDC
        HASHCU
  }>
  $OnionAuction$_fun_getCurrentRoundTokensSold PROCREF:<{
    s6 PUSH
    257 PUSHINT
    17 s() PUSH
    __tact_dict_get_int_cell INLINECALLDICT
    $RoundStats$_load_opt INLINECALLDICT
    DUP
    ISNULL
    NOT
    IFJMP:<{
      $RoundStats$_not_null INLINECALLDICT
      $RoundStats$_get_tokens_sold INLINECALLDICT
    }>
    DROP
    0 PUSHINT
  }>
  $OnionAuction$_fun_handleUSDTWithSignature PROCREF:<{
    s3 POP
    51009 PUSHINT
    s8 PUSH
    0 NEQINT
    THROWANYIFNOT
    s0 s2 XCHG
    __tact_not_null INLINECALLDICT
    24 s() 26 s() XCHG
    23 s() 25 s() XCHG
    22 s() 26 s() XCHG
    21 s() 25 s() XCHG
    20 s() 26 s() XCHG
    19 s() 25 s() XCHG
    18 s() 26 s() XCHG
    17 s() 25 s() XCHG
    16 s() 26 s() XCHG
    s15 25 s() XCHG
    s14 26 s() XCHG
    s13 25 s() XCHG
    s12 26 s() XCHG
    s11 25 s() XCHG
    s10 26 s() XCHG
    s9 25 s() XCHG
    s8 26 s() XCHG
    s7 25 s() XCHG
    s6 26 s() XCHG
    s5 25 s() XCHG
    s4 26 s() XCHG
    s3 25 s() XCHG
    s2 26 s() XCHG
    s1 25 s() XCHG
    $OnionAuction$_fun_parsePurchaseFromPayload INLINECALLDICT
    51014 PUSHINT
    s8 PUSH
    1 EQINT
    THROWANYIFNOT
    s8 PUSH
    51015 PUSHINT
    s0 36 s() XCHG
    EQUAL
    s1 35 s() XCHG
    THROWANYIFNOT
    s8 PUSH
    51016 PUSHINT
    s0 36 s() XCHG
    SDEQ
    s1 35 s() XCHG
    THROWANYIFNOT
        NOW
    51010 PUSHINT
    s1 s3 PUSH2
    SUB
    s13 PUSH
    LEQ
    THROWANYIFNOT
    s2 PUSH
    51011 PUSHINT
    s0 s2 XCHG
    LEQ
    THROWANYIFNOT
    51012 PUSHINT
    s12 PUSH
    257 PUSHINT
    s3 PUSH
    1 PUSHINT
    __tact_dict_get_int_int INLINECALLDICT
    ISNULL
    THROWANYIFNOT
    s7 PUSH
    s3 s7 XCPU
    s7 s1 s3 XCHG3
    s6 s5 s(-1) PUXC2
    s0 s6 PUXC
    s9 PUSH
    38 s() PUSH
    32 s() 37 s() XCHG
    31 s() 36 s() XCHG
    30 s() 35 s() XCHG
    29 s() 34 s() XCHG
    28 s() 33 s() XCHG
    27 s() 37 s() XCHG
    26 s() 36 s() XCHG
    25 s() 35 s() XCHG
    24 s() 34 s() XCHG
    23 s() 33 s() XCHG
    22 s() 37 s() XCHG
    21 s() 36 s() XCHG
    20 s() 35 s() XCHG
    19 s() 34 s() XCHG
    18 s() 33 s() XCHG
    17 s() 37 s() XCHG
    16 s() 36 s() XCHG
    s15 35 s() XCHG
    s14 34 s() XCHG
    s13 33 s() XCHG
    s12 37 s() XCHG
    s11 36 s() XCHG
    s10 35 s() XCHG
    s9 34 s() XCHG
    $OnionAuction$_fun_hashPurchaseCalculation INLINECALLDICT
    51013 PUSHINT
    s0 31 s() XCHG
    s6 PUSH
        CHKSIGNU
    s1 30 s() XCHG
    THROWANYIFNOT
    s0 s2 XCHG
    257 PUSHINT
    27 s() PUSH
    TRUE
    1 PUSHINT
    __tact_dict_set_int_int INLINECALLDICT
    51006 PUSHINT
    s12 PUSH
    27 s() PUSH
    ADD
    18 s() PUSH
    LEQ
    THROWANYIFNOT
    22 s() 23 s() XCHG
    21 s() 22 s() XCHG
    20 s() 21 s() XCHG
    19 s() 20 s() XCHG
    18 s() 19 s() XCHG
    17 s() 18 s() XCHG
    16 s() 17 s() XCHG
    s15 16 s() XCHG
    s14 s15 XCHG
    s13 s14 XCHG
    s12 s13 XCHG
    s11 s12 XCHG
    s10 s11 XCHG
    s9 s10 XCHG
    s8 s9 XCHG
    s7 s8 XCHG
    s6 s7 XCHG
    s5 s6 XCHG
    s4 s5 XCHG
    s0 s3 XCHG
    s0 s4 XCHG
    s0 28 s() XCHG
    s1 s2 XCHG
    $OnionAuction$_fun_getCurrentRoundTokensSold INLINECALLDICT
    51022 PUSHINT
    SWAP
    27 s() PUSH
    ADD
    21 s() PUSH
    LEQ
    THROWANYIFNOT
    s0 s9 XCHG
    27 s() PUSH
    ADD
    s0 s8 XCHG
    29 s() PUSH
    ADD
    s0 s12 XCHG
    25 s() PUSH
    ADD
    s0 s7 XCHG
    INC
    s7 s12 XCHG
    s8 s9 XCHG
    s0 s7 XCHG
    s0 s8 XCHG
    28 s() PUSH
    28 s() PUSH
    27 s() PUSH
    1 PUSHINT
    33 s() PUSH
    29 s() PUSH
    $OnionAuction$_fun_updateRoundStatsForPurchase INLINECALLDICT
    s12 PUSH
    19 s() PUSH
    GEQ
    IF:<{
      s11 POP
      2 PUSHINT
      DUP
          NOW
      s2 s(-1) PUXC
      16 s() PUSH
      s13 s1 PUXC
      17 s() PUSH
      SWAP
      $AuctionEnded$_constructor_end_reason_final_status_total_raised_total_raised_usdt_total_tokens_sold_end_time INLINECALLDICT
      NEWC
      $AuctionEnded$_store_cell INLINECALLDICT
      $global_emit INLINECALLDICT
      s0 s11 XCHG
    }>
    1 PUSHINT
    DUP
    25 s() 26 s() XCHG
    24 s() 25 s() XCHG
    23 s() 24 s() XCHG
    22 s() 23 s() XCHG
    21 s() 22 s() XCHG
    20 s() 21 s() XCHG
    19 s() 20 s() XCHG
    18 s() 19 s() XCHG
    17 s() 18 s() XCHG
    16 s() 17 s() XCHG
    s15 16 s() XCHG
    s14 s15 XCHG
    s13 s14 XCHG
    s12 s13 XCHG
    s11 s12 XCHG
    s10 s11 XCHG
    s9 s10 XCHG
    s8 s9 XCHG
    s7 s8 XCHG
    s6 s7 XCHG
    s5 s6 XCHG
    s4 s5 XCHG
    s3 s4 XCHG
    s2 s3 XCHG
    30 s() PUSH
    s0 s3 XCHG
    30 s() PUSH
    s0 s3 XCHG
    29 s() PUSH
    s1 s3 s3 XCHG3
    31 s() PUSH
    SWAP
    s0 35 s() XCHG
    35 s() PUSH
    $OnionAuction$_fun_createOrUpdateUserPurchase INLINECALLDICT
    1 PUSHINT
    DUP
    s6 29 s() XCHG
    s5 28 s() XCHG
    s4 26 s() XCHG
    2SWAP
    s1 30 s() XCHG
    s0 27 s() XCHG
    s15 s11 s14 PUSH3
    $PurchaseCompleted$_constructor_user_amount_tokens_received_currency_purchase_method_round_number_nonce_total_raised_total_raised_usdt_total_tokens_sold INLINECALLDICT
    NEWC
    $PurchaseCompleted$_store_cell INLINECALLDICT
    $global_emit INLINECALLDICT
    18 s() 23 s() XCHG
    17 s() 22 s() XCHG
    16 s() 21 s() XCHG
    s15 20 s() XCHG
    s14 19 s() XCHG
    s13 18 s() XCHG
    s12 17 s() XCHG
    s11 16 s() XCHG
    s10 s15 XCHG
    5 10 BLKSWAP
    s4 s3 s0 XCHG3
  }>
  $OnionAuction$_fun_getJettonWalletAddress PROCREF:<{
    c2 SAVE
    SAMEALTSAVE
    DROP
    s11 PUSH
    ISNULL
    NOT
    IF:<{
      s11 PUSH
      $USDTConfig$_not_null INLINECALLDICT
      DROP
      NIP
      DUP
      ISNULL
      NOT
      IFJMP:<{
        NIP
        __tact_not_null INLINECALLDICT
        RETALT
      }>
      DROP
    }>
  }>
  $OnionAuction$_fun_auction_info PROCREF:<{
    21 s() PUSH
    21 s() PUSH
    21 s() PUSH
    21 s() PUSH
    21 s() PUSH
    21 s() PUSH
  }>
  $OnionAuction$_fun_current_round PROCREF:<{
    s15 PUSH
  }>
  $OnionAuction$_fun_current_price PROCREF:<{
    s14 PUSH
  }>
  $OnionAuction$_fun_total_raised PROCREF:<{
    s13 PUSH
  }>
  $OnionAuction$_fun_total_tokens_sold PROCREF:<{
    s12 PUSH
  }>
  $OnionAuction$_fun_auction_status PROCREF:<{
    s11 PUSH
  }>
  $OnionAuction$_fun_purchase_count PROCREF:<{
    s7 PUSH
  }>
  $OnionAuction$_fun_user_purchase_address PROCREF:<{
    $OnionAuction$_fun_getUserPurchaseInit INLINECALLDICT
    $global_contractAddress INLINECALLDICT
  }>
  $OnionAuction$_fun_remaining_tokens PROCREF:<{
    17 s() PUSH
    s13 PUSH
    SUB
  }>
  $OnionAuction$_fun_is_auction_active PROCREF:<{
        NOW
    s12 PUSH
    1 EQINT
    IF:<{
      DUP
      23 s() PUSH
      GEQ
    }>ELSE<{
      FALSE
    }>
    IF:<{
      21 s() PUSH
      LEQ
    }>ELSE<{
      DROP
      FALSE
    }>
  }>
  $OnionAuction$_fun_usdt_config PROCREF:<{
    s10 PUSH
  }>
  $OnionAuction$_fun_total_raised_usdt PROCREF:<{
    s9 PUSH
  }>
  $OnionAuction$_fun_total_raised_equivalent PROCREF:<{
    s9 PUSH
    1000 PUSHINT
    MUL
    s14 s(-1) PUXC
    ADD
  }>
  $OnionAuction$_fun_total_raised_usdt_equivalent PROCREF:<{
    s8 PUSH
  }>
  $OnionAuction$_fun_is_usdt_enabled PROCREF:<{
    s10 PUSH
    ISNULL
    NOT
  }>
  $OnionAuction$_fun_signing_public_key PROCREF:<{
    s4 PUSH
  }>
  $OnionAuction$_fun_signature_timeout PROCREF:<{
    s2 PUSH
  }>
  $OnionAuction$_fun_is_nonce_used PROCREF:<{
    257 PUSHINT
    s5 PUSH
    s0 s2 XCHG
    1 PUSHINT
    __tact_dict_get_int_int INLINECALLDICT
    ISNULL
    NOT
  }>
  $OnionAuction$_fun_is_signature_verification_enabled PROCREF:<{
    s4 PUSH
    0 NEQINT
  }>
  $OnionAuction$_fun_min_purchase PROCREF:<{
    OVER
  }>
  $OnionAuction$_fun_treasury_address PROCREF:<{
    DUP
  }>
  $OnionAuction$_fun_withdrawable_ton PROCREF:<{
    s11 PUSH
    2 EQINT
    IFJMP:<{
      s13 PUSH
    }>
    0 PUSHINT
  }>
  $OnionAuction$_fun_withdrawable_usdt PROCREF:<{
    s9 PUSH
  }>
  $OnionAuction$_fun_can_withdraw PROCREF:<{
    TRUE
  }>
  $OnionAuction$_fun_withdrawal_summary PROCREF:<{
    PUSHNULL
    257 PUSHINT
    1 PUSHINT
    s14 s1 PUSH2
    __tact_dict_set_int_int INLINECALLDICT
    21 s() 24 s() XCHG
    20 s() 23 s() XCHG
    19 s() 22 s() XCHG
    18 s() 24 s() XCHG
    17 s() 23 s() XCHG
    16 s() 22 s() XCHG
    s15 24 s() XCHG
    s14 23 s() XCHG
    s13 22 s() XCHG
    s12 24 s() XCHG
    s11 23 s() XCHG
    s10 22 s() XCHG
    s9 24 s() XCHG
    s8 23 s() XCHG
    s7 22 s() XCHG
    s6 24 s() XCHG
    s5 23 s() XCHG
    s4 22 s() XCHG
    s3 24 s() XCHG
    s2 23 s() XCHG
    s1 22 s() XCHG
    s0 24 s() XCHG
    257 PUSHINT
    s0 24 s() XCHG
    2 PUSHINT
    s0 24 s() XCHG
    $OnionAuction$_fun_withdrawable_ton INLINECALLDICT
    26 s() PUSH
    s4 28 s() XCHG
    s3 27 s() XCHG
    s2 26 s() XCHG
    __tact_dict_set_int_int INLINECALLDICT
    s0 24 s() XCHG
    257 PUSHINT
    s0 24 s() XCHG
    3 PUSHINT
    s0 24 s() XCHG
    $OnionAuction$_fun_withdrawable_usdt INLINECALLDICT
    26 s() PUSH
    s4 28 s() XCHG
    s3 27 s() XCHG
    s2 26 s() XCHG
    __tact_dict_set_int_int INLINECALLDICT
    21 s() 24 s() XCHG
    20 s() 23 s() XCHG
    19 s() 22 s() XCHG
    18 s() 21 s() XCHG
    17 s() 20 s() XCHG
    16 s() 19 s() XCHG
    s15 18 s() XCHG
    s14 17 s() XCHG
    s13 16 s() XCHG
    s12 s15 XCHG
    s11 s14 XCHG
    s10 s13 XCHG
    s9 s12 XCHG
    s8 s11 XCHG
    s7 s10 XCHG
    s6 s9 XCHG
    s5 s8 XCHG
    s4 s7 XCHG
    s3 s6 XCHG
    s5 s4 s0 XCHG3
  }>
  $OnionAuction$_fun_round_stats PROCREF:<{
    257 PUSHINT
    s8 PUSH
    s0 s2 XCHG
    __tact_dict_get_int_cell INLINECALLDICT
    $RoundStats$_load_opt INLINECALLDICT
  }>
  $OnionAuction$_fun_current_round_stats PROCREF:<{
    s6 PUSH
    257 PUSHINT
    17 s() PUSH
    __tact_dict_get_int_cell INLINECALLDICT
    $RoundStats$_load_opt INLINECALLDICT
  }>
  $OnionAuction$_fun_total_rounds PROCREF:<{
        NOW
    s12 PUSH
    1 NEQINT
    IF:<{
      TRUE
    }>ELSE<{
      DUP
      23 s() PUSH
      LESS
    }>
    IFJMP:<{
      DROP
      0 PUSHINT
    }>
    22 s() PUSH
    SUB
    3600 PUSHINT
    DIV
    INC
  }>
  $OnionAuction$_fun_round_summary PROCREF:<{
    s7 PUSH
    257 PUSHINT
    s2 PUSH
    __tact_dict_get_int_cell INLINECALLDICT
    $RoundStats$_load_opt INLINECALLDICT
    DUP
    ISNULL
    IFJMP:<{
      2DROP
      PUSHNULL
    }>
    SWAP
    17 s() PUSH
    EQUAL
    IF:<{
      s12 PUSH
      1 EQINT
    }>ELSE<{
      FALSE
    }>
    IFJMP:<{
      $RoundStats$_not_null INLINECALLDICT
      s11 POP
          NOW
      s0 s11 XCHG
      $RoundStats$_constructor_round_number_start_time_end_time_price_total_raised_ton_total_raised_usdt_raised_usdt_equivalent_tokens_sold_purchase_count_unique_users_refund_count_refunded_amount_ton_refunded_amount_usdt_refunded_usdt_equivalent INLINECALLDICT
      $RoundStats$_as_optional INLINECALLDICT
    }>
  }>
  $OnionAuction$_fun_all_rounds_summary PROCREF:<{
    22 s() 23 s() XCHG
    21 s() 23 s() XCHG
    20 s() 23 s() XCHG
    19 s() 23 s() XCHG
    18 s() 23 s() XCHG
    17 s() 23 s() XCHG
    16 s() 23 s() XCHG
    s15 23 s() XCHG
    s14 23 s() XCHG
    s13 23 s() XCHG
    s12 23 s() XCHG
    s11 23 s() XCHG
    s10 23 s() XCHG
    s9 23 s() XCHG
    s0 23 s() XCHG
    s0 s8 XCHG
    s0 s7 XCHG
    s0 s6 XCHG
    5 -ROLL
    PUSHNULL
    s0 24 s() XCHG
    $OnionAuction$_fun_total_rounds INLINECALLDICT
    1 PUSHINT
    WHILE:<{
      s0 s1 PUSH2
      LEQ
    }>DO<{
      23 s() 25 s() XCHG
      22 s() 24 s() XCHG
      21 s() 25 s() XCHG
      20 s() 24 s() XCHG
      19 s() 25 s() XCHG
      18 s() 24 s() XCHG
      17 s() 25 s() XCHG
      16 s() 24 s() XCHG
      s15 25 s() XCHG
      s14 24 s() XCHG
      s13 25 s() XCHG
      s12 24 s() XCHG
      s11 25 s() XCHG
      s10 24 s() XCHG
      s9 25 s() XCHG
      s8 24 s() XCHG
      s7 25 s() XCHG
      s6 24 s() XCHG
      s5 25 s() XCHG
      s4 24 s() XCHG
      s3 25 s() XCHG
      s2 24 s() XCHG
      s1 25 s() XCHG
      s0 24 s() XCHG
      24 s() PUSH
      $OnionAuction$_fun_round_summary INLINECALLDICT
      DUP
      ISNULL
      NOT
      IF:<{
        257 PUSHINT
        SWAP
        $RoundStats$_not_null INLINECALLDICT
        NEWC
        $RoundStats$_store_cell INLINECALLDICT
        s2 28 s() XCHG
        26 s() PUSH
        SWAP
        __tact_dict_set_int_cell INLINECALLDICT
        s0 26 s() XCHG
      }>ELSE<{
        DROP
      }>
      s0 24 s() XCHG
      INC
      23 s() 25 s() XCHG
      22 s() 24 s() XCHG
      21 s() 23 s() XCHG
      20 s() 22 s() XCHG
      19 s() 21 s() XCHG
      18 s() 20 s() XCHG
      17 s() 19 s() XCHG
      16 s() 18 s() XCHG
      s15 17 s() XCHG
      s14 16 s() XCHG
      s13 s15 XCHG
      s12 s14 XCHG
      s11 s13 XCHG
      s10 s12 XCHG
      s9 s11 XCHG
      s8 s10 XCHG
      s7 s9 XCHG
      s6 s8 XCHG
      s5 s7 XCHG
      s4 s6 XCHG
      s3 s5 XCHG
      s4 s3 s0 XCHG3
    }>
    2DROP
    23 s() 24 s() XCHG
    22 s() 23 s() XCHG
    21 s() 22 s() XCHG
    20 s() 21 s() XCHG
    19 s() 20 s() XCHG
    18 s() 19 s() XCHG
    17 s() 18 s() XCHG
    16 s() 17 s() XCHG
    s15 16 s() XCHG
    15 ROLL
  }>
  $OnionAuction$_fun_round_participants_count PROCREF:<{
    257 PUSHINT
    s8 PUSH
    s0 s2 XCHG
    __tact_dict_get_int_cell INLINECALLDICT
    $RoundStats$_load_opt INLINECALLDICT
    DUP
    ISNULL
    NOT
    IFJMP:<{
      $RoundStats$_not_null INLINECALLDICT
      $RoundStats$_get_unique_users INLINECALLDICT
    }>
    DROP
    0 PUSHINT
  }>
  $OnionAuction$_fun_aggregated_stats PROCREF:<{
    0 PUSHINT
    s0 s0 s0 PUSH3
    s0 s0 s0 PUSH3
    s0 s0 s0 PUSH3
    23 s() 33 s() XCHG
    22 s() 32 s() XCHG
    21 s() 31 s() XCHG
    20 s() 30 s() XCHG
    19 s() 29 s() XCHG
    18 s() 28 s() XCHG
    17 s() 27 s() XCHG
    16 s() 26 s() XCHG
    s15 25 s() XCHG
    s14 24 s() XCHG
    s13 33 s() XCHG
    s12 32 s() XCHG
    s11 31 s() XCHG
    s10 30 s() XCHG
    s9 29 s() XCHG
    s8 28 s() XCHG
    s7 27 s() XCHG
    s6 26 s() XCHG
    s5 25 s() XCHG
    s4 24 s() XCHG
    s3 33 s() XCHG
    s2 32 s() XCHG
    s1 31 s() XCHG
    s0 30 s() XCHG
    $OnionAuction$_fun_total_rounds INLINECALLDICT
    1 PUSHINT
    WHILE:<{
      s0 s1 PUSH2
      LEQ
    }>DO<{
      s8 PUSH
      257 PUSHINT
      s2 PUSH
      __tact_dict_get_int_cell INLINECALLDICT
      $RoundStats$_load_opt INLINECALLDICT
      DUP
      ISNULL
      NOT
      IF:<{
        $RoundStats$_not_null INLINECALLDICT
        s10 POP
        s10 POP
        s10 POP
        s10 POP
        s1 41 s() XCHG
        s0 s5 XCHG
        ADD
        s1 39 s() XCHG
        s0 s3 XCHG
        ADD
        s0 37 s() XCHG
        SWAP
        ADD
        s1 35 s() XCHG
        s0 37 s() XCHG
        ADD
        s1 33 s() XCHG
        s0 37 s() XCHG
        ADD
        s1 40 s() XCHG
        s0 s4 XCHG
        ADD
        s1 38 s() XCHG
        s0 s2 XCHG
        ADD
        s1 36 s() XCHG
        s0 37 s() XCHG
        ADD
        s1 34 s() XCHG
        s0 37 s() XCHG
        ADD
        s1 27 s() XCHG
        s0 28 s() XCHG
        ADD
        33 s() 35 s() XCHG
        27 s() 32 s() XCHG
        28 s() 31 s() XCHG
        29 s() 30 s() XCHG
        s0 26 s() XCHG
      }>ELSE<{
        DROP
      }>
      INC
    }>
    2DROP
    0 PUSHINT
        NOW
    s11 s0 s11 XC2PU
    24 s() PUSH
    s11 s10 s12 XCHG3
    s9 33 s() XCHG
    s8 32 s() XCHG
    s7 31 s() XCHG
    s6 30 s() XCHG
    s5 29 s() XCHG
    s4 28 s() XCHG
    s3 37 s() XCHG
    s2 36 s() XCHG
    s1 35 s() XCHG
    s0 34 s() XCHG
    $RoundStats$_constructor_round_number_start_time_end_time_price_total_raised_ton_total_raised_usdt_raised_usdt_equivalent_tokens_sold_purchase_count_unique_users_refund_count_refunded_amount_ton_refunded_amount_usdt_refunded_usdt_equivalent INLINECALLDICT
    27 s() 37 s() XCHG
    26 s() 36 s() XCHG
    25 s() 35 s() XCHG
    24 s() 34 s() XCHG
    23 s() 33 s() XCHG
    22 s() 32 s() XCHG
    21 s() 31 s() XCHG
    20 s() 30 s() XCHG
    19 s() 29 s() XCHG
    18 s() 28 s() XCHG
    17 s() 27 s() XCHG
    16 s() 26 s() XCHG
    s15 25 s() XCHG
    s14 24 s() XCHG
    17 s() 23 s() XCHG
    s14 22 s() XCHG
    16 s() 21 s() XCHG
    s15 20 s() XCHG
    17 s() 19 s() XCHG
    s14 18 s() XCHG
    16 s() 17 s() XCHG
    s15 16 s() XCHG
  }>
  $OnionAuction$_fun_reply PROCINLINE:<{
    __tact_context_get_sender INLINECALLDICT
    TRUE
    0 PUSHINT
    s0 s3 XCHG2
    66 PUSHINT
    SWAP
    $MessageParameters$_constructor_bounce_to_value_mode_body INLINECALLDICT
        NEWC
        b{01} STSLICECONST  // store tag = $0 and ihr_disabled = true
        1 STI               // store `bounce`
        b{000} STSLICECONST // store bounced = false and src = addr_none
        STSLICE             // store `to`
        SWAP
        STGRAMS             // store `value`
        106 PUSHINT         // 1 + 4 + 4 + 64 + 32 + 1
        STZEROES
        // → Stack state
        // s0: Builder
        // s1: `body`
        // s2: `mode`
        STDICT
        ENDC
        SWAP
        SENDRAWMSG
  }>
  $OnionAuction$_fun_notify PROCINLINE:<{
    __tact_context_get_sender INLINECALLDICT
    FALSE
    0 PUSHINT
    s0 s3 XCHG2
    66 PUSHINT
    SWAP
    $MessageParameters$_constructor_bounce_to_value_mode_body INLINECALLDICT
        NEWC
        b{01} STSLICECONST  // store tag = $0 and ihr_disabled = true
        1 STI               // store `bounce`
        b{000} STSLICECONST // store bounced = false and src = addr_none
        STSLICE             // store `to`
        SWAP
        STGRAMS             // store `value`
        106 PUSHINT         // 1 + 4 + 4 + 64 + 32 + 1
        STZEROES
        // → Stack state
        // s0: Builder
        // s1: `body`
        // s2: `mode`
        STDICT
        ENDC
        SWAP
        SENDRAWMSG
  }>
  $OnionAuction$_fun_requireOwner PROCREF:<{
    __tact_context_get_sender INLINECALLDICT
    24 s() PUSH
    SDEQ
    132 THROWIFNOT
  }>
  $OnionAuction$_fun_owner PROCREF:<{
    23 s() PUSH
  }>
  $OnionAuction$_fun_requireNotStopped PROCREF:<{
    22 s() PUSH
    NOT
    133 THROWIFNOT
  }>
  $OnionAuction$_fun_stopped PROCREF:<{
    22 s() PUSH
  }>
  $USDTConfig$_constructor_master_address_wallet_address_decimals PROCINLINE:<{
  }>
  $USDTConfigured$_constructor_usdt_master_usdt_wallet_configured_by PROCINLINE:<{
  }>
  $SigningKeySet$_constructor_public_key_set_by PROCINLINE:<{
  }>
  $MinPurchaseUpdated$_constructor_old_min_purchase_new_min_purchase_updated_by PROCINLINE:<{
  }>
  $TreasurySet$_constructor_old_treasury_new_treasury_set_by PROCINLINE:<{
  }>
  $SendParameters$_constructor_to_value_mode_bounce_body PROCINLINE:<{
    s2 s4 XCHG
    PUSHNULL
    s4 s3 XCHG2
    PUSHNULL
    s0 s3 XCHG
  }>
  $FundsWithdrawn$_constructor_currency_amount_destination_withdrawn_by_remaining_balance PROCINLINE:<{
  }>
  $SendParameters$_constructor_to_value_bounce_body PROCINLINE:<{
    0 PUSHINT
    s3 s4 XCHG2
    PUSHNULL
    s0 s3 XCHG
    PUSHNULL
    3 -ROLL
  }>
  $JettonTransfer$_constructor_query_id_amount_destination_response_destination_custom_payload_forward_ton_amount_forward_payload PROCINLINE:<{
  }>
  $AuctionStarted$_constructor_start_time_end_time_soft_cap_hard_cap_initial_price_total_supply PROCINLINE:<{
  }>
  $RoundUpdated$_constructor_old_round_new_round_old_price_new_price_updated_by PROCINLINE:<{
  }>
  $DeployOk$_constructor_queryId PROCINLINE:<{
  }>
  %auction_info PROC:<{
    $OnionAuction$_contract_load INLINECALLDICT
    $OnionAuction$_fun_auction_info INLINECALLDICT
    12 6 BLKDROP2
    12 6 BLKDROP2
    $AuctionConfig$_to_external INLINECALLDICT
  }>
  %current_round PROC:<{
    $OnionAuction$_contract_load INLINECALLDICT
    $OnionAuction$_fun_current_round INLINECALLDICT
    16 s() POP
    15 BLKDROP
    8 1 BLKDROP2
  }>
  %current_price PROC:<{
    $OnionAuction$_contract_load INLINECALLDICT
    $OnionAuction$_fun_current_price INLINECALLDICT
    16 s() POP
    15 BLKDROP
    8 1 BLKDROP2
  }>
  %total_raised PROC:<{
    $OnionAuction$_contract_load INLINECALLDICT
    $OnionAuction$_fun_total_raised INLINECALLDICT
    16 s() POP
    15 BLKDROP
    8 1 BLKDROP2
  }>
  %total_tokens_sold PROC:<{
    $OnionAuction$_contract_load INLINECALLDICT
    $OnionAuction$_fun_total_tokens_sold INLINECALLDICT
    16 s() POP
    15 BLKDROP
    8 1 BLKDROP2
  }>
  %auction_status PROC:<{
    $OnionAuction$_contract_load INLINECALLDICT
    $OnionAuction$_fun_auction_status INLINECALLDICT
    16 s() POP
    15 BLKDROP
    8 1 BLKDROP2
  }>
  %purchase_count PROC:<{
    $OnionAuction$_contract_load INLINECALLDICT
    $OnionAuction$_fun_purchase_count INLINECALLDICT
    16 s() POP
    15 BLKDROP
    8 1 BLKDROP2
  }>
  %user_purchase_address PROC:<{
    $OnionAuction$_contract_load INLINECALLDICT
    23 s() 24 s() XCHG
    22 s() 23 s() XCHG
    21 s() 22 s() XCHG
    20 s() 21 s() XCHG
    19 s() 20 s() XCHG
    18 s() 19 s() XCHG
    17 s() 18 s() XCHG
    16 s() 17 s() XCHG
    s15 16 s() XCHG
    15 ROLL
    $OnionAuction$_fun_user_purchase_address INLINECALLDICT
    16 s() POP
    15 BLKDROP
    8 1 BLKDROP2
  }>
  %remaining_tokens PROC:<{
    $OnionAuction$_contract_load INLINECALLDICT
    $OnionAuction$_fun_remaining_tokens INLINECALLDICT
    16 s() POP
    15 BLKDROP
    8 1 BLKDROP2
  }>
  %is_auction_active PROC:<{
    $OnionAuction$_contract_load INLINECALLDICT
    $OnionAuction$_fun_is_auction_active INLINECALLDICT
    16 s() POP
    15 BLKDROP
    8 1 BLKDROP2
  }>
  %usdt_config PROC:<{
    $OnionAuction$_contract_load INLINECALLDICT
    $OnionAuction$_fun_usdt_config INLINECALLDICT
    16 s() POP
    15 BLKDROP
    8 1 BLKDROP2
    $USDTConfig$_to_opt_external INLINECALLDICT
  }>
  %total_raised_usdt PROC:<{
    $OnionAuction$_contract_load INLINECALLDICT
    $OnionAuction$_fun_total_raised_usdt INLINECALLDICT
    16 s() POP
    15 BLKDROP
    8 1 BLKDROP2
  }>
  %total_raised_equivalent PROC:<{
    $OnionAuction$_contract_load INLINECALLDICT
    $OnionAuction$_fun_total_raised_equivalent INLINECALLDICT
    16 s() POP
    15 BLKDROP
    8 1 BLKDROP2
  }>
  %total_raised_usdt_equivalent PROC:<{
    $OnionAuction$_contract_load INLINECALLDICT
    $OnionAuction$_fun_total_raised_usdt_equivalent INLINECALLDICT
    16 s() POP
    15 BLKDROP
    8 1 BLKDROP2
  }>
  %is_usdt_enabled PROC:<{
    $OnionAuction$_contract_load INLINECALLDICT
    $OnionAuction$_fun_is_usdt_enabled INLINECALLDICT
    16 s() POP
    15 BLKDROP
    8 1 BLKDROP2
  }>
  %signing_public_key PROC:<{
    $OnionAuction$_contract_load INLINECALLDICT
    $OnionAuction$_fun_signing_public_key INLINECALLDICT
    16 s() POP
    15 BLKDROP
    8 1 BLKDROP2
  }>
  %signature_timeout PROC:<{
    $OnionAuction$_contract_load INLINECALLDICT
    $OnionAuction$_fun_signature_timeout INLINECALLDICT
    16 s() POP
    15 BLKDROP
    8 1 BLKDROP2
  }>
  %is_nonce_used PROC:<{
    $OnionAuction$_contract_load INLINECALLDICT
    23 s() 24 s() XCHG
    22 s() 23 s() XCHG
    21 s() 22 s() XCHG
    20 s() 21 s() XCHG
    19 s() 20 s() XCHG
    18 s() 19 s() XCHG
    17 s() 18 s() XCHG
    16 s() 17 s() XCHG
    s15 16 s() XCHG
    15 ROLL
    $OnionAuction$_fun_is_nonce_used INLINECALLDICT
    16 s() POP
    15 BLKDROP
    8 1 BLKDROP2
  }>
  %is_signature_verification_enabled PROC:<{
    $OnionAuction$_contract_load INLINECALLDICT
    $OnionAuction$_fun_is_signature_verification_enabled INLINECALLDICT
    16 s() POP
    15 BLKDROP
    8 1 BLKDROP2
  }>
  %min_purchase PROC:<{
    $OnionAuction$_contract_load INLINECALLDICT
    $OnionAuction$_fun_min_purchase INLINECALLDICT
    16 s() POP
    15 BLKDROP
    8 1 BLKDROP2
  }>
  %treasury_address PROC:<{
    $OnionAuction$_contract_load INLINECALLDICT
    $OnionAuction$_fun_treasury_address INLINECALLDICT
    16 s() POP
    15 BLKDROP
    8 1 BLKDROP2
  }>
  %withdrawable_ton PROC:<{
    $OnionAuction$_contract_load INLINECALLDICT
    $OnionAuction$_fun_withdrawable_ton INLINECALLDICT
    16 s() POP
    15 BLKDROP
    8 1 BLKDROP2
  }>
  %withdrawable_usdt PROC:<{
    $OnionAuction$_contract_load INLINECALLDICT
    $OnionAuction$_fun_withdrawable_usdt INLINECALLDICT
    16 s() POP
    15 BLKDROP
    8 1 BLKDROP2
  }>
  %can_withdraw PROC:<{
    $OnionAuction$_contract_load INLINECALLDICT
    $OnionAuction$_fun_can_withdraw INLINECALLDICT
    16 s() POP
    15 BLKDROP
    8 1 BLKDROP2
  }>
  %withdrawal_summary PROC:<{
    $OnionAuction$_contract_load INLINECALLDICT
    $OnionAuction$_fun_withdrawal_summary INLINECALLDICT
    16 s() POP
    15 BLKDROP
    8 1 BLKDROP2
  }>
  %round_stats PROC:<{
    $OnionAuction$_contract_load INLINECALLDICT
    23 s() 24 s() XCHG
    22 s() 23 s() XCHG
    21 s() 22 s() XCHG
    20 s() 21 s() XCHG
    19 s() 20 s() XCHG
    18 s() 19 s() XCHG
    17 s() 18 s() XCHG
    16 s() 17 s() XCHG
    s15 16 s() XCHG
    15 ROLL
    $OnionAuction$_fun_round_stats INLINECALLDICT
    16 s() POP
    15 BLKDROP
    8 1 BLKDROP2
    $RoundStats$_to_opt_external INLINECALLDICT
  }>
  %current_round_stats PROC:<{
    $OnionAuction$_contract_load INLINECALLDICT
    $OnionAuction$_fun_current_round_stats INLINECALLDICT
    16 s() POP
    15 BLKDROP
    8 1 BLKDROP2
    $RoundStats$_to_opt_external INLINECALLDICT
  }>
  %total_rounds PROC:<{
    $OnionAuction$_contract_load INLINECALLDICT
    $OnionAuction$_fun_total_rounds INLINECALLDICT
    16 s() POP
    15 BLKDROP
    8 1 BLKDROP2
  }>
  %round_summary PROC:<{
    $OnionAuction$_contract_load INLINECALLDICT
    23 s() 24 s() XCHG
    22 s() 23 s() XCHG
    21 s() 22 s() XCHG
    20 s() 21 s() XCHG
    19 s() 20 s() XCHG
    18 s() 19 s() XCHG
    17 s() 18 s() XCHG
    16 s() 17 s() XCHG
    s15 16 s() XCHG
    15 ROLL
    $OnionAuction$_fun_round_summary INLINECALLDICT
    16 s() POP
    15 BLKDROP
    8 1 BLKDROP2
    $RoundStats$_to_opt_external INLINECALLDICT
  }>
  %all_rounds_summary PROC:<{
    $OnionAuction$_contract_load INLINECALLDICT
    $OnionAuction$_fun_all_rounds_summary INLINECALLDICT
    16 s() POP
    15 BLKDROP
    8 1 BLKDROP2
  }>
  %round_participants_count PROC:<{
    $OnionAuction$_contract_load INLINECALLDICT
    23 s() 24 s() XCHG
    22 s() 23 s() XCHG
    21 s() 22 s() XCHG
    20 s() 21 s() XCHG
    19 s() 20 s() XCHG
    18 s() 19 s() XCHG
    17 s() 18 s() XCHG
    16 s() 17 s() XCHG
    s15 16 s() XCHG
    15 ROLL
    $OnionAuction$_fun_round_participants_count INLINECALLDICT
    16 s() POP
    15 BLKDROP
    8 1 BLKDROP2
  }>
  %aggregated_stats PROC:<{
    $OnionAuction$_contract_load INLINECALLDICT
    $OnionAuction$_fun_aggregated_stats INLINECALLDICT
    14 14 BLKDROP2
    s14 POP
    s14 POP
    s14 POP
    s14 POP
    s14 POP
    s14 POP
    s14 POP
    s14 POP
    s14 POP
    s14 POP
    10 4 BLKSWAP
    $RoundStats$_to_external INLINECALLDICT
  }>
  %owner PROC:<{
    $OnionAuction$_contract_load INLINECALLDICT
    $OnionAuction$_fun_owner INLINECALLDICT
    16 s() POP
    15 BLKDROP
    8 1 BLKDROP2
  }>
  %stopped PROC:<{
    $OnionAuction$_contract_load INLINECALLDICT
    $OnionAuction$_fun_stopped INLINECALLDICT
    16 s() POP
    15 BLKDROP
    8 1 BLKDROP2
  }>
  recv_internal PROC:<{
    c2 SAVE
    SAMEALTSAVE
    SWAP
    CTOS
    2 PUSHINT
    SDSKIPFIRST
    1 LDI
    1 LDI
    LDMSGADDR
    OVER
    s3 s4 XCHG
    s6 s6 XCHG2
    4 TUPLE
    __tact_context SETGLOB
    s0 s2 XCHG
    __tact_context_sender SETGLOB
    $OnionAuction$_contract_load INLINECALLDICT
    s0 25 s() XCHG
    IFJMP:<{
      15 BLKDROP
      10 BLKDROP
    }>
    23 s() PUSH
    SBITS
    31 GTINT
    IF:<{
      s0 23 s() XCHG
      32 LDU
      OVER
      2703444734 PUSHINT
      EQUAL
      IFJMP:<{
        NIP
        LDMSGADDR
        LDMSGADDR
        DROP
        s1 24 s() XCHG
        s0 25 s() XCHG
        $OnionAuction$_fun_requireOwner INLINECALLDICT
        s10 POP
        23 s() PUSH
        25 s() PUSH
        6 PUSHINT
        $USDTConfig$_constructor_master_address_wallet_address_decimals INLINECALLDICT
        $USDTConfig$_as_optional INLINECALLDICT
        __tact_context_get_sender INLINECALLDICT
        s2 25 s() XCHG
        s1 26 s() XCHG
        $USDTConfigured$_constructor_usdt_master_usdt_wallet_configured_by INLINECALLDICT
        NEWC
        $USDTConfigured$_store_cell INLINECALLDICT
        $global_emit INLINECALLDICT
        21 s() 23 s() XCHG
        20 s() 22 s() XCHG
        19 s() 21 s() XCHG
        18 s() 20 s() XCHG
        17 s() 19 s() XCHG
        16 s() 18 s() XCHG
        s15 17 s() XCHG
        s14 16 s() XCHG
        s13 s15 XCHG
        s12 s14 XCHG
        s11 s13 XCHG
        s10 s12 XCHG
        s9 s11 XCHG
        s9 s10 XCHG
        2 8 BLKSWAP
        $OnionAuction$_contract_store INLINECALLDICT
        RETALT
      }>
      OVER
      3315975678 PUSHINT
      EQUAL
      IFJMP:<{
        NIP
        256 LDU
        DROP
        22 s() 23 s() XCHG
        21 s() 22 s() XCHG
        20 s() 21 s() XCHG
        19 s() 20 s() XCHG
        18 s() 19 s() XCHG
        17 s() 18 s() XCHG
        16 s() 17 s() XCHG
        s15 16 s() XCHG
        s14 s15 XCHG
        s13 s14 XCHG
        s12 s13 XCHG
        s11 s12 XCHG
        s10 s11 XCHG
        s9 s10 XCHG
        s8 s9 XCHG
        s7 s8 XCHG
        s6 s7 XCHG
        s5 s6 XCHG
        s4 s5 XCHG
        s3 s4 XCHG
        s0 24 s() XCHG
        s1 s3 s0 XCHG3
        $OnionAuction$_fun_requireOwner INLINECALLDICT
        s4 POP
        23 s() PUSH
        __tact_context_get_sender INLINECALLDICT
        s1 25 s() XCHG
        $SigningKeySet$_constructor_public_key_set_by INLINECALLDICT
        NEWC
        $SigningKeySet$_store_cell INLINECALLDICT
        $global_emit INLINECALLDICT
        22 s() 23 s() XCHG
        21 s() 22 s() XCHG
        20 s() 21 s() XCHG
        19 s() 20 s() XCHG
        18 s() 19 s() XCHG
        17 s() 18 s() XCHG
        16 s() 17 s() XCHG
        s15 16 s() XCHG
        s14 s15 XCHG
        s13 s14 XCHG
        s12 s13 XCHG
        s11 s12 XCHG
        s10 s11 XCHG
        s9 s10 XCHG
        s8 s9 XCHG
        s7 s8 XCHG
        s6 s7 XCHG
        s5 s6 XCHG
        s4 s5 XCHG
        3 ROLL
        $OnionAuction$_contract_store INLINECALLDICT
        RETALT
      }>
      OVER
      3390874056 PUSHINT
      EQUAL
      IFJMP:<{
        NIP
        LDVARUINT16
        DROP
        22 s() 23 s() XCHG
        21 s() 22 s() XCHG
        20 s() 21 s() XCHG
        19 s() 20 s() XCHG
        18 s() 19 s() XCHG
        17 s() 18 s() XCHG
        16 s() 17 s() XCHG
        s15 16 s() XCHG
        s14 s15 XCHG
        s13 s14 XCHG
        s12 s13 XCHG
        s11 s12 XCHG
        s10 s11 XCHG
        s9 s10 XCHG
        s8 s9 XCHG
        s7 s8 XCHG
        s6 s7 XCHG
        s5 s6 XCHG
        s4 s5 XCHG
        s3 s4 XCHG
        s0 24 s() XCHG
        s1 s3 s0 XCHG3
        $OnionAuction$_fun_requireOwner INLINECALLDICT
        51018 PUSHINT
        25 s() PUSH
        0 GTINT
        THROWANYIFNOT
        24 s() PUSH
        __tact_context_get_sender INLINECALLDICT
        s2 s3 XCHG
        s1 26 s() XCHG
        $MinPurchaseUpdated$_constructor_old_min_purchase_new_min_purchase_updated_by INLINECALLDICT
        NEWC
        $MinPurchaseUpdated$_store_cell INLINECALLDICT
        $global_emit INLINECALLDICT
        22 s() 23 s() XCHG
        21 s() 22 s() XCHG
        20 s() 21 s() XCHG
        19 s() 20 s() XCHG
        18 s() 19 s() XCHG
        17 s() 18 s() XCHG
        16 s() 17 s() XCHG
        s15 16 s() XCHG
        s14 s15 XCHG
        s13 s14 XCHG
        s12 s13 XCHG
        s11 s12 XCHG
        s10 s11 XCHG
        s9 s10 XCHG
        s8 s9 XCHG
        s7 s8 XCHG
        s6 s7 XCHG
        s5 s6 XCHG
        s4 s5 XCHG
        s3 s4 XCHG
        s1 s3 s0 XCHG3
        $OnionAuction$_contract_store INLINECALLDICT
        RETALT
      }>
      OVER
      3553874899 PUSHINT
      EQUAL
      IFJMP:<{
        NIP
        LDMSGADDR
        DROP
        22 s() 23 s() XCHG
        21 s() 22 s() XCHG
        20 s() 21 s() XCHG
        19 s() 20 s() XCHG
        18 s() 19 s() XCHG
        17 s() 18 s() XCHG
        16 s() 17 s() XCHG
        s15 16 s() XCHG
        s14 s15 XCHG
        s13 s14 XCHG
        s12 s13 XCHG
        s11 s12 XCHG
        s10 s11 XCHG
        s9 s10 XCHG
        s8 s9 XCHG
        s7 s8 XCHG
        s6 s7 XCHG
        s5 s6 XCHG
        s4 s5 XCHG
        s3 s4 XCHG
        s0 24 s() XCHG
        s1 s3 s0 XCHG3
        $OnionAuction$_fun_requireOwner INLINECALLDICT
            MYADDR
        OVER
        ISNULL
        NOT
        IF:<{
          DROP
          __tact_not_null INLINECALLDICT
        }>ELSE<{
          NIP
        }>
        24 s() PUSH
        __tact_context_get_sender INLINECALLDICT
        s1 26 s() XCHG
        $TreasurySet$_constructor_old_treasury_new_treasury_set_by INLINECALLDICT
        NEWC
        $TreasurySet$_store_cell INLINECALLDICT
        $global_emit INLINECALLDICT
        22 s() 23 s() XCHG
        21 s() 22 s() XCHG
        20 s() 21 s() XCHG
        19 s() 20 s() XCHG
        18 s() 19 s() XCHG
        17 s() 18 s() XCHG
        16 s() 17 s() XCHG
        s15 16 s() XCHG
        15 ROLL
        $OnionAuction$_contract_store INLINECALLDICT
        RETALT
      }>
      OVER
      3520188881 PUSHINT
      EQUAL
      IFJMP:<{
        NIP
        LDVARUINT16
        LDMSGADDR
        DROP
        s1 24 s() XCHG
        s0 25 s() XCHG
        $OnionAuction$_fun_requireOwner INLINECALLDICT
        24 s() PUSH
        IFNOT:<{
          24 s() POP
          s12 PUSH
          s0 24 s() XCHG
        }>
        51020 PUSHINT
        25 s() PUSH
        s15 PUSH
        LEQ
        THROWANYIFNOT
        51020 PUSHINT
        25 s() PUSH
        0 GTINT
        THROWANYIFNOT
        s0 s13 XCHG
        24 s() PUSH
        SUB
        2 PUSHINT
        FALSE
            B{b5ee9c7241010101002600004800000000544f4e207769746864726177616c2066726f6d204f6e696f6e41756374696f6e02f58f8e} B>boc PUSHREF
        28 s() PUSH
        s0 s3 XCHG
        28 s() PUSH
        s1 s3 s3 XCHG3
        $SendParameters$_constructor_to_value_mode_bounce_body INLINECALLDICT
            NEWC
            b{01} STSLICECONST  // store tag = $0 and ihr_disabled = true
            1 STI               // store `bounce`
            b{000} STSLICECONST // store bounced = false and src = addr_none
            STSLICE             // store `to`
            SWAP
            STGRAMS             // store `value`
            105 PUSHINT         // 1 + 4 + 4 + 64 + 32
            STZEROES            // store currency_collection, ihr_fee, fwd_fee, created_lt and created_at
            // → Stack state
            // s0: Builder
            // s1: `data`
            // s2: `code`
            // s3: `body`
            // s4: `mode`
            // Group 2: Placing the Builder after code and data, then checking those for nullability
            s2 XCHG0
            DUP2
            ISNULL
            SWAP
            ISNULL
            AND
            // → Stack state
            // s0: -1 (true) if `data` and `code` are both null, 0 (false) otherwise
            // s1: `code`
            // s2: `data`
            // s3: Builder
            // s4: `body`
            // s5: `mode`
            // Group 3: Left branch of the IFELSE, executed if s0 is -1 (true)
            <{
                DROP2 // drop `data` and `code`, since either of those is null
                b{0} STSLICECONST
            }> PUSHCONT
            // Group 3: Right branch of the IFELSE, executed if s0 is 0 (false)
            <{
                // _ split_depth:(Maybe (## 5))
                //   special:(Maybe TickTock)
                //   code:(Maybe ^Cell)
                //   data:(Maybe ^Cell)
                //   library:(Maybe ^Cell)
                // = StateInit;
                ROT                // place message Builder on top
                b{10} STSLICECONST // store Maybe = true, Either = false
                // Start composing inlined StateInit
                b{00} STSLICECONST // store split_depth and special first
                STDICT             // store code
                STDICT             // store data
                b{0} STSLICECONST  // store library
            }> PUSHCONT
            // Group 3: IFELSE that does the branching shown above
            IFELSE
            // → Stack state
            // s0: Builder
            // s1: null or StateInit
            // s2: `body`
            // s3: `mode`
            // Group 4: Finalizing the message
            STDICT // store `body` as ref with an extra Maybe bit, since `body` might be null
            ENDC
            // → Stack state
            // s0: Cell
            // s1: `mode`
            // Group 5: Sending the message, with `mode` on top
            SWAP
            SENDRAWMSG // https://github.com/tact-lang/tact/issues/1558
        0 PUSHINT
        __tact_context_get_sender INLINECALLDICT
        s1 s3 XCHG
        s2 26 s() XCHG
        s1 27 s() XCHG
        26 s() PUSH
        $FundsWithdrawn$_constructor_currency_amount_destination_withdrawn_by_remaining_balance INLINECALLDICT
        NEWC
        $FundsWithdrawn$_store_cell INLINECALLDICT
        $global_emit INLINECALLDICT
        21 s() 23 s() XCHG
        20 s() 22 s() XCHG
        19 s() 21 s() XCHG
        18 s() 20 s() XCHG
        17 s() 19 s() XCHG
        16 s() 18 s() XCHG
        s15 17 s() XCHG
        s14 16 s() XCHG
        s13 s15 XCHG
        s12 s14 XCHG
        s12 s13 XCHG
        2 11 BLKSWAP
        $OnionAuction$_contract_store INLINECALLDICT
        RETALT
      }>
      OVER
      3537031890 PUSHINT
      EQUAL
      IFJMP:<{
        NIP
        LDVARUINT16
        LDMSGADDR
        DROP
        s1 24 s() XCHG
        s0 25 s() XCHG
        $OnionAuction$_fun_requireOwner INLINECALLDICT
        51007 PUSHINT
        s11 PUSH
        ISNULL
        NOT
        THROWANYIFNOT
        s10 PUSH
        $USDTConfig$_not_null INLINECALLDICT
        DROP
        NIP
        51007 PUSHINT
        OVER
        ISNULL
        NOT
        THROWANYIFNOT
        25 s() PUSH
        IFNOT:<{
          25 s() POP
          s9 PUSH
          s0 25 s() XCHG
        }>
        51020 PUSHINT
        26 s() PUSH
        s12 PUSH
        LEQ
        THROWANYIFNOT
        51020 PUSHINT
        26 s() PUSH
        0 GTINT
        THROWANYIFNOT
        s0 s10 XCHG
        25 s() PUSH
        SUB
        s0 s10 XCHG
        __tact_not_null INLINECALLDICT
        200000000 PUSHINT
        FALSE
        0 PUSHINT
            MYADDR
        PUSHNULL
        1 PUSHINT
            B{b5ee9c7241010101002700004a0000000055534454207769746864726177616c2066726f6d204f6e696f6e41756374696f6e8b28d83e} B>boc PUSHREF
        32 s() PUSH
        s0 s4 XCHG
        34 s() PUSH
        s4 s3 s4 XCHG3
        $JettonTransfer$_constructor_query_id_amount_destination_response_destination_custom_payload_forward_ton_amount_forward_payload INLINECALLDICT
        NEWC
        $JettonTransfer$_store_cell INLINECALLDICT
        $SendParameters$_constructor_to_value_bounce_body INLINECALLDICT
            NEWC
            b{01} STSLICECONST  // store tag = $0 and ihr_disabled = true
            1 STI               // store `bounce`
            b{000} STSLICECONST // store bounced = false and src = addr_none
            STSLICE             // store `to`
            SWAP
            STGRAMS             // store `value`
            105 PUSHINT         // 1 + 4 + 4 + 64 + 32
            STZEROES            // store currency_collection, ihr_fee, fwd_fee, created_lt and created_at
            // → Stack state
            // s0: Builder
            // s1: `data`
            // s2: `code`
            // s3: `body`
            // s4: `mode`
            // Group 2: Placing the Builder after code and data, then checking those for nullability
            s2 XCHG0
            DUP2
            ISNULL
            SWAP
            ISNULL
            AND
            // → Stack state
            // s0: -1 (true) if `data` and `code` are both null, 0 (false) otherwise
            // s1: `code`
            // s2: `data`
            // s3: Builder
            // s4: `body`
            // s5: `mode`
            // Group 3: Left branch of the IFELSE, executed if s0 is -1 (true)
            <{
                DROP2 // drop `data` and `code`, since either of those is null
                b{0} STSLICECONST
            }> PUSHCONT
            // Group 3: Right branch of the IFELSE, executed if s0 is 0 (false)
            <{
                // _ split_depth:(Maybe (## 5))
                //   special:(Maybe TickTock)
                //   code:(Maybe ^Cell)
                //   data:(Maybe ^Cell)
                //   library:(Maybe ^Cell)
                // = StateInit;
                ROT                // place message Builder on top
                b{10} STSLICECONST // store Maybe = true, Either = false
                // Start composing inlined StateInit
                b{00} STSLICECONST // store split_depth and special first
                STDICT             // store code
                STDICT             // store data
                b{0} STSLICECONST  // store library
            }> PUSHCONT
            // Group 3: IFELSE that does the branching shown above
            IFELSE
            // → Stack state
            // s0: Builder
            // s1: null or StateInit
            // s2: `body`
            // s3: `mode`
            // Group 4: Finalizing the message
            STDICT // store `body` as ref with an extra Maybe bit, since `body` might be null
            ENDC
            // → Stack state
            // s0: Cell
            // s1: `mode`
            // Group 5: Sending the message, with `mode` on top
            SWAP
            SENDRAWMSG // https://github.com/tact-lang/tact/issues/1558
        1 PUSHINT
        __tact_context_get_sender INLINECALLDICT
        s1 s3 XCHG
        s2 26 s() XCHG
        s1 27 s() XCHG
        s11 PUSH
        $FundsWithdrawn$_constructor_currency_amount_destination_withdrawn_by_remaining_balance INLINECALLDICT
        NEWC
        $FundsWithdrawn$_store_cell INLINECALLDICT
        $global_emit INLINECALLDICT
        21 s() 23 s() XCHG
        20 s() 22 s() XCHG
        19 s() 21 s() XCHG
        18 s() 20 s() XCHG
        17 s() 19 s() XCHG
        16 s() 18 s() XCHG
        s15 17 s() XCHG
        s14 16 s() XCHG
        2 14 BLKSWAP
        $OnionAuction$_contract_store INLINECALLDICT
        RETALT
      }>
      OVER
      1082886929 PUSHINT
      EQUAL
      IFJMP:<{
        NIP
        64 LDU
        64 LDU
        LDVARUINT16
        LDVARUINT16
        LDVARUINT16
        DROP
        23 s() 26 s() XCHG
        22 s() 25 s() XCHG
        21 s() 24 s() XCHG
        20 s() 26 s() XCHG
        19 s() 25 s() XCHG
        18 s() 24 s() XCHG
        17 s() 26 s() XCHG
        16 s() 25 s() XCHG
        s15 24 s() XCHG
        s14 26 s() XCHG
        s13 25 s() XCHG
        s12 24 s() XCHG
        s11 26 s() XCHG
        s10 25 s() XCHG
        s9 24 s() XCHG
        s8 26 s() XCHG
        s7 25 s() XCHG
        s6 24 s() XCHG
        s5 26 s() XCHG
        s4 25 s() XCHG
        s3 24 s() XCHG
        s2 26 s() XCHG
        s1 27 s() XCHG
        s0 28 s() XCHG
        $OnionAuction$_fun_requireOwner INLINECALLDICT
        s11 POP
        s13 POP
        16 s() POP
        16 s() POP
        16 s() POP
        16 s() POP
        19 s() PUSH
        19 s() PUSH
        22 s() PUSH
        24 s() PUSH
        26 s() PUSH
        1 PUSHINT
        257 PUSHINT
        s7 s1 s(-1) PU2XC
        __tact_dict_get_int_cell INLINECALLDICT
        $RoundStats$_load_opt INLINECALLDICT
        DUP
        ISNULL
        NOT
        IF:<{
          $RoundStats$_not_null INLINECALLDICT
          s10 POP
          s10 POP
          s10 POP
          36 s() PUSH
          3600 PUSHINT
          ADD
          37 s() PUSH
          s10 s11 XCHG2
          41 s() PUSH
          s2 s10 XCHG2
          $RoundStats$_constructor_round_number_start_time_end_time_price_total_raised_ton_total_raised_usdt_raised_usdt_equivalent_tokens_sold_purchase_count_unique_users_refund_count_refunded_amount_ton_refunded_amount_usdt_refunded_usdt_equivalent INLINECALLDICT
          257 PUSHINT
          s15 PUSH
          14 2 BLKSWAP
          NEWC
          $RoundStats$_store_cell INLINECALLDICT
          s3 s9 XCHG
          __tact_dict_set_int_cell INLINECALLDICT
          s0 s6 XCHG
        }>ELSE<{
          DROP
        }>
        s4 25 s() XCHG
        s3 24 s() XCHG
        s2 26 s() XCHG
        s1 27 s() XCHG
        s0 28 s() XCHG
        17 s() PUSH
        $AuctionStarted$_constructor_start_time_end_time_soft_cap_hard_cap_initial_price_total_supply INLINECALLDICT
        NEWC
        $AuctionStarted$_store_cell INLINECALLDICT
        $global_emit INLINECALLDICT
        18 s() 23 s() XCHG
        17 s() 22 s() XCHG
        s0 21 s() XCHG
        s0 18 s() XCHG
        s12 17 s() XCHG
        s11 16 s() XCHG
        s10 s15 XCHG
        s12 s14 XCHG
        s8 s13 XCHG
        s7 s12 XCHG
        s0 s11 XCHG
        s5 s10 XCHG
        s4 s9 XCHG
        s3 s8 XCHG
        s7 s1 s5 XCHG3
        s3 s6 s4 XCHG3
        SWAP
        $OnionAuction$_contract_store INLINECALLDICT
        RETALT
      }>
      OVER
      2861271945 PUSHINT
      EQUAL
      IFJMP:<{
        NIP
        LDVARUINT16
        32 LDU
        DROP
        s1 24 s() XCHG
        s0 25 s() XCHG
        $OnionAuction$_fun_requireOwner INLINECALLDICT
        51001 PUSHINT
        s12 PUSH
        1 EQINT
        THROWANYIFNOT
        25 s() PUSH
        25 s() PUSH
        s8 PUSH
        257 PUSHINT
        29 s() PUSH
        __tact_dict_get_int_cell INLINECALLDICT
        $RoundStats$_load_opt INLINECALLDICT
        ISNULL
        IF:<{
              NOW
          24 s() 26 s() XCHG
          23 s() 25 s() XCHG
          22 s() 26 s() XCHG
          21 s() 25 s() XCHG
          20 s() 26 s() XCHG
          19 s() 25 s() XCHG
          18 s() 26 s() XCHG
          17 s() 25 s() XCHG
          s2 16 s() XCHG
          s14 s15 s0 XCHG3
          s12 s13 s0 XCHG3
          s10 s11 s0 XCHG3
          s8 s9 s0 XCHG3
          s6 s7 s0 XCHG3
          5 1 REVERSE
          s1 s3 XCHG
          28 s() PUSH
          SWAP
          $OnionAuction$_fun_initializeNewRound INLINECALLDICT
          23 s() 25 s() XCHG
          22 s() 24 s() XCHG
          21 s() 23 s() XCHG
          20 s() 22 s() XCHG
          19 s() 21 s() XCHG
          18 s() 20 s() XCHG
          17 s() 19 s() XCHG
          16 s() 18 s() XCHG
          2 14 BLKSWAP
        }>
        __tact_context_get_sender INLINECALLDICT
        s4 18 s() XCHG
        s3 28 s() XCHG
        s2 17 s() XCHG
        s1 27 s() XCHG
        $RoundUpdated$_constructor_old_round_new_round_old_price_new_price_updated_by INLINECALLDICT
        NEWC
        $RoundUpdated$_store_cell INLINECALLDICT
        $global_emit INLINECALLDICT
        21 s() 23 s() XCHG
        20 s() 22 s() XCHG
        19 s() 21 s() XCHG
        18 s() 20 s() XCHG
        17 s() 19 s() XCHG
        16 s() 18 s() XCHG
        s15 17 s() XCHG
        s14 16 s() XCHG
        s12 s15 XCHG
        2 12 BLKSWAP
        $OnionAuction$_contract_store INLINECALLDICT
        RETALT
      }>
      OVER
      1935855772 PUSHINT
      EQUAL
      IFJMP:<{
        NIP
        64 LDU
        LDVARUINT16
        LDMSGADDR
        LDOPTREF
        DROP
        23 s() 25 s() XCHG
        22 s() 24 s() XCHG
        21 s() 25 s() XCHG
        20 s() 24 s() XCHG
        19 s() 25 s() XCHG
        18 s() 24 s() XCHG
        17 s() 25 s() XCHG
        16 s() 24 s() XCHG
        s15 25 s() XCHG
        s14 24 s() XCHG
        s13 25 s() XCHG
        s12 24 s() XCHG
        s11 25 s() XCHG
        s10 24 s() XCHG
        s9 25 s() XCHG
        s8 24 s() XCHG
        s7 25 s() XCHG
        s6 24 s() XCHG
        s5 25 s() XCHG
        s4 24 s() XCHG
        s3 25 s() XCHG
        s2 24 s() XCHG
        s1 26 s() XCHG
        s0 27 s() XCHG
        $OnionAuction$_fun_requireNotStopped INLINECALLDICT
        51001 PUSHINT
        s12 PUSH
        1 EQINT
        THROWANYIFNOT
        51002 PUSHINT
            NOW
        23 s() PUSH
        GEQ
        THROWANYIFNOT
        51003 PUSHINT
            NOW
        22 s() PUSH
        LEQ
        THROWANYIFNOT
        51007 PUSHINT
        s11 PUSH
        ISNULL
        NOT
        THROWANYIFNOT
        s10 PUSH
        $USDTConfig$_not_null INLINECALLDICT
        2DROP
            MYADDR
        $OnionAuction$_fun_getJettonWalletAddress INLINECALLDICT
        51008 PUSHINT
        __tact_context_get_sender INLINECALLDICT
        ROT
        SDEQ
        THROWANYIFNOT
        51009 PUSHINT
        28 s() PUSH
        ISNULL
        NOT
        THROWANYIFNOT
        s3 25 s() XCHG
        s2 24 s() XCHG
        s1 26 s() XCHG
        s0 23 s() XCHG
        s0 27 s() XCHG
        22 s() 26 s() XCHG
        21 s() 25 s() XCHG
        20 s() 24 s() XCHG
        19 s() 23 s() XCHG
        18 s() 22 s() XCHG
        17 s() 21 s() XCHG
        16 s() 20 s() XCHG
        s15 19 s() XCHG
        s14 18 s() XCHG
        s13 17 s() XCHG
        s12 16 s() XCHG
        s11 s15 XCHG
        s10 s14 XCHG
        s9 s13 XCHG
        s8 s12 XCHG
        s7 s11 XCHG
        s6 s10 XCHG
        s5 s9 XCHG
        5 4 REVERSE
        s4 s6 XCHG
        s4 s5 XCHG
        $OnionAuction$_fun_handleUSDTWithSignature INLINECALLDICT
        $OnionAuction$_contract_store INLINECALLDICT
        RETALT
      }>
      OVER
      1524770820 PUSHINT
      EQUAL
      IFJMP:<{
        NIP
        $PurchaseCalculation$_load INLINECALLDICT
        s0 s9 XCHG
        LDREF
        DROP
        CTOS
        23 s() 31 s() XCHG
        22 s() 30 s() XCHG
        21 s() 29 s() XCHG
        20 s() 28 s() XCHG
        19 s() 27 s() XCHG
        18 s() 26 s() XCHG
        17 s() 25 s() XCHG
        16 s() 24 s() XCHG
        s15 31 s() XCHG
        s14 30 s() XCHG
        s13 29 s() XCHG
        s12 28 s() XCHG
        s11 27 s() XCHG
        s10 26 s() XCHG
        s9 25 s() XCHG
        s8 24 s() XCHG
        s7 31 s() XCHG
        s6 30 s() XCHG
        s5 29 s() XCHG
        s4 28 s() XCHG
        s3 27 s() XCHG
        s2 26 s() XCHG
        s1 32 s() XCHG
        s0 33 s() XCHG
        $OnionAuction$_fun_requireNotStopped INLINECALLDICT
        51001 PUSHINT
        s12 PUSH
        1 EQINT
        THROWANYIFNOT
        51002 PUSHINT
            NOW
        23 s() PUSH
        GEQ
        THROWANYIFNOT
        51003 PUSHINT
            NOW
        22 s() PUSH
        LEQ
        THROWANYIFNOT
        51009 PUSHINT
        s5 PUSH
        0 NEQINT
        THROWANYIFNOT
            NOW
        51010 PUSHINT
        OVER
        29 s() PUSH
        SUB
        s5 PUSH
        LEQ
        THROWANYIFNOT
        27 s() PUSH
        51011 PUSHINT
        s0 s2 XCHG
        LEQ
        THROWANYIFNOT
        51012 PUSHINT
        s4 PUSH
        257 PUSHINT
        35 s() PUSH
        1 PUSHINT
        __tact_dict_get_int_int INLINECALLDICT
        ISNULL
        THROWANYIFNOT
        24 s() PUSH
        s0 s2 XCHG
        32 s() PUSH
        s0 s2 XCHG
        32 s() PUSH
        s0 s2 XCHG
        32 s() PUSH
        s0 s2 XCHG
        s1 32 s() XCHG
        31 s() PUSH
        SWAP
        s0 31 s() XCHG
        37 s() PUSH
        31 s() PUSH
        30 s() 32 s() XCHG
        29 s() 31 s() XCHG
        28 s() 30 s() XCHG
        27 s() 29 s() XCHG
        26 s() 28 s() XCHG
        25 s() 27 s() XCHG
        24 s() 26 s() XCHG
        23 s() 25 s() XCHG
        22 s() 24 s() XCHG
        21 s() 23 s() XCHG
        20 s() 22 s() XCHG
        19 s() 21 s() XCHG
        18 s() 20 s() XCHG
        17 s() 19 s() XCHG
        16 s() 18 s() XCHG
        s15 17 s() XCHG
        s14 16 s() XCHG
        s13 s15 XCHG
        s12 s14 XCHG
        s11 s13 XCHG
        s10 s12 XCHG
        s9 s11 XCHG
        s10 35 s() XCHG
        s9 33 s() XCHG
        $OnionAuction$_fun_hashPurchaseCalculation INLINECALLDICT
        51013 PUSHINT
        s0 33 s() XCHG
        s6 PUSH
            CHKSIGNU
        s1 32 s() XCHG
        THROWANYIFNOT
        s0 s2 XCHG
        257 PUSHINT
        30 s() PUSH
        TRUE
        1 PUSHINT
        __tact_dict_set_int_int INLINECALLDICT
        51016 PUSHINT
        __tact_context_get_sender INLINECALLDICT
        25 s() PUSH
        SWAP
        SDEQ
        THROWANYIFNOT
        51006 PUSHINT
        s12 PUSH
        28 s() PUSH
        ADD
        18 s() PUSH
        LEQ
        THROWANYIFNOT
        22 s() 23 s() XCHG
        21 s() 22 s() XCHG
        20 s() 21 s() XCHG
        19 s() 20 s() XCHG
        18 s() 19 s() XCHG
        17 s() 18 s() XCHG
        16 s() 17 s() XCHG
        s15 16 s() XCHG
        s14 s15 XCHG
        s13 s14 XCHG
        s12 s13 XCHG
        s11 s12 XCHG
        s10 s11 XCHG
        s9 s10 XCHG
        s8 s9 XCHG
        s7 s8 XCHG
        s6 s7 XCHG
        s5 s6 XCHG
        s4 s5 XCHG
        s0 s3 XCHG
        s0 s4 XCHG
        s0 30 s() XCHG
        s1 s2 XCHG
        $OnionAuction$_fun_getCurrentRoundTokensSold INLINECALLDICT
        51022 PUSHINT
        SWAP
        28 s() PUSH
        ADD
        21 s() PUSH
        LEQ
        THROWANYIFNOT
        27 s() PUSH
        IF:<{
          27 s() PUSH
          1 EQINT
          IF:<{
            51007 PUSHINT
            s11 PUSH
            ISNULL
            NOT
            THROWANYIFNOT
            s0 s9 XCHG
            28 s() PUSH
            ADD
          }>ELSE<{
            51004 PUSHINT
            THROWANY
            s0 s9 XCHG
          }>
        }>ELSE<{
          __tact_context_get INLINECALLDICT
          $Context$_get_value INLINECALLDICT
          100000000 PUSHINT
          SUB
          51015 PUSHINT
          SWAP
          30 s() PUSH
          GEQ
          THROWANYIFNOT
          s0 s13 XCHG
          28 s() PUSH
          ADD
          s0 s13 XCHG
          s0 s9 XCHG
        }>
        s0 s8 XCHG
        25 s() PUSH
        ADD
        s0 s12 XCHG
        26 s() PUSH
        ADD
        s0 s7 XCHG
        INC
        s7 s12 XCHG
        s8 s9 XCHG
        s0 s7 XCHG
        s0 s8 XCHG
        30 s() PUSH
        29 s() PUSH
        28 s() PUSH
        30 s() PUSH
        29 s() PUSH
        29 s() PUSH
        $OnionAuction$_fun_updateRoundStatsForPurchase INLINECALLDICT
        s12 PUSH
        19 s() PUSH
        GEQ
        IF:<{
          s11 POP
          2 PUSHINT
          DUP
              NOW
          s2 s(-1) PUXC
          16 s() PUSH
          s13 s1 PUXC
          17 s() PUSH
          SWAP
          $AuctionEnded$_constructor_end_reason_final_status_total_raised_total_raised_usdt_total_tokens_sold_end_time INLINECALLDICT
          NEWC
          $AuctionEnded$_store_cell INLINECALLDICT
          $global_emit INLINECALLDICT
          s0 s11 XCHG
        }>
        23 s() 24 s() XCHG
        22 s() 23 s() XCHG
        21 s() 22 s() XCHG
        20 s() 21 s() XCHG
        19 s() 20 s() XCHG
        18 s() 19 s() XCHG
        17 s() 18 s() XCHG
        16 s() 17 s() XCHG
        s15 16 s() XCHG
        15 ROLL
        1 PUSHINT
        31 s() PUSH
        s0 s2 XCHG
        30 s() PUSH
        s0 s2 XCHG
        29 s() PUSH
        s0 s2 XCHG
        31 s() PUSH
        -ROT
        34 s() PUSH
        SWAP
        s0 31 s() XCHG
        31 s() PUSH
        $OnionAuction$_fun_createOrUpdateUserPurchase INLINECALLDICT
        s5 29 s() XCHG
        s4 27 s() XCHG
        s3 25 s() XCHG
        s2 26 s() XCHG
        1 PUSHINT
        s0 s2 XCHG
        s1 25 s() XCHG
        s0 29 s() XCHG
        s14 s10 s13 PUSH3
        $PurchaseCompleted$_constructor_user_amount_tokens_received_currency_purchase_method_round_number_nonce_total_raised_total_raised_usdt_total_tokens_sold INLINECALLDICT
        NEWC
        $PurchaseCompleted$_store_cell INLINECALLDICT
        $global_emit INLINECALLDICT
        17 s() 23 s() XCHG
        16 s() 22 s() XCHG
        s15 21 s() XCHG
        s14 20 s() XCHG
        s13 19 s() XCHG
        s12 18 s() XCHG
        s11 17 s() XCHG
        s10 16 s() XCHG
        s9 s15 XCHG
        s8 s14 XCHG
        s7 s13 XCHG
        s6 s12 XCHG
        6 6 BLKSWAP
        s1 s3 XCHG
        s1 s4 XCHG
        $OnionAuction$_contract_store INLINECALLDICT
        RETALT
      }>
      OVER
      929991442 PUSHINT
      EQUAL
      IFJMP:<{
        NIP
        LDMSGADDR
        LDVARUINT16
        LDVARUINT16
        NIP
        8 LDU
        32 LDU
        LDVARUINT16
        DROP
        23 s() 26 s() XCHG
        22 s() 25 s() XCHG
        21 s() 24 s() XCHG
        20 s() 26 s() XCHG
        19 s() 25 s() XCHG
        18 s() 24 s() XCHG
        17 s() 26 s() XCHG
        16 s() 25 s() XCHG
        s15 24 s() XCHG
        s14 26 s() XCHG
        s13 25 s() XCHG
        s12 24 s() XCHG
        s11 26 s() XCHG
        s10 25 s() XCHG
        s9 24 s() XCHG
        s8 26 s() XCHG
        s7 25 s() XCHG
        s6 24 s() XCHG
        s5 26 s() XCHG
        s4 25 s() XCHG
        s3 24 s() XCHG
        s2 26 s() XCHG
        s1 27 s() XCHG
        s0 28 s() XCHG
        $OnionAuction$_fun_requireNotStopped INLINECALLDICT
        51001 PUSHINT
        s12 PUSH
        1 EQINT
        THROWANYIFNOT
        51003 PUSHINT
            NOW
        22 s() PUSH
        LEQ
        THROWANYIFNOT
        25 s() PUSH
        $OnionAuction$_fun_getUserPurchaseInit INLINECALLDICT
        $global_contractAddress INLINECALLDICT
        51017 PUSHINT
        __tact_context_get_sender INLINECALLDICT
        s1 s2 XCHG
        SDEQ
        THROWANYIFNOT
        24 s() PUSH
        17 s() PUSH
        MUL
        100 PUSHINT
        DIV
        25 s() PUSH
        SWAP
        SUB
        s0 28 s() XCHG
        25 s() PUSH
        28 s() PUSH
        31 s() PUSH
        $OnionAuction$_fun_updateRoundStatsForRefund INLINECALLDICT
        s0 28 s() XCHG
        s1 s8 XCHG
        SUB
        25 s() PUSH
        IF:<{
          s0 25 s() XCHG
          1 EQINT
          IF:<{
            51007 PUSHINT
            s9 PUSH
            ISNULL
            NOT
            THROWANYIFNOT
            s0 22 s() XCHG
            s1 s7 XCHG
            SUB
            s7 PUSH
            $USDTConfig$_not_null INLINECALLDICT
            DROP
            NIP
            51007 PUSHINT
            OVER
            ISNULL
            NOT
            THROWANYIFNOT
            __tact_not_null INLINECALLDICT
            100000000 PUSHINT
            FALSE
            0 PUSHINT
                MYADDR
            PUSHNULL
            1 PUSHINT
                B{b5ee9c7241010101001b000032000000005553445420726566756e642070726f6365737365647a5489f9} B>boc PUSHREF
            s4 s6 XCHG
            s5 32 s() XCHG
            s4 30 s() XCHG
            $JettonTransfer$_constructor_query_id_amount_destination_response_destination_custom_payload_forward_ton_amount_forward_payload INLINECALLDICT
            NEWC
            $JettonTransfer$_store_cell INLINECALLDICT
            s1 s3 XCHG
            s2 24 s() XCHG
            s1 26 s() XCHG
            $SendParameters$_constructor_to_value_bounce_body INLINECALLDICT
                NEWC
                b{01} STSLICECONST  // store tag = $0 and ihr_disabled = true
                1 STI               // store `bounce`
                b{000} STSLICECONST // store bounced = false and src = addr_none
                STSLICE             // store `to`
                SWAP
                STGRAMS             // store `value`
                105 PUSHINT         // 1 + 4 + 4 + 64 + 32
                STZEROES            // store currency_collection, ihr_fee, fwd_fee, created_lt and created_at
                // → Stack state
                // s0: Builder
                // s1: `data`
                // s2: `code`
                // s3: `body`
                // s4: `mode`
                // Group 2: Placing the Builder after code and data, then checking those for nullability
                s2 XCHG0
                DUP2
                ISNULL
                SWAP
                ISNULL
                AND
                // → Stack state
                // s0: -1 (true) if `data` and `code` are both null, 0 (false) otherwise
                // s1: `code`
                // s2: `data`
                // s3: Builder
                // s4: `body`
                // s5: `mode`
                // Group 3: Left branch of the IFELSE, executed if s0 is -1 (true)
                <{
                    DROP2 // drop `data` and `code`, since either of those is null
                    b{0} STSLICECONST
                }> PUSHCONT
                // Group 3: Right branch of the IFELSE, executed if s0 is 0 (false)
                <{
                    // _ split_depth:(Maybe (## 5))
                    //   special:(Maybe TickTock)
                    //   code:(Maybe ^Cell)
                    //   data:(Maybe ^Cell)
                    //   library:(Maybe ^Cell)
                    // = StateInit;
                    ROT                // place message Builder on top
                    b{10} STSLICECONST // store Maybe = true, Either = false
                    // Start composing inlined StateInit
                    b{00} STSLICECONST // store split_depth and special first
                    STDICT             // store code
                    STDICT             // store data
                    b{0} STSLICECONST  // store library
                }> PUSHCONT
                // Group 3: IFELSE that does the branching shown above
                IFELSE
                // → Stack state
                // s0: Builder
                // s1: null or StateInit
                // s2: `body`
                // s3: `mode`
                // Group 4: Finalizing the message
                STDICT // store `body` as ref with an extra Maybe bit, since `body` might be null
                ENDC
                // → Stack state
                // s0: Cell
                // s1: `mode`
                // Group 5: Sending the message, with `mode` on top
                SWAP
                SENDRAWMSG // https://github.com/tact-lang/tact/issues/1558
            s4 20 s() XCHG
          }>ELSE<{
            s2 25 s() XCHG
            22 s() POP
            22 s() POP
            DROP
          }>
        }>ELSE<{
          25 s() POP
          s0 22 s() XCHG
          s1 s11 XCHG
          SUB
          2 PUSHINT
          FALSE
              B{b5ee9c7241010101001a00003000000000544f4e20726566756e642070726f63657373656413a585a3} B>boc PUSHREF
          s4 25 s() XCHG
          s3 27 s() XCHG
          $SendParameters$_constructor_to_value_mode_bounce_body INLINECALLDICT
              NEWC
              b{01} STSLICECONST  // store tag = $0 and ihr_disabled = true
              1 STI               // store `bounce`
              b{000} STSLICECONST // store bounced = false and src = addr_none
              STSLICE             // store `to`
              SWAP
              STGRAMS             // store `value`
              105 PUSHINT         // 1 + 4 + 4 + 64 + 32
              STZEROES            // store currency_collection, ihr_fee, fwd_fee, created_lt and created_at
              // → Stack state
              // s0: Builder
              // s1: `data`
              // s2: `code`
              // s3: `body`
              // s4: `mode`
              // Group 2: Placing the Builder after code and data, then checking those for nullability
              s2 XCHG0
              DUP2
              ISNULL
              SWAP
              ISNULL
              AND
              // → Stack state
              // s0: -1 (true) if `data` and `code` are both null, 0 (false) otherwise
              // s1: `code`
              // s2: `data`
              // s3: Builder
              // s4: `body`
              // s5: `mode`
              // Group 3: Left branch of the IFELSE, executed if s0 is -1 (true)
              <{
                  DROP2 // drop `data` and `code`, since either of those is null
                  b{0} STSLICECONST
              }> PUSHCONT
              // Group 3: Right branch of the IFELSE, executed if s0 is 0 (false)
              <{
                  // _ split_depth:(Maybe (## 5))
                  //   special:(Maybe TickTock)
                  //   code:(Maybe ^Cell)
                  //   data:(Maybe ^Cell)
                  //   library:(Maybe ^Cell)
                  // = StateInit;
                  ROT                // place message Builder on top
                  b{10} STSLICECONST // store Maybe = true, Either = false
                  // Start composing inlined StateInit
                  b{00} STSLICECONST // store split_depth and special first
                  STDICT             // store code
                  STDICT             // store data
                  b{0} STSLICECONST  // store library
              }> PUSHCONT
              // Group 3: IFELSE that does the branching shown above
              IFELSE
              // → Stack state
              // s0: Builder
              // s1: null or StateInit
              // s2: `body`
              // s3: `mode`
              // Group 4: Finalizing the message
              STDICT // store `body` as ref with an extra Maybe bit, since `body` might be null
              ENDC
              // → Stack state
              // s0: Cell
              // s1: `mode`
              // Group 5: Sending the message, with `mode` on top
              SWAP
              SENDRAWMSG // https://github.com/tact-lang/tact/issues/1558
          20 s() 22 s() XCHG
          s8 20 s() XCHG
        }>
        18 s() 23 s() XCHG
        17 s() 22 s() XCHG
        16 s() 21 s() XCHG
        s15 20 s() XCHG
        s14 19 s() XCHG
        s13 18 s() XCHG
        s12 17 s() XCHG
        s11 16 s() XCHG
        s10 s15 XCHG
        s9 s14 XCHG
        s8 s13 XCHG
        s7 s12 XCHG
        s6 s11 XCHG
        s5 s10 XCHG
        s4 s9 XCHG
        s6 s8 XCHG
        s7 s6 s5 XCHG3
        s4 s3 s3 XCHG3
        $OnionAuction$_contract_store INLINECALLDICT
        RETALT
      }>
      SWAP
      2490013878 PUSHINT
      EQUAL
      IFJMP:<{
        64 LDU
        DROP
        $DeployOk$_constructor_queryId INLINECALLDICT
        NEWC
        $DeployOk$_store_cell INLINECALLDICT
        22 s() 24 s() XCHG
        21 s() 23 s() XCHG
        20 s() 22 s() XCHG
        19 s() 21 s() XCHG
        18 s() 20 s() XCHG
        17 s() 19 s() XCHG
        16 s() 18 s() XCHG
        s15 17 s() XCHG
        s14 16 s() XCHG
        s13 s15 XCHG
        s12 s14 XCHG
        s11 s13 XCHG
        s10 s12 XCHG
        s9 s11 XCHG
        s8 s10 XCHG
        s7 s9 XCHG
        s6 s8 XCHG
        s5 s7 XCHG
        s4 s6 XCHG
        s3 s5 XCHG
        s4 s3 s0 XCHG3
        s1 s2 XCHG
        $OnionAuction$_fun_notify INLINECALLDICT
        $OnionAuction$_contract_store INLINECALLDICT
        RETALT
      }>
      s0 23 s() XCHG
    }>
    s0 23 s() XCHG
    HASHSU
    DUP
    87651377378655782259476005716871872139955156637688197487361824677095371478895 PUSHINT
    EQUAL
    IFJMP:<{
      DROP
      21 s() 23 s() XCHG
      20 s() 22 s() XCHG
      19 s() 21 s() XCHG
      18 s() 20 s() XCHG
      17 s() 19 s() XCHG
      16 s() 18 s() XCHG
      s15 17 s() XCHG
      s14 16 s() XCHG
      s13 s15 XCHG
      2 13 BLKSWAP
      $OnionAuction$_contract_store INLINECALLDICT
    }>
    DUP
    95819923835608642605438444009006233327095633892014065289346243875960550012198 PUSHINT
    EQUAL
    IFJMP:<{
      DROP
      21 s() 23 s() XCHG
      20 s() 22 s() XCHG
      19 s() 21 s() XCHG
      18 s() 20 s() XCHG
      17 s() 19 s() XCHG
      16 s() 18 s() XCHG
      s15 17 s() XCHG
      s14 16 s() XCHG
      s13 s15 XCHG
      2 13 BLKSWAP
      $OnionAuction$_fun_requireOwner INLINECALLDICT
      51001 PUSHINT
      s0 s12 XCHG
      1 EQINT
      s1 s12 XCHG
      THROWANYIFNOT
      3 PUSHINT
          NOW
      21 s() PUSH
      GREATER
      IF:<{
        DROP
        1 PUSHINT
      }>ELSE<{
        s12 PUSH
        19 s() PUSH
        GEQ
        IF:<{
          DROP
          2 PUSHINT
        }>
      }>
      3 PUSHINT
      s13 PUSH
      0 GTINT
      IF:<{
        DROP
        2 PUSHINT
      }>
      DUP
          NOW
      s3 s3 s0 XCHG3
      16 s() PUSH
      s13 s1 PUXC
      17 s() PUSH
      SWAP
      $AuctionEnded$_constructor_end_reason_final_status_total_raised_total_raised_usdt_total_tokens_sold_end_time INLINECALLDICT
      NEWC
      $AuctionEnded$_store_cell INLINECALLDICT
      $global_emit INLINECALLDICT
      s0 s11 XCHG
      $OnionAuction$_contract_store INLINECALLDICT
    }>
    102431878623108463813452777067982877708008819854481748891612374642872904676328 PUSHINT
    EQUAL
    IFJMP:<{
      21 s() 23 s() XCHG
      20 s() 22 s() XCHG
      19 s() 21 s() XCHG
      18 s() 20 s() XCHG
      17 s() 19 s() XCHG
      16 s() 18 s() XCHG
      s15 17 s() XCHG
      s14 16 s() XCHG
      s13 s15 XCHG
      2 13 BLKSWAP
      $OnionAuction$_fun_requireOwner INLINECALLDICT
      $OnionAuction$_fun_requireNotStopped INLINECALLDICT
      22 s() POP
      TRUE
          B{b5ee9c7241010101000d0000160000000053746f707065646f94ddb2} B>boc PUSHREF
      s1 23 s() XCHG
      $OnionAuction$_fun_reply INLINECALLDICT
      $OnionAuction$_contract_store INLINECALLDICT
    }>
    15 BLKDROP
    9 BLKDROP
    130 THROW
  }>
  __tact_selector_hack PROC:<{
    @atend @ 1 {
            execute current@ context@ current!
            {
                // The core idea of this function is to save gas by avoiding unnecessary dict jump, when recv_internal/recv_external is called
                // We want to extract recv_internal/recv_external from the dict and select needed function
                // not by jumping to the needed function by it's index, but by using usual IF statements.
                }END> b> // Close previous builder, now we have a cell of previous code on top of the stack
                <{ // Start of the new code builder
                    SETCP0
                    // Swap the new code builder with the previous code, now we have previous code on top of the stack
                    swap
                    // Transform cell to slice and load first ref from the previous code, now we have the dict on top of the stack
                    <s ref@
                    // Extract the recv_internal from the dict
                    dup 0 swap @procdictkeylen idict@ { "internal shortcut error" abort } ifnot
                    swap
                    // Delete the recv_internal from the dict
                    0 swap @procdictkeylen idict- drop
                    // Delete the recv_external from the dict (it's okay if it's not there)
                    -1 swap @procdictkeylen idict- drop
                    // Delete the __tact_selector_hack from the dict
                    65535 swap @procdictkeylen idict- drop
                    // Bring the code builder from the bottom of the stack
                    // because if recv_external extraction is optional, and the number of elements on the stack is not fixed
                    depth 1- roll
                    // Swap with the dict from which we extracted recv_internal and (maybe) recv_external
                    swap
                    // Check if the dict is empty
                    dup null?
                    // Store a copy of this flag in the bottom of the stack
                    dup depth 1- -roll
                    {
                        // If the dict is empty, just drop it (it will be null if it's empty)
                        drop
                    }
                    {
                        // If the dict is not empty, prepare continuation to be stored in c3
                        <{
                            // Save this dict as first ref in this continuation, it will be pushed in runtime by DICTPUSHCONST
                            swap @procdictkeylen DICTPUSHCONST
                            // Jump to the needed function by it's index
                            DICTIGETJMPZ
                            // If such key is not found, throw 11 along with the key as an argument
                            11 THROWARG
                        }> PUSHCONT
                        // Store the continuation in c3
                        c3 POP
                    } cond
                    // Function id is on top of the (runtime) stack
                    DUP IFNOTJMP:<{
                        // place recv_internal here
                        DROP swap @addop
                    }>
                    // Bring back the flag, indicating if the dict is empty or not from the bottom of the stack
                    depth 1- roll
                    {
                        // If the dict is empty, throw 11
                        11 THROWARG
                    }
                    {
                        // If the dict is not empty, jump to continuation from c3
                        c3 PUSH JMPX
                    } cond
                }> b>
            } : }END>c
            current@ context! current!
        } does @atend !
  }>
}END>c
