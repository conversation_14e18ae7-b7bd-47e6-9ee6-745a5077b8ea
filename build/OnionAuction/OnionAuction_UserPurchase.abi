{"name": "UserPurchase", "types": [{"name": "DataSize", "header": null, "fields": [{"name": "cells", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "bits", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "refs", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}]}, {"name": "SignedBundle", "header": null, "fields": [{"name": "signature", "type": {"kind": "simple", "type": "fixed-bytes", "optional": false, "format": 64}}, {"name": "signedData", "type": {"kind": "simple", "type": "slice", "optional": false, "format": "remainder"}}]}, {"name": "StateInit", "header": null, "fields": [{"name": "code", "type": {"kind": "simple", "type": "cell", "optional": false}}, {"name": "data", "type": {"kind": "simple", "type": "cell", "optional": false}}]}, {"name": "Context", "header": null, "fields": [{"name": "bounceable", "type": {"kind": "simple", "type": "bool", "optional": false}}, {"name": "sender", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "value", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "raw", "type": {"kind": "simple", "type": "slice", "optional": false}}]}, {"name": "SendParameters", "header": null, "fields": [{"name": "mode", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "body", "type": {"kind": "simple", "type": "cell", "optional": true}}, {"name": "code", "type": {"kind": "simple", "type": "cell", "optional": true}}, {"name": "data", "type": {"kind": "simple", "type": "cell", "optional": true}}, {"name": "value", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "to", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "bounce", "type": {"kind": "simple", "type": "bool", "optional": false}}]}, {"name": "MessageParameters", "header": null, "fields": [{"name": "mode", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "body", "type": {"kind": "simple", "type": "cell", "optional": true}}, {"name": "value", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "to", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "bounce", "type": {"kind": "simple", "type": "bool", "optional": false}}]}, {"name": "DeployParameters", "header": null, "fields": [{"name": "mode", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "body", "type": {"kind": "simple", "type": "cell", "optional": true}}, {"name": "value", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "bounce", "type": {"kind": "simple", "type": "bool", "optional": false}}, {"name": "init", "type": {"kind": "simple", "type": "StateInit", "optional": false}}]}, {"name": "StdAddress", "header": null, "fields": [{"name": "workchain", "type": {"kind": "simple", "type": "int", "optional": false, "format": 8}}, {"name": "address", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 256}}]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "header": null, "fields": [{"name": "workchain", "type": {"kind": "simple", "type": "int", "optional": false, "format": 32}}, {"name": "address", "type": {"kind": "simple", "type": "slice", "optional": false}}]}, {"name": "BasechainAdd<PERSON>", "header": null, "fields": [{"name": "hash", "type": {"kind": "simple", "type": "int", "optional": true, "format": 257}}]}, {"name": "ChangeOwner", "header": 2174598809, "fields": [{"name": "queryId", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "new<PERSON>wner", "type": {"kind": "simple", "type": "address", "optional": false}}]}, {"name": "ChangeOwnerOk", "header": 846932810, "fields": [{"name": "queryId", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "new<PERSON>wner", "type": {"kind": "simple", "type": "address", "optional": false}}]}, {"name": "Deploy", "header": 2490013878, "fields": [{"name": "queryId", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}]}, {"name": "DeployOk", "header": 2952335191, "fields": [{"name": "queryId", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}]}, {"name": "FactoryDeploy", "header": 1829761339, "fields": [{"name": "queryId", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "cashback", "type": {"kind": "simple", "type": "address", "optional": false}}]}, {"name": "JettonTransfer", "header": 260734629, "fields": [{"name": "query_id", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "amount", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "destination", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "response_destination", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "custom_payload", "type": {"kind": "simple", "type": "cell", "optional": true}}, {"name": "forward_ton_amount", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "forward_payload", "type": {"kind": "simple", "type": "cell", "optional": true}}]}, {"name": "JettonTransferNotification", "header": 1935855772, "fields": [{"name": "query_id", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "amount", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "sender", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "forward_payload", "type": {"kind": "simple", "type": "cell", "optional": true}}]}, {"name": "JettonBurn", "header": 1499400124, "fields": [{"name": "query_id", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "amount", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "response_destination", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "custom_payload", "type": {"kind": "simple", "type": "cell", "optional": true}}]}, {"name": "JettonExcesses", "header": 3576854235, "fields": [{"name": "query_id", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}]}, {"name": "JettonInternalTransfer", "header": 395134233, "fields": [{"name": "query_id", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "amount", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "from", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "response_address", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "forward_ton_amount", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "forward_payload", "type": {"kind": "simple", "type": "cell", "optional": true}}]}, {"name": "JettonBurnNotification", "header": 2078119902, "fields": [{"name": "query_id", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "amount", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "sender", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "response_destination", "type": {"kind": "simple", "type": "address", "optional": false}}]}, {"name": "WalletData", "header": null, "fields": [{"name": "balance", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "owner", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "jetton", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "jetton_wallet_code", "type": {"kind": "simple", "type": "cell", "optional": false}}]}, {"name": "CreateUserPurchase", "header": 1098075148, "fields": [{"name": "user", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "amount", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "tokens", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "currency", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 8}}, {"name": "purchase_method", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 8}}, {"name": "nonce", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "round_number", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 32}}, {"name": "usdt_equivalent_amount", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}]}, {"name": "Refund", "header": 836089260, "fields": [{"name": "purchase_id", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 32}}]}, {"name": "ProcessRefund", "header": 929991442, "fields": [{"name": "user", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "amount", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "fee", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "currency", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 8}}, {"name": "round_number", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 32}}, {"name": "usdt_equivalent_amount", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}]}, {"name": "PurchaseRecord", "header": null, "fields": [{"name": "id", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 32}}, {"name": "user", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "amount", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "tokens", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "timestamp", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "currency", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 8}}, {"name": "purchase_method", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 8}}, {"name": "nonce", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "round_number", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 32}}, {"name": "usdt_equivalent_amount", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}]}, {"name": "UserPurchase$Data", "header": null, "fields": [{"name": "owner", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "auction_address", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "user_address", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "total_purchased", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "total_paid", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "purchase_history", "type": {"kind": "dict", "key": "int", "value": "PurchaseRecord", "valueFormat": "ref"}}, {"name": "refund_history", "type": {"kind": "dict", "key": "int", "value": "int"}}, {"name": "purchase_id_counter", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 32}}, {"name": "participated_rounds", "type": {"kind": "dict", "key": "int", "value": "bool"}}]}, {"name": "StartAuction", "header": 1082886929, "fields": [{"name": "start_time", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "end_time", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "soft_cap", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "hard_cap", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "initial_price", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}]}, {"name": "UpdateRound", "header": 2861271945, "fields": [{"name": "new_price", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "round_number", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 32}}]}, {"name": "SetUSDTAddress", "header": 2703444734, "fields": [{"name": "usdt_master", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "usdt_wallet", "type": {"kind": "simple", "type": "address", "optional": false}}]}, {"name": "PurchaseWithSignature", "header": 1524770820, "fields": [{"name": "calculation", "type": {"kind": "simple", "type": "PurchaseCalculation", "optional": false}}, {"name": "signature", "type": {"kind": "simple", "type": "slice", "optional": false}}]}, {"name": "SetSigningKey", "header": 3315975678, "fields": [{"name": "public_key", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 256}}]}, {"name": "SetMinPurchase", "header": 3390874056, "fields": [{"name": "min_purchase", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}]}, {"name": "WithdrawTON", "header": 3520188881, "fields": [{"name": "amount", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "destination", "type": {"kind": "simple", "type": "address", "optional": false}}]}, {"name": "WithdrawUSDT", "header": 3537031890, "fields": [{"name": "amount", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "destination", "type": {"kind": "simple", "type": "address", "optional": false}}]}, {"name": "SetTreasury", "header": 3553874899, "fields": [{"name": "treasury_address", "type": {"kind": "simple", "type": "address", "optional": false}}]}, {"name": "AuctionStarted", "header": 2217068725, "fields": [{"name": "start_time", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "end_time", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "soft_cap", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "hard_cap", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "initial_price", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "total_supply", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}]}, {"name": "PurchaseCompleted", "header": 3975568033, "fields": [{"name": "user", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "amount", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "tokens_received", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "currency", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 8}}, {"name": "purchase_method", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 8}}, {"name": "round_number", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 32}}, {"name": "nonce", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "total_raised", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "total_raised_usdt", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "total_tokens_sold", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}]}, {"name": "USDTConfigured", "header": 1031232428, "fields": [{"name": "usdt_master", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "usdt_wallet", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "configured_by", "type": {"kind": "simple", "type": "address", "optional": false}}]}, {"name": "SigningKeySet", "header": 2441211440, "fields": [{"name": "public_key", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 256}}, {"name": "set_by", "type": {"kind": "simple", "type": "address", "optional": false}}]}, {"name": "MinPurchaseUpdated", "header": 760782792, "fields": [{"name": "old_min_purchase", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "new_min_purchase", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "updated_by", "type": {"kind": "simple", "type": "address", "optional": false}}]}, {"name": "TreasurySet", "header": 4013598566, "fields": [{"name": "old_treasury", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "new_treasury", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "set_by", "type": {"kind": "simple", "type": "address", "optional": false}}]}, {"name": "FundsWithdrawn", "header": 3674912463, "fields": [{"name": "currency", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 8}}, {"name": "amount", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "destination", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "withdrawn_by", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "remaining_balance", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}]}, {"name": "RoundUpdated", "header": 190065307, "fields": [{"name": "old_round", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 32}}, {"name": "new_round", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 32}}, {"name": "old_price", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "new_price", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "updated_by", "type": {"kind": "simple", "type": "address", "optional": false}}]}, {"name": "AuctionEnded", "header": 397814633, "fields": [{"name": "end_reason", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 8}}, {"name": "final_status", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 8}}, {"name": "total_raised", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "total_raised_usdt", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "total_tokens_sold", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "end_time", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}]}, {"name": "AuctionConfig", "header": null, "fields": [{"name": "start_time", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "end_time", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "soft_cap", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "hard_cap", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "total_supply", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "refund_fee_percent", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 8}}]}, {"name": "RoundStats", "header": null, "fields": [{"name": "round_number", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 32}}, {"name": "start_time", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "end_time", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "price", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "total_raised_ton", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "total_raised_usdt", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "raised_usdt_equivalent", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "tokens_sold", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "purchase_count", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 32}}, {"name": "unique_users", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 32}}, {"name": "refund_count", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 32}}, {"name": "refunded_amount_ton", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "refunded_amount_usdt", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "refunded_usdt_equivalent", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}]}, {"name": "USDTConfig", "header": null, "fields": [{"name": "master_address", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "wallet_address", "type": {"kind": "simple", "type": "address", "optional": true}}, {"name": "decimals", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 8}}]}, {"name": "PurchaseCalculation", "header": null, "fields": [{"name": "user", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "amount", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "currency", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 8}}, {"name": "tokens_to_receive", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "current_price", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "current_round", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 32}}, {"name": "timestamp", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "nonce", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "usdt_equivalent_amount", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}]}, {"name": "ParsedPurchaseData", "header": null, "fields": [{"name": "calculation", "type": {"kind": "simple", "type": "PurchaseCalculation", "optional": false}}, {"name": "signature", "type": {"kind": "simple", "type": "slice", "optional": false}}]}, {"name": "OnionAuction$Data", "header": null, "fields": [{"name": "owner", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "stopped", "type": {"kind": "simple", "type": "bool", "optional": false}}, {"name": "auction_config", "type": {"kind": "simple", "type": "AuctionConfig", "optional": false}}, {"name": "current_round", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 32}}, {"name": "current_price", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "total_raised", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "total_tokens_sold", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "auction_status", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 8}}, {"name": "usdt_config", "type": {"kind": "simple", "type": "USDTConfig", "optional": true}}, {"name": "total_raised_usdt", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "total_raised_usdt_equivalent", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "purchase_count", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 32}}, {"name": "round_stats", "type": {"kind": "dict", "key": "int", "value": "RoundStats", "valueFormat": "ref"}}, {"name": "user_round_purchases", "type": {"kind": "dict", "key": "address", "value": "int"}}, {"name": "signing_public_key", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 256}}, {"name": "used_nonces", "type": {"kind": "dict", "key": "int", "value": "bool"}}, {"name": "signature_timeout", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "min_purchase", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "treasury_address", "type": {"kind": "simple", "type": "address", "optional": true}}]}], "receivers": [{"receiver": "internal", "message": {"kind": "text", "text": "Deploy"}}, {"receiver": "internal", "message": {"kind": "typed", "type": "CreateUserPurchase"}}, {"receiver": "internal", "message": {"kind": "typed", "type": "Refund"}}], "getters": [{"name": "total_purchased", "methodId": 97654, "arguments": [], "returnType": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "total_paid", "methodId": 127313, "arguments": [], "returnType": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "purchase_id_counter", "methodId": 90655, "arguments": [], "returnType": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "purchase_details", "methodId": 108146, "arguments": [{"name": "purchase_id", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}], "returnType": {"kind": "simple", "type": "PurchaseRecord", "optional": true}}, {"name": "is_refunded", "methodId": 70580, "arguments": [{"name": "purchase_id", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}], "returnType": {"kind": "simple", "type": "bool", "optional": false}}, {"name": "signature_verified_purchases", "methodId": 90079, "arguments": [], "returnType": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "purchase_method_stats", "methodId": 123538, "arguments": [], "returnType": {"kind": "dict", "key": "int", "value": "int"}}, {"name": "purchases_by_round", "methodId": 98340, "arguments": [{"name": "round_number", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}], "returnType": {"kind": "dict", "key": "int", "value": "PurchaseRecord", "valueFormat": "ref"}}, {"name": "round_total_amount", "methodId": 67459, "arguments": [{"name": "round_number", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}], "returnType": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "round_total_tokens", "methodId": 123888, "arguments": [{"name": "round_number", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}], "returnType": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "round_purchase_count", "methodId": 82786, "arguments": [{"name": "round_number", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}], "returnType": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "participated_rounds", "methodId": 108842, "arguments": [], "returnType": {"kind": "dict", "key": "int", "value": "bool"}}, {"name": "participated_in_round", "methodId": 124279, "arguments": [{"name": "round_number", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}], "returnType": {"kind": "simple", "type": "bool", "optional": false}}, {"name": "round_count", "methodId": 86414, "arguments": [], "returnType": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "all_records", "methodId": 96455, "arguments": [], "returnType": {"kind": "dict", "key": "int", "value": "PurchaseRecord", "valueFormat": "ref"}}, {"name": "owner", "methodId": 83229, "arguments": [], "returnType": {"kind": "simple", "type": "address", "optional": false}}], "errors": {"2": {"message": "Stack underflow"}, "3": {"message": "Stack overflow"}, "4": {"message": "Integer overflow"}, "5": {"message": "Integer out of expected range"}, "6": {"message": "Invalid opcode"}, "7": {"message": "Type check error"}, "8": {"message": "Cell overflow"}, "9": {"message": "Cell underflow"}, "10": {"message": "Dictionary error"}, "11": {"message": "'Unknown' error"}, "12": {"message": "Fatal error"}, "13": {"message": "Out of gas error"}, "14": {"message": "Virtualization error"}, "32": {"message": "Action list is invalid"}, "33": {"message": "Action list is too long"}, "34": {"message": "Action is invalid or not supported"}, "35": {"message": "Invalid source address in outbound message"}, "36": {"message": "Invalid destination address in outbound message"}, "37": {"message": "Not enough Toncoin"}, "38": {"message": "Not enough extra currencies"}, "39": {"message": "Outbound message does not fit into a cell after rewriting"}, "40": {"message": "Cannot process a message"}, "41": {"message": "Library reference is null"}, "42": {"message": "Library change action error"}, "43": {"message": "Exceeded maximum number of cells in the library or the maximum depth of the Merkle tree"}, "50": {"message": "Account state size exceeded limits"}, "128": {"message": "Null reference exception"}, "129": {"message": "Invalid serialization prefix"}, "130": {"message": "Invalid incoming message"}, "131": {"message": "Constraints error"}, "132": {"message": "Access denied"}, "133": {"message": "Contract stopped"}, "134": {"message": "Invalid argument"}, "135": {"message": "Code of a contract was not found"}, "136": {"message": "Invalid standard address"}, "138": {"message": "Not a basechain address"}, "2296": {"message": "JettonWallet: Only Jetton master or Jetton wallet can call this function"}, "13105": {"message": "JettonWallet: Not enough jettons to transfer"}, "22411": {"message": "Already refunded"}, "27831": {"message": "Only owner can call this function"}, "29133": {"message": "JettonWallet: Not allow negative balance after internal transfer"}, "37185": {"message": "Not enough funds to transfer"}, "39144": {"message": "Purchase not found"}, "47048": {"message": "JettonWallet: Only owner can burn tokens"}, "49729": {"message": "Unauthorized"}, "53296": {"message": "Contract not stopped"}, "60354": {"message": "JettonWallet: Not enough balance to burn tokens"}}, "interfaces": ["org.ton.introspection.v0", "org.ton.abi.ipfs.v0", "org.ton.deploy.lazy.v0", "org.ton.ownable"]}