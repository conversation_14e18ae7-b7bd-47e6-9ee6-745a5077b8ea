{"name": "UserPurchase", "code": "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", "abi": "{\"name\":\"UserPurchase\",\"types\":[{\"name\":\"DataSize\",\"header\":null,\"fields\":[{\"name\":\"cells\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}},{\"name\":\"bits\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}},{\"name\":\"refs\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}}]},{\"name\":\"SignedBundle\",\"header\":null,\"fields\":[{\"name\":\"signature\",\"type\":{\"kind\":\"simple\",\"type\":\"fixed-bytes\",\"optional\":false,\"format\":64}},{\"name\":\"signedData\",\"type\":{\"kind\":\"simple\",\"type\":\"slice\",\"optional\":false,\"format\":\"remainder\"}}]},{\"name\":\"StateInit\",\"header\":null,\"fields\":[{\"name\":\"code\",\"type\":{\"kind\":\"simple\",\"type\":\"cell\",\"optional\":false}},{\"name\":\"data\",\"type\":{\"kind\":\"simple\",\"type\":\"cell\",\"optional\":false}}]},{\"name\":\"Context\",\"header\":null,\"fields\":[{\"name\":\"bounceable\",\"type\":{\"kind\":\"simple\",\"type\":\"bool\",\"optional\":false}},{\"name\":\"sender\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"value\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}},{\"name\":\"raw\",\"type\":{\"kind\":\"simple\",\"type\":\"slice\",\"optional\":false}}]},{\"name\":\"SendParameters\",\"header\":null,\"fields\":[{\"name\":\"mode\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}},{\"name\":\"body\",\"type\":{\"kind\":\"simple\",\"type\":\"cell\",\"optional\":true}},{\"name\":\"code\",\"type\":{\"kind\":\"simple\",\"type\":\"cell\",\"optional\":true}},{\"name\":\"data\",\"type\":{\"kind\":\"simple\",\"type\":\"cell\",\"optional\":true}},{\"name\":\"value\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}},{\"name\":\"to\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"bounce\",\"type\":{\"kind\":\"simple\",\"type\":\"bool\",\"optional\":false}}]},{\"name\":\"MessageParameters\",\"header\":null,\"fields\":[{\"name\":\"mode\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}},{\"name\":\"body\",\"type\":{\"kind\":\"simple\",\"type\":\"cell\",\"optional\":true}},{\"name\":\"value\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}},{\"name\":\"to\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"bounce\",\"type\":{\"kind\":\"simple\",\"type\":\"bool\",\"optional\":false}}]},{\"name\":\"DeployParameters\",\"header\":null,\"fields\":[{\"name\":\"mode\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}},{\"name\":\"body\",\"type\":{\"kind\":\"simple\",\"type\":\"cell\",\"optional\":true}},{\"name\":\"value\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}},{\"name\":\"bounce\",\"type\":{\"kind\":\"simple\",\"type\":\"bool\",\"optional\":false}},{\"name\":\"init\",\"type\":{\"kind\":\"simple\",\"type\":\"StateInit\",\"optional\":false}}]},{\"name\":\"StdAddress\",\"header\":null,\"fields\":[{\"name\":\"workchain\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":8}},{\"name\":\"address\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":256}}]},{\"name\":\"VarAddress\",\"header\":null,\"fields\":[{\"name\":\"workchain\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":32}},{\"name\":\"address\",\"type\":{\"kind\":\"simple\",\"type\":\"slice\",\"optional\":false}}]},{\"name\":\"BasechainAddress\",\"header\":null,\"fields\":[{\"name\":\"hash\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":true,\"format\":257}}]},{\"name\":\"ChangeOwner\",\"header\":2174598809,\"fields\":[{\"name\":\"queryId\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":64}},{\"name\":\"newOwner\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}}]},{\"name\":\"ChangeOwnerOk\",\"header\":846932810,\"fields\":[{\"name\":\"queryId\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":64}},{\"name\":\"newOwner\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}}]},{\"name\":\"Deploy\",\"header\":2490013878,\"fields\":[{\"name\":\"queryId\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":64}}]},{\"name\":\"DeployOk\",\"header\":2952335191,\"fields\":[{\"name\":\"queryId\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":64}}]},{\"name\":\"FactoryDeploy\",\"header\":1829761339,\"fields\":[{\"name\":\"queryId\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":64}},{\"name\":\"cashback\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}}]},{\"name\":\"JettonTransfer\",\"header\":260734629,\"fields\":[{\"name\":\"query_id\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":64}},{\"name\":\"amount\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"destination\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"response_destination\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"custom_payload\",\"type\":{\"kind\":\"simple\",\"type\":\"cell\",\"optional\":true}},{\"name\":\"forward_ton_amount\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"forward_payload\",\"type\":{\"kind\":\"simple\",\"type\":\"cell\",\"optional\":true}}]},{\"name\":\"JettonTransferNotification\",\"header\":1935855772,\"fields\":[{\"name\":\"query_id\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":64}},{\"name\":\"amount\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"sender\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"forward_payload\",\"type\":{\"kind\":\"simple\",\"type\":\"cell\",\"optional\":true}}]},{\"name\":\"JettonBurn\",\"header\":1499400124,\"fields\":[{\"name\":\"query_id\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":64}},{\"name\":\"amount\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"response_destination\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"custom_payload\",\"type\":{\"kind\":\"simple\",\"type\":\"cell\",\"optional\":true}}]},{\"name\":\"JettonExcesses\",\"header\":3576854235,\"fields\":[{\"name\":\"query_id\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":64}}]},{\"name\":\"JettonInternalTransfer\",\"header\":395134233,\"fields\":[{\"name\":\"query_id\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":64}},{\"name\":\"amount\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"from\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"response_address\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"forward_ton_amount\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"forward_payload\",\"type\":{\"kind\":\"simple\",\"type\":\"cell\",\"optional\":true}}]},{\"name\":\"JettonBurnNotification\",\"header\":2078119902,\"fields\":[{\"name\":\"query_id\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":64}},{\"name\":\"amount\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"sender\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"response_destination\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}}]},{\"name\":\"WalletData\",\"header\":null,\"fields\":[{\"name\":\"balance\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"owner\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"jetton\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"jetton_wallet_code\",\"type\":{\"kind\":\"simple\",\"type\":\"cell\",\"optional\":false}}]},{\"name\":\"CreateUserPurchase\",\"header\":1098075148,\"fields\":[{\"name\":\"user\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"amount\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"tokens\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"currency\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":8}},{\"name\":\"purchase_method\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":8}},{\"name\":\"nonce\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":64}},{\"name\":\"round_number\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":32}},{\"name\":\"usdt_equivalent_amount\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}}]},{\"name\":\"Refund\",\"header\":836089260,\"fields\":[{\"name\":\"purchase_id\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":32}}]},{\"name\":\"ProcessRefund\",\"header\":929991442,\"fields\":[{\"name\":\"user\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"amount\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"fee\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"currency\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":8}},{\"name\":\"round_number\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":32}},{\"name\":\"usdt_equivalent_amount\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}}]},{\"name\":\"PurchaseRecord\",\"header\":null,\"fields\":[{\"name\":\"id\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":32}},{\"name\":\"user\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"amount\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"tokens\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"timestamp\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":64}},{\"name\":\"currency\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":8}},{\"name\":\"purchase_method\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":8}},{\"name\":\"nonce\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":64}},{\"name\":\"round_number\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":32}},{\"name\":\"usdt_equivalent_amount\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}}]},{\"name\":\"UserPurchase$Data\",\"header\":null,\"fields\":[{\"name\":\"owner\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"auction_address\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"user_address\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"total_purchased\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"total_paid\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"purchase_history\",\"type\":{\"kind\":\"dict\",\"key\":\"int\",\"value\":\"PurchaseRecord\",\"valueFormat\":\"ref\"}},{\"name\":\"refund_history\",\"type\":{\"kind\":\"dict\",\"key\":\"int\",\"value\":\"int\"}},{\"name\":\"purchase_id_counter\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":32}},{\"name\":\"participated_rounds\",\"type\":{\"kind\":\"dict\",\"key\":\"int\",\"value\":\"bool\"}}]},{\"name\":\"StartAuction\",\"header\":1082886929,\"fields\":[{\"name\":\"start_time\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":64}},{\"name\":\"end_time\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":64}},{\"name\":\"soft_cap\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"hard_cap\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"initial_price\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}}]},{\"name\":\"UpdateRound\",\"header\":2861271945,\"fields\":[{\"name\":\"new_price\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"round_number\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":32}}]},{\"name\":\"SetUSDTAddress\",\"header\":2703444734,\"fields\":[{\"name\":\"usdt_master\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"usdt_wallet\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}}]},{\"name\":\"PurchaseWithSignature\",\"header\":1524770820,\"fields\":[{\"name\":\"calculation\",\"type\":{\"kind\":\"simple\",\"type\":\"PurchaseCalculation\",\"optional\":false}},{\"name\":\"signature\",\"type\":{\"kind\":\"simple\",\"type\":\"slice\",\"optional\":false}}]},{\"name\":\"SetSigningKey\",\"header\":3315975678,\"fields\":[{\"name\":\"public_key\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":256}}]},{\"name\":\"SetMinPurchase\",\"header\":3390874056,\"fields\":[{\"name\":\"min_purchase\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}}]},{\"name\":\"WithdrawTON\",\"header\":3520188881,\"fields\":[{\"name\":\"amount\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"destination\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}}]},{\"name\":\"WithdrawUSDT\",\"header\":3537031890,\"fields\":[{\"name\":\"amount\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"destination\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}}]},{\"name\":\"SetTreasury\",\"header\":3553874899,\"fields\":[{\"name\":\"treasury_address\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}}]},{\"name\":\"AuctionStarted\",\"header\":2217068725,\"fields\":[{\"name\":\"start_time\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":64}},{\"name\":\"end_time\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":64}},{\"name\":\"soft_cap\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"hard_cap\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"initial_price\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"total_supply\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}}]},{\"name\":\"PurchaseCompleted\",\"header\":3975568033,\"fields\":[{\"name\":\"user\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"amount\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"tokens_received\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"currency\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":8}},{\"name\":\"purchase_method\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":8}},{\"name\":\"round_number\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":32}},{\"name\":\"nonce\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":64}},{\"name\":\"total_raised\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"total_raised_usdt\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"total_tokens_sold\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}}]},{\"name\":\"USDTConfigured\",\"header\":1031232428,\"fields\":[{\"name\":\"usdt_master\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"usdt_wallet\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"configured_by\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}}]},{\"name\":\"SigningKeySet\",\"header\":2441211440,\"fields\":[{\"name\":\"public_key\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":256}},{\"name\":\"set_by\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}}]},{\"name\":\"MinPurchaseUpdated\",\"header\":760782792,\"fields\":[{\"name\":\"old_min_purchase\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"new_min_purchase\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"updated_by\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}}]},{\"name\":\"TreasurySet\",\"header\":4013598566,\"fields\":[{\"name\":\"old_treasury\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"new_treasury\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"set_by\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}}]},{\"name\":\"FundsWithdrawn\",\"header\":3674912463,\"fields\":[{\"name\":\"currency\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":8}},{\"name\":\"amount\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"destination\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"withdrawn_by\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"remaining_balance\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}}]},{\"name\":\"RoundUpdated\",\"header\":190065307,\"fields\":[{\"name\":\"old_round\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":32}},{\"name\":\"new_round\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":32}},{\"name\":\"old_price\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"new_price\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"updated_by\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}}]},{\"name\":\"AuctionEnded\",\"header\":397814633,\"fields\":[{\"name\":\"end_reason\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":8}},{\"name\":\"final_status\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":8}},{\"name\":\"total_raised\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"total_raised_usdt\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"total_tokens_sold\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"end_time\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":64}}]},{\"name\":\"AuctionConfig\",\"header\":null,\"fields\":[{\"name\":\"start_time\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":64}},{\"name\":\"end_time\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":64}},{\"name\":\"soft_cap\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"hard_cap\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"total_supply\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"refund_fee_percent\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":8}}]},{\"name\":\"RoundStats\",\"header\":null,\"fields\":[{\"name\":\"round_number\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":32}},{\"name\":\"start_time\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":64}},{\"name\":\"end_time\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":64}},{\"name\":\"price\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"total_raised_ton\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"total_raised_usdt\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"raised_usdt_equivalent\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"tokens_sold\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"purchase_count\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":32}},{\"name\":\"unique_users\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":32}},{\"name\":\"refund_count\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":32}},{\"name\":\"refunded_amount_ton\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"refunded_amount_usdt\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"refunded_usdt_equivalent\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}}]},{\"name\":\"USDTConfig\",\"header\":null,\"fields\":[{\"name\":\"master_address\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"wallet_address\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":true}},{\"name\":\"decimals\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":8}}]},{\"name\":\"PurchaseCalculation\",\"header\":null,\"fields\":[{\"name\":\"user\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"amount\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"currency\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":8}},{\"name\":\"tokens_to_receive\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"current_price\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"current_round\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":32}},{\"name\":\"timestamp\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":64}},{\"name\":\"nonce\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":64}},{\"name\":\"usdt_equivalent_amount\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}}]},{\"name\":\"ParsedPurchaseData\",\"header\":null,\"fields\":[{\"name\":\"calculation\",\"type\":{\"kind\":\"simple\",\"type\":\"PurchaseCalculation\",\"optional\":false}},{\"name\":\"signature\",\"type\":{\"kind\":\"simple\",\"type\":\"slice\",\"optional\":false}}]},{\"name\":\"OnionAuction$Data\",\"header\":null,\"fields\":[{\"name\":\"owner\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"stopped\",\"type\":{\"kind\":\"simple\",\"type\":\"bool\",\"optional\":false}},{\"name\":\"auction_config\",\"type\":{\"kind\":\"simple\",\"type\":\"AuctionConfig\",\"optional\":false}},{\"name\":\"current_round\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":32}},{\"name\":\"current_price\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"total_raised\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"total_tokens_sold\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"auction_status\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":8}},{\"name\":\"usdt_config\",\"type\":{\"kind\":\"simple\",\"type\":\"USDTConfig\",\"optional\":true}},{\"name\":\"total_raised_usdt\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"total_raised_usdt_equivalent\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"purchase_count\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":32}},{\"name\":\"round_stats\",\"type\":{\"kind\":\"dict\",\"key\":\"int\",\"value\":\"RoundStats\",\"valueFormat\":\"ref\"}},{\"name\":\"user_round_purchases\",\"type\":{\"kind\":\"dict\",\"key\":\"address\",\"value\":\"int\"}},{\"name\":\"signing_public_key\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":256}},{\"name\":\"used_nonces\",\"type\":{\"kind\":\"dict\",\"key\":\"int\",\"value\":\"bool\"}},{\"name\":\"signature_timeout\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":64}},{\"name\":\"min_purchase\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"treasury_address\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":true}}]}],\"receivers\":[{\"receiver\":\"internal\",\"message\":{\"kind\":\"text\",\"text\":\"Deploy\"}},{\"receiver\":\"internal\",\"message\":{\"kind\":\"typed\",\"type\":\"CreateUserPurchase\"}},{\"receiver\":\"internal\",\"message\":{\"kind\":\"typed\",\"type\":\"Refund\"}}],\"getters\":[{\"name\":\"total_purchased\",\"methodId\":97654,\"arguments\":[],\"returnType\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}},{\"name\":\"total_paid\",\"methodId\":127313,\"arguments\":[],\"returnType\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}},{\"name\":\"purchase_id_counter\",\"methodId\":90655,\"arguments\":[],\"returnType\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}},{\"name\":\"purchase_details\",\"methodId\":108146,\"arguments\":[{\"name\":\"purchase_id\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}}],\"returnType\":{\"kind\":\"simple\",\"type\":\"PurchaseRecord\",\"optional\":true}},{\"name\":\"is_refunded\",\"methodId\":70580,\"arguments\":[{\"name\":\"purchase_id\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}}],\"returnType\":{\"kind\":\"simple\",\"type\":\"bool\",\"optional\":false}},{\"name\":\"signature_verified_purchases\",\"methodId\":90079,\"arguments\":[],\"returnType\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}},{\"name\":\"purchase_method_stats\",\"methodId\":123538,\"arguments\":[],\"returnType\":{\"kind\":\"dict\",\"key\":\"int\",\"value\":\"int\"}},{\"name\":\"purchases_by_round\",\"methodId\":98340,\"arguments\":[{\"name\":\"round_number\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}}],\"returnType\":{\"kind\":\"dict\",\"key\":\"int\",\"value\":\"PurchaseRecord\",\"valueFormat\":\"ref\"}},{\"name\":\"round_total_amount\",\"methodId\":67459,\"arguments\":[{\"name\":\"round_number\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}}],\"returnType\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}},{\"name\":\"round_total_tokens\",\"methodId\":123888,\"arguments\":[{\"name\":\"round_number\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}}],\"returnType\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}},{\"name\":\"round_purchase_count\",\"methodId\":82786,\"arguments\":[{\"name\":\"round_number\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}}],\"returnType\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}},{\"name\":\"participated_rounds\",\"methodId\":108842,\"arguments\":[],\"returnType\":{\"kind\":\"dict\",\"key\":\"int\",\"value\":\"bool\"}},{\"name\":\"participated_in_round\",\"methodId\":124279,\"arguments\":[{\"name\":\"round_number\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}}],\"returnType\":{\"kind\":\"simple\",\"type\":\"bool\",\"optional\":false}},{\"name\":\"round_count\",\"methodId\":86414,\"arguments\":[],\"returnType\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}},{\"name\":\"all_records\",\"methodId\":96455,\"arguments\":[],\"returnType\":{\"kind\":\"dict\",\"key\":\"int\",\"value\":\"PurchaseRecord\",\"valueFormat\":\"ref\"}},{\"name\":\"owner\",\"methodId\":83229,\"arguments\":[],\"returnType\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}}],\"errors\":{\"2\":{\"message\":\"Stack underflow\"},\"3\":{\"message\":\"Stack overflow\"},\"4\":{\"message\":\"Integer overflow\"},\"5\":{\"message\":\"Integer out of expected range\"},\"6\":{\"message\":\"Invalid opcode\"},\"7\":{\"message\":\"Type check error\"},\"8\":{\"message\":\"Cell overflow\"},\"9\":{\"message\":\"Cell underflow\"},\"10\":{\"message\":\"Dictionary error\"},\"11\":{\"message\":\"'Unknown' error\"},\"12\":{\"message\":\"Fatal error\"},\"13\":{\"message\":\"Out of gas error\"},\"14\":{\"message\":\"Virtualization error\"},\"32\":{\"message\":\"Action list is invalid\"},\"33\":{\"message\":\"Action list is too long\"},\"34\":{\"message\":\"Action is invalid or not supported\"},\"35\":{\"message\":\"Invalid source address in outbound message\"},\"36\":{\"message\":\"Invalid destination address in outbound message\"},\"37\":{\"message\":\"Not enough Toncoin\"},\"38\":{\"message\":\"Not enough extra currencies\"},\"39\":{\"message\":\"Outbound message does not fit into a cell after rewriting\"},\"40\":{\"message\":\"Cannot process a message\"},\"41\":{\"message\":\"Library reference is null\"},\"42\":{\"message\":\"Library change action error\"},\"43\":{\"message\":\"Exceeded maximum number of cells in the library or the maximum depth of the Merkle tree\"},\"50\":{\"message\":\"Account state size exceeded limits\"},\"128\":{\"message\":\"Null reference exception\"},\"129\":{\"message\":\"Invalid serialization prefix\"},\"130\":{\"message\":\"Invalid incoming message\"},\"131\":{\"message\":\"Constraints error\"},\"132\":{\"message\":\"Access denied\"},\"133\":{\"message\":\"Contract stopped\"},\"134\":{\"message\":\"Invalid argument\"},\"135\":{\"message\":\"Code of a contract was not found\"},\"136\":{\"message\":\"Invalid standard address\"},\"138\":{\"message\":\"Not a basechain address\"},\"2296\":{\"message\":\"JettonWallet: Only Jetton master or Jetton wallet can call this function\"},\"13105\":{\"message\":\"JettonWallet: Not enough jettons to transfer\"},\"22411\":{\"message\":\"Already refunded\"},\"27831\":{\"message\":\"Only owner can call this function\"},\"29133\":{\"message\":\"JettonWallet: Not allow negative balance after internal transfer\"},\"37185\":{\"message\":\"Not enough funds to transfer\"},\"39144\":{\"message\":\"Purchase not found\"},\"47048\":{\"message\":\"JettonWallet: Only owner can burn tokens\"},\"49729\":{\"message\":\"Unauthorized\"},\"53296\":{\"message\":\"Contract not stopped\"},\"60354\":{\"message\":\"JettonWallet: Not enough balance to burn tokens\"}},\"interfaces\":[\"org.ton.introspection.v0\",\"org.ton.abi.ipfs.v0\",\"org.ton.deploy.lazy.v0\",\"org.ton.ownable\"]}", "init": {"kind": "direct", "args": [{"name": "auction_address", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "user_address", "type": {"kind": "simple", "type": "address", "optional": false}}], "prefix": {"bits": 1, "value": 0}, "deployment": {"kind": "system-cell", "system": null}}, "sources": {"contracts/jetton/JettonWallet.tact": "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", "contracts/user_purchase.tact": "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", "contracts/onion_auction.tact": "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"}, "compiler": {"name": "tact", "version": "1.6.13", "parameters": "{\"entrypoint\":\"contracts/onion_auction.tact\",\"options\":{\"debug\":false,\"external\":false}}"}}