#pragma version =0.4.6;
#pragma allow-post-modification;
#pragma compute-asm-ltr;

;; UserPurchase_UserPurchase.headers.fc
;;
;; Header files for UserPurchase
;; NOTE: declarations are sorted for optimal order
;;

;; __tact_not_null
forall X -> X __tact_not_null(X x) impure inline;

;; __tact_context_get_sender
slice __tact_context_get_sender() inline;

;; __tact_dict_get_int_int
int __tact_dict_get_int_int(cell d, int kl, int k, int vl) inline;

;; __tact_dict_set_int_int
(cell, ()) __tact_dict_set_int_int(cell d, int kl, int k, int v, int vl) inline;

;; __tact_dict_get_int_cell
cell __tact_dict_get_int_cell(cell d, int kl, int k) inline;

;; __tact_dict_set_int_cell
(cell, ()) __tact_dict_set_int_cell(cell d, int kl, int k, cell v) inline;

;; $ProcessRefund$_store
builder $ProcessRefund$_store(builder build_0, (slice, int, int, int, int, int) v) inline;

;; $ProcessRefund$_store_cell
cell $ProcessRefund$_store_cell((slice, int, int, int, int, int) v, builder b) inline;

;; $PurchaseRecord$_store
builder $PurchaseRecord$_store(builder build_0, (int, slice, int, int, int, int, int, int, int, int) v) inline;

;; $PurchaseRecord$_store_cell
cell $PurchaseRecord$_store_cell((int, slice, int, int, int, int, int, int, int, int) v, builder b) inline;

;; $PurchaseRecord$_load
(slice, ((int, slice, int, int, int, int, int, int, int, int))) $PurchaseRecord$_load(slice sc_0) inline;

;; $PurchaseRecord$_as_optional
tuple $PurchaseRecord$_as_optional((int, slice, int, int, int, int, int, int, int, int) v) inline;

;; $PurchaseRecord$_load_opt
tuple $PurchaseRecord$_load_opt(cell cl) inline;

;; $UserPurchase$_store
builder $UserPurchase$_store(builder build_0, (slice, slice, slice, int, int, cell, cell, int, cell) v) inline;

;; $UserPurchase$_load
(slice, ((slice, slice, slice, int, int, cell, cell, int, cell))) $UserPurchase$_load(slice sc_0) inline;

;; $PurchaseRecord$_not_null
((int, slice, int, int, int, int, int, int, int, int)) $PurchaseRecord$_not_null(tuple v) inline;

;; $PurchaseRecord$_to_tuple
tuple $PurchaseRecord$_to_tuple(((int, slice, int, int, int, int, int, int, int, int)) v) inline;

;; $PurchaseRecord$_to_opt_tuple
tuple $PurchaseRecord$_to_opt_tuple(tuple v) inline;

;; $PurchaseRecord$_to_opt_external
tuple $PurchaseRecord$_to_opt_external(tuple v) inline;

;; $UserPurchase$init$_load
(slice, ((slice, slice))) $UserPurchase$init$_load(slice sc_0) inline;

;; $UserPurchase$_contract_init
(slice, slice, slice, int, int, cell, cell, int, cell) $UserPurchase$_contract_init(slice $auction_address, slice $user_address) impure inline;

;; $UserPurchase$_contract_load
(slice, slice, slice, int, int, cell, cell, int, cell) $UserPurchase$_contract_load() impure inline;

;; $UserPurchase$_contract_store
() $UserPurchase$_contract_store((slice, slice, slice, int, int, cell, cell, int, cell) v) impure inline;

;; $UserPurchase$_fun_total_purchased
((slice, slice, slice, int, int, cell, cell, int, cell), int) $UserPurchase$_fun_total_purchased((slice, slice, slice, int, int, cell, cell, int, cell) $self) impure inline_ref;

;; $UserPurchase$_fun_total_paid
((slice, slice, slice, int, int, cell, cell, int, cell), int) $UserPurchase$_fun_total_paid((slice, slice, slice, int, int, cell, cell, int, cell) $self) impure inline_ref;

;; $UserPurchase$_fun_purchase_id_counter
((slice, slice, slice, int, int, cell, cell, int, cell), int) $UserPurchase$_fun_purchase_id_counter((slice, slice, slice, int, int, cell, cell, int, cell) $self) impure inline_ref;

;; $UserPurchase$_fun_purchase_details
((slice, slice, slice, int, int, cell, cell, int, cell), tuple) $UserPurchase$_fun_purchase_details((slice, slice, slice, int, int, cell, cell, int, cell) $self, int $purchase_id) impure inline_ref;

;; $UserPurchase$_fun_is_refunded
((slice, slice, slice, int, int, cell, cell, int, cell), int) $UserPurchase$_fun_is_refunded((slice, slice, slice, int, int, cell, cell, int, cell) $self, int $purchase_id) impure inline_ref;

;; $UserPurchase$_fun_signature_verified_purchases
((slice, slice, slice, int, int, cell, cell, int, cell), int) $UserPurchase$_fun_signature_verified_purchases((slice, slice, slice, int, int, cell, cell, int, cell) $self) impure inline_ref;

;; $UserPurchase$_fun_purchase_method_stats
((slice, slice, slice, int, int, cell, cell, int, cell), cell) $UserPurchase$_fun_purchase_method_stats((slice, slice, slice, int, int, cell, cell, int, cell) $self) impure inline_ref;

;; $UserPurchase$_fun_purchases_by_round
((slice, slice, slice, int, int, cell, cell, int, cell), cell) $UserPurchase$_fun_purchases_by_round((slice, slice, slice, int, int, cell, cell, int, cell) $self, int $round_number) impure inline_ref;

;; $UserPurchase$_fun_round_total_amount
((slice, slice, slice, int, int, cell, cell, int, cell), int) $UserPurchase$_fun_round_total_amount((slice, slice, slice, int, int, cell, cell, int, cell) $self, int $round_number) impure inline_ref;

;; $UserPurchase$_fun_round_total_tokens
((slice, slice, slice, int, int, cell, cell, int, cell), int) $UserPurchase$_fun_round_total_tokens((slice, slice, slice, int, int, cell, cell, int, cell) $self, int $round_number) impure inline_ref;

;; $UserPurchase$_fun_round_purchase_count
((slice, slice, slice, int, int, cell, cell, int, cell), int) $UserPurchase$_fun_round_purchase_count((slice, slice, slice, int, int, cell, cell, int, cell) $self, int $round_number) impure inline_ref;

;; $UserPurchase$_fun_participated_rounds
((slice, slice, slice, int, int, cell, cell, int, cell), cell) $UserPurchase$_fun_participated_rounds((slice, slice, slice, int, int, cell, cell, int, cell) $self) impure inline_ref;

;; $UserPurchase$_fun_participated_in_round
((slice, slice, slice, int, int, cell, cell, int, cell), int) $UserPurchase$_fun_participated_in_round((slice, slice, slice, int, int, cell, cell, int, cell) $self, int $round_number) impure inline_ref;

;; $UserPurchase$_fun_round_count
((slice, slice, slice, int, int, cell, cell, int, cell), int) $UserPurchase$_fun_round_count((slice, slice, slice, int, int, cell, cell, int, cell) $self) impure inline_ref;

;; $UserPurchase$_fun_all_records
((slice, slice, slice, int, int, cell, cell, int, cell), cell) $UserPurchase$_fun_all_records((slice, slice, slice, int, int, cell, cell, int, cell) $self) impure inline_ref;

;; $UserPurchase$_fun_owner
((slice, slice, slice, int, int, cell, cell, int, cell), slice) $UserPurchase$_fun_owner((slice, slice, slice, int, int, cell, cell, int, cell) $self) impure inline_ref;

;; $PurchaseRecord$_constructor_id_user_amount_tokens_timestamp_currency_purchase_method_nonce_round_number_usdt_equivalent_amount
((int, slice, int, int, int, int, int, int, int, int)) $PurchaseRecord$_constructor_id_user_amount_tokens_timestamp_currency_purchase_method_nonce_round_number_usdt_equivalent_amount(int $id, slice $user, int $amount, int $tokens, int $timestamp, int $currency, int $purchase_method, int $nonce, int $round_number, int $usdt_equivalent_amount) inline;

;; $SendParameters$_constructor_to_value_mode_bounce_body
((int, cell, cell, cell, int, slice, int)) $SendParameters$_constructor_to_value_mode_bounce_body(slice $to, int $value, int $mode, int $bounce, cell $body) inline;

;; $ProcessRefund$_constructor_user_amount_fee_currency_round_number_usdt_equivalent_amount
((slice, int, int, int, int, int)) $ProcessRefund$_constructor_user_amount_fee_currency_round_number_usdt_equivalent_amount(slice $user, int $amount, int $fee, int $currency, int $round_number, int $usdt_equivalent_amount) inline;


;; UserPurchase_UserPurchase.stdlib.fc
global (int, slice, int, slice) __tact_context;
global slice __tact_context_sender;
global cell __tact_child_contract_codes;
global int __tact_randomized;

forall X -> X __tact_not_null(X x) impure inline {
    throw_if(128, null?(x)); return x;
}

slice __tact_context_get_sender() inline {
    return __tact_context_sender;
}

forall X0, X1, X2, X3, X4, X5, X6, X7, X8, X9 -> tuple __tact_tuple_create_10((X0, X1, X2, X3, X4, X5, X6, X7, X8, X9) v) asm """
    10 TUPLE
""";

forall X0, X1, X2, X3, X4, X5, X6, X7, X8, X9 -> (X0, X1, X2, X3, X4, X5, X6, X7, X8, X9) __tact_tuple_destroy_10(tuple v) asm """
    10 UNTUPLE
""";

int __tact_dict_get_int_int(cell d, int kl, int k, int vl) inline {
    var (r, ok) = idict_get?(d, kl, k);
    if (ok) {
        return r~load_int(vl);
    } else {
        return null();
    }
}

(cell, ()) __tact_dict_set_int_int(cell d, int kl, int k, int v, int vl) inline {
    if (null?(v)) {
        var (r, ok) = idict_delete?(d, kl, k);
        return (r, ());
    } else {
        return (idict_set_builder(d, kl, k, begin_cell().store_int(v, vl)), ());
    }
}

cell __tact_dict_get_int_cell(cell d, int kl, int k) inline {
    var (r, ok) = idict_get_ref?(d, kl, k);
    if (ok) {
        return r;
    } else {
        return null();
    }
}

(cell, ()) __tact_dict_set_int_cell(cell d, int kl, int k, cell v) inline {
    if (null?(v)) {
        var (r, ok) = idict_delete?(d, kl, k);
        return (r, ());
    } else {
        return (idict_set_ref(d, kl, k, v), ());
    }
}

() $global_send((int, cell, cell, cell, int, slice, int) $params) impure asm """
    NEWC
    b{01} STSLICECONST  // store tag = $0 and ihr_disabled = true
    1 STI               // store `bounce`
    b{000} STSLICECONST // store bounced = false and src = addr_none
    STSLICE             // store `to`
    SWAP
    STGRAMS             // store `value`
    105 PUSHINT         // 1 + 4 + 4 + 64 + 32
    STZEROES            // store currency_collection, ihr_fee, fwd_fee, created_lt and created_at
    // → Stack state
    // s0: Builder
    // s1: `data`
    // s2: `code`
    // s3: `body`
    // s4: `mode`

    // Group 2: Placing the Builder after code and data, then checking those for nullability
    s2 XCHG0
    DUP2
    ISNULL
    SWAP
    ISNULL
    AND
    // → Stack state
    // s0: -1 (true) if `data` and `code` are both null, 0 (false) otherwise
    // s1: `code`
    // s2: `data`
    // s3: Builder
    // s4: `body`
    // s5: `mode`

    // Group 3: Left branch of the IFELSE, executed if s0 is -1 (true)
    <{
        DROP2 // drop `data` and `code`, since either of those is null
        b{0} STSLICECONST
    }> PUSHCONT

    // Group 3: Right branch of the IFELSE, executed if s0 is 0 (false)
    <{
        // _ split_depth:(Maybe (## 5))
        //   special:(Maybe TickTock)
        //   code:(Maybe ^Cell)
        //   data:(Maybe ^Cell)
        //   library:(Maybe ^Cell)
        // = StateInit;
        ROT                // place message Builder on top
        b{10} STSLICECONST // store Maybe = true, Either = false
        // Start composing inlined StateInit
        b{00} STSLICECONST // store split_depth and special first
        STDICT             // store code
        STDICT             // store data
        b{0} STSLICECONST  // store library
    }> PUSHCONT

    // Group 3: IFELSE that does the branching shown above
    IFELSE
    // → Stack state
    // s0: Builder
    // s1: null or StateInit
    // s2: `body`
    // s3: `mode`

    // Group 4: Finalizing the message
    STDICT // store `body` as ref with an extra Maybe bit, since `body` might be null
    ENDC
    // → Stack state
    // s0: Cell
    // s1: `mode`

    // Group 5: Sending the message, with `mode` on top
    SWAP
    SENDRAWMSG // https://github.com/tact-lang/tact/issues/1558
""";

() $global_cashback(slice $to) impure asm """
    NEWC
    x{42_} STSLICECONST // .storeUint(0x10, 6)
    STSLICE          // .storeAddress(to)
    0 PUSHINT        // 0
    111 STUR         // .storeUint(0, 111)
                     // 4 zeros for coins and 107 zeros for lt, fees, etc.
    ENDC
    66 PUSHINT       // SendRemainingValue | SendIgnoreErrors
    SENDRAWMSG
""";

int $global_now() impure asm """
    NOW
""";

((slice, slice, slice, int, int, cell, cell, int, cell), slice) $UserPurchase$_fun_owner((slice, slice, slice, int, int, cell, cell, int, cell) $self) impure inline_ref {
    var (($self'owner, $self'auction_address, $self'user_address, $self'total_purchased, $self'total_paid, $self'purchase_history, $self'refund_history, $self'purchase_id_counter, $self'participated_rounds)) = $self;
    var $fresh$ret_15 = $self'owner;
    return (($self'owner, $self'auction_address, $self'user_address, $self'total_purchased, $self'total_paid, $self'purchase_history, $self'refund_history, $self'purchase_id_counter, $self'participated_rounds), $fresh$ret_15);
}

;; UserPurchase_UserPurchase.storage.fc
;;
;; Type: SendParameters
;; TLB: _ mode:int257 body:Maybe ^cell code:Maybe ^cell data:Maybe ^cell value:int257 to:address bounce:bool = SendParameters
;;

((int, cell, cell, cell, int, slice, int)) $SendParameters$_constructor_to_value_mode_bounce_body(slice $to, int $value, int $mode, int $bounce, cell $body) inline {
    return ($mode, $body, null(), null(), $value, $to, $bounce);
}

;;
;; Type: ProcessRefund
;; Header: 0x376e8b12
;; TLB: process_refund#376e8b12 user:address amount:coins fee:coins currency:uint8 round_number:uint32 usdt_equivalent_amount:coins = ProcessRefund
;;

builder $ProcessRefund$_store(builder build_0, (slice, int, int, int, int, int) v) inline {
    var (v'user, v'amount, v'fee, v'currency, v'round_number, v'usdt_equivalent_amount) = v;
    build_0 = store_uint(build_0, 929991442, 32);
    build_0 = build_0.store_slice(v'user);
    build_0 = build_0.store_varuint16(v'amount);
    build_0 = build_0.store_varuint16(v'fee);
    build_0 = build_0.store_uint(v'currency, 8);
    build_0 = build_0.store_uint(v'round_number, 32);
    build_0 = build_0.store_varuint16(v'usdt_equivalent_amount);
    return build_0;
}

cell $ProcessRefund$_store_cell((slice, int, int, int, int, int) v, builder b) inline {
    return $ProcessRefund$_store(b, v).end_cell();
}

((slice, int, int, int, int, int)) $ProcessRefund$_constructor_user_amount_fee_currency_round_number_usdt_equivalent_amount(slice $user, int $amount, int $fee, int $currency, int $round_number, int $usdt_equivalent_amount) inline {
    return ($user, $amount, $fee, $currency, $round_number, $usdt_equivalent_amount);
}

;;
;; Type: PurchaseRecord
;; TLB: _ id:uint32 user:address amount:coins tokens:coins timestamp:uint64 currency:uint8 purchase_method:uint8 nonce:uint64 round_number:uint32 usdt_equivalent_amount:coins = PurchaseRecord
;;

builder $PurchaseRecord$_store(builder build_0, (int, slice, int, int, int, int, int, int, int, int) v) inline {
    var (v'id, v'user, v'amount, v'tokens, v'timestamp, v'currency, v'purchase_method, v'nonce, v'round_number, v'usdt_equivalent_amount) = v;
    build_0 = build_0.store_uint(v'id, 32);
    build_0 = build_0.store_slice(v'user);
    build_0 = build_0.store_varuint16(v'amount);
    build_0 = build_0.store_varuint16(v'tokens);
    build_0 = build_0.store_uint(v'timestamp, 64);
    build_0 = build_0.store_uint(v'currency, 8);
    build_0 = build_0.store_uint(v'purchase_method, 8);
    build_0 = build_0.store_uint(v'nonce, 64);
    build_0 = build_0.store_uint(v'round_number, 32);
    build_0 = build_0.store_varuint16(v'usdt_equivalent_amount);
    return build_0;
}

cell $PurchaseRecord$_store_cell((int, slice, int, int, int, int, int, int, int, int) v, builder b) inline {
    return $PurchaseRecord$_store(b, v).end_cell();
}

(slice, ((int, slice, int, int, int, int, int, int, int, int))) $PurchaseRecord$_load(slice sc_0) inline {
    var v'id = sc_0~load_uint(32);
    var v'user = sc_0~load_msg_addr();
    var v'amount = sc_0~load_varuint16();
    var v'tokens = sc_0~load_varuint16();
    var v'timestamp = sc_0~load_uint(64);
    var v'currency = sc_0~load_uint(8);
    var v'purchase_method = sc_0~load_uint(8);
    var v'nonce = sc_0~load_uint(64);
    var v'round_number = sc_0~load_uint(32);
    var v'usdt_equivalent_amount = sc_0~load_varuint16();
    return (sc_0, (v'id, v'user, v'amount, v'tokens, v'timestamp, v'currency, v'purchase_method, v'nonce, v'round_number, v'usdt_equivalent_amount));
}

tuple $PurchaseRecord$_as_optional((int, slice, int, int, int, int, int, int, int, int) v) inline {
    var (v'id, v'user, v'amount, v'tokens, v'timestamp, v'currency, v'purchase_method, v'nonce, v'round_number, v'usdt_equivalent_amount) = v;
    return __tact_tuple_create_10(v'id, v'user, v'amount, v'tokens, v'timestamp, v'currency, v'purchase_method, v'nonce, v'round_number, v'usdt_equivalent_amount);
}

tuple $PurchaseRecord$_load_opt(cell cl) inline {
    if (null?(cl)) {
        return null();
    }
    var sc = cl.begin_parse();
    return $PurchaseRecord$_as_optional(sc~$PurchaseRecord$_load());
}

((int, slice, int, int, int, int, int, int, int, int)) $PurchaseRecord$_not_null(tuple v) inline {
    throw_if(128, null?(v));
    var (int vvv'id, slice vvv'user, int vvv'amount, int vvv'tokens, int vvv'timestamp, int vvv'currency, int vvv'purchase_method, int vvv'nonce, int vvv'round_number, int vvv'usdt_equivalent_amount) = __tact_tuple_destroy_10(v);
    return (vvv'id, vvv'user, vvv'amount, vvv'tokens, vvv'timestamp, vvv'currency, vvv'purchase_method, vvv'nonce, vvv'round_number, vvv'usdt_equivalent_amount);
}

tuple $PurchaseRecord$_to_tuple(((int, slice, int, int, int, int, int, int, int, int)) v) inline {
    var (v'id, v'user, v'amount, v'tokens, v'timestamp, v'currency, v'purchase_method, v'nonce, v'round_number, v'usdt_equivalent_amount) = v;
    return __tact_tuple_create_10(v'id, v'user, v'amount, v'tokens, v'timestamp, v'currency, v'purchase_method, v'nonce, v'round_number, v'usdt_equivalent_amount);
}

tuple $PurchaseRecord$_to_opt_tuple(tuple v) inline {
    if (null?(v)) { return null(); } 
    return $PurchaseRecord$_to_tuple($PurchaseRecord$_not_null(v)); 
}

tuple $PurchaseRecord$_to_opt_external(tuple v) inline {
    var loaded = $PurchaseRecord$_to_opt_tuple(v);
    if (null?(loaded)) {
        return null();
    } else {
        return (loaded);
    }
}

((int, slice, int, int, int, int, int, int, int, int)) $PurchaseRecord$_constructor_id_user_amount_tokens_timestamp_currency_purchase_method_nonce_round_number_usdt_equivalent_amount(int $id, slice $user, int $amount, int $tokens, int $timestamp, int $currency, int $purchase_method, int $nonce, int $round_number, int $usdt_equivalent_amount) inline {
    return ($id, $user, $amount, $tokens, $timestamp, $currency, $purchase_method, $nonce, $round_number, $usdt_equivalent_amount);
}

;;
;; Type: UserPurchase
;; TLB: _ owner:address auction_address:address user_address:address total_purchased:coins total_paid:coins purchase_history:dict<int, ^PurchaseRecord{id:uint32,user:address,amount:coins,tokens:coins,timestamp:uint64,currency:uint8,purchase_method:uint8,nonce:uint64,round_number:uint32,usdt_equivalent_amount:coins}> refund_history:dict<int, int> purchase_id_counter:uint32 participated_rounds:dict<int, bool> = UserPurchase
;;

builder $UserPurchase$_store(builder build_0, (slice, slice, slice, int, int, cell, cell, int, cell) v) inline {
    var (v'owner, v'auction_address, v'user_address, v'total_purchased, v'total_paid, v'purchase_history, v'refund_history, v'purchase_id_counter, v'participated_rounds) = v;
    build_0 = build_0.store_slice(v'owner);
    build_0 = build_0.store_slice(v'auction_address);
    build_0 = build_0.store_slice(v'user_address);
    build_0 = build_0.store_varuint16(v'total_purchased);
    var build_1 = begin_cell();
    build_1 = build_1.store_varuint16(v'total_paid);
    build_1 = build_1.store_dict(v'purchase_history);
    build_1 = build_1.store_dict(v'refund_history);
    build_1 = build_1.store_uint(v'purchase_id_counter, 32);
    build_1 = build_1.store_dict(v'participated_rounds);
    build_0 = store_builder_ref(build_0, build_1);
    return build_0;
}

(slice, ((slice, slice, slice, int, int, cell, cell, int, cell))) $UserPurchase$_load(slice sc_0) inline {
    var v'owner = sc_0~load_msg_addr();
    var v'auction_address = sc_0~load_msg_addr();
    var v'user_address = sc_0~load_msg_addr();
    var v'total_purchased = sc_0~load_varuint16();
    slice sc_1 = sc_0~load_ref().begin_parse();
    var v'total_paid = sc_1~load_varuint16();
    var v'purchase_history = sc_1~load_dict();
    var v'refund_history = sc_1~load_dict();
    var v'purchase_id_counter = sc_1~load_uint(32);
    var v'participated_rounds = sc_1~load_dict();
    return (sc_0, (v'owner, v'auction_address, v'user_address, v'total_purchased, v'total_paid, v'purchase_history, v'refund_history, v'purchase_id_counter, v'participated_rounds));
}

(slice, ((slice, slice))) $UserPurchase$init$_load(slice sc_0) inline {
    var v'auction_address = sc_0~load_msg_addr();
    var v'user_address = sc_0~load_msg_addr();
    return (sc_0, (v'auction_address, v'user_address));
}

(slice, slice, slice, int, int, cell, cell, int, cell) $UserPurchase$_contract_load() impure inline {
    slice $sc = get_data().begin_parse();
    int $loaded = $sc~load_int(1);
    if ($loaded) {
        return $sc~$UserPurchase$_load();
    }
    else {
        (slice $auction_address, slice $user_address) = $sc~$UserPurchase$init$_load();
        $sc.end_parse();
        return $UserPurchase$_contract_init($auction_address, $user_address);
    }
}

() $UserPurchase$_contract_store((slice, slice, slice, int, int, cell, cell, int, cell) v) impure inline {
    builder b = begin_cell();
    b = b.store_int(true, 1);
    b = $UserPurchase$_store(b, v);
    set_data(b.end_cell());
}

;;
;; Contract UserPurchase functions
;;

(slice, slice, slice, int, int, cell, cell, int, cell) $UserPurchase$_contract_init(slice $auction_address, slice $user_address) impure inline {
    var (($self'owner, $self'auction_address, $self'user_address, $self'total_purchased, $self'total_paid, $self'purchase_history, $self'refund_history, $self'purchase_id_counter, $self'participated_rounds)) = (null(), null(), null(), null(), null(), null(), null(), null(), null());
    $self'owner = $user_address;
    $self'auction_address = $auction_address;
    $self'user_address = $user_address;
    $self'total_purchased = 0;
    $self'total_paid = 0;
    $self'purchase_id_counter = 0;
    return ($self'owner, $self'auction_address, $self'user_address, $self'total_purchased, $self'total_paid, $self'purchase_history, $self'refund_history, $self'purchase_id_counter, $self'participated_rounds);
}

((slice, slice, slice, int, int, cell, cell, int, cell), int) $UserPurchase$_fun_total_purchased((slice, slice, slice, int, int, cell, cell, int, cell) $self) impure inline_ref {
    var (($self'owner, $self'auction_address, $self'user_address, $self'total_purchased, $self'total_paid, $self'purchase_history, $self'refund_history, $self'purchase_id_counter, $self'participated_rounds)) = $self;
    var $fresh$ret_0 = $self'total_purchased;
    return (($self'owner, $self'auction_address, $self'user_address, $self'total_purchased, $self'total_paid, $self'purchase_history, $self'refund_history, $self'purchase_id_counter, $self'participated_rounds), $fresh$ret_0);
}

((slice, slice, slice, int, int, cell, cell, int, cell), int) $UserPurchase$_fun_total_paid((slice, slice, slice, int, int, cell, cell, int, cell) $self) impure inline_ref {
    var (($self'owner, $self'auction_address, $self'user_address, $self'total_purchased, $self'total_paid, $self'purchase_history, $self'refund_history, $self'purchase_id_counter, $self'participated_rounds)) = $self;
    var $fresh$ret_1 = $self'total_paid;
    return (($self'owner, $self'auction_address, $self'user_address, $self'total_purchased, $self'total_paid, $self'purchase_history, $self'refund_history, $self'purchase_id_counter, $self'participated_rounds), $fresh$ret_1);
}

((slice, slice, slice, int, int, cell, cell, int, cell), int) $UserPurchase$_fun_purchase_id_counter((slice, slice, slice, int, int, cell, cell, int, cell) $self) impure inline_ref {
    var (($self'owner, $self'auction_address, $self'user_address, $self'total_purchased, $self'total_paid, $self'purchase_history, $self'refund_history, $self'purchase_id_counter, $self'participated_rounds)) = $self;
    var $fresh$ret_2 = $self'purchase_id_counter;
    return (($self'owner, $self'auction_address, $self'user_address, $self'total_purchased, $self'total_paid, $self'purchase_history, $self'refund_history, $self'purchase_id_counter, $self'participated_rounds), $fresh$ret_2);
}

((slice, slice, slice, int, int, cell, cell, int, cell), tuple) $UserPurchase$_fun_purchase_details((slice, slice, slice, int, int, cell, cell, int, cell) $self, int $purchase_id) impure inline_ref {
    var (($self'owner, $self'auction_address, $self'user_address, $self'total_purchased, $self'total_paid, $self'purchase_history, $self'refund_history, $self'purchase_id_counter, $self'participated_rounds)) = $self;
    var $fresh$ret_3 = $PurchaseRecord$_load_opt(__tact_dict_get_int_cell($self'purchase_history, 257, $purchase_id));
    return (($self'owner, $self'auction_address, $self'user_address, $self'total_purchased, $self'total_paid, $self'purchase_history, $self'refund_history, $self'purchase_id_counter, $self'participated_rounds), $fresh$ret_3);
}

((slice, slice, slice, int, int, cell, cell, int, cell), int) $UserPurchase$_fun_is_refunded((slice, slice, slice, int, int, cell, cell, int, cell) $self, int $purchase_id) impure inline_ref {
    var (($self'owner, $self'auction_address, $self'user_address, $self'total_purchased, $self'total_paid, $self'purchase_history, $self'refund_history, $self'purchase_id_counter, $self'participated_rounds)) = $self;
    var $fresh$ret_4 = (~ null?(__tact_dict_get_int_int($self'refund_history, 257, $purchase_id, 257)));
    return (($self'owner, $self'auction_address, $self'user_address, $self'total_purchased, $self'total_paid, $self'purchase_history, $self'refund_history, $self'purchase_id_counter, $self'participated_rounds), $fresh$ret_4);
}

((slice, slice, slice, int, int, cell, cell, int, cell), int) $UserPurchase$_fun_signature_verified_purchases((slice, slice, slice, int, int, cell, cell, int, cell) $self) impure inline_ref {
    var (($self'owner, $self'auction_address, $self'user_address, $self'total_purchased, $self'total_paid, $self'purchase_history, $self'refund_history, $self'purchase_id_counter, $self'participated_rounds)) = $self;
    int $count = 0;
    int $i = 1;
    while (($i <= $self'purchase_id_counter)) {
        tuple $purchase = $PurchaseRecord$_load_opt(__tact_dict_get_int_cell($self'purchase_history, 257, $i));
        if ((~ null?($purchase))) {
            var ($record'id, $record'user, $record'amount, $record'tokens, $record'timestamp, $record'currency, $record'purchase_method, $record'nonce, $record'round_number, $record'usdt_equivalent_amount) = $PurchaseRecord$_not_null($purchase);
            if (($record'purchase_method == 1)) {
                $count = $count + 1;
            }
        }
        $i = $i + 1;
    }
    var $fresh$ret_5 = $count;
    return (($self'owner, $self'auction_address, $self'user_address, $self'total_purchased, $self'total_paid, $self'purchase_history, $self'refund_history, $self'purchase_id_counter, $self'participated_rounds), $fresh$ret_5);
}

((slice, slice, slice, int, int, cell, cell, int, cell), cell) $UserPurchase$_fun_purchase_method_stats((slice, slice, slice, int, int, cell, cell, int, cell) $self) impure inline_ref {
    var (($self'owner, $self'auction_address, $self'user_address, $self'total_purchased, $self'total_paid, $self'purchase_history, $self'refund_history, $self'purchase_id_counter, $self'participated_rounds)) = $self;
    cell $stats = null();
    int $i = 1;
    while (($i <= $self'purchase_id_counter)) {
        tuple $purchase = $PurchaseRecord$_load_opt(__tact_dict_get_int_cell($self'purchase_history, 257, $i));
        if ((~ null?($purchase))) {
            var ($record'id, $record'user, $record'amount, $record'tokens, $record'timestamp, $record'currency, $record'purchase_method, $record'nonce, $record'round_number, $record'usdt_equivalent_amount) = $PurchaseRecord$_not_null($purchase);
            int $current_count = __tact_dict_get_int_int($stats, 257, $record'purchase_method, 257);
            if (null?($current_count)) {
                $stats~__tact_dict_set_int_int(257, $record'purchase_method, 1, 257);
            } else {
                $stats~__tact_dict_set_int_int(257, $record'purchase_method, (__tact_not_null($current_count) + 1), 257);
            }
        }
        $i = $i + 1;
    }
    var $fresh$ret_6 = $stats;
    return (($self'owner, $self'auction_address, $self'user_address, $self'total_purchased, $self'total_paid, $self'purchase_history, $self'refund_history, $self'purchase_id_counter, $self'participated_rounds), $fresh$ret_6);
}

((slice, slice, slice, int, int, cell, cell, int, cell), cell) $UserPurchase$_fun_purchases_by_round((slice, slice, slice, int, int, cell, cell, int, cell) $self, int $round_number) impure inline_ref {
    var (($self'owner, $self'auction_address, $self'user_address, $self'total_purchased, $self'total_paid, $self'purchase_history, $self'refund_history, $self'purchase_id_counter, $self'participated_rounds)) = $self;
    cell $round_purchases = null();
    int $i = 1;
    while (($i <= $self'purchase_id_counter)) {
        tuple $purchase = $PurchaseRecord$_load_opt(__tact_dict_get_int_cell($self'purchase_history, 257, $i));
        if ((~ null?($purchase))) {
            var ($record'id, $record'user, $record'amount, $record'tokens, $record'timestamp, $record'currency, $record'purchase_method, $record'nonce, $record'round_number, $record'usdt_equivalent_amount) = $PurchaseRecord$_not_null($purchase);
            if (($record'round_number == $round_number)) {
                $round_purchases~__tact_dict_set_int_cell(257, $i, $PurchaseRecord$_store_cell(($record'id, $record'user, $record'amount, $record'tokens, $record'timestamp, $record'currency, $record'purchase_method, $record'nonce, $record'round_number, $record'usdt_equivalent_amount), begin_cell()));
            }
        }
        $i = $i + 1;
    }
    var $fresh$ret_7 = $round_purchases;
    return (($self'owner, $self'auction_address, $self'user_address, $self'total_purchased, $self'total_paid, $self'purchase_history, $self'refund_history, $self'purchase_id_counter, $self'participated_rounds), $fresh$ret_7);
}

((slice, slice, slice, int, int, cell, cell, int, cell), int) $UserPurchase$_fun_round_total_amount((slice, slice, slice, int, int, cell, cell, int, cell) $self, int $round_number) impure inline_ref {
    var (($self'owner, $self'auction_address, $self'user_address, $self'total_purchased, $self'total_paid, $self'purchase_history, $self'refund_history, $self'purchase_id_counter, $self'participated_rounds)) = $self;
    int $total_amount = 0;
    int $i = 1;
    while (($i <= $self'purchase_id_counter)) {
        tuple $purchase = $PurchaseRecord$_load_opt(__tact_dict_get_int_cell($self'purchase_history, 257, $i));
        if ((~ null?($purchase))) {
            var ($record'id, $record'user, $record'amount, $record'tokens, $record'timestamp, $record'currency, $record'purchase_method, $record'nonce, $record'round_number, $record'usdt_equivalent_amount) = $PurchaseRecord$_not_null($purchase);
            if (($record'round_number == $round_number)) {
                $total_amount = $total_amount + $record'amount;
            }
        }
        $i = $i + 1;
    }
    var $fresh$ret_8 = $total_amount;
    return (($self'owner, $self'auction_address, $self'user_address, $self'total_purchased, $self'total_paid, $self'purchase_history, $self'refund_history, $self'purchase_id_counter, $self'participated_rounds), $fresh$ret_8);
}

((slice, slice, slice, int, int, cell, cell, int, cell), int) $UserPurchase$_fun_round_total_tokens((slice, slice, slice, int, int, cell, cell, int, cell) $self, int $round_number) impure inline_ref {
    var (($self'owner, $self'auction_address, $self'user_address, $self'total_purchased, $self'total_paid, $self'purchase_history, $self'refund_history, $self'purchase_id_counter, $self'participated_rounds)) = $self;
    int $total_tokens = 0;
    int $i = 1;
    while (($i <= $self'purchase_id_counter)) {
        tuple $purchase = $PurchaseRecord$_load_opt(__tact_dict_get_int_cell($self'purchase_history, 257, $i));
        if ((~ null?($purchase))) {
            var ($record'id, $record'user, $record'amount, $record'tokens, $record'timestamp, $record'currency, $record'purchase_method, $record'nonce, $record'round_number, $record'usdt_equivalent_amount) = $PurchaseRecord$_not_null($purchase);
            if (($record'round_number == $round_number)) {
                $total_tokens = $total_tokens + $record'tokens;
            }
        }
        $i = $i + 1;
    }
    var $fresh$ret_9 = $total_tokens;
    return (($self'owner, $self'auction_address, $self'user_address, $self'total_purchased, $self'total_paid, $self'purchase_history, $self'refund_history, $self'purchase_id_counter, $self'participated_rounds), $fresh$ret_9);
}

((slice, slice, slice, int, int, cell, cell, int, cell), int) $UserPurchase$_fun_round_purchase_count((slice, slice, slice, int, int, cell, cell, int, cell) $self, int $round_number) impure inline_ref {
    var (($self'owner, $self'auction_address, $self'user_address, $self'total_purchased, $self'total_paid, $self'purchase_history, $self'refund_history, $self'purchase_id_counter, $self'participated_rounds)) = $self;
    int $purchase_count = 0;
    int $i = 1;
    while (($i <= $self'purchase_id_counter)) {
        tuple $purchase = $PurchaseRecord$_load_opt(__tact_dict_get_int_cell($self'purchase_history, 257, $i));
        if ((~ null?($purchase))) {
            var ($record'id, $record'user, $record'amount, $record'tokens, $record'timestamp, $record'currency, $record'purchase_method, $record'nonce, $record'round_number, $record'usdt_equivalent_amount) = $PurchaseRecord$_not_null($purchase);
            if (($record'round_number == $round_number)) {
                $purchase_count = $purchase_count + 1;
            }
        }
        $i = $i + 1;
    }
    var $fresh$ret_10 = $purchase_count;
    return (($self'owner, $self'auction_address, $self'user_address, $self'total_purchased, $self'total_paid, $self'purchase_history, $self'refund_history, $self'purchase_id_counter, $self'participated_rounds), $fresh$ret_10);
}

((slice, slice, slice, int, int, cell, cell, int, cell), cell) $UserPurchase$_fun_participated_rounds((slice, slice, slice, int, int, cell, cell, int, cell) $self) impure inline_ref {
    var (($self'owner, $self'auction_address, $self'user_address, $self'total_purchased, $self'total_paid, $self'purchase_history, $self'refund_history, $self'purchase_id_counter, $self'participated_rounds)) = $self;
    var $fresh$ret_11 = $self'participated_rounds;
    return (($self'owner, $self'auction_address, $self'user_address, $self'total_purchased, $self'total_paid, $self'purchase_history, $self'refund_history, $self'purchase_id_counter, $self'participated_rounds), $fresh$ret_11);
}

((slice, slice, slice, int, int, cell, cell, int, cell), int) $UserPurchase$_fun_participated_in_round((slice, slice, slice, int, int, cell, cell, int, cell) $self, int $round_number) impure inline_ref {
    var (($self'owner, $self'auction_address, $self'user_address, $self'total_purchased, $self'total_paid, $self'purchase_history, $self'refund_history, $self'purchase_id_counter, $self'participated_rounds)) = $self;
    var $fresh$ret_12 = (~ null?(__tact_dict_get_int_int($self'participated_rounds, 257, $round_number, 1)));
    return (($self'owner, $self'auction_address, $self'user_address, $self'total_purchased, $self'total_paid, $self'purchase_history, $self'refund_history, $self'purchase_id_counter, $self'participated_rounds), $fresh$ret_12);
}

((slice, slice, slice, int, int, cell, cell, int, cell), int) $UserPurchase$_fun_round_count((slice, slice, slice, int, int, cell, cell, int, cell) $self) impure inline_ref {
    var (($self'owner, $self'auction_address, $self'user_address, $self'total_purchased, $self'total_paid, $self'purchase_history, $self'refund_history, $self'purchase_id_counter, $self'participated_rounds)) = $self;
    int $count = 0;
    int $j = 1;
    while (($j <= 1000)) {
        if ((~ null?(__tact_dict_get_int_int($self'participated_rounds, 257, $j, 1)))) {
            $count = $count + 1;
        }
        $j = $j + 1;
    }
    var $fresh$ret_13 = $count;
    return (($self'owner, $self'auction_address, $self'user_address, $self'total_purchased, $self'total_paid, $self'purchase_history, $self'refund_history, $self'purchase_id_counter, $self'participated_rounds), $fresh$ret_13);
}

((slice, slice, slice, int, int, cell, cell, int, cell), cell) $UserPurchase$_fun_all_records((slice, slice, slice, int, int, cell, cell, int, cell) $self) impure inline_ref {
    var (($self'owner, $self'auction_address, $self'user_address, $self'total_purchased, $self'total_paid, $self'purchase_history, $self'refund_history, $self'purchase_id_counter, $self'participated_rounds)) = $self;
    var $fresh$ret_14 = $self'purchase_history;
    return (($self'owner, $self'auction_address, $self'user_address, $self'total_purchased, $self'total_paid, $self'purchase_history, $self'refund_history, $self'purchase_id_counter, $self'participated_rounds), $fresh$ret_14);
}

;;
;; Get methods of a Contract UserPurchase
;;

_ %total_purchased() method_id(97654) {
    var self = $UserPurchase$_contract_load();
    var res = self~$UserPurchase$_fun_total_purchased();
    return res;
}

_ %total_paid() method_id(127313) {
    var self = $UserPurchase$_contract_load();
    var res = self~$UserPurchase$_fun_total_paid();
    return res;
}

_ %purchase_id_counter() method_id(90655) {
    var self = $UserPurchase$_contract_load();
    var res = self~$UserPurchase$_fun_purchase_id_counter();
    return res;
}

_ %purchase_details(int $purchase_id) method_id(108146) {
    int $purchase_id = $purchase_id;
    var self = $UserPurchase$_contract_load();
    var res = self~$UserPurchase$_fun_purchase_details($purchase_id);
    return $PurchaseRecord$_to_opt_external(res);
}

_ %is_refunded(int $purchase_id) method_id(70580) {
    int $purchase_id = $purchase_id;
    var self = $UserPurchase$_contract_load();
    var res = self~$UserPurchase$_fun_is_refunded($purchase_id);
    return res;
}

_ %signature_verified_purchases() method_id(90079) {
    var self = $UserPurchase$_contract_load();
    var res = self~$UserPurchase$_fun_signature_verified_purchases();
    return res;
}

_ %purchase_method_stats() method_id(123538) {
    var self = $UserPurchase$_contract_load();
    var res = self~$UserPurchase$_fun_purchase_method_stats();
    return res;
}

_ %purchases_by_round(int $round_number) method_id(98340) {
    int $round_number = $round_number;
    var self = $UserPurchase$_contract_load();
    var res = self~$UserPurchase$_fun_purchases_by_round($round_number);
    return res;
}

_ %round_total_amount(int $round_number) method_id(67459) {
    int $round_number = $round_number;
    var self = $UserPurchase$_contract_load();
    var res = self~$UserPurchase$_fun_round_total_amount($round_number);
    return res;
}

_ %round_total_tokens(int $round_number) method_id(123888) {
    int $round_number = $round_number;
    var self = $UserPurchase$_contract_load();
    var res = self~$UserPurchase$_fun_round_total_tokens($round_number);
    return res;
}

_ %round_purchase_count(int $round_number) method_id(82786) {
    int $round_number = $round_number;
    var self = $UserPurchase$_contract_load();
    var res = self~$UserPurchase$_fun_round_purchase_count($round_number);
    return res;
}

_ %participated_rounds() method_id(108842) {
    var self = $UserPurchase$_contract_load();
    var res = self~$UserPurchase$_fun_participated_rounds();
    return res;
}

_ %participated_in_round(int $round_number) method_id(124279) {
    int $round_number = $round_number;
    var self = $UserPurchase$_contract_load();
    var res = self~$UserPurchase$_fun_participated_in_round($round_number);
    return res;
}

_ %round_count() method_id(86414) {
    var self = $UserPurchase$_contract_load();
    var res = self~$UserPurchase$_fun_round_count();
    return res;
}

_ %all_records() method_id(96455) {
    var self = $UserPurchase$_contract_load();
    var res = self~$UserPurchase$_fun_all_records();
    return res;
}

_ %owner() method_id(83229) {
    var self = $UserPurchase$_contract_load();
    var res = self~$UserPurchase$_fun_owner();
    return res;
}

;;
;; Routing of a Contract UserPurchase
;;

;; message opcode reader utility: only binary receivers
;; Returns 32 bit message opcode, otherwise throws the "Invalid incoming message" exit code
(slice, int) ~load_opcode_internal(slice s) asm( -> 1 0) "32 LDUQ 130 THROWIFNOT";

() recv_internal(int msg_value, cell in_msg_cell, slice in_msg) impure {
    
    ;; Context
    var cs = in_msg_cell.begin_parse();
    cs~skip_bits(2);
    var msg_bounceable = cs~load_int(1);
    var msg_bounced = cs~load_int(1);
    slice msg_sender_addr = cs~load_msg_addr();
    __tact_context = (msg_bounceable, msg_sender_addr, msg_value, cs);
    __tact_context_sender = msg_sender_addr;
    
    ;; Load contract data
    var ($self'owner, $self'auction_address, $self'user_address, $self'total_purchased, $self'total_paid, $self'purchase_history, $self'refund_history, $self'purchase_id_counter, $self'participated_rounds) = $UserPurchase$_contract_load();
    
    ;; Handle bounced messages
    if (msg_bounced) { return (); }
    int op = 0;
    int in_msg_length = slice_bits(in_msg);
    if (in_msg_length >= 32) {
        op = in_msg~load_uint(32);
        ;; Receive CreateUserPurchase message
        if (op == 0x41734c0c) {
            var $msg'user = in_msg~load_msg_addr();
            var $msg'amount = in_msg~load_varuint16();
            var $msg'tokens = in_msg~load_varuint16();
            var $msg'currency = in_msg~load_uint(8);
            var $msg'purchase_method = in_msg~load_uint(8);
            var $msg'nonce = in_msg~load_uint(64);
            var $msg'round_number = in_msg~load_uint(32);
            var $msg'usdt_equivalent_amount = in_msg~load_varuint16();
            throw_unless(49729, ( equal_slices_bits(__tact_context_get_sender(), $self'auction_address) ));
            $self'purchase_id_counter = $self'purchase_id_counter + 1;
            var ($new_purchase'id, $new_purchase'user, $new_purchase'amount, $new_purchase'tokens, $new_purchase'timestamp, $new_purchase'currency, $new_purchase'purchase_method, $new_purchase'nonce, $new_purchase'round_number, $new_purchase'usdt_equivalent_amount) = $PurchaseRecord$_constructor_id_user_amount_tokens_timestamp_currency_purchase_method_nonce_round_number_usdt_equivalent_amount($self'purchase_id_counter, $msg'user, $msg'amount, $msg'tokens, $global_now(), $msg'currency, $msg'purchase_method, $msg'nonce, $msg'round_number, $msg'usdt_equivalent_amount);
            $self'purchase_history~__tact_dict_set_int_cell(257, $self'purchase_id_counter, $PurchaseRecord$_store_cell(($new_purchase'id, $new_purchase'user, $new_purchase'amount, $new_purchase'tokens, $new_purchase'timestamp, $new_purchase'currency, $new_purchase'purchase_method, $new_purchase'nonce, $new_purchase'round_number, $new_purchase'usdt_equivalent_amount), begin_cell()));
            $self'total_purchased = $self'total_purchased + $msg'tokens;
            $self'total_paid = $self'total_paid + $msg'amount;
            $self'participated_rounds~__tact_dict_set_int_int(257, $msg'round_number, true, 1);
            $global_cashback(__tact_context_get_sender());
            $UserPurchase$_contract_store(($self'owner, $self'auction_address, $self'user_address, $self'total_purchased, $self'total_paid, $self'purchase_history, $self'refund_history, $self'purchase_id_counter, $self'participated_rounds));
            return ();
        }
        
        ;; Receive Refund message
        if (op == 0x31d5b5ac) {
            var $msg'purchase_id = in_msg~load_uint(32);
            throw_unless(49729, ( equal_slices_bits(__tact_context_get_sender(), $self'user_address) ));
            tuple $purchase = $PurchaseRecord$_load_opt(__tact_dict_get_int_cell($self'purchase_history, 257, $msg'purchase_id));
            throw_unless(39144, (~ null?($purchase)));
            throw_unless(22411, null?(__tact_dict_get_int_int($self'refund_history, 257, $msg'purchase_id, 257)));
            var ($purchase_data'id, $purchase_data'user, $purchase_data'amount, $purchase_data'tokens, $purchase_data'timestamp, $purchase_data'currency, $purchase_data'purchase_method, $purchase_data'nonce, $purchase_data'round_number, $purchase_data'usdt_equivalent_amount) = $PurchaseRecord$_not_null($purchase);
            $self'refund_history~__tact_dict_set_int_int(257, $msg'purchase_id, $purchase_data'amount, 257);
            $self'total_purchased = $self'total_purchased - $purchase_data'tokens;
            $self'total_paid = $self'total_paid - $purchase_data'amount;
            $global_send($SendParameters$_constructor_to_value_mode_bounce_body($self'auction_address, 50000000, 2, false, $ProcessRefund$_store_cell($ProcessRefund$_constructor_user_amount_fee_currency_round_number_usdt_equivalent_amount($self'user_address, $purchase_data'amount, (($purchase_data'amount * 5) / 100), $purchase_data'currency, $purchase_data'round_number, $purchase_data'usdt_equivalent_amount), begin_cell())));
            $UserPurchase$_contract_store(($self'owner, $self'auction_address, $self'user_address, $self'total_purchased, $self'total_paid, $self'purchase_history, $self'refund_history, $self'purchase_id_counter, $self'participated_rounds));
            return ();
        }
        
    }
    ;; Empty Receiver and Text Receivers
    var text_op = slice_hash(in_msg);
    ;; Receive "Deploy" message
    if (text_op == 0xc1c8ebe8e42f1458f2693e8bef345c9c08db8c56d2ca637be9b436ea1f68976f) {
        $UserPurchase$_contract_store(($self'owner, $self'auction_address, $self'user_address, $self'total_purchased, $self'total_paid, $self'purchase_history, $self'refund_history, $self'purchase_id_counter, $self'participated_rounds));
        return ();
    }
    ;; Throw if not handled
    throw(130);
}


() __tact_selector_hack_asm() impure asm """
@atend @ 1 {
        execute current@ context@ current!
        {
            // The core idea of this function is to save gas by avoiding unnecessary dict jump, when recv_internal/recv_external is called
            // We want to extract recv_internal/recv_external from the dict and select needed function
            // not by jumping to the needed function by it's index, but by using usual IF statements.

            }END> b> // Close previous builder, now we have a cell of previous code on top of the stack

            <{ // Start of the new code builder
                SETCP0
                // Swap the new code builder with the previous code, now we have previous code on top of the stack
                swap
                // Transform cell to slice and load first ref from the previous code, now we have the dict on top of the stack
                <s ref@

                // Extract the recv_internal from the dict
                dup 0 swap @procdictkeylen idict@ { "internal shortcut error" abort } ifnot
                swap

                // Delete the recv_internal from the dict
                0 swap @procdictkeylen idict- drop
                // Delete the recv_external from the dict (it's okay if it's not there)
                -1 swap @procdictkeylen idict- drop
                // Delete the __tact_selector_hack from the dict
                65535 swap @procdictkeylen idict- drop

                // Bring the code builder from the bottom of the stack
                // because if recv_external extraction is optional, and the number of elements on the stack is not fixed
                depth 1- roll
                // Swap with the dict from which we extracted recv_internal and (maybe) recv_external
                swap

                // Check if the dict is empty
                dup null?
                // Store a copy of this flag in the bottom of the stack
                dup depth 1- -roll
                {
                    // If the dict is empty, just drop it (it will be null if it's empty)
                    drop
                }
                {
                    // If the dict is not empty, prepare continuation to be stored in c3
                    <{
                        // Save this dict as first ref in this continuation, it will be pushed in runtime by DICTPUSHCONST
                        swap @procdictkeylen DICTPUSHCONST
                        // Jump to the needed function by it's index
                        DICTIGETJMPZ
                        // If such key is not found, throw 11 along with the key as an argument
                        11 THROWARG
                    }> PUSHCONT
                    // Store the continuation in c3
                    c3 POP
                } cond

                // Function id is on top of the (runtime) stack
                DUP IFNOTJMP:<{
                    // place recv_internal here
                    DROP swap @addop
                }>

                // Bring back the flag, indicating if the dict is empty or not from the bottom of the stack
                depth 1- roll
                {
                    // If the dict is empty, throw 11
                    11 THROWARG
                }
                {
                    // If the dict is not empty, jump to continuation from c3
                    c3 PUSH JMPX
                } cond
            }> b>
        } : }END>c
        current@ context! current!
    } does @atend !
""";

() __tact_selector_hack() method_id(65535) {
    return __tact_selector_hack_asm();
}