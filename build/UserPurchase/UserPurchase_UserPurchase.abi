{"name": "UserPurchase", "types": [{"name": "DataSize", "header": null, "fields": [{"name": "cells", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "bits", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "refs", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}]}, {"name": "SignedBundle", "header": null, "fields": [{"name": "signature", "type": {"kind": "simple", "type": "fixed-bytes", "optional": false, "format": 64}}, {"name": "signedData", "type": {"kind": "simple", "type": "slice", "optional": false, "format": "remainder"}}]}, {"name": "StateInit", "header": null, "fields": [{"name": "code", "type": {"kind": "simple", "type": "cell", "optional": false}}, {"name": "data", "type": {"kind": "simple", "type": "cell", "optional": false}}]}, {"name": "Context", "header": null, "fields": [{"name": "bounceable", "type": {"kind": "simple", "type": "bool", "optional": false}}, {"name": "sender", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "value", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "raw", "type": {"kind": "simple", "type": "slice", "optional": false}}]}, {"name": "SendParameters", "header": null, "fields": [{"name": "mode", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "body", "type": {"kind": "simple", "type": "cell", "optional": true}}, {"name": "code", "type": {"kind": "simple", "type": "cell", "optional": true}}, {"name": "data", "type": {"kind": "simple", "type": "cell", "optional": true}}, {"name": "value", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "to", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "bounce", "type": {"kind": "simple", "type": "bool", "optional": false}}]}, {"name": "MessageParameters", "header": null, "fields": [{"name": "mode", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "body", "type": {"kind": "simple", "type": "cell", "optional": true}}, {"name": "value", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "to", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "bounce", "type": {"kind": "simple", "type": "bool", "optional": false}}]}, {"name": "DeployParameters", "header": null, "fields": [{"name": "mode", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "body", "type": {"kind": "simple", "type": "cell", "optional": true}}, {"name": "value", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "bounce", "type": {"kind": "simple", "type": "bool", "optional": false}}, {"name": "init", "type": {"kind": "simple", "type": "StateInit", "optional": false}}]}, {"name": "StdAddress", "header": null, "fields": [{"name": "workchain", "type": {"kind": "simple", "type": "int", "optional": false, "format": 8}}, {"name": "address", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 256}}]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "header": null, "fields": [{"name": "workchain", "type": {"kind": "simple", "type": "int", "optional": false, "format": 32}}, {"name": "address", "type": {"kind": "simple", "type": "slice", "optional": false}}]}, {"name": "BasechainAdd<PERSON>", "header": null, "fields": [{"name": "hash", "type": {"kind": "simple", "type": "int", "optional": true, "format": 257}}]}, {"name": "ChangeOwner", "header": 2174598809, "fields": [{"name": "queryId", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "new<PERSON>wner", "type": {"kind": "simple", "type": "address", "optional": false}}]}, {"name": "ChangeOwnerOk", "header": 846932810, "fields": [{"name": "queryId", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "new<PERSON>wner", "type": {"kind": "simple", "type": "address", "optional": false}}]}, {"name": "Deploy", "header": 2490013878, "fields": [{"name": "queryId", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}]}, {"name": "DeployOk", "header": 2952335191, "fields": [{"name": "queryId", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}]}, {"name": "FactoryDeploy", "header": 1829761339, "fields": [{"name": "queryId", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "cashback", "type": {"kind": "simple", "type": "address", "optional": false}}]}, {"name": "CreateUserPurchase", "header": 1098075148, "fields": [{"name": "user", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "amount", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "tokens", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "currency", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 8}}, {"name": "purchase_method", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 8}}, {"name": "nonce", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "round_number", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 32}}, {"name": "usdt_equivalent_amount", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}]}, {"name": "Refund", "header": 836089260, "fields": [{"name": "purchase_id", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 32}}]}, {"name": "ProcessRefund", "header": 929991442, "fields": [{"name": "user", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "amount", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "fee", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "currency", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 8}}, {"name": "round_number", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 32}}, {"name": "usdt_equivalent_amount", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}]}, {"name": "PurchaseRecord", "header": null, "fields": [{"name": "id", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 32}}, {"name": "user", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "amount", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "tokens", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "timestamp", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "currency", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 8}}, {"name": "purchase_method", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 8}}, {"name": "nonce", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "round_number", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 32}}, {"name": "usdt_equivalent_amount", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}]}, {"name": "UserPurchase$Data", "header": null, "fields": [{"name": "owner", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "auction_address", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "user_address", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "total_purchased", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "total_paid", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "purchase_history", "type": {"kind": "dict", "key": "int", "value": "PurchaseRecord", "valueFormat": "ref"}}, {"name": "refund_history", "type": {"kind": "dict", "key": "int", "value": "int"}}, {"name": "purchase_id_counter", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 32}}, {"name": "participated_rounds", "type": {"kind": "dict", "key": "int", "value": "bool"}}]}], "receivers": [{"receiver": "internal", "message": {"kind": "text", "text": "Deploy"}}, {"receiver": "internal", "message": {"kind": "typed", "type": "CreateUserPurchase"}}, {"receiver": "internal", "message": {"kind": "typed", "type": "Refund"}}], "getters": [{"name": "total_purchased", "methodId": 97654, "arguments": [], "returnType": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "total_paid", "methodId": 127313, "arguments": [], "returnType": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "purchase_id_counter", "methodId": 90655, "arguments": [], "returnType": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "purchase_details", "methodId": 108146, "arguments": [{"name": "purchase_id", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}], "returnType": {"kind": "simple", "type": "PurchaseRecord", "optional": true}}, {"name": "is_refunded", "methodId": 70580, "arguments": [{"name": "purchase_id", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}], "returnType": {"kind": "simple", "type": "bool", "optional": false}}, {"name": "signature_verified_purchases", "methodId": 90079, "arguments": [], "returnType": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "purchase_method_stats", "methodId": 123538, "arguments": [], "returnType": {"kind": "dict", "key": "int", "value": "int"}}, {"name": "purchases_by_round", "methodId": 98340, "arguments": [{"name": "round_number", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}], "returnType": {"kind": "dict", "key": "int", "value": "PurchaseRecord", "valueFormat": "ref"}}, {"name": "round_total_amount", "methodId": 67459, "arguments": [{"name": "round_number", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}], "returnType": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "round_total_tokens", "methodId": 123888, "arguments": [{"name": "round_number", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}], "returnType": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "round_purchase_count", "methodId": 82786, "arguments": [{"name": "round_number", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}], "returnType": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "participated_rounds", "methodId": 108842, "arguments": [], "returnType": {"kind": "dict", "key": "int", "value": "bool"}}, {"name": "participated_in_round", "methodId": 124279, "arguments": [{"name": "round_number", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}], "returnType": {"kind": "simple", "type": "bool", "optional": false}}, {"name": "round_count", "methodId": 86414, "arguments": [], "returnType": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "all_records", "methodId": 96455, "arguments": [], "returnType": {"kind": "dict", "key": "int", "value": "PurchaseRecord", "valueFormat": "ref"}}, {"name": "owner", "methodId": 83229, "arguments": [], "returnType": {"kind": "simple", "type": "address", "optional": false}}], "errors": {"2": {"message": "Stack underflow"}, "3": {"message": "Stack overflow"}, "4": {"message": "Integer overflow"}, "5": {"message": "Integer out of expected range"}, "6": {"message": "Invalid opcode"}, "7": {"message": "Type check error"}, "8": {"message": "Cell overflow"}, "9": {"message": "Cell underflow"}, "10": {"message": "Dictionary error"}, "11": {"message": "'Unknown' error"}, "12": {"message": "Fatal error"}, "13": {"message": "Out of gas error"}, "14": {"message": "Virtualization error"}, "32": {"message": "Action list is invalid"}, "33": {"message": "Action list is too long"}, "34": {"message": "Action is invalid or not supported"}, "35": {"message": "Invalid source address in outbound message"}, "36": {"message": "Invalid destination address in outbound message"}, "37": {"message": "Not enough Toncoin"}, "38": {"message": "Not enough extra currencies"}, "39": {"message": "Outbound message does not fit into a cell after rewriting"}, "40": {"message": "Cannot process a message"}, "41": {"message": "Library reference is null"}, "42": {"message": "Library change action error"}, "43": {"message": "Exceeded maximum number of cells in the library or the maximum depth of the Merkle tree"}, "50": {"message": "Account state size exceeded limits"}, "128": {"message": "Null reference exception"}, "129": {"message": "Invalid serialization prefix"}, "130": {"message": "Invalid incoming message"}, "131": {"message": "Constraints error"}, "132": {"message": "Access denied"}, "133": {"message": "Contract stopped"}, "134": {"message": "Invalid argument"}, "135": {"message": "Code of a contract was not found"}, "136": {"message": "Invalid standard address"}, "138": {"message": "Not a basechain address"}, "22411": {"message": "Already refunded"}, "39144": {"message": "Purchase not found"}, "49729": {"message": "Unauthorized"}}, "interfaces": ["org.ton.introspection.v0", "org.ton.abi.ipfs.v0", "org.ton.deploy.lazy.v0", "org.ton.ownable"]}