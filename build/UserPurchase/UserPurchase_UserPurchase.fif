// automatically generated from `@stdlib/std/stdlib.fc` `/Users/<USER>/code/tbook/onton_cc/onion-launch/build/UserPurchase/UserPurchase_UserPurchase.fc` 
PROGRAM{
  DECLPROC __tact_not_null
  DECLPROC __tact_context_get_sender
  DECLPROC __tact_dict_get_int_int
  DECLPROC __tact_dict_set_int_int
  DECLPROC __tact_dict_get_int_cell
  DECLPROC __tact_dict_set_int_cell
  DECLPROC $ProcessRefund$_store
  DECLPROC $ProcessRefund$_store_cell
  DECLPROC $PurchaseRecord$_store
  DECLPROC $PurchaseRecord$_store_cell
  DECLPROC $PurchaseRecord$_load
  DECLPROC $PurchaseRecord$_as_optional
  DECLPROC $PurchaseRecord$_load_opt
  DECLPROC $UserPurchase$_store
  DECLPROC $UserPurchase$_load
  DECLPROC $PurchaseRecord$_not_null
  DECLPROC $PurchaseRecord$_to_tuple
  DECLPROC $PurchaseRecord$_to_opt_tuple
  DECLPROC $PurchaseRecord$_to_opt_external
  DECLPROC $UserPurchase$init$_load
  DECLPROC $UserPurchase$_contract_init
  DECLPROC $UserPurchase$_contract_load
  DECLPROC $UserPurchase$_contract_store
  DECLPROC $UserPurchase$_fun_total_purchased
  DECLPROC $UserPurchase$_fun_total_paid
  DECLPROC $UserPurchase$_fun_purchase_id_counter
  DECLPROC $UserPurchase$_fun_purchase_details
  DECLPROC $UserPurchase$_fun_is_refunded
  DECLPROC $UserPurchase$_fun_signature_verified_purchases
  DECLPROC $UserPurchase$_fun_purchase_method_stats
  DECLPROC $UserPurchase$_fun_purchases_by_round
  DECLPROC $UserPurchase$_fun_round_total_amount
  DECLPROC $UserPurchase$_fun_round_total_tokens
  DECLPROC $UserPurchase$_fun_round_purchase_count
  DECLPROC $UserPurchase$_fun_participated_rounds
  DECLPROC $UserPurchase$_fun_participated_in_round
  DECLPROC $UserPurchase$_fun_round_count
  DECLPROC $UserPurchase$_fun_all_records
  DECLPROC $UserPurchase$_fun_owner
  DECLPROC $PurchaseRecord$_constructor_id_user_amount_tokens_timestamp_currency_purchase_method_nonce_round_number_usdt_equivalent_amount
  DECLPROC $SendParameters$_constructor_to_value_mode_bounce_body
  DECLPROC $ProcessRefund$_constructor_user_amount_fee_currency_round_number_usdt_equivalent_amount
  97654 DECLMETHOD %total_purchased
  127313 DECLMETHOD %total_paid
  90655 DECLMETHOD %purchase_id_counter
  108146 DECLMETHOD %purchase_details
  70580 DECLMETHOD %is_refunded
  90079 DECLMETHOD %signature_verified_purchases
  123538 DECLMETHOD %purchase_method_stats
  98340 DECLMETHOD %purchases_by_round
  67459 DECLMETHOD %round_total_amount
  123888 DECLMETHOD %round_total_tokens
  82786 DECLMETHOD %round_purchase_count
  108842 DECLMETHOD %participated_rounds
  124279 DECLMETHOD %participated_in_round
  86414 DECLMETHOD %round_count
  96455 DECLMETHOD %all_records
  83229 DECLMETHOD %owner
  DECLPROC recv_internal
  65535 DECLMETHOD __tact_selector_hack
  DECLGLOBVAR __tact_context
  DECLGLOBVAR __tact_context_sender
  DECLGLOBVAR __tact_child_contract_codes
  DECLGLOBVAR __tact_randomized
  __tact_not_null PROCINLINE:<{
    DUP
    ISNULL
    128 THROWIF
  }>
  __tact_context_get_sender PROCINLINE:<{
    __tact_context_sender GETGLOB
  }>
  __tact_dict_get_int_int PROCINLINE:<{
    s1 s3 s3 XCHG3
    DICTIGET
    NULLSWAPIFNOT
    IF:<{
      SWAP
      LDIX
      DROP
    }>ELSE<{
      2DROP
      PUSHNULL
    }>
  }>
  __tact_dict_set_int_int PROCINLINE:<{
    OVER
    ISNULL
    IF:<{
      2DROP
      -ROT
      DICTIDEL
      DROP
    }>ELSE<{
      NEWC
      SWAP
      STIX
      s1 s3 s3 XCHG3
      DICTISETB
    }>
  }>
  __tact_dict_get_int_cell PROCINLINE:<{
    -ROT
    DICTIGETREF
    NULLSWAPIFNOT
    IF:<{
    }>ELSE<{
      DROP
      PUSHNULL
    }>
  }>
  __tact_dict_set_int_cell PROCINLINE:<{
    DUP
    ISNULL
    IF:<{
      DROP
      -ROT
      DICTIDEL
      DROP
    }>ELSE<{
      s1 s3 s3 XCHG3
      DICTISETREF
    }>
  }>
  $ProcessRefund$_store PROCINLINE:<{
    929991442 PUSHINT
    s0 s7 XCHG2
    32 STU
    s1 s5 XCHG
    STSLICE
    s0 s3 XCHG2
    STVARUINT16
    SWAP
    STVARUINT16
    8 STU
    32 STU
    SWAP
    STVARUINT16
  }>
  $ProcessRefund$_store_cell PROCINLINE:<{
    6 -ROLL
    $ProcessRefund$_store INLINECALLDICT
    ENDC
  }>
  $PurchaseRecord$_store PROCINLINE:<{
    s9 s10 XCHG2
    32 STU
    s1 s7 XCHG
    STSLICE
    s0 s5 XCHG2
    STVARUINT16
    s0 s3 XCHG2
    STVARUINT16
    64 STU
    8 STU
    8 STU
    64 STU
    32 STU
    SWAP
    STVARUINT16
  }>
  $PurchaseRecord$_store_cell PROCINLINE:<{
    10 -ROLL
    $PurchaseRecord$_store INLINECALLDICT
    ENDC
  }>
  $PurchaseRecord$_load PROCINLINE:<{
    32 LDU
    LDMSGADDR
    LDVARUINT16
    LDVARUINT16
    64 LDU
    8 LDU
    8 LDU
    64 LDU
    32 LDU
    LDVARUINT16
    10 -ROLL
  }>
  $PurchaseRecord$_as_optional PROCINLINE:<{
        10 TUPLE
  }>
  $PurchaseRecord$_load_opt PROCINLINE:<{
    DUP
    ISNULL
    IF:<{
      DROP
      PUSHNULL
    }>ELSE<{
      CTOS
      $PurchaseRecord$_load INLINECALLDICT
      1 10 BLKDROP2
      $PurchaseRecord$_as_optional INLINECALLDICT
    }>
  }>
  $UserPurchase$_store PROCINLINE:<{
    s8 s9 XCHG2
    STSLICE
    s1 s6 XCHG
    STSLICE
    s1 s4 XCHG
    STSLICE
    ROT
    STVARUINT16
    NEWC
    ROT
    STVARUINT16
    s1 s2 XCHG
    STDICT
    s1 s2 XCHG
    STDICT
    s1 s2 XCHG
    32 STU
    s1 s2 XCHG
    STDICT
    STBREFR
  }>
  $UserPurchase$_load PROCINLINE:<{
    LDMSGADDR
    LDMSGADDR
    LDMSGADDR
    LDVARUINT16
    LDREF
    SWAP
    CTOS
    LDVARUINT16
    LDDICT
    LDDICT
    32 LDU
    LDDICT
    DROP
    s5 s9 XCHG
    s5 s8 XCHG
    s5 s7 XCHG
    s5 s6 XCHG
  }>
  $PurchaseRecord$_not_null PROCINLINE:<{
    DUP
    ISNULL
    128 THROWIF
        10 UNTUPLE
  }>
  $PurchaseRecord$_to_tuple PROCINLINE:<{
        10 TUPLE
  }>
  $PurchaseRecord$_to_opt_tuple PROCINLINE:<{
    DUP
    ISNULL
    IF:<{
      DROP
      PUSHNULL
    }>ELSE<{
      $PurchaseRecord$_not_null INLINECALLDICT
      $PurchaseRecord$_to_tuple INLINECALLDICT
    }>
  }>
  $PurchaseRecord$_to_opt_external PROCINLINE:<{
    $PurchaseRecord$_to_opt_tuple INLINECALLDICT
    DUP
    ISNULL
    IF:<{
      DROP
      PUSHNULL
    }>ELSE<{
    }>
  }>
  $UserPurchase$init$_load PROCINLINE:<{
    LDMSGADDR
    LDMSGADDR
    -ROT
  }>
  $UserPurchase$_contract_init PROCINLINE:<{
    PUSHNULL
    PUSHNULL
    PUSHNULL
    s3 PUSH
    0 PUSHINT
    s0 s0 PUSH2
    s3 s8 XCHG
    s3 s7 XCHG
    s3 s6 XCHG
    s5 s0 s4 XCHG3
  }>
  $UserPurchase$_contract_load PROCINLINE:<{
    c4 PUSH
    CTOS
    1 LDI
    SWAP
    IF:<{
      $UserPurchase$_load INLINECALLDICT
      1 9 BLKDROP2
    }>ELSE<{
      $UserPurchase$init$_load INLINECALLDICT
      s0 s2 XCHG
      ENDS
      SWAP
      $UserPurchase$_contract_init INLINECALLDICT
    }>
  }>
  $UserPurchase$_contract_store PROCINLINE:<{
    NEWC
    TRUE
    SWAP
    1 STI
    9 -ROLL
    $UserPurchase$_store INLINECALLDICT
    ENDC
    c4 POP
  }>
  $UserPurchase$_fun_total_purchased PROCREF:<{
    s5 PUSH
  }>
  $UserPurchase$_fun_total_paid PROCREF:<{
    s4 PUSH
  }>
  $UserPurchase$_fun_purchase_id_counter PROCREF:<{
    OVER
  }>
  $UserPurchase$_fun_purchase_details PROCREF:<{
    257 PUSHINT
    s5 PUSH
    s0 s2 XCHG
    __tact_dict_get_int_cell INLINECALLDICT
    $PurchaseRecord$_load_opt INLINECALLDICT
  }>
  $UserPurchase$_fun_is_refunded PROCREF:<{
    257 PUSHINT
    s0 s4 PUSH2
    s3 s3 XCHG2
    __tact_dict_get_int_int INLINECALLDICT
    ISNULL
    NOT
  }>
  $UserPurchase$_fun_signature_verified_purchases PROCREF:<{
    0 PUSHINT
    1 PUSHINT
    WHILE:<{
      s0 s3 PUSH2
      LEQ
    }>DO<{
      s5 PUSH
      257 PUSHINT
      s2 PUSH
      __tact_dict_get_int_cell INLINECALLDICT
      $PurchaseRecord$_load_opt INLINECALLDICT
      DUP
      ISNULL
      NOT
      IF:<{
        $PurchaseRecord$_not_null INLINECALLDICT
        s3 s9 XCHG
        9 BLKDROP
        1 EQINT
        IF:<{
          SWAP
          INC
          SWAP
        }>
      }>ELSE<{
        DROP
      }>
      INC
    }>
    DROP
  }>
  $UserPurchase$_fun_purchase_method_stats PROCREF:<{
    PUSHNULL
    1 PUSHINT
    WHILE:<{
      s0 s3 PUSH2
      LEQ
    }>DO<{
      s5 PUSH
      257 PUSHINT
      s2 PUSH
      __tact_dict_get_int_cell INLINECALLDICT
      $PurchaseRecord$_load_opt INLINECALLDICT
      DUP
      ISNULL
      NOT
      IF:<{
        $PurchaseRecord$_not_null INLINECALLDICT
        s3 s9 XCHG
        9 BLKDROP
        257 PUSHINT
        s3 s(-1) s(-1) PUXCPU
        s3 s(-1) PUXC
        __tact_dict_get_int_int INLINECALLDICT
        DUP
        ISNULL
        IF:<{
          DROP
          257 PUSHINT
          1 PUSHINT
          OVER
          s4 s5 XCHG
          s2 s3 XCHG
          __tact_dict_set_int_int INLINECALLDICT
        }>ELSE<{
          257 PUSHINT
          SWAP
          __tact_not_null INLINECALLDICT
          INC
          OVER
          s4 s5 XCHG
          s2 s3 XCHG
          __tact_dict_set_int_int INLINECALLDICT
        }>
        SWAP
      }>ELSE<{
        DROP
      }>
      INC
    }>
    DROP
  }>
  $UserPurchase$_fun_purchases_by_round PROCREF:<{
    PUSHNULL
    1 PUSHINT
    WHILE:<{
      s0 s4 PUSH2
      LEQ
    }>DO<{
      s6 PUSH
      257 PUSHINT
      s2 PUSH
      __tact_dict_get_int_cell INLINECALLDICT
      $PurchaseRecord$_load_opt INLINECALLDICT
      DUP
      ISNULL
      NOT
      IF:<{
        $PurchaseRecord$_not_null INLINECALLDICT
        s1 s12 PUSH2
        EQUAL
        IF:<{
          s0 s9 XCHG
          s0 s8 XCHG
          s0 s7 XCHG
          s0 s6 XCHG
          s0 s5 XCHG
          s0 s4 XCHG
          s3 s1 s3 XCHG3
          257 PUSHINT
          s2 s10 XCHG2
          NEWC
          $PurchaseRecord$_store_cell INLINECALLDICT
          s2 PUSH
          s3 s4 XCHG
          SWAP
          __tact_dict_set_int_cell INLINECALLDICT
          SWAP
        }>ELSE<{
          10 BLKDROP
        }>
      }>ELSE<{
        DROP
      }>
      INC
    }>
    DROP
    NIP
  }>
  $UserPurchase$_fun_round_total_amount PROCREF:<{
    0 PUSHINT
    1 PUSHINT
    WHILE:<{
      s0 s4 PUSH2
      LEQ
    }>DO<{
      s6 PUSH
      257 PUSHINT
      s2 PUSH
      __tact_dict_get_int_cell INLINECALLDICT
      $PurchaseRecord$_load_opt INLINECALLDICT
      DUP
      ISNULL
      NOT
      IF:<{
        $PurchaseRecord$_not_null INLINECALLDICT
        s1 s6 XCHG
        6 BLKDROP
        2 2 BLKDROP2
        s4 PUSH
        EQUAL
        IF:<{
          s1 s2 XCHG
          ADD
          SWAP
        }>ELSE<{
          DROP
        }>
      }>ELSE<{
        DROP
      }>
      INC
    }>
    DROP
    NIP
  }>
  $UserPurchase$_fun_round_total_tokens PROCREF:<{
    0 PUSHINT
    1 PUSHINT
    WHILE:<{
      s0 s4 PUSH2
      LEQ
    }>DO<{
      s6 PUSH
      257 PUSHINT
      s2 PUSH
      __tact_dict_get_int_cell INLINECALLDICT
      $PurchaseRecord$_load_opt INLINECALLDICT
      DUP
      ISNULL
      NOT
      IF:<{
        $PurchaseRecord$_not_null INLINECALLDICT
        s1 s5 XCHG
        5 BLKDROP
        3 2 BLKDROP2
        s4 PUSH
        EQUAL
        IF:<{
          s1 s2 XCHG
          ADD
          SWAP
        }>ELSE<{
          DROP
        }>
      }>ELSE<{
        DROP
      }>
      INC
    }>
    DROP
    NIP
  }>
  $UserPurchase$_fun_round_purchase_count PROCREF:<{
    0 PUSHINT
    1 PUSHINT
    WHILE:<{
      s0 s4 PUSH2
      LEQ
    }>DO<{
      s6 PUSH
      257 PUSHINT
      s2 PUSH
      __tact_dict_get_int_cell INLINECALLDICT
      $PurchaseRecord$_load_opt INLINECALLDICT
      DUP
      ISNULL
      NOT
      IF:<{
        $PurchaseRecord$_not_null INLINECALLDICT
        s1 s9 XCHG
        9 BLKDROP
        s3 PUSH
        EQUAL
        IF:<{
          SWAP
          INC
          SWAP
        }>
      }>ELSE<{
        DROP
      }>
      INC
    }>
    DROP
    NIP
  }>
  $UserPurchase$_fun_participated_rounds PROCREF:<{
    DUP
  }>
  $UserPurchase$_fun_participated_in_round PROCREF:<{
    257 PUSHINT
    s2 PUSH
    s0 s2 XCHG
    1 PUSHINT
    __tact_dict_get_int_int INLINECALLDICT
    ISNULL
    NOT
  }>
  $UserPurchase$_fun_round_count PROCREF:<{
    0 PUSHINT
    1 PUSHINT
    WHILE:<{
      DUP
      1000 PUSHINT
      LEQ
    }>DO<{
      s2 PUSH
      257 PUSHINT
      s2 PUSH
      1 PUSHINT
      __tact_dict_get_int_int INLINECALLDICT
      ISNULL
      NOT
      IF:<{
        SWAP
        INC
        SWAP
      }>
      INC
    }>
    DROP
  }>
  $UserPurchase$_fun_all_records PROCREF:<{
    s3 PUSH
  }>
  $UserPurchase$_fun_owner PROCREF:<{
    s8 PUSH
  }>
  $PurchaseRecord$_constructor_id_user_amount_tokens_timestamp_currency_purchase_method_nonce_round_number_usdt_equivalent_amount PROCINLINE:<{
  }>
  $SendParameters$_constructor_to_value_mode_bounce_body PROCINLINE:<{
    s2 s4 XCHG
    PUSHNULL
    s4 s3 XCHG2
    PUSHNULL
    s0 s3 XCHG
  }>
  $ProcessRefund$_constructor_user_amount_fee_currency_round_number_usdt_equivalent_amount PROCINLINE:<{
  }>
  %total_purchased PROC:<{
    $UserPurchase$_contract_load INLINECALLDICT
    $UserPurchase$_fun_total_purchased INLINECALLDICT
    9 1 BLKDROP2
  }>
  %total_paid PROC:<{
    $UserPurchase$_contract_load INLINECALLDICT
    $UserPurchase$_fun_total_paid INLINECALLDICT
    9 1 BLKDROP2
  }>
  %purchase_id_counter PROC:<{
    $UserPurchase$_contract_load INLINECALLDICT
    $UserPurchase$_fun_purchase_id_counter INLINECALLDICT
    9 1 BLKDROP2
  }>
  %purchase_details PROC:<{
    $UserPurchase$_contract_load INLINECALLDICT
    9 ROLL
    $UserPurchase$_fun_purchase_details INLINECALLDICT
    9 1 BLKDROP2
    $PurchaseRecord$_to_opt_external INLINECALLDICT
  }>
  %is_refunded PROC:<{
    $UserPurchase$_contract_load INLINECALLDICT
    9 ROLL
    $UserPurchase$_fun_is_refunded INLINECALLDICT
    9 1 BLKDROP2
  }>
  %signature_verified_purchases PROC:<{
    $UserPurchase$_contract_load INLINECALLDICT
    $UserPurchase$_fun_signature_verified_purchases INLINECALLDICT
    9 1 BLKDROP2
  }>
  %purchase_method_stats PROC:<{
    $UserPurchase$_contract_load INLINECALLDICT
    $UserPurchase$_fun_purchase_method_stats INLINECALLDICT
    9 1 BLKDROP2
  }>
  %purchases_by_round PROC:<{
    $UserPurchase$_contract_load INLINECALLDICT
    9 ROLL
    $UserPurchase$_fun_purchases_by_round INLINECALLDICT
    9 1 BLKDROP2
  }>
  %round_total_amount PROC:<{
    $UserPurchase$_contract_load INLINECALLDICT
    9 ROLL
    $UserPurchase$_fun_round_total_amount INLINECALLDICT
    9 1 BLKDROP2
  }>
  %round_total_tokens PROC:<{
    $UserPurchase$_contract_load INLINECALLDICT
    9 ROLL
    $UserPurchase$_fun_round_total_tokens INLINECALLDICT
    9 1 BLKDROP2
  }>
  %round_purchase_count PROC:<{
    $UserPurchase$_contract_load INLINECALLDICT
    9 ROLL
    $UserPurchase$_fun_round_purchase_count INLINECALLDICT
    9 1 BLKDROP2
  }>
  %participated_rounds PROC:<{
    $UserPurchase$_contract_load INLINECALLDICT
    $UserPurchase$_fun_participated_rounds INLINECALLDICT
    9 1 BLKDROP2
  }>
  %participated_in_round PROC:<{
    $UserPurchase$_contract_load INLINECALLDICT
    9 ROLL
    $UserPurchase$_fun_participated_in_round INLINECALLDICT
    9 1 BLKDROP2
  }>
  %round_count PROC:<{
    $UserPurchase$_contract_load INLINECALLDICT
    $UserPurchase$_fun_round_count INLINECALLDICT
    9 1 BLKDROP2
  }>
  %all_records PROC:<{
    $UserPurchase$_contract_load INLINECALLDICT
    $UserPurchase$_fun_all_records INLINECALLDICT
    9 1 BLKDROP2
  }>
  %owner PROC:<{
    $UserPurchase$_contract_load INLINECALLDICT
    $UserPurchase$_fun_owner INLINECALLDICT
    9 1 BLKDROP2
  }>
  recv_internal PROC:<{
    c2 SAVE
    SAMEALTSAVE
    SWAP
    CTOS
    2 PUSHINT
    SDSKIPFIRST
    1 LDI
    1 LDI
    LDMSGADDR
    OVER
    s3 s4 XCHG
    s6 s6 XCHG2
    4 TUPLE
    __tact_context SETGLOB
    s0 s2 XCHG
    __tact_context_sender SETGLOB
    $UserPurchase$_contract_load INLINECALLDICT
    s0 s10 XCHG
    IFJMP:<{
      10 BLKDROP
    }>
    s8 PUSH
    SBITS
    31 GTINT
    IF:<{
      s0 s8 XCHG
      32 LDU
      OVER
      1098075148 PUSHINT
      EQUAL
      IFJMP:<{
        NIP
        LDMSGADDR
        LDVARUINT16
        LDVARUINT16
        8 LDU
        8 LDU
        64 LDU
        32 LDU
        LDVARUINT16
        DROP
        49729 PUSHINT
        __tact_context_get_sender INLINECALLDICT
        s15 PUSH
        SDEQ
        THROWANYIFNOT
        s0 s15 XCHG
        INC
            NOW
        s1 s0 s5 PUXC2
        s8 PUSH
        s6 s10 XCHG
        s5 s8 XCPU
        s5 s0 XCPU
        s4 s5 XCHG
        s3 s4 XCHG
        s3 s12 XCHG
        s0 20 s() XCHG
        $PurchaseRecord$_constructor_id_user_amount_tokens_timestamp_currency_purchase_method_nonce_round_number_usdt_equivalent_amount INLINECALLDICT
        s0 s9 XCHG
        s0 s8 XCHG
        s0 s7 XCHG
        s0 s6 XCHG
        s0 s5 XCHG
        s0 s4 XCHG
        s3 s1 s3 XCHG3
        257 PUSHINT
        s2 s10 XCHG2
        NEWC
        $PurchaseRecord$_store_cell INLINECALLDICT
        s4 PUSH
        s3 s7 XCHG
        SWAP
        __tact_dict_set_int_cell INLINECALLDICT
        s6 s4 XCHG2
        ADD
        s4 s5 XCHG2
        ADD
        s1 s9 XCHG
        257 PUSHINT
        s0 s9 XCHG2
        TRUE
        1 PUSHINT
        __tact_dict_set_int_int INLINECALLDICT
        __tact_context_get_sender INLINECALLDICT
            NEWC
            x{42_} STSLICECONST // .storeUint(0x10, 6)
            STSLICE          // .storeAddress(to)
            0 PUSHINT        // 0
            111 STUR         // .storeUint(0, 111)
                             // 4 zeros for coins and 107 zeros for lt, fees, etc.
            ENDC
            66 PUSHINT       // SendRemainingValue | SendIgnoreErrors
            SENDRAWMSG
        s6 s8 XCHG
        s5 s7 XCHG
        s4 s6 XCHG
        s1 s3 XCHG
        s1 s5 XCHG
        s4 s4 s0 XCHG3
        $UserPurchase$_contract_store INLINECALLDICT
        RETALT
      }>
      SWAP
      836089260 PUSHINT
      EQUAL
      IFJMP:<{
        32 LDU
        DROP
        49729 PUSHINT
        __tact_context_get_sender INLINECALLDICT
        s7 PUSH
        SDEQ
        THROWANYIFNOT
        s2 PUSH
        257 PUSHINT
        s2 PUSH
        __tact_dict_get_int_cell INLINECALLDICT
        $PurchaseRecord$_load_opt INLINECALLDICT
        39144 PUSHINT
        OVER
        ISNULL
        NOT
        THROWANYIFNOT
        22411 PUSHINT
        257 PUSHINT
        s4 s(-1) s(-1) PUXCPU
        s5 s(-1) PUXC
        __tact_dict_get_int_int INLINECALLDICT
        ISNULL
        THROWANYIFNOT
        $PurchaseRecord$_not_null INLINECALLDICT
        s5 POP
        s7 POP
        2DROP
        s5 POP
        257 PUSHINT
        DUP
        s3 s8 XCHG
        s1 s7 s0 XCHG3
        s4 s(-1) PUXC
        __tact_dict_set_int_int INLINECALLDICT
        s8 s5 XCHG2
        SUB
        s6 s7 XCPU
        SUB
        50000000 PUSHINT
        2 PUSHINT
        FALSE
        s10 PUSH
        5 MULCONST
        100 PUSHINT
        DIV
        s12 PUSH
        s0 s5 XCHG
        s4 s12 XCHG
        s7 s3 s8 XCHG3
        s1 s6 XCHG
        $ProcessRefund$_constructor_user_amount_fee_currency_round_number_usdt_equivalent_amount INLINECALLDICT
        NEWC
        $ProcessRefund$_store_cell INLINECALLDICT
        s9 PUSH
        s0 s4 XCHG
        s0 s8 XCHG
        s3 s3 XCHG2
        $SendParameters$_constructor_to_value_mode_bounce_body INLINECALLDICT
            NEWC
            b{01} STSLICECONST  // store tag = $0 and ihr_disabled = true
            1 STI               // store `bounce`
            b{000} STSLICECONST // store bounced = false and src = addr_none
            STSLICE             // store `to`
            SWAP
            STGRAMS             // store `value`
            105 PUSHINT         // 1 + 4 + 4 + 64 + 32
            STZEROES            // store currency_collection, ihr_fee, fwd_fee, created_lt and created_at
            // → Stack state
            // s0: Builder
            // s1: `data`
            // s2: `code`
            // s3: `body`
            // s4: `mode`
            // Group 2: Placing the Builder after code and data, then checking those for nullability
            s2 XCHG0
            DUP2
            ISNULL
            SWAP
            ISNULL
            AND
            // → Stack state
            // s0: -1 (true) if `data` and `code` are both null, 0 (false) otherwise
            // s1: `code`
            // s2: `data`
            // s3: Builder
            // s4: `body`
            // s5: `mode`
            // Group 3: Left branch of the IFELSE, executed if s0 is -1 (true)
            <{
                DROP2 // drop `data` and `code`, since either of those is null
                b{0} STSLICECONST
            }> PUSHCONT
            // Group 3: Right branch of the IFELSE, executed if s0 is 0 (false)
            <{
                // _ split_depth:(Maybe (## 5))
                //   special:(Maybe TickTock)
                //   code:(Maybe ^Cell)
                //   data:(Maybe ^Cell)
                //   library:(Maybe ^Cell)
                // = StateInit;
                ROT                // place message Builder on top
                b{10} STSLICECONST // store Maybe = true, Either = false
                // Start composing inlined StateInit
                b{00} STSLICECONST // store split_depth and special first
                STDICT             // store code
                STDICT             // store data
                b{0} STSLICECONST  // store library
            }> PUSHCONT
            // Group 3: IFELSE that does the branching shown above
            IFELSE
            // → Stack state
            // s0: Builder
            // s1: null or StateInit
            // s2: `body`
            // s3: `mode`
            // Group 4: Finalizing the message
            STDICT // store `body` as ref with an extra Maybe bit, since `body` might be null
            ENDC
            // → Stack state
            // s0: Cell
            // s1: `mode`
            // Group 5: Sending the message, with `mode` on top
            SWAP
            SENDRAWMSG // https://github.com/tact-lang/tact/issues/1558
        s6 s8 XCHG
        s5 s7 XCHG
        s4 s6 XCHG
        s0 s5 s5 XCHG3
        s0 s3 XCHG
        s0 s4 XCHG
        $UserPurchase$_contract_store INLINECALLDICT
        RETALT
      }>
      s0 s8 XCHG
    }>
    s0 s8 XCHG
    HASHSU
    87651377378655782259476005716871872139955156637688197487361824677095371478895 PUSHINT
    EQUAL
    IFJMP:<{
      s6 s8 XCHG
      2 6 BLKSWAP
      $UserPurchase$_contract_store INLINECALLDICT
    }>
    9 BLKDROP
    130 THROW
  }>
  __tact_selector_hack PROC:<{
    @atend @ 1 {
            execute current@ context@ current!
            {
                // The core idea of this function is to save gas by avoiding unnecessary dict jump, when recv_internal/recv_external is called
                // We want to extract recv_internal/recv_external from the dict and select needed function
                // not by jumping to the needed function by it's index, but by using usual IF statements.
                }END> b> // Close previous builder, now we have a cell of previous code on top of the stack
                <{ // Start of the new code builder
                    SETCP0
                    // Swap the new code builder with the previous code, now we have previous code on top of the stack
                    swap
                    // Transform cell to slice and load first ref from the previous code, now we have the dict on top of the stack
                    <s ref@
                    // Extract the recv_internal from the dict
                    dup 0 swap @procdictkeylen idict@ { "internal shortcut error" abort } ifnot
                    swap
                    // Delete the recv_internal from the dict
                    0 swap @procdictkeylen idict- drop
                    // Delete the recv_external from the dict (it's okay if it's not there)
                    -1 swap @procdictkeylen idict- drop
                    // Delete the __tact_selector_hack from the dict
                    65535 swap @procdictkeylen idict- drop
                    // Bring the code builder from the bottom of the stack
                    // because if recv_external extraction is optional, and the number of elements on the stack is not fixed
                    depth 1- roll
                    // Swap with the dict from which we extracted recv_internal and (maybe) recv_external
                    swap
                    // Check if the dict is empty
                    dup null?
                    // Store a copy of this flag in the bottom of the stack
                    dup depth 1- -roll
                    {
                        // If the dict is empty, just drop it (it will be null if it's empty)
                        drop
                    }
                    {
                        // If the dict is not empty, prepare continuation to be stored in c3
                        <{
                            // Save this dict as first ref in this continuation, it will be pushed in runtime by DICTPUSHCONST
                            swap @procdictkeylen DICTPUSHCONST
                            // Jump to the needed function by it's index
                            DICTIGETJMPZ
                            // If such key is not found, throw 11 along with the key as an argument
                            11 THROWARG
                        }> PUSHCONT
                        // Store the continuation in c3
                        c3 POP
                    } cond
                    // Function id is on top of the (runtime) stack
                    DUP IFNOTJMP:<{
                        // place recv_internal here
                        DROP swap @addop
                    }>
                    // Bring back the flag, indicating if the dict is empty or not from the bottom of the stack
                    depth 1- roll
                    {
                        // If the dict is empty, throw 11
                        11 THROWARG
                    }
                    {
                        // If the dict is not empty, jump to continuation from c3
                        c3 PUSH JMPX
                    } cond
                }> b>
            } : }END>c
            current@ context! current!
        } does @atend !
  }>
}END>c
