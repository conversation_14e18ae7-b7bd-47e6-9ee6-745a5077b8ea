import {
    Cell,
    Slice,
    Address,
    Builder,
    begin<PERSON>ell,
    ComputeError,
    <PERSON>pleItem,
    TupleReader,
    Dictionary,
    contractAddress,
    address,
    ContractProvider,
    Sender,
    Contract,
    ContractAB<PERSON>,
    ABIType,
    ABIGetter,
    ABIReceiver,
    TupleBuilder,
    DictionaryValue
} from '@ton/core';

export type DataSize = {
    $$type: 'DataSize';
    cells: bigint;
    bits: bigint;
    refs: bigint;
}

export function storeDataSize(src: DataSize) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeInt(src.cells, 257);
        b_0.storeInt(src.bits, 257);
        b_0.storeInt(src.refs, 257);
    };
}

export function loadDataSize(slice: Slice) {
    const sc_0 = slice;
    const _cells = sc_0.loadIntBig(257);
    const _bits = sc_0.loadIntBig(257);
    const _refs = sc_0.loadIntBig(257);
    return { $$type: 'DataSize' as const, cells: _cells, bits: _bits, refs: _refs };
}

export function loadTupleDataSize(source: <PERSON>pleReader) {
    const _cells = source.readBigNumber();
    const _bits = source.readBigNumber();
    const _refs = source.readBigNumber();
    return { $$type: 'DataSize' as const, cells: _cells, bits: _bits, refs: _refs };
}

export function loadGetterTupleDataSize(source: TupleReader) {
    const _cells = source.readBigNumber();
    const _bits = source.readBigNumber();
    const _refs = source.readBigNumber();
    return { $$type: 'DataSize' as const, cells: _cells, bits: _bits, refs: _refs };
}

export function storeTupleDataSize(source: DataSize) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.cells);
    builder.writeNumber(source.bits);
    builder.writeNumber(source.refs);
    return builder.build();
}

export function dictValueParserDataSize(): DictionaryValue<DataSize> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeDataSize(src)).endCell());
        },
        parse: (src) => {
            return loadDataSize(src.loadRef().beginParse());
        }
    }
}

export type SignedBundle = {
    $$type: 'SignedBundle';
    signature: Buffer;
    signedData: Slice;
}

export function storeSignedBundle(src: SignedBundle) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeBuffer(src.signature);
        b_0.storeBuilder(src.signedData.asBuilder());
    };
}

export function loadSignedBundle(slice: Slice) {
    const sc_0 = slice;
    const _signature = sc_0.loadBuffer(64);
    const _signedData = sc_0;
    return { $$type: 'SignedBundle' as const, signature: _signature, signedData: _signedData };
}

export function loadTupleSignedBundle(source: TupleReader) {
    const _signature = source.readBuffer();
    const _signedData = source.readCell().asSlice();
    return { $$type: 'SignedBundle' as const, signature: _signature, signedData: _signedData };
}

export function loadGetterTupleSignedBundle(source: TupleReader) {
    const _signature = source.readBuffer();
    const _signedData = source.readCell().asSlice();
    return { $$type: 'SignedBundle' as const, signature: _signature, signedData: _signedData };
}

export function storeTupleSignedBundle(source: SignedBundle) {
    const builder = new TupleBuilder();
    builder.writeBuffer(source.signature);
    builder.writeSlice(source.signedData.asCell());
    return builder.build();
}

export function dictValueParserSignedBundle(): DictionaryValue<SignedBundle> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeSignedBundle(src)).endCell());
        },
        parse: (src) => {
            return loadSignedBundle(src.loadRef().beginParse());
        }
    }
}

export type StateInit = {
    $$type: 'StateInit';
    code: Cell;
    data: Cell;
}

export function storeStateInit(src: StateInit) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeRef(src.code);
        b_0.storeRef(src.data);
    };
}

export function loadStateInit(slice: Slice) {
    const sc_0 = slice;
    const _code = sc_0.loadRef();
    const _data = sc_0.loadRef();
    return { $$type: 'StateInit' as const, code: _code, data: _data };
}

export function loadTupleStateInit(source: TupleReader) {
    const _code = source.readCell();
    const _data = source.readCell();
    return { $$type: 'StateInit' as const, code: _code, data: _data };
}

export function loadGetterTupleStateInit(source: TupleReader) {
    const _code = source.readCell();
    const _data = source.readCell();
    return { $$type: 'StateInit' as const, code: _code, data: _data };
}

export function storeTupleStateInit(source: StateInit) {
    const builder = new TupleBuilder();
    builder.writeCell(source.code);
    builder.writeCell(source.data);
    return builder.build();
}

export function dictValueParserStateInit(): DictionaryValue<StateInit> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeStateInit(src)).endCell());
        },
        parse: (src) => {
            return loadStateInit(src.loadRef().beginParse());
        }
    }
}

export type Context = {
    $$type: 'Context';
    bounceable: boolean;
    sender: Address;
    value: bigint;
    raw: Slice;
}

export function storeContext(src: Context) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeBit(src.bounceable);
        b_0.storeAddress(src.sender);
        b_0.storeInt(src.value, 257);
        b_0.storeRef(src.raw.asCell());
    };
}

export function loadContext(slice: Slice) {
    const sc_0 = slice;
    const _bounceable = sc_0.loadBit();
    const _sender = sc_0.loadAddress();
    const _value = sc_0.loadIntBig(257);
    const _raw = sc_0.loadRef().asSlice();
    return { $$type: 'Context' as const, bounceable: _bounceable, sender: _sender, value: _value, raw: _raw };
}

export function loadTupleContext(source: TupleReader) {
    const _bounceable = source.readBoolean();
    const _sender = source.readAddress();
    const _value = source.readBigNumber();
    const _raw = source.readCell().asSlice();
    return { $$type: 'Context' as const, bounceable: _bounceable, sender: _sender, value: _value, raw: _raw };
}

export function loadGetterTupleContext(source: TupleReader) {
    const _bounceable = source.readBoolean();
    const _sender = source.readAddress();
    const _value = source.readBigNumber();
    const _raw = source.readCell().asSlice();
    return { $$type: 'Context' as const, bounceable: _bounceable, sender: _sender, value: _value, raw: _raw };
}

export function storeTupleContext(source: Context) {
    const builder = new TupleBuilder();
    builder.writeBoolean(source.bounceable);
    builder.writeAddress(source.sender);
    builder.writeNumber(source.value);
    builder.writeSlice(source.raw.asCell());
    return builder.build();
}

export function dictValueParserContext(): DictionaryValue<Context> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeContext(src)).endCell());
        },
        parse: (src) => {
            return loadContext(src.loadRef().beginParse());
        }
    }
}

export type SendParameters = {
    $$type: 'SendParameters';
    mode: bigint;
    body: Cell | null;
    code: Cell | null;
    data: Cell | null;
    value: bigint;
    to: Address;
    bounce: boolean;
}

export function storeSendParameters(src: SendParameters) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeInt(src.mode, 257);
        if (src.body !== null && src.body !== undefined) { b_0.storeBit(true).storeRef(src.body); } else { b_0.storeBit(false); }
        if (src.code !== null && src.code !== undefined) { b_0.storeBit(true).storeRef(src.code); } else { b_0.storeBit(false); }
        if (src.data !== null && src.data !== undefined) { b_0.storeBit(true).storeRef(src.data); } else { b_0.storeBit(false); }
        b_0.storeInt(src.value, 257);
        b_0.storeAddress(src.to);
        b_0.storeBit(src.bounce);
    };
}

export function loadSendParameters(slice: Slice) {
    const sc_0 = slice;
    const _mode = sc_0.loadIntBig(257);
    const _body = sc_0.loadBit() ? sc_0.loadRef() : null;
    const _code = sc_0.loadBit() ? sc_0.loadRef() : null;
    const _data = sc_0.loadBit() ? sc_0.loadRef() : null;
    const _value = sc_0.loadIntBig(257);
    const _to = sc_0.loadAddress();
    const _bounce = sc_0.loadBit();
    return { $$type: 'SendParameters' as const, mode: _mode, body: _body, code: _code, data: _data, value: _value, to: _to, bounce: _bounce };
}

export function loadTupleSendParameters(source: TupleReader) {
    const _mode = source.readBigNumber();
    const _body = source.readCellOpt();
    const _code = source.readCellOpt();
    const _data = source.readCellOpt();
    const _value = source.readBigNumber();
    const _to = source.readAddress();
    const _bounce = source.readBoolean();
    return { $$type: 'SendParameters' as const, mode: _mode, body: _body, code: _code, data: _data, value: _value, to: _to, bounce: _bounce };
}

export function loadGetterTupleSendParameters(source: TupleReader) {
    const _mode = source.readBigNumber();
    const _body = source.readCellOpt();
    const _code = source.readCellOpt();
    const _data = source.readCellOpt();
    const _value = source.readBigNumber();
    const _to = source.readAddress();
    const _bounce = source.readBoolean();
    return { $$type: 'SendParameters' as const, mode: _mode, body: _body, code: _code, data: _data, value: _value, to: _to, bounce: _bounce };
}

export function storeTupleSendParameters(source: SendParameters) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.mode);
    builder.writeCell(source.body);
    builder.writeCell(source.code);
    builder.writeCell(source.data);
    builder.writeNumber(source.value);
    builder.writeAddress(source.to);
    builder.writeBoolean(source.bounce);
    return builder.build();
}

export function dictValueParserSendParameters(): DictionaryValue<SendParameters> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeSendParameters(src)).endCell());
        },
        parse: (src) => {
            return loadSendParameters(src.loadRef().beginParse());
        }
    }
}

export type MessageParameters = {
    $$type: 'MessageParameters';
    mode: bigint;
    body: Cell | null;
    value: bigint;
    to: Address;
    bounce: boolean;
}

export function storeMessageParameters(src: MessageParameters) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeInt(src.mode, 257);
        if (src.body !== null && src.body !== undefined) { b_0.storeBit(true).storeRef(src.body); } else { b_0.storeBit(false); }
        b_0.storeInt(src.value, 257);
        b_0.storeAddress(src.to);
        b_0.storeBit(src.bounce);
    };
}

export function loadMessageParameters(slice: Slice) {
    const sc_0 = slice;
    const _mode = sc_0.loadIntBig(257);
    const _body = sc_0.loadBit() ? sc_0.loadRef() : null;
    const _value = sc_0.loadIntBig(257);
    const _to = sc_0.loadAddress();
    const _bounce = sc_0.loadBit();
    return { $$type: 'MessageParameters' as const, mode: _mode, body: _body, value: _value, to: _to, bounce: _bounce };
}

export function loadTupleMessageParameters(source: TupleReader) {
    const _mode = source.readBigNumber();
    const _body = source.readCellOpt();
    const _value = source.readBigNumber();
    const _to = source.readAddress();
    const _bounce = source.readBoolean();
    return { $$type: 'MessageParameters' as const, mode: _mode, body: _body, value: _value, to: _to, bounce: _bounce };
}

export function loadGetterTupleMessageParameters(source: TupleReader) {
    const _mode = source.readBigNumber();
    const _body = source.readCellOpt();
    const _value = source.readBigNumber();
    const _to = source.readAddress();
    const _bounce = source.readBoolean();
    return { $$type: 'MessageParameters' as const, mode: _mode, body: _body, value: _value, to: _to, bounce: _bounce };
}

export function storeTupleMessageParameters(source: MessageParameters) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.mode);
    builder.writeCell(source.body);
    builder.writeNumber(source.value);
    builder.writeAddress(source.to);
    builder.writeBoolean(source.bounce);
    return builder.build();
}

export function dictValueParserMessageParameters(): DictionaryValue<MessageParameters> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeMessageParameters(src)).endCell());
        },
        parse: (src) => {
            return loadMessageParameters(src.loadRef().beginParse());
        }
    }
}

export type DeployParameters = {
    $$type: 'DeployParameters';
    mode: bigint;
    body: Cell | null;
    value: bigint;
    bounce: boolean;
    init: StateInit;
}

export function storeDeployParameters(src: DeployParameters) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeInt(src.mode, 257);
        if (src.body !== null && src.body !== undefined) { b_0.storeBit(true).storeRef(src.body); } else { b_0.storeBit(false); }
        b_0.storeInt(src.value, 257);
        b_0.storeBit(src.bounce);
        b_0.store(storeStateInit(src.init));
    };
}

export function loadDeployParameters(slice: Slice) {
    const sc_0 = slice;
    const _mode = sc_0.loadIntBig(257);
    const _body = sc_0.loadBit() ? sc_0.loadRef() : null;
    const _value = sc_0.loadIntBig(257);
    const _bounce = sc_0.loadBit();
    const _init = loadStateInit(sc_0);
    return { $$type: 'DeployParameters' as const, mode: _mode, body: _body, value: _value, bounce: _bounce, init: _init };
}

export function loadTupleDeployParameters(source: TupleReader) {
    const _mode = source.readBigNumber();
    const _body = source.readCellOpt();
    const _value = source.readBigNumber();
    const _bounce = source.readBoolean();
    const _init = loadTupleStateInit(source);
    return { $$type: 'DeployParameters' as const, mode: _mode, body: _body, value: _value, bounce: _bounce, init: _init };
}

export function loadGetterTupleDeployParameters(source: TupleReader) {
    const _mode = source.readBigNumber();
    const _body = source.readCellOpt();
    const _value = source.readBigNumber();
    const _bounce = source.readBoolean();
    const _init = loadGetterTupleStateInit(source);
    return { $$type: 'DeployParameters' as const, mode: _mode, body: _body, value: _value, bounce: _bounce, init: _init };
}

export function storeTupleDeployParameters(source: DeployParameters) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.mode);
    builder.writeCell(source.body);
    builder.writeNumber(source.value);
    builder.writeBoolean(source.bounce);
    builder.writeTuple(storeTupleStateInit(source.init));
    return builder.build();
}

export function dictValueParserDeployParameters(): DictionaryValue<DeployParameters> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeDeployParameters(src)).endCell());
        },
        parse: (src) => {
            return loadDeployParameters(src.loadRef().beginParse());
        }
    }
}

export type StdAddress = {
    $$type: 'StdAddress';
    workchain: bigint;
    address: bigint;
}

export function storeStdAddress(src: StdAddress) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeInt(src.workchain, 8);
        b_0.storeUint(src.address, 256);
    };
}

export function loadStdAddress(slice: Slice) {
    const sc_0 = slice;
    const _workchain = sc_0.loadIntBig(8);
    const _address = sc_0.loadUintBig(256);
    return { $$type: 'StdAddress' as const, workchain: _workchain, address: _address };
}

export function loadTupleStdAddress(source: TupleReader) {
    const _workchain = source.readBigNumber();
    const _address = source.readBigNumber();
    return { $$type: 'StdAddress' as const, workchain: _workchain, address: _address };
}

export function loadGetterTupleStdAddress(source: TupleReader) {
    const _workchain = source.readBigNumber();
    const _address = source.readBigNumber();
    return { $$type: 'StdAddress' as const, workchain: _workchain, address: _address };
}

export function storeTupleStdAddress(source: StdAddress) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.workchain);
    builder.writeNumber(source.address);
    return builder.build();
}

export function dictValueParserStdAddress(): DictionaryValue<StdAddress> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeStdAddress(src)).endCell());
        },
        parse: (src) => {
            return loadStdAddress(src.loadRef().beginParse());
        }
    }
}

export type VarAddress = {
    $$type: 'VarAddress';
    workchain: bigint;
    address: Slice;
}

export function storeVarAddress(src: VarAddress) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeInt(src.workchain, 32);
        b_0.storeRef(src.address.asCell());
    };
}

export function loadVarAddress(slice: Slice) {
    const sc_0 = slice;
    const _workchain = sc_0.loadIntBig(32);
    const _address = sc_0.loadRef().asSlice();
    return { $$type: 'VarAddress' as const, workchain: _workchain, address: _address };
}

export function loadTupleVarAddress(source: TupleReader) {
    const _workchain = source.readBigNumber();
    const _address = source.readCell().asSlice();
    return { $$type: 'VarAddress' as const, workchain: _workchain, address: _address };
}

export function loadGetterTupleVarAddress(source: TupleReader) {
    const _workchain = source.readBigNumber();
    const _address = source.readCell().asSlice();
    return { $$type: 'VarAddress' as const, workchain: _workchain, address: _address };
}

export function storeTupleVarAddress(source: VarAddress) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.workchain);
    builder.writeSlice(source.address.asCell());
    return builder.build();
}

export function dictValueParserVarAddress(): DictionaryValue<VarAddress> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeVarAddress(src)).endCell());
        },
        parse: (src) => {
            return loadVarAddress(src.loadRef().beginParse());
        }
    }
}

export type BasechainAddress = {
    $$type: 'BasechainAddress';
    hash: bigint | null;
}

export function storeBasechainAddress(src: BasechainAddress) {
    return (builder: Builder) => {
        const b_0 = builder;
        if (src.hash !== null && src.hash !== undefined) { b_0.storeBit(true).storeInt(src.hash, 257); } else { b_0.storeBit(false); }
    };
}

export function loadBasechainAddress(slice: Slice) {
    const sc_0 = slice;
    const _hash = sc_0.loadBit() ? sc_0.loadIntBig(257) : null;
    return { $$type: 'BasechainAddress' as const, hash: _hash };
}

export function loadTupleBasechainAddress(source: TupleReader) {
    const _hash = source.readBigNumberOpt();
    return { $$type: 'BasechainAddress' as const, hash: _hash };
}

export function loadGetterTupleBasechainAddress(source: TupleReader) {
    const _hash = source.readBigNumberOpt();
    return { $$type: 'BasechainAddress' as const, hash: _hash };
}

export function storeTupleBasechainAddress(source: BasechainAddress) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.hash);
    return builder.build();
}

export function dictValueParserBasechainAddress(): DictionaryValue<BasechainAddress> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeBasechainAddress(src)).endCell());
        },
        parse: (src) => {
            return loadBasechainAddress(src.loadRef().beginParse());
        }
    }
}

export type ChangeOwner = {
    $$type: 'ChangeOwner';
    queryId: bigint;
    newOwner: Address;
}

export function storeChangeOwner(src: ChangeOwner) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(2174598809, 32);
        b_0.storeUint(src.queryId, 64);
        b_0.storeAddress(src.newOwner);
    };
}

export function loadChangeOwner(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 2174598809) { throw Error('Invalid prefix'); }
    const _queryId = sc_0.loadUintBig(64);
    const _newOwner = sc_0.loadAddress();
    return { $$type: 'ChangeOwner' as const, queryId: _queryId, newOwner: _newOwner };
}

export function loadTupleChangeOwner(source: TupleReader) {
    const _queryId = source.readBigNumber();
    const _newOwner = source.readAddress();
    return { $$type: 'ChangeOwner' as const, queryId: _queryId, newOwner: _newOwner };
}

export function loadGetterTupleChangeOwner(source: TupleReader) {
    const _queryId = source.readBigNumber();
    const _newOwner = source.readAddress();
    return { $$type: 'ChangeOwner' as const, queryId: _queryId, newOwner: _newOwner };
}

export function storeTupleChangeOwner(source: ChangeOwner) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.queryId);
    builder.writeAddress(source.newOwner);
    return builder.build();
}

export function dictValueParserChangeOwner(): DictionaryValue<ChangeOwner> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeChangeOwner(src)).endCell());
        },
        parse: (src) => {
            return loadChangeOwner(src.loadRef().beginParse());
        }
    }
}

export type ChangeOwnerOk = {
    $$type: 'ChangeOwnerOk';
    queryId: bigint;
    newOwner: Address;
}

export function storeChangeOwnerOk(src: ChangeOwnerOk) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(846932810, 32);
        b_0.storeUint(src.queryId, 64);
        b_0.storeAddress(src.newOwner);
    };
}

export function loadChangeOwnerOk(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 846932810) { throw Error('Invalid prefix'); }
    const _queryId = sc_0.loadUintBig(64);
    const _newOwner = sc_0.loadAddress();
    return { $$type: 'ChangeOwnerOk' as const, queryId: _queryId, newOwner: _newOwner };
}

export function loadTupleChangeOwnerOk(source: TupleReader) {
    const _queryId = source.readBigNumber();
    const _newOwner = source.readAddress();
    return { $$type: 'ChangeOwnerOk' as const, queryId: _queryId, newOwner: _newOwner };
}

export function loadGetterTupleChangeOwnerOk(source: TupleReader) {
    const _queryId = source.readBigNumber();
    const _newOwner = source.readAddress();
    return { $$type: 'ChangeOwnerOk' as const, queryId: _queryId, newOwner: _newOwner };
}

export function storeTupleChangeOwnerOk(source: ChangeOwnerOk) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.queryId);
    builder.writeAddress(source.newOwner);
    return builder.build();
}

export function dictValueParserChangeOwnerOk(): DictionaryValue<ChangeOwnerOk> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeChangeOwnerOk(src)).endCell());
        },
        parse: (src) => {
            return loadChangeOwnerOk(src.loadRef().beginParse());
        }
    }
}

export type Deploy = {
    $$type: 'Deploy';
    queryId: bigint;
}

export function storeDeploy(src: Deploy) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(2490013878, 32);
        b_0.storeUint(src.queryId, 64);
    };
}

export function loadDeploy(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 2490013878) { throw Error('Invalid prefix'); }
    const _queryId = sc_0.loadUintBig(64);
    return { $$type: 'Deploy' as const, queryId: _queryId };
}

export function loadTupleDeploy(source: TupleReader) {
    const _queryId = source.readBigNumber();
    return { $$type: 'Deploy' as const, queryId: _queryId };
}

export function loadGetterTupleDeploy(source: TupleReader) {
    const _queryId = source.readBigNumber();
    return { $$type: 'Deploy' as const, queryId: _queryId };
}

export function storeTupleDeploy(source: Deploy) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.queryId);
    return builder.build();
}

export function dictValueParserDeploy(): DictionaryValue<Deploy> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeDeploy(src)).endCell());
        },
        parse: (src) => {
            return loadDeploy(src.loadRef().beginParse());
        }
    }
}

export type DeployOk = {
    $$type: 'DeployOk';
    queryId: bigint;
}

export function storeDeployOk(src: DeployOk) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(2952335191, 32);
        b_0.storeUint(src.queryId, 64);
    };
}

export function loadDeployOk(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 2952335191) { throw Error('Invalid prefix'); }
    const _queryId = sc_0.loadUintBig(64);
    return { $$type: 'DeployOk' as const, queryId: _queryId };
}

export function loadTupleDeployOk(source: TupleReader) {
    const _queryId = source.readBigNumber();
    return { $$type: 'DeployOk' as const, queryId: _queryId };
}

export function loadGetterTupleDeployOk(source: TupleReader) {
    const _queryId = source.readBigNumber();
    return { $$type: 'DeployOk' as const, queryId: _queryId };
}

export function storeTupleDeployOk(source: DeployOk) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.queryId);
    return builder.build();
}

export function dictValueParserDeployOk(): DictionaryValue<DeployOk> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeDeployOk(src)).endCell());
        },
        parse: (src) => {
            return loadDeployOk(src.loadRef().beginParse());
        }
    }
}

export type FactoryDeploy = {
    $$type: 'FactoryDeploy';
    queryId: bigint;
    cashback: Address;
}

export function storeFactoryDeploy(src: FactoryDeploy) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(1829761339, 32);
        b_0.storeUint(src.queryId, 64);
        b_0.storeAddress(src.cashback);
    };
}

export function loadFactoryDeploy(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 1829761339) { throw Error('Invalid prefix'); }
    const _queryId = sc_0.loadUintBig(64);
    const _cashback = sc_0.loadAddress();
    return { $$type: 'FactoryDeploy' as const, queryId: _queryId, cashback: _cashback };
}

export function loadTupleFactoryDeploy(source: TupleReader) {
    const _queryId = source.readBigNumber();
    const _cashback = source.readAddress();
    return { $$type: 'FactoryDeploy' as const, queryId: _queryId, cashback: _cashback };
}

export function loadGetterTupleFactoryDeploy(source: TupleReader) {
    const _queryId = source.readBigNumber();
    const _cashback = source.readAddress();
    return { $$type: 'FactoryDeploy' as const, queryId: _queryId, cashback: _cashback };
}

export function storeTupleFactoryDeploy(source: FactoryDeploy) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.queryId);
    builder.writeAddress(source.cashback);
    return builder.build();
}

export function dictValueParserFactoryDeploy(): DictionaryValue<FactoryDeploy> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeFactoryDeploy(src)).endCell());
        },
        parse: (src) => {
            return loadFactoryDeploy(src.loadRef().beginParse());
        }
    }
}

export type CreateUserPurchase = {
    $$type: 'CreateUserPurchase';
    user: Address;
    amount: bigint;
    tokens: bigint;
    currency: bigint;
    purchase_method: bigint;
    nonce: bigint;
    round_number: bigint;
    usdt_equivalent_amount: bigint;
}

export function storeCreateUserPurchase(src: CreateUserPurchase) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(1098075148, 32);
        b_0.storeAddress(src.user);
        b_0.storeCoins(src.amount);
        b_0.storeCoins(src.tokens);
        b_0.storeUint(src.currency, 8);
        b_0.storeUint(src.purchase_method, 8);
        b_0.storeUint(src.nonce, 64);
        b_0.storeUint(src.round_number, 32);
        b_0.storeCoins(src.usdt_equivalent_amount);
    };
}

export function loadCreateUserPurchase(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 1098075148) { throw Error('Invalid prefix'); }
    const _user = sc_0.loadAddress();
    const _amount = sc_0.loadCoins();
    const _tokens = sc_0.loadCoins();
    const _currency = sc_0.loadUintBig(8);
    const _purchase_method = sc_0.loadUintBig(8);
    const _nonce = sc_0.loadUintBig(64);
    const _round_number = sc_0.loadUintBig(32);
    const _usdt_equivalent_amount = sc_0.loadCoins();
    return { $$type: 'CreateUserPurchase' as const, user: _user, amount: _amount, tokens: _tokens, currency: _currency, purchase_method: _purchase_method, nonce: _nonce, round_number: _round_number, usdt_equivalent_amount: _usdt_equivalent_amount };
}

export function loadTupleCreateUserPurchase(source: TupleReader) {
    const _user = source.readAddress();
    const _amount = source.readBigNumber();
    const _tokens = source.readBigNumber();
    const _currency = source.readBigNumber();
    const _purchase_method = source.readBigNumber();
    const _nonce = source.readBigNumber();
    const _round_number = source.readBigNumber();
    const _usdt_equivalent_amount = source.readBigNumber();
    return { $$type: 'CreateUserPurchase' as const, user: _user, amount: _amount, tokens: _tokens, currency: _currency, purchase_method: _purchase_method, nonce: _nonce, round_number: _round_number, usdt_equivalent_amount: _usdt_equivalent_amount };
}

export function loadGetterTupleCreateUserPurchase(source: TupleReader) {
    const _user = source.readAddress();
    const _amount = source.readBigNumber();
    const _tokens = source.readBigNumber();
    const _currency = source.readBigNumber();
    const _purchase_method = source.readBigNumber();
    const _nonce = source.readBigNumber();
    const _round_number = source.readBigNumber();
    const _usdt_equivalent_amount = source.readBigNumber();
    return { $$type: 'CreateUserPurchase' as const, user: _user, amount: _amount, tokens: _tokens, currency: _currency, purchase_method: _purchase_method, nonce: _nonce, round_number: _round_number, usdt_equivalent_amount: _usdt_equivalent_amount };
}

export function storeTupleCreateUserPurchase(source: CreateUserPurchase) {
    const builder = new TupleBuilder();
    builder.writeAddress(source.user);
    builder.writeNumber(source.amount);
    builder.writeNumber(source.tokens);
    builder.writeNumber(source.currency);
    builder.writeNumber(source.purchase_method);
    builder.writeNumber(source.nonce);
    builder.writeNumber(source.round_number);
    builder.writeNumber(source.usdt_equivalent_amount);
    return builder.build();
}

export function dictValueParserCreateUserPurchase(): DictionaryValue<CreateUserPurchase> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeCreateUserPurchase(src)).endCell());
        },
        parse: (src) => {
            return loadCreateUserPurchase(src.loadRef().beginParse());
        }
    }
}

export type Refund = {
    $$type: 'Refund';
    purchase_id: bigint;
}

export function storeRefund(src: Refund) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(836089260, 32);
        b_0.storeUint(src.purchase_id, 32);
    };
}

export function loadRefund(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 836089260) { throw Error('Invalid prefix'); }
    const _purchase_id = sc_0.loadUintBig(32);
    return { $$type: 'Refund' as const, purchase_id: _purchase_id };
}

export function loadTupleRefund(source: TupleReader) {
    const _purchase_id = source.readBigNumber();
    return { $$type: 'Refund' as const, purchase_id: _purchase_id };
}

export function loadGetterTupleRefund(source: TupleReader) {
    const _purchase_id = source.readBigNumber();
    return { $$type: 'Refund' as const, purchase_id: _purchase_id };
}

export function storeTupleRefund(source: Refund) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.purchase_id);
    return builder.build();
}

export function dictValueParserRefund(): DictionaryValue<Refund> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeRefund(src)).endCell());
        },
        parse: (src) => {
            return loadRefund(src.loadRef().beginParse());
        }
    }
}

export type ProcessRefund = {
    $$type: 'ProcessRefund';
    user: Address;
    amount: bigint;
    fee: bigint;
    currency: bigint;
    round_number: bigint;
    usdt_equivalent_amount: bigint;
}

export function storeProcessRefund(src: ProcessRefund) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(929991442, 32);
        b_0.storeAddress(src.user);
        b_0.storeCoins(src.amount);
        b_0.storeCoins(src.fee);
        b_0.storeUint(src.currency, 8);
        b_0.storeUint(src.round_number, 32);
        b_0.storeCoins(src.usdt_equivalent_amount);
    };
}

export function loadProcessRefund(slice: Slice) {
    const sc_0 = slice;
    if (sc_0.loadUint(32) !== 929991442) { throw Error('Invalid prefix'); }
    const _user = sc_0.loadAddress();
    const _amount = sc_0.loadCoins();
    const _fee = sc_0.loadCoins();
    const _currency = sc_0.loadUintBig(8);
    const _round_number = sc_0.loadUintBig(32);
    const _usdt_equivalent_amount = sc_0.loadCoins();
    return { $$type: 'ProcessRefund' as const, user: _user, amount: _amount, fee: _fee, currency: _currency, round_number: _round_number, usdt_equivalent_amount: _usdt_equivalent_amount };
}

export function loadTupleProcessRefund(source: TupleReader) {
    const _user = source.readAddress();
    const _amount = source.readBigNumber();
    const _fee = source.readBigNumber();
    const _currency = source.readBigNumber();
    const _round_number = source.readBigNumber();
    const _usdt_equivalent_amount = source.readBigNumber();
    return { $$type: 'ProcessRefund' as const, user: _user, amount: _amount, fee: _fee, currency: _currency, round_number: _round_number, usdt_equivalent_amount: _usdt_equivalent_amount };
}

export function loadGetterTupleProcessRefund(source: TupleReader) {
    const _user = source.readAddress();
    const _amount = source.readBigNumber();
    const _fee = source.readBigNumber();
    const _currency = source.readBigNumber();
    const _round_number = source.readBigNumber();
    const _usdt_equivalent_amount = source.readBigNumber();
    return { $$type: 'ProcessRefund' as const, user: _user, amount: _amount, fee: _fee, currency: _currency, round_number: _round_number, usdt_equivalent_amount: _usdt_equivalent_amount };
}

export function storeTupleProcessRefund(source: ProcessRefund) {
    const builder = new TupleBuilder();
    builder.writeAddress(source.user);
    builder.writeNumber(source.amount);
    builder.writeNumber(source.fee);
    builder.writeNumber(source.currency);
    builder.writeNumber(source.round_number);
    builder.writeNumber(source.usdt_equivalent_amount);
    return builder.build();
}

export function dictValueParserProcessRefund(): DictionaryValue<ProcessRefund> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeProcessRefund(src)).endCell());
        },
        parse: (src) => {
            return loadProcessRefund(src.loadRef().beginParse());
        }
    }
}

export type PurchaseRecord = {
    $$type: 'PurchaseRecord';
    id: bigint;
    user: Address;
    amount: bigint;
    tokens: bigint;
    timestamp: bigint;
    currency: bigint;
    purchase_method: bigint;
    nonce: bigint;
    round_number: bigint;
    usdt_equivalent_amount: bigint;
}

export function storePurchaseRecord(src: PurchaseRecord) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeUint(src.id, 32);
        b_0.storeAddress(src.user);
        b_0.storeCoins(src.amount);
        b_0.storeCoins(src.tokens);
        b_0.storeUint(src.timestamp, 64);
        b_0.storeUint(src.currency, 8);
        b_0.storeUint(src.purchase_method, 8);
        b_0.storeUint(src.nonce, 64);
        b_0.storeUint(src.round_number, 32);
        b_0.storeCoins(src.usdt_equivalent_amount);
    };
}

export function loadPurchaseRecord(slice: Slice) {
    const sc_0 = slice;
    const _id = sc_0.loadUintBig(32);
    const _user = sc_0.loadAddress();
    const _amount = sc_0.loadCoins();
    const _tokens = sc_0.loadCoins();
    const _timestamp = sc_0.loadUintBig(64);
    const _currency = sc_0.loadUintBig(8);
    const _purchase_method = sc_0.loadUintBig(8);
    const _nonce = sc_0.loadUintBig(64);
    const _round_number = sc_0.loadUintBig(32);
    const _usdt_equivalent_amount = sc_0.loadCoins();
    return { $$type: 'PurchaseRecord' as const, id: _id, user: _user, amount: _amount, tokens: _tokens, timestamp: _timestamp, currency: _currency, purchase_method: _purchase_method, nonce: _nonce, round_number: _round_number, usdt_equivalent_amount: _usdt_equivalent_amount };
}

export function loadTuplePurchaseRecord(source: TupleReader) {
    const _id = source.readBigNumber();
    const _user = source.readAddress();
    const _amount = source.readBigNumber();
    const _tokens = source.readBigNumber();
    const _timestamp = source.readBigNumber();
    const _currency = source.readBigNumber();
    const _purchase_method = source.readBigNumber();
    const _nonce = source.readBigNumber();
    const _round_number = source.readBigNumber();
    const _usdt_equivalent_amount = source.readBigNumber();
    return { $$type: 'PurchaseRecord' as const, id: _id, user: _user, amount: _amount, tokens: _tokens, timestamp: _timestamp, currency: _currency, purchase_method: _purchase_method, nonce: _nonce, round_number: _round_number, usdt_equivalent_amount: _usdt_equivalent_amount };
}

export function loadGetterTuplePurchaseRecord(source: TupleReader) {
    const _id = source.readBigNumber();
    const _user = source.readAddress();
    const _amount = source.readBigNumber();
    const _tokens = source.readBigNumber();
    const _timestamp = source.readBigNumber();
    const _currency = source.readBigNumber();
    const _purchase_method = source.readBigNumber();
    const _nonce = source.readBigNumber();
    const _round_number = source.readBigNumber();
    const _usdt_equivalent_amount = source.readBigNumber();
    return { $$type: 'PurchaseRecord' as const, id: _id, user: _user, amount: _amount, tokens: _tokens, timestamp: _timestamp, currency: _currency, purchase_method: _purchase_method, nonce: _nonce, round_number: _round_number, usdt_equivalent_amount: _usdt_equivalent_amount };
}

export function storeTuplePurchaseRecord(source: PurchaseRecord) {
    const builder = new TupleBuilder();
    builder.writeNumber(source.id);
    builder.writeAddress(source.user);
    builder.writeNumber(source.amount);
    builder.writeNumber(source.tokens);
    builder.writeNumber(source.timestamp);
    builder.writeNumber(source.currency);
    builder.writeNumber(source.purchase_method);
    builder.writeNumber(source.nonce);
    builder.writeNumber(source.round_number);
    builder.writeNumber(source.usdt_equivalent_amount);
    return builder.build();
}

export function dictValueParserPurchaseRecord(): DictionaryValue<PurchaseRecord> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storePurchaseRecord(src)).endCell());
        },
        parse: (src) => {
            return loadPurchaseRecord(src.loadRef().beginParse());
        }
    }
}

export type UserPurchase$Data = {
    $$type: 'UserPurchase$Data';
    owner: Address;
    auction_address: Address;
    user_address: Address;
    total_purchased: bigint;
    total_paid: bigint;
    purchase_history: Dictionary<bigint, PurchaseRecord>;
    refund_history: Dictionary<bigint, bigint>;
    purchase_id_counter: bigint;
    participated_rounds: Dictionary<bigint, boolean>;
}

export function storeUserPurchase$Data(src: UserPurchase$Data) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeAddress(src.owner);
        b_0.storeAddress(src.auction_address);
        b_0.storeAddress(src.user_address);
        b_0.storeCoins(src.total_purchased);
        const b_1 = new Builder();
        b_1.storeCoins(src.total_paid);
        b_1.storeDict(src.purchase_history, Dictionary.Keys.BigInt(257), dictValueParserPurchaseRecord());
        b_1.storeDict(src.refund_history, Dictionary.Keys.BigInt(257), Dictionary.Values.BigInt(257));
        b_1.storeUint(src.purchase_id_counter, 32);
        b_1.storeDict(src.participated_rounds, Dictionary.Keys.BigInt(257), Dictionary.Values.Bool());
        b_0.storeRef(b_1.endCell());
    };
}

export function loadUserPurchase$Data(slice: Slice) {
    const sc_0 = slice;
    const _owner = sc_0.loadAddress();
    const _auction_address = sc_0.loadAddress();
    const _user_address = sc_0.loadAddress();
    const _total_purchased = sc_0.loadCoins();
    const sc_1 = sc_0.loadRef().beginParse();
    const _total_paid = sc_1.loadCoins();
    const _purchase_history = Dictionary.load(Dictionary.Keys.BigInt(257), dictValueParserPurchaseRecord(), sc_1);
    const _refund_history = Dictionary.load(Dictionary.Keys.BigInt(257), Dictionary.Values.BigInt(257), sc_1);
    const _purchase_id_counter = sc_1.loadUintBig(32);
    const _participated_rounds = Dictionary.load(Dictionary.Keys.BigInt(257), Dictionary.Values.Bool(), sc_1);
    return { $$type: 'UserPurchase$Data' as const, owner: _owner, auction_address: _auction_address, user_address: _user_address, total_purchased: _total_purchased, total_paid: _total_paid, purchase_history: _purchase_history, refund_history: _refund_history, purchase_id_counter: _purchase_id_counter, participated_rounds: _participated_rounds };
}

export function loadTupleUserPurchase$Data(source: TupleReader) {
    const _owner = source.readAddress();
    const _auction_address = source.readAddress();
    const _user_address = source.readAddress();
    const _total_purchased = source.readBigNumber();
    const _total_paid = source.readBigNumber();
    const _purchase_history = Dictionary.loadDirect(Dictionary.Keys.BigInt(257), dictValueParserPurchaseRecord(), source.readCellOpt());
    const _refund_history = Dictionary.loadDirect(Dictionary.Keys.BigInt(257), Dictionary.Values.BigInt(257), source.readCellOpt());
    const _purchase_id_counter = source.readBigNumber();
    const _participated_rounds = Dictionary.loadDirect(Dictionary.Keys.BigInt(257), Dictionary.Values.Bool(), source.readCellOpt());
    return { $$type: 'UserPurchase$Data' as const, owner: _owner, auction_address: _auction_address, user_address: _user_address, total_purchased: _total_purchased, total_paid: _total_paid, purchase_history: _purchase_history, refund_history: _refund_history, purchase_id_counter: _purchase_id_counter, participated_rounds: _participated_rounds };
}

export function loadGetterTupleUserPurchase$Data(source: TupleReader) {
    const _owner = source.readAddress();
    const _auction_address = source.readAddress();
    const _user_address = source.readAddress();
    const _total_purchased = source.readBigNumber();
    const _total_paid = source.readBigNumber();
    const _purchase_history = Dictionary.loadDirect(Dictionary.Keys.BigInt(257), dictValueParserPurchaseRecord(), source.readCellOpt());
    const _refund_history = Dictionary.loadDirect(Dictionary.Keys.BigInt(257), Dictionary.Values.BigInt(257), source.readCellOpt());
    const _purchase_id_counter = source.readBigNumber();
    const _participated_rounds = Dictionary.loadDirect(Dictionary.Keys.BigInt(257), Dictionary.Values.Bool(), source.readCellOpt());
    return { $$type: 'UserPurchase$Data' as const, owner: _owner, auction_address: _auction_address, user_address: _user_address, total_purchased: _total_purchased, total_paid: _total_paid, purchase_history: _purchase_history, refund_history: _refund_history, purchase_id_counter: _purchase_id_counter, participated_rounds: _participated_rounds };
}

export function storeTupleUserPurchase$Data(source: UserPurchase$Data) {
    const builder = new TupleBuilder();
    builder.writeAddress(source.owner);
    builder.writeAddress(source.auction_address);
    builder.writeAddress(source.user_address);
    builder.writeNumber(source.total_purchased);
    builder.writeNumber(source.total_paid);
    builder.writeCell(source.purchase_history.size > 0 ? beginCell().storeDictDirect(source.purchase_history, Dictionary.Keys.BigInt(257), dictValueParserPurchaseRecord()).endCell() : null);
    builder.writeCell(source.refund_history.size > 0 ? beginCell().storeDictDirect(source.refund_history, Dictionary.Keys.BigInt(257), Dictionary.Values.BigInt(257)).endCell() : null);
    builder.writeNumber(source.purchase_id_counter);
    builder.writeCell(source.participated_rounds.size > 0 ? beginCell().storeDictDirect(source.participated_rounds, Dictionary.Keys.BigInt(257), Dictionary.Values.Bool()).endCell() : null);
    return builder.build();
}

export function dictValueParserUserPurchase$Data(): DictionaryValue<UserPurchase$Data> {
    return {
        serialize: (src, builder) => {
            builder.storeRef(beginCell().store(storeUserPurchase$Data(src)).endCell());
        },
        parse: (src) => {
            return loadUserPurchase$Data(src.loadRef().beginParse());
        }
    }
}

 type UserPurchase_init_args = {
    $$type: 'UserPurchase_init_args';
    auction_address: Address;
    user_address: Address;
}

function initUserPurchase_init_args(src: UserPurchase_init_args) {
    return (builder: Builder) => {
        const b_0 = builder;
        b_0.storeAddress(src.auction_address);
        b_0.storeAddress(src.user_address);
    };
}

async function UserPurchase_init(auction_address: Address, user_address: Address) {
    const __code = Cell.fromHex('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');
    const builder = beginCell();
    builder.storeUint(0, 1);
    initUserPurchase_init_args({ $$type: 'UserPurchase_init_args', auction_address, user_address })(builder);
    const __data = builder.endCell();
    return { code: __code, data: __data };
}

export const UserPurchase_errors = {
    2: { message: "Stack underflow" },
    3: { message: "Stack overflow" },
    4: { message: "Integer overflow" },
    5: { message: "Integer out of expected range" },
    6: { message: "Invalid opcode" },
    7: { message: "Type check error" },
    8: { message: "Cell overflow" },
    9: { message: "Cell underflow" },
    10: { message: "Dictionary error" },
    11: { message: "'Unknown' error" },
    12: { message: "Fatal error" },
    13: { message: "Out of gas error" },
    14: { message: "Virtualization error" },
    32: { message: "Action list is invalid" },
    33: { message: "Action list is too long" },
    34: { message: "Action is invalid or not supported" },
    35: { message: "Invalid source address in outbound message" },
    36: { message: "Invalid destination address in outbound message" },
    37: { message: "Not enough Toncoin" },
    38: { message: "Not enough extra currencies" },
    39: { message: "Outbound message does not fit into a cell after rewriting" },
    40: { message: "Cannot process a message" },
    41: { message: "Library reference is null" },
    42: { message: "Library change action error" },
    43: { message: "Exceeded maximum number of cells in the library or the maximum depth of the Merkle tree" },
    50: { message: "Account state size exceeded limits" },
    128: { message: "Null reference exception" },
    129: { message: "Invalid serialization prefix" },
    130: { message: "Invalid incoming message" },
    131: { message: "Constraints error" },
    132: { message: "Access denied" },
    133: { message: "Contract stopped" },
    134: { message: "Invalid argument" },
    135: { message: "Code of a contract was not found" },
    136: { message: "Invalid standard address" },
    138: { message: "Not a basechain address" },
    22411: { message: "Already refunded" },
    39144: { message: "Purchase not found" },
    49729: { message: "Unauthorized" },
} as const

export const UserPurchase_errors_backward = {
    "Stack underflow": 2,
    "Stack overflow": 3,
    "Integer overflow": 4,
    "Integer out of expected range": 5,
    "Invalid opcode": 6,
    "Type check error": 7,
    "Cell overflow": 8,
    "Cell underflow": 9,
    "Dictionary error": 10,
    "'Unknown' error": 11,
    "Fatal error": 12,
    "Out of gas error": 13,
    "Virtualization error": 14,
    "Action list is invalid": 32,
    "Action list is too long": 33,
    "Action is invalid or not supported": 34,
    "Invalid source address in outbound message": 35,
    "Invalid destination address in outbound message": 36,
    "Not enough Toncoin": 37,
    "Not enough extra currencies": 38,
    "Outbound message does not fit into a cell after rewriting": 39,
    "Cannot process a message": 40,
    "Library reference is null": 41,
    "Library change action error": 42,
    "Exceeded maximum number of cells in the library or the maximum depth of the Merkle tree": 43,
    "Account state size exceeded limits": 50,
    "Null reference exception": 128,
    "Invalid serialization prefix": 129,
    "Invalid incoming message": 130,
    "Constraints error": 131,
    "Access denied": 132,
    "Contract stopped": 133,
    "Invalid argument": 134,
    "Code of a contract was not found": 135,
    "Invalid standard address": 136,
    "Not a basechain address": 138,
    "Already refunded": 22411,
    "Purchase not found": 39144,
    "Unauthorized": 49729,
} as const

const UserPurchase_types: ABIType[] = [
    {"name":"DataSize","header":null,"fields":[{"name":"cells","type":{"kind":"simple","type":"int","optional":false,"format":257}},{"name":"bits","type":{"kind":"simple","type":"int","optional":false,"format":257}},{"name":"refs","type":{"kind":"simple","type":"int","optional":false,"format":257}}]},
    {"name":"SignedBundle","header":null,"fields":[{"name":"signature","type":{"kind":"simple","type":"fixed-bytes","optional":false,"format":64}},{"name":"signedData","type":{"kind":"simple","type":"slice","optional":false,"format":"remainder"}}]},
    {"name":"StateInit","header":null,"fields":[{"name":"code","type":{"kind":"simple","type":"cell","optional":false}},{"name":"data","type":{"kind":"simple","type":"cell","optional":false}}]},
    {"name":"Context","header":null,"fields":[{"name":"bounceable","type":{"kind":"simple","type":"bool","optional":false}},{"name":"sender","type":{"kind":"simple","type":"address","optional":false}},{"name":"value","type":{"kind":"simple","type":"int","optional":false,"format":257}},{"name":"raw","type":{"kind":"simple","type":"slice","optional":false}}]},
    {"name":"SendParameters","header":null,"fields":[{"name":"mode","type":{"kind":"simple","type":"int","optional":false,"format":257}},{"name":"body","type":{"kind":"simple","type":"cell","optional":true}},{"name":"code","type":{"kind":"simple","type":"cell","optional":true}},{"name":"data","type":{"kind":"simple","type":"cell","optional":true}},{"name":"value","type":{"kind":"simple","type":"int","optional":false,"format":257}},{"name":"to","type":{"kind":"simple","type":"address","optional":false}},{"name":"bounce","type":{"kind":"simple","type":"bool","optional":false}}]},
    {"name":"MessageParameters","header":null,"fields":[{"name":"mode","type":{"kind":"simple","type":"int","optional":false,"format":257}},{"name":"body","type":{"kind":"simple","type":"cell","optional":true}},{"name":"value","type":{"kind":"simple","type":"int","optional":false,"format":257}},{"name":"to","type":{"kind":"simple","type":"address","optional":false}},{"name":"bounce","type":{"kind":"simple","type":"bool","optional":false}}]},
    {"name":"DeployParameters","header":null,"fields":[{"name":"mode","type":{"kind":"simple","type":"int","optional":false,"format":257}},{"name":"body","type":{"kind":"simple","type":"cell","optional":true}},{"name":"value","type":{"kind":"simple","type":"int","optional":false,"format":257}},{"name":"bounce","type":{"kind":"simple","type":"bool","optional":false}},{"name":"init","type":{"kind":"simple","type":"StateInit","optional":false}}]},
    {"name":"StdAddress","header":null,"fields":[{"name":"workchain","type":{"kind":"simple","type":"int","optional":false,"format":8}},{"name":"address","type":{"kind":"simple","type":"uint","optional":false,"format":256}}]},
    {"name":"VarAddress","header":null,"fields":[{"name":"workchain","type":{"kind":"simple","type":"int","optional":false,"format":32}},{"name":"address","type":{"kind":"simple","type":"slice","optional":false}}]},
    {"name":"BasechainAddress","header":null,"fields":[{"name":"hash","type":{"kind":"simple","type":"int","optional":true,"format":257}}]},
    {"name":"ChangeOwner","header":2174598809,"fields":[{"name":"queryId","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"newOwner","type":{"kind":"simple","type":"address","optional":false}}]},
    {"name":"ChangeOwnerOk","header":846932810,"fields":[{"name":"queryId","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"newOwner","type":{"kind":"simple","type":"address","optional":false}}]},
    {"name":"Deploy","header":2490013878,"fields":[{"name":"queryId","type":{"kind":"simple","type":"uint","optional":false,"format":64}}]},
    {"name":"DeployOk","header":2952335191,"fields":[{"name":"queryId","type":{"kind":"simple","type":"uint","optional":false,"format":64}}]},
    {"name":"FactoryDeploy","header":1829761339,"fields":[{"name":"queryId","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"cashback","type":{"kind":"simple","type":"address","optional":false}}]},
    {"name":"CreateUserPurchase","header":1098075148,"fields":[{"name":"user","type":{"kind":"simple","type":"address","optional":false}},{"name":"amount","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"tokens","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"currency","type":{"kind":"simple","type":"uint","optional":false,"format":8}},{"name":"purchase_method","type":{"kind":"simple","type":"uint","optional":false,"format":8}},{"name":"nonce","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"round_number","type":{"kind":"simple","type":"uint","optional":false,"format":32}},{"name":"usdt_equivalent_amount","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}}]},
    {"name":"Refund","header":836089260,"fields":[{"name":"purchase_id","type":{"kind":"simple","type":"uint","optional":false,"format":32}}]},
    {"name":"ProcessRefund","header":929991442,"fields":[{"name":"user","type":{"kind":"simple","type":"address","optional":false}},{"name":"amount","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"fee","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"currency","type":{"kind":"simple","type":"uint","optional":false,"format":8}},{"name":"round_number","type":{"kind":"simple","type":"uint","optional":false,"format":32}},{"name":"usdt_equivalent_amount","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}}]},
    {"name":"PurchaseRecord","header":null,"fields":[{"name":"id","type":{"kind":"simple","type":"uint","optional":false,"format":32}},{"name":"user","type":{"kind":"simple","type":"address","optional":false}},{"name":"amount","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"tokens","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"timestamp","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"currency","type":{"kind":"simple","type":"uint","optional":false,"format":8}},{"name":"purchase_method","type":{"kind":"simple","type":"uint","optional":false,"format":8}},{"name":"nonce","type":{"kind":"simple","type":"uint","optional":false,"format":64}},{"name":"round_number","type":{"kind":"simple","type":"uint","optional":false,"format":32}},{"name":"usdt_equivalent_amount","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}}]},
    {"name":"UserPurchase$Data","header":null,"fields":[{"name":"owner","type":{"kind":"simple","type":"address","optional":false}},{"name":"auction_address","type":{"kind":"simple","type":"address","optional":false}},{"name":"user_address","type":{"kind":"simple","type":"address","optional":false}},{"name":"total_purchased","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"total_paid","type":{"kind":"simple","type":"uint","optional":false,"format":"coins"}},{"name":"purchase_history","type":{"kind":"dict","key":"int","value":"PurchaseRecord","valueFormat":"ref"}},{"name":"refund_history","type":{"kind":"dict","key":"int","value":"int"}},{"name":"purchase_id_counter","type":{"kind":"simple","type":"uint","optional":false,"format":32}},{"name":"participated_rounds","type":{"kind":"dict","key":"int","value":"bool"}}]},
]

const UserPurchase_opcodes = {
    "ChangeOwner": 2174598809,
    "ChangeOwnerOk": 846932810,
    "Deploy": 2490013878,
    "DeployOk": 2952335191,
    "FactoryDeploy": 1829761339,
    "CreateUserPurchase": 1098075148,
    "Refund": 836089260,
    "ProcessRefund": 929991442,
}

const UserPurchase_getters: ABIGetter[] = [
    {"name":"total_purchased","methodId":97654,"arguments":[],"returnType":{"kind":"simple","type":"int","optional":false,"format":257}},
    {"name":"total_paid","methodId":127313,"arguments":[],"returnType":{"kind":"simple","type":"int","optional":false,"format":257}},
    {"name":"purchase_id_counter","methodId":90655,"arguments":[],"returnType":{"kind":"simple","type":"int","optional":false,"format":257}},
    {"name":"purchase_details","methodId":108146,"arguments":[{"name":"purchase_id","type":{"kind":"simple","type":"int","optional":false,"format":257}}],"returnType":{"kind":"simple","type":"PurchaseRecord","optional":true}},
    {"name":"is_refunded","methodId":70580,"arguments":[{"name":"purchase_id","type":{"kind":"simple","type":"int","optional":false,"format":257}}],"returnType":{"kind":"simple","type":"bool","optional":false}},
    {"name":"signature_verified_purchases","methodId":90079,"arguments":[],"returnType":{"kind":"simple","type":"int","optional":false,"format":257}},
    {"name":"purchase_method_stats","methodId":123538,"arguments":[],"returnType":{"kind":"dict","key":"int","value":"int"}},
    {"name":"purchases_by_round","methodId":98340,"arguments":[{"name":"round_number","type":{"kind":"simple","type":"int","optional":false,"format":257}}],"returnType":{"kind":"dict","key":"int","value":"PurchaseRecord","valueFormat":"ref"}},
    {"name":"round_total_amount","methodId":67459,"arguments":[{"name":"round_number","type":{"kind":"simple","type":"int","optional":false,"format":257}}],"returnType":{"kind":"simple","type":"int","optional":false,"format":257}},
    {"name":"round_total_tokens","methodId":123888,"arguments":[{"name":"round_number","type":{"kind":"simple","type":"int","optional":false,"format":257}}],"returnType":{"kind":"simple","type":"int","optional":false,"format":257}},
    {"name":"round_purchase_count","methodId":82786,"arguments":[{"name":"round_number","type":{"kind":"simple","type":"int","optional":false,"format":257}}],"returnType":{"kind":"simple","type":"int","optional":false,"format":257}},
    {"name":"participated_rounds","methodId":108842,"arguments":[],"returnType":{"kind":"dict","key":"int","value":"bool"}},
    {"name":"participated_in_round","methodId":124279,"arguments":[{"name":"round_number","type":{"kind":"simple","type":"int","optional":false,"format":257}}],"returnType":{"kind":"simple","type":"bool","optional":false}},
    {"name":"round_count","methodId":86414,"arguments":[],"returnType":{"kind":"simple","type":"int","optional":false,"format":257}},
    {"name":"all_records","methodId":96455,"arguments":[],"returnType":{"kind":"dict","key":"int","value":"PurchaseRecord","valueFormat":"ref"}},
    {"name":"owner","methodId":83229,"arguments":[],"returnType":{"kind":"simple","type":"address","optional":false}},
]

export const UserPurchase_getterMapping: { [key: string]: string } = {
    'total_purchased': 'getTotalPurchased',
    'total_paid': 'getTotalPaid',
    'purchase_id_counter': 'getPurchaseIdCounter',
    'purchase_details': 'getPurchaseDetails',
    'is_refunded': 'getIsRefunded',
    'signature_verified_purchases': 'getSignatureVerifiedPurchases',
    'purchase_method_stats': 'getPurchaseMethodStats',
    'purchases_by_round': 'getPurchasesByRound',
    'round_total_amount': 'getRoundTotalAmount',
    'round_total_tokens': 'getRoundTotalTokens',
    'round_purchase_count': 'getRoundPurchaseCount',
    'participated_rounds': 'getParticipatedRounds',
    'participated_in_round': 'getParticipatedInRound',
    'round_count': 'getRoundCount',
    'all_records': 'getAllRecords',
    'owner': 'getOwner',
}

const UserPurchase_receivers: ABIReceiver[] = [
    {"receiver":"internal","message":{"kind":"text","text":"Deploy"}},
    {"receiver":"internal","message":{"kind":"typed","type":"CreateUserPurchase"}},
    {"receiver":"internal","message":{"kind":"typed","type":"Refund"}},
]

export const ERROR_UNAUTHORIZED = 62001n;
export const ERROR_PURCHASE_NOT_FOUND = 62002n;
export const ERROR_ALREADY_REFUNDED = 62003n;
export const OP_CREATE_USER_PURCHASE = 1098075148n;
export const OP_REFUND = 836089260n;
export const OP_PROCESS_REFUND = 929991442n;

export class UserPurchase implements Contract {
    
    public static readonly storageReserve = 0n;
    public static readonly errors = UserPurchase_errors_backward;
    public static readonly opcodes = UserPurchase_opcodes;
    
    static async init(auction_address: Address, user_address: Address) {
        return await UserPurchase_init(auction_address, user_address);
    }
    
    static async fromInit(auction_address: Address, user_address: Address) {
        const __gen_init = await UserPurchase_init(auction_address, user_address);
        const address = contractAddress(0, __gen_init);
        return new UserPurchase(address, __gen_init);
    }
    
    static fromAddress(address: Address) {
        return new UserPurchase(address);
    }
    
    readonly address: Address; 
    readonly init?: { code: Cell, data: Cell };
    readonly abi: ContractABI = {
        types:  UserPurchase_types,
        getters: UserPurchase_getters,
        receivers: UserPurchase_receivers,
        errors: UserPurchase_errors,
    };
    
    constructor(address: Address, init?: { code: Cell, data: Cell }) {
        this.address = address;
        this.init = init;
    }
    
    async send(provider: ContractProvider, via: Sender, args: { value: bigint, bounce?: boolean| null | undefined }, message: "Deploy" | CreateUserPurchase | Refund) {
        
        let body: Cell | null = null;
        if (message === "Deploy") {
            body = beginCell().storeUint(0, 32).storeStringTail(message).endCell();
        }
        if (message && typeof message === 'object' && !(message instanceof Slice) && message.$$type === 'CreateUserPurchase') {
            body = beginCell().store(storeCreateUserPurchase(message)).endCell();
        }
        if (message && typeof message === 'object' && !(message instanceof Slice) && message.$$type === 'Refund') {
            body = beginCell().store(storeRefund(message)).endCell();
        }
        if (body === null) { throw new Error('Invalid message type'); }
        
        await provider.internal(via, { ...args, body: body });
        
    }
    
    async getTotalPurchased(provider: ContractProvider) {
        const builder = new TupleBuilder();
        const source = (await provider.get('total_purchased', builder.build())).stack;
        const result = source.readBigNumber();
        return result;
    }
    
    async getTotalPaid(provider: ContractProvider) {
        const builder = new TupleBuilder();
        const source = (await provider.get('total_paid', builder.build())).stack;
        const result = source.readBigNumber();
        return result;
    }
    
    async getPurchaseIdCounter(provider: ContractProvider) {
        const builder = new TupleBuilder();
        const source = (await provider.get('purchase_id_counter', builder.build())).stack;
        const result = source.readBigNumber();
        return result;
    }
    
    async getPurchaseDetails(provider: ContractProvider, purchase_id: bigint) {
        const builder = new TupleBuilder();
        builder.writeNumber(purchase_id);
        const source = (await provider.get('purchase_details', builder.build())).stack;
        const result_p = source.readTupleOpt();
        const result = result_p ? loadTuplePurchaseRecord(result_p) : null;
        return result;
    }
    
    async getIsRefunded(provider: ContractProvider, purchase_id: bigint) {
        const builder = new TupleBuilder();
        builder.writeNumber(purchase_id);
        const source = (await provider.get('is_refunded', builder.build())).stack;
        const result = source.readBoolean();
        return result;
    }
    
    async getSignatureVerifiedPurchases(provider: ContractProvider) {
        const builder = new TupleBuilder();
        const source = (await provider.get('signature_verified_purchases', builder.build())).stack;
        const result = source.readBigNumber();
        return result;
    }
    
    async getPurchaseMethodStats(provider: ContractProvider) {
        const builder = new TupleBuilder();
        const source = (await provider.get('purchase_method_stats', builder.build())).stack;
        const result = Dictionary.loadDirect(Dictionary.Keys.BigInt(257), Dictionary.Values.BigInt(257), source.readCellOpt());
        return result;
    }
    
    async getPurchasesByRound(provider: ContractProvider, round_number: bigint) {
        const builder = new TupleBuilder();
        builder.writeNumber(round_number);
        const source = (await provider.get('purchases_by_round', builder.build())).stack;
        const result = Dictionary.loadDirect(Dictionary.Keys.BigInt(257), dictValueParserPurchaseRecord(), source.readCellOpt());
        return result;
    }
    
    async getRoundTotalAmount(provider: ContractProvider, round_number: bigint) {
        const builder = new TupleBuilder();
        builder.writeNumber(round_number);
        const source = (await provider.get('round_total_amount', builder.build())).stack;
        const result = source.readBigNumber();
        return result;
    }
    
    async getRoundTotalTokens(provider: ContractProvider, round_number: bigint) {
        const builder = new TupleBuilder();
        builder.writeNumber(round_number);
        const source = (await provider.get('round_total_tokens', builder.build())).stack;
        const result = source.readBigNumber();
        return result;
    }
    
    async getRoundPurchaseCount(provider: ContractProvider, round_number: bigint) {
        const builder = new TupleBuilder();
        builder.writeNumber(round_number);
        const source = (await provider.get('round_purchase_count', builder.build())).stack;
        const result = source.readBigNumber();
        return result;
    }
    
    async getParticipatedRounds(provider: ContractProvider) {
        const builder = new TupleBuilder();
        const source = (await provider.get('participated_rounds', builder.build())).stack;
        const result = Dictionary.loadDirect(Dictionary.Keys.BigInt(257), Dictionary.Values.Bool(), source.readCellOpt());
        return result;
    }
    
    async getParticipatedInRound(provider: ContractProvider, round_number: bigint) {
        const builder = new TupleBuilder();
        builder.writeNumber(round_number);
        const source = (await provider.get('participated_in_round', builder.build())).stack;
        const result = source.readBoolean();
        return result;
    }
    
    async getRoundCount(provider: ContractProvider) {
        const builder = new TupleBuilder();
        const source = (await provider.get('round_count', builder.build())).stack;
        const result = source.readBigNumber();
        return result;
    }
    
    async getAllRecords(provider: ContractProvider) {
        const builder = new TupleBuilder();
        const source = (await provider.get('all_records', builder.build())).stack;
        const result = Dictionary.loadDirect(Dictionary.Keys.BigInt(257), dictValueParserPurchaseRecord(), source.readCellOpt());
        return result;
    }
    
    async getOwner(provider: ContractProvider) {
        const builder = new TupleBuilder();
        const source = (await provider.get('owner', builder.build())).stack;
        const result = source.readAddress();
        return result;
    }
    
}