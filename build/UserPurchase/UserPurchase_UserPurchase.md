# Tact compilation report
Contract: UserPurchase
BoC Size: 2966 bytes

## Structures (Structs and Messages)
Total structures: 20

### DataSize
TL-B: `_ cells:int257 bits:int257 refs:int257 = DataSize`
Signature: `DataSize{cells:int257,bits:int257,refs:int257}`

### SignedBundle
TL-B: `_ signature:fixed_bytes64 signedData:remainder<slice> = SignedBundle`
Signature: `SignedBundle{signature:fixed_bytes64,signedData:remainder<slice>}`

### StateInit
TL-B: `_ code:^cell data:^cell = StateInit`
Signature: `StateInit{code:^cell,data:^cell}`

### Context
TL-B: `_ bounceable:bool sender:address value:int257 raw:^slice = Context`
Signature: `Context{bounceable:bool,sender:address,value:int257,raw:^slice}`

### SendParameters
TL-B: `_ mode:int257 body:Maybe ^cell code:Maybe ^cell data:Maybe ^cell value:int257 to:address bounce:bool = SendParameters`
Signature: `SendParameters{mode:int257,body:Maybe ^cell,code:Maybe ^cell,data:Maybe ^cell,value:int257,to:address,bounce:bool}`

### MessageParameters
TL-B: `_ mode:int257 body:Maybe ^cell value:int257 to:address bounce:bool = MessageParameters`
Signature: `MessageParameters{mode:int257,body:Maybe ^cell,value:int257,to:address,bounce:bool}`

### DeployParameters
TL-B: `_ mode:int257 body:Maybe ^cell value:int257 bounce:bool init:StateInit{code:^cell,data:^cell} = DeployParameters`
Signature: `DeployParameters{mode:int257,body:Maybe ^cell,value:int257,bounce:bool,init:StateInit{code:^cell,data:^cell}}`

### StdAddress
TL-B: `_ workchain:int8 address:uint256 = StdAddress`
Signature: `StdAddress{workchain:int8,address:uint256}`

### VarAddress
TL-B: `_ workchain:int32 address:^slice = VarAddress`
Signature: `VarAddress{workchain:int32,address:^slice}`

### BasechainAddress
TL-B: `_ hash:Maybe int257 = BasechainAddress`
Signature: `BasechainAddress{hash:Maybe int257}`

### ChangeOwner
TL-B: `change_owner#819dbe99 queryId:uint64 newOwner:address = ChangeOwner`
Signature: `ChangeOwner{queryId:uint64,newOwner:address}`

### ChangeOwnerOk
TL-B: `change_owner_ok#327b2b4a queryId:uint64 newOwner:address = ChangeOwnerOk`
Signature: `ChangeOwnerOk{queryId:uint64,newOwner:address}`

### Deploy
TL-B: `deploy#946a98b6 queryId:uint64 = Deploy`
Signature: `Deploy{queryId:uint64}`

### DeployOk
TL-B: `deploy_ok#aff90f57 queryId:uint64 = DeployOk`
Signature: `DeployOk{queryId:uint64}`

### FactoryDeploy
TL-B: `factory_deploy#6d0ff13b queryId:uint64 cashback:address = FactoryDeploy`
Signature: `FactoryDeploy{queryId:uint64,cashback:address}`

### CreateUserPurchase
TL-B: `create_user_purchase#41734c0c user:address amount:coins tokens:coins currency:uint8 purchase_method:uint8 nonce:uint64 round_number:uint32 usdt_equivalent_amount:coins = CreateUserPurchase`
Signature: `CreateUserPurchase{user:address,amount:coins,tokens:coins,currency:uint8,purchase_method:uint8,nonce:uint64,round_number:uint32,usdt_equivalent_amount:coins}`

### Refund
TL-B: `refund#31d5b5ac purchase_id:uint32 = Refund`
Signature: `Refund{purchase_id:uint32}`

### ProcessRefund
TL-B: `process_refund#376e8b12 user:address amount:coins fee:coins currency:uint8 round_number:uint32 usdt_equivalent_amount:coins = ProcessRefund`
Signature: `ProcessRefund{user:address,amount:coins,fee:coins,currency:uint8,round_number:uint32,usdt_equivalent_amount:coins}`

### PurchaseRecord
TL-B: `_ id:uint32 user:address amount:coins tokens:coins timestamp:uint64 currency:uint8 purchase_method:uint8 nonce:uint64 round_number:uint32 usdt_equivalent_amount:coins = PurchaseRecord`
Signature: `PurchaseRecord{id:uint32,user:address,amount:coins,tokens:coins,timestamp:uint64,currency:uint8,purchase_method:uint8,nonce:uint64,round_number:uint32,usdt_equivalent_amount:coins}`

### UserPurchase$Data
TL-B: `_ owner:address auction_address:address user_address:address total_purchased:coins total_paid:coins purchase_history:dict<int, ^PurchaseRecord{id:uint32,user:address,amount:coins,tokens:coins,timestamp:uint64,currency:uint8,purchase_method:uint8,nonce:uint64,round_number:uint32,usdt_equivalent_amount:coins}> refund_history:dict<int, int> purchase_id_counter:uint32 participated_rounds:dict<int, bool> = UserPurchase`
Signature: `UserPurchase{owner:address,auction_address:address,user_address:address,total_purchased:coins,total_paid:coins,purchase_history:dict<int, ^PurchaseRecord{id:uint32,user:address,amount:coins,tokens:coins,timestamp:uint64,currency:uint8,purchase_method:uint8,nonce:uint64,round_number:uint32,usdt_equivalent_amount:coins}>,refund_history:dict<int, int>,purchase_id_counter:uint32,participated_rounds:dict<int, bool>}`

## Get methods
Total get methods: 16

## total_purchased
No arguments

## total_paid
No arguments

## purchase_id_counter
No arguments

## purchase_details
Argument: purchase_id

## is_refunded
Argument: purchase_id

## signature_verified_purchases
No arguments

## purchase_method_stats
No arguments

## purchases_by_round
Argument: round_number

## round_total_amount
Argument: round_number

## round_total_tokens
Argument: round_number

## round_purchase_count
Argument: round_number

## participated_rounds
No arguments

## participated_in_round
Argument: round_number

## round_count
No arguments

## all_records
No arguments

## owner
No arguments

## Exit codes
* 2: Stack underflow
* 3: Stack overflow
* 4: Integer overflow
* 5: Integer out of expected range
* 6: Invalid opcode
* 7: Type check error
* 8: Cell overflow
* 9: Cell underflow
* 10: Dictionary error
* 11: 'Unknown' error
* 12: Fatal error
* 13: Out of gas error
* 14: Virtualization error
* 32: Action list is invalid
* 33: Action list is too long
* 34: Action is invalid or not supported
* 35: Invalid source address in outbound message
* 36: Invalid destination address in outbound message
* 37: Not enough Toncoin
* 38: Not enough extra currencies
* 39: Outbound message does not fit into a cell after rewriting
* 40: Cannot process a message
* 41: Library reference is null
* 42: Library change action error
* 43: Exceeded maximum number of cells in the library or the maximum depth of the Merkle tree
* 50: Account state size exceeded limits
* 128: Null reference exception
* 129: Invalid serialization prefix
* 130: Invalid incoming message
* 131: Constraints error
* 132: Access denied
* 133: Contract stopped
* 134: Invalid argument
* 135: Code of a contract was not found
* 136: Invalid standard address
* 138: Not a basechain address
* 22411: Already refunded
* 39144: Purchase not found
* 49729: Unauthorized

## Trait inheritance diagram

```mermaid
graph TD
UserPurchase
UserPurchase --> BaseTrait
UserPurchase --> Ownable
Ownable --> BaseTrait
```

## Contract dependency diagram

```mermaid
graph TD
UserPurchase
```