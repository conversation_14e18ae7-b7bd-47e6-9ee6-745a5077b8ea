{"name": "UserPurchase", "code": "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", "abi": "{\"name\":\"UserPurchase\",\"types\":[{\"name\":\"DataSize\",\"header\":null,\"fields\":[{\"name\":\"cells\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}},{\"name\":\"bits\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}},{\"name\":\"refs\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}}]},{\"name\":\"SignedBundle\",\"header\":null,\"fields\":[{\"name\":\"signature\",\"type\":{\"kind\":\"simple\",\"type\":\"fixed-bytes\",\"optional\":false,\"format\":64}},{\"name\":\"signedData\",\"type\":{\"kind\":\"simple\",\"type\":\"slice\",\"optional\":false,\"format\":\"remainder\"}}]},{\"name\":\"StateInit\",\"header\":null,\"fields\":[{\"name\":\"code\",\"type\":{\"kind\":\"simple\",\"type\":\"cell\",\"optional\":false}},{\"name\":\"data\",\"type\":{\"kind\":\"simple\",\"type\":\"cell\",\"optional\":false}}]},{\"name\":\"Context\",\"header\":null,\"fields\":[{\"name\":\"bounceable\",\"type\":{\"kind\":\"simple\",\"type\":\"bool\",\"optional\":false}},{\"name\":\"sender\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"value\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}},{\"name\":\"raw\",\"type\":{\"kind\":\"simple\",\"type\":\"slice\",\"optional\":false}}]},{\"name\":\"SendParameters\",\"header\":null,\"fields\":[{\"name\":\"mode\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}},{\"name\":\"body\",\"type\":{\"kind\":\"simple\",\"type\":\"cell\",\"optional\":true}},{\"name\":\"code\",\"type\":{\"kind\":\"simple\",\"type\":\"cell\",\"optional\":true}},{\"name\":\"data\",\"type\":{\"kind\":\"simple\",\"type\":\"cell\",\"optional\":true}},{\"name\":\"value\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}},{\"name\":\"to\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"bounce\",\"type\":{\"kind\":\"simple\",\"type\":\"bool\",\"optional\":false}}]},{\"name\":\"MessageParameters\",\"header\":null,\"fields\":[{\"name\":\"mode\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}},{\"name\":\"body\",\"type\":{\"kind\":\"simple\",\"type\":\"cell\",\"optional\":true}},{\"name\":\"value\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}},{\"name\":\"to\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"bounce\",\"type\":{\"kind\":\"simple\",\"type\":\"bool\",\"optional\":false}}]},{\"name\":\"DeployParameters\",\"header\":null,\"fields\":[{\"name\":\"mode\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}},{\"name\":\"body\",\"type\":{\"kind\":\"simple\",\"type\":\"cell\",\"optional\":true}},{\"name\":\"value\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}},{\"name\":\"bounce\",\"type\":{\"kind\":\"simple\",\"type\":\"bool\",\"optional\":false}},{\"name\":\"init\",\"type\":{\"kind\":\"simple\",\"type\":\"StateInit\",\"optional\":false}}]},{\"name\":\"StdAddress\",\"header\":null,\"fields\":[{\"name\":\"workchain\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":8}},{\"name\":\"address\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":256}}]},{\"name\":\"VarAddress\",\"header\":null,\"fields\":[{\"name\":\"workchain\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":32}},{\"name\":\"address\",\"type\":{\"kind\":\"simple\",\"type\":\"slice\",\"optional\":false}}]},{\"name\":\"BasechainAddress\",\"header\":null,\"fields\":[{\"name\":\"hash\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":true,\"format\":257}}]},{\"name\":\"ChangeOwner\",\"header\":2174598809,\"fields\":[{\"name\":\"queryId\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":64}},{\"name\":\"newOwner\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}}]},{\"name\":\"ChangeOwnerOk\",\"header\":846932810,\"fields\":[{\"name\":\"queryId\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":64}},{\"name\":\"newOwner\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}}]},{\"name\":\"Deploy\",\"header\":2490013878,\"fields\":[{\"name\":\"queryId\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":64}}]},{\"name\":\"DeployOk\",\"header\":2952335191,\"fields\":[{\"name\":\"queryId\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":64}}]},{\"name\":\"FactoryDeploy\",\"header\":1829761339,\"fields\":[{\"name\":\"queryId\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":64}},{\"name\":\"cashback\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}}]},{\"name\":\"CreateUserPurchase\",\"header\":1098075148,\"fields\":[{\"name\":\"user\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"amount\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"tokens\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"currency\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":8}},{\"name\":\"purchase_method\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":8}},{\"name\":\"nonce\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":64}},{\"name\":\"round_number\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":32}},{\"name\":\"usdt_equivalent_amount\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}}]},{\"name\":\"Refund\",\"header\":836089260,\"fields\":[{\"name\":\"purchase_id\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":32}}]},{\"name\":\"ProcessRefund\",\"header\":929991442,\"fields\":[{\"name\":\"user\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"amount\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"fee\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"currency\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":8}},{\"name\":\"round_number\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":32}},{\"name\":\"usdt_equivalent_amount\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}}]},{\"name\":\"PurchaseRecord\",\"header\":null,\"fields\":[{\"name\":\"id\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":32}},{\"name\":\"user\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"amount\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"tokens\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"timestamp\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":64}},{\"name\":\"currency\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":8}},{\"name\":\"purchase_method\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":8}},{\"name\":\"nonce\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":64}},{\"name\":\"round_number\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":32}},{\"name\":\"usdt_equivalent_amount\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}}]},{\"name\":\"UserPurchase$Data\",\"header\":null,\"fields\":[{\"name\":\"owner\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"auction_address\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"user_address\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"total_purchased\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"total_paid\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"purchase_history\",\"type\":{\"kind\":\"dict\",\"key\":\"int\",\"value\":\"PurchaseRecord\",\"valueFormat\":\"ref\"}},{\"name\":\"refund_history\",\"type\":{\"kind\":\"dict\",\"key\":\"int\",\"value\":\"int\"}},{\"name\":\"purchase_id_counter\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":32}},{\"name\":\"participated_rounds\",\"type\":{\"kind\":\"dict\",\"key\":\"int\",\"value\":\"bool\"}}]}],\"receivers\":[{\"receiver\":\"internal\",\"message\":{\"kind\":\"text\",\"text\":\"Deploy\"}},{\"receiver\":\"internal\",\"message\":{\"kind\":\"typed\",\"type\":\"CreateUserPurchase\"}},{\"receiver\":\"internal\",\"message\":{\"kind\":\"typed\",\"type\":\"Refund\"}}],\"getters\":[{\"name\":\"total_purchased\",\"methodId\":97654,\"arguments\":[],\"returnType\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}},{\"name\":\"total_paid\",\"methodId\":127313,\"arguments\":[],\"returnType\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}},{\"name\":\"purchase_id_counter\",\"methodId\":90655,\"arguments\":[],\"returnType\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}},{\"name\":\"purchase_details\",\"methodId\":108146,\"arguments\":[{\"name\":\"purchase_id\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}}],\"returnType\":{\"kind\":\"simple\",\"type\":\"PurchaseRecord\",\"optional\":true}},{\"name\":\"is_refunded\",\"methodId\":70580,\"arguments\":[{\"name\":\"purchase_id\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}}],\"returnType\":{\"kind\":\"simple\",\"type\":\"bool\",\"optional\":false}},{\"name\":\"signature_verified_purchases\",\"methodId\":90079,\"arguments\":[],\"returnType\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}},{\"name\":\"purchase_method_stats\",\"methodId\":123538,\"arguments\":[],\"returnType\":{\"kind\":\"dict\",\"key\":\"int\",\"value\":\"int\"}},{\"name\":\"purchases_by_round\",\"methodId\":98340,\"arguments\":[{\"name\":\"round_number\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}}],\"returnType\":{\"kind\":\"dict\",\"key\":\"int\",\"value\":\"PurchaseRecord\",\"valueFormat\":\"ref\"}},{\"name\":\"round_total_amount\",\"methodId\":67459,\"arguments\":[{\"name\":\"round_number\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}}],\"returnType\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}},{\"name\":\"round_total_tokens\",\"methodId\":123888,\"arguments\":[{\"name\":\"round_number\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}}],\"returnType\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}},{\"name\":\"round_purchase_count\",\"methodId\":82786,\"arguments\":[{\"name\":\"round_number\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}}],\"returnType\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}},{\"name\":\"participated_rounds\",\"methodId\":108842,\"arguments\":[],\"returnType\":{\"kind\":\"dict\",\"key\":\"int\",\"value\":\"bool\"}},{\"name\":\"participated_in_round\",\"methodId\":124279,\"arguments\":[{\"name\":\"round_number\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}}],\"returnType\":{\"kind\":\"simple\",\"type\":\"bool\",\"optional\":false}},{\"name\":\"round_count\",\"methodId\":86414,\"arguments\":[],\"returnType\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}},{\"name\":\"all_records\",\"methodId\":96455,\"arguments\":[],\"returnType\":{\"kind\":\"dict\",\"key\":\"int\",\"value\":\"PurchaseRecord\",\"valueFormat\":\"ref\"}},{\"name\":\"owner\",\"methodId\":83229,\"arguments\":[],\"returnType\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}}],\"errors\":{\"2\":{\"message\":\"Stack underflow\"},\"3\":{\"message\":\"Stack overflow\"},\"4\":{\"message\":\"Integer overflow\"},\"5\":{\"message\":\"Integer out of expected range\"},\"6\":{\"message\":\"Invalid opcode\"},\"7\":{\"message\":\"Type check error\"},\"8\":{\"message\":\"Cell overflow\"},\"9\":{\"message\":\"Cell underflow\"},\"10\":{\"message\":\"Dictionary error\"},\"11\":{\"message\":\"'Unknown' error\"},\"12\":{\"message\":\"Fatal error\"},\"13\":{\"message\":\"Out of gas error\"},\"14\":{\"message\":\"Virtualization error\"},\"32\":{\"message\":\"Action list is invalid\"},\"33\":{\"message\":\"Action list is too long\"},\"34\":{\"message\":\"Action is invalid or not supported\"},\"35\":{\"message\":\"Invalid source address in outbound message\"},\"36\":{\"message\":\"Invalid destination address in outbound message\"},\"37\":{\"message\":\"Not enough Toncoin\"},\"38\":{\"message\":\"Not enough extra currencies\"},\"39\":{\"message\":\"Outbound message does not fit into a cell after rewriting\"},\"40\":{\"message\":\"Cannot process a message\"},\"41\":{\"message\":\"Library reference is null\"},\"42\":{\"message\":\"Library change action error\"},\"43\":{\"message\":\"Exceeded maximum number of cells in the library or the maximum depth of the Merkle tree\"},\"50\":{\"message\":\"Account state size exceeded limits\"},\"128\":{\"message\":\"Null reference exception\"},\"129\":{\"message\":\"Invalid serialization prefix\"},\"130\":{\"message\":\"Invalid incoming message\"},\"131\":{\"message\":\"Constraints error\"},\"132\":{\"message\":\"Access denied\"},\"133\":{\"message\":\"Contract stopped\"},\"134\":{\"message\":\"Invalid argument\"},\"135\":{\"message\":\"Code of a contract was not found\"},\"136\":{\"message\":\"Invalid standard address\"},\"138\":{\"message\":\"Not a basechain address\"},\"22411\":{\"message\":\"Already refunded\"},\"39144\":{\"message\":\"Purchase not found\"},\"49729\":{\"message\":\"Unauthorized\"}},\"interfaces\":[\"org.ton.introspection.v0\",\"org.ton.abi.ipfs.v0\",\"org.ton.deploy.lazy.v0\",\"org.ton.ownable\"]}", "init": {"kind": "direct", "args": [{"name": "auction_address", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "user_address", "type": {"kind": "simple", "type": "address", "optional": false}}], "prefix": {"bits": 1, "value": 0}, "deployment": {"kind": "system-cell", "system": null}}, "sources": {"contracts/user_purchase.tact": "aW1wb3J0ICJAc3RkbGliL293bmFibGUiOwppbXBvcnQgIkBzdGRsaWIvZGVwbG95IjsKCi8vIEVycm9yIGNvZGVzIGZvciBiZXR0ZXIgZGVidWdnaW5nCmNvbnN0IEVSUk9SX1VOQVVUSE9SSVpFRDogSW50ID0gNjIwMDE7CmNvbnN0IEVSUk9SX1BVUkNIQVNFX05PVF9GT1VORDogSW50ID0gNjIwMDI7CmNvbnN0IEVSUk9SX0FMUkVBRFlfUkVGVU5ERUQ6IEludCA9IDYyMDAzOwoKLy8gT3AgY29kZXMgZm9yIG1lc3NhZ2VzCmNvbnN0IE9QX0NSRUFURV9VU0VSX1BVUkNIQVNFOiBJbnQgPSAweDQxNzM0YzBjOyAvLyAxMDk4MTY0NjA0CmNvbnN0IE9QX1JFRlVORDogSW50ID0gMHgzMWQ1YjVhYzsgLy8gODM2NTYwMTcyCmNvbnN0IE9QX1BST0NFU1NfUkVGVU5EOiBJbnQgPSAweDM3NmU4YjEyOyAvLyA5MzAwNTcxMDYKCi8vIE1lc3NhZ2VzIHdpdGggZXhwbGljaXQgb3AgY29kZXMKbWVzc2FnZSgweDQxNzM0YzBjKSBDcmVhdGVVc2VyUHVyY2hhc2UgewogICAgdXNlcjogQWRkcmVzczsKICAgIGFtb3VudDogSW50IGFzIGNvaW5zOwogICAgdG9rZW5zOiBJbnQgYXMgY29pbnM7CiAgICBjdXJyZW5jeTogSW50IGFzIHVpbnQ4OyAvLyAwPVRPTiwgMT1VU0RUCiAgICBwdXJjaGFzZV9tZXRob2Q6IEludCBhcyB1aW50ODsgLy8gMD1kaXJlY3QsIDE9c2lnbmF0dXJlX3ZlcmlmaWVkCiAgICBub25jZTogSW50IGFzIHVpbnQ2NDsgLy8gRm9yIHNpZ25hdHVyZSB2ZXJpZmljYXRpb24gcHVyY2hhc2VzCiAgICByb3VuZF9udW1iZXI6IEludCBhcyB1aW50MzI7IC8vIFJvdW5kIHdoZW4gcHVyY2hhc2Ugd2FzIG1hZGUKICAgIHVzZHRfZXF1aXZhbGVudF9hbW91bnQ6IEludCBhcyBjb2luczsgLy8gQW1vdW50IGluIFVTRFQgZXF1aXZhbGVudCB1bml0cyBmb3IgY2FwIHRyYWNraW5nCn0KCm1lc3NhZ2UoMHgzMWQ1YjVhYykgUmVmdW5kIHsKICAgIHB1cmNoYXNlX2lkOiBJbnQgYXMgdWludDMyOwp9CgptZXNzYWdlKDB4Mzc2ZThiMTIpIFByb2Nlc3NSZWZ1bmQgewogICAgdXNlcjogQWRkcmVzczsKICAgIGFtb3VudDogSW50IGFzIGNvaW5zOwogICAgZmVlOiBJbnQgYXMgY29pbnM7CiAgICBjdXJyZW5jeTogSW50IGFzIHVpbnQ4OyAvLyAwPVRPTiwgMT1VU0RUCiAgICByb3VuZF9udW1iZXI6IEludCBhcyB1aW50MzI7IC8vIFJvdW5kIHdoZW4gcHVyY2hhc2Ugd2FzIG1hZGUKICAgIHVzZHRfZXF1aXZhbGVudF9hbW91bnQ6IEludCBhcyBjb2luczsgLy8gQW1vdW50IGluIFVTRFQgZXF1aXZhbGVudCB1bml0cyBmb3IgY2FwIHRyYWNraW5nCn0KCi8vIFN0cnVjdHMKc3RydWN0IFB1cmNoYXNlUmVjb3JkIHsKICAgIGlkOiBJbnQgYXMgdWludDMyOwogICAgdXNlcjogQWRkcmVzczsKICAgIGFtb3VudDogSW50IGFzIGNvaW5zOwogICAgdG9rZW5zOiBJbnQgYXMgY29pbnM7CiAgICB0aW1lc3RhbXA6IEludCBhcyB1aW50NjQ7CiAgICBjdXJyZW5jeTogSW50IGFzIHVpbnQ4OwogICAgcHVyY2hhc2VfbWV0aG9kOiBJbnQgYXMgdWludDg7IC8vIDA9ZGlyZWN0LCAxPXNpZ25hdHVyZV92ZXJpZmllZAogICAgbm9uY2U6IEludCBhcyB1aW50NjQ7IC8vIEZvciBzaWduYXR1cmUgdmVyaWZpY2F0aW9uIHB1cmNoYXNlcwogICAgcm91bmRfbnVtYmVyOiBJbnQgYXMgdWludDMyOyAvLyBSb3VuZCB3aGVuIHB1cmNoYXNlIHdhcyBtYWRlCiAgICB1c2R0X2VxdWl2YWxlbnRfYW1vdW50OiBJbnQgYXMgY29pbnM7IC8vIEFtb3VudCBpbiBVU0RUIGVxdWl2YWxlbnQgdW5pdHMgZm9yIGNhcCB0cmFja2luZwp9Cgpjb250cmFjdCBVc2VyUHVyY2hhc2Ugd2l0aCBPd25hYmxlIHsKICAgIG93bmVyOiBBZGRyZXNzOwogICAgYXVjdGlvbl9hZGRyZXNzOiBBZGRyZXNzOwogICAgdXNlcl9hZGRyZXNzOiBBZGRyZXNzOwogICAgdG90YWxfcHVyY2hhc2VkOiBJbnQgYXMgY29pbnM7CiAgICB0b3RhbF9wYWlkOiBJbnQgYXMgY29pbnM7CiAgICBwdXJjaGFzZV9oaXN0b3J5OiBtYXA8SW50LCBQdXJjaGFzZVJlY29yZD47CiAgICByZWZ1bmRfaGlzdG9yeTogbWFwPEludCwgSW50PjsgLy8gcHVyY2hhc2VfaWQgLT4gcmVmdW5kX2Ftb3VudAogICAgcHVyY2hhc2VfaWRfY291bnRlcjogSW50IGFzIHVpbnQzMjsKICAgIHBhcnRpY2lwYXRlZF9yb3VuZHM6IG1hcDxJbnQsIEJvb2w+OyAvLyByb3VuZF9udW1iZXIgLT4gcGFydGljaXBhdGVkCiAgICAKICAgIGluaXQoYXVjdGlvbl9hZGRyZXNzOiBBZGRyZXNzLCB1c2VyX2FkZHJlc3M6IEFkZHJlc3MpIHsKICAgICAgICBzZWxmLm93bmVyID0gdXNlcl9hZGRyZXNzOyAvLyBVc2VyIG93bnMgdGhlaXIgcHVyY2hhc2UgY29udHJhY3QKICAgICAgICBzZWxmLmF1Y3Rpb25fYWRkcmVzcyA9IGF1Y3Rpb25fYWRkcmVzczsKICAgICAgICBzZWxmLnVzZXJfYWRkcmVzcyA9IHVzZXJfYWRkcmVzczsKICAgICAgICBzZWxmLnRvdGFsX3B1cmNoYXNlZCA9IDA7CiAgICAgICAgc2VsZi50b3RhbF9wYWlkID0gMDsKICAgICAgICBzZWxmLnB1cmNoYXNlX2lkX2NvdW50ZXIgPSAwOwogICAgICAgIC8vIE5vIHJvdW5kcyBwYXJ0aWNpcGF0ZWQgaW5pdGlhbGx5CiAgICB9CiAgICAKICAgIAogICAgLy8gRGVwbG95IG1lc3NhZ2UgaGFuZGxlcgogICAgcmVjZWl2ZSgiRGVwbG95IikgewogICAgICAgIC8vIEhhbmRsZSBkZXBsb3ltZW50IC0gbm8gc3BlY2lhbCBsb2dpYyBuZWVkZWQgZm9yIHRoaXMgY29udHJhY3QKICAgIH0KICAgIAogICAgLy8gQ3JlYXRlIG5ldyBwdXJjaGFzZSByZWNvcmQKICAgIHJlY2VpdmUobXNnOiBDcmVhdGVVc2VyUHVyY2hhc2UpIHsKICAgICAgICByZXF1aXJlKHNlbmRlcigpID09IHNlbGYuYXVjdGlvbl9hZGRyZXNzLCAiVW5hdXRob3JpemVkIik7CgogICAgICAgIHNlbGYucHVyY2hhc2VfaWRfY291bnRlciArPSAxOwogICAgICAgIGxldCBuZXdfcHVyY2hhc2U6IFB1cmNoYXNlUmVjb3JkID0gUHVyY2hhc2VSZWNvcmR7CiAgICAgICAgICAgIGlkOiBzZWxmLnB1cmNoYXNlX2lkX2NvdW50ZXIsCiAgICAgICAgICAgIHVzZXI6IG1zZy51c2VyLAogICAgICAgICAgICBhbW91bnQ6IG1zZy5hbW91bnQsCiAgICAgICAgICAgIHRva2VuczogbXNnLnRva2VucywKICAgICAgICAgICAgdGltZXN0YW1wOiBub3coKSwKICAgICAgICAgICAgY3VycmVuY3k6IG1zZy5jdXJyZW5jeSwKICAgICAgICAgICAgcHVyY2hhc2VfbWV0aG9kOiBtc2cucHVyY2hhc2VfbWV0aG9kLAogICAgICAgICAgICBub25jZTogbXNnLm5vbmNlLAogICAgICAgICAgICByb3VuZF9udW1iZXI6IG1zZy5yb3VuZF9udW1iZXIsCiAgICAgICAgICAgIHVzZHRfZXF1aXZhbGVudF9hbW91bnQ6IG1zZy51c2R0X2VxdWl2YWxlbnRfYW1vdW50CiAgICAgICAgfTsKCiAgICAgICAgc2VsZi5wdXJjaGFzZV9oaXN0b3J5LnNldChzZWxmLnB1cmNoYXNlX2lkX2NvdW50ZXIsIG5ld19wdXJjaGFzZSk7CiAgICAgICAgc2VsZi50b3RhbF9wdXJjaGFzZWQgKz0gbXNnLnRva2VuczsKICAgICAgICBzZWxmLnRvdGFsX3BhaWQgKz0gbXNnLmFtb3VudDsKCiAgICAgICAgLy8gVHJhY2sgcm91bmQgcGFydGljaXBhdGlvbgogICAgICAgIHNlbGYucGFydGljaXBhdGVkX3JvdW5kcy5zZXQobXNnLnJvdW5kX251bWJlciwgdHJ1ZSk7CgogICAgICAgIGNhc2hiYWNrKHNlbmRlcigpKTsKICAgIH0KICAgIAogICAgLy8gUmVxdWVzdCByZWZ1bmQKICAgIHJlY2VpdmUobXNnOiBSZWZ1bmQpIHsKICAgICAgICByZXF1aXJlKHNlbmRlcigpID09IHNlbGYudXNlcl9hZGRyZXNzLCAiVW5hdXRob3JpemVkIik7CgogICAgICAgIGxldCBwdXJjaGFzZTogUHVyY2hhc2VSZWNvcmQ/ID0gc2VsZi5wdXJjaGFzZV9oaXN0b3J5LmdldChtc2cucHVyY2hhc2VfaWQpOwogICAgICAgIHJlcXVpcmUocHVyY2hhc2UgIT0gbnVsbCwgIlB1cmNoYXNlIG5vdCBmb3VuZCIpOwogICAgICAgIHJlcXVpcmUoc2VsZi5yZWZ1bmRfaGlzdG9yeS5nZXQobXNnLnB1cmNoYXNlX2lkKSA9PSBudWxsLCAiQWxyZWFkeSByZWZ1bmRlZCIpOwogICAgICAgIAogICAgICAgIGxldCBwdXJjaGFzZV9kYXRhOiBQdXJjaGFzZVJlY29yZCA9IHB1cmNoYXNlISE7CiAgICAgICAgCiAgICAgICAgLy8gTWFyayBhcyByZWZ1bmRlZAogICAgICAgIHNlbGYucmVmdW5kX2hpc3Rvcnkuc2V0KG1zZy5wdXJjaGFzZV9pZCwgcHVyY2hhc2VfZGF0YS5hbW91bnQpOwogICAgICAgIHNlbGYudG90YWxfcHVyY2hhc2VkIC09IHB1cmNoYXNlX2RhdGEudG9rZW5zOwogICAgICAgIHNlbGYudG90YWxfcGFpZCAtPSBwdXJjaGFzZV9kYXRhLmFtb3VudDsKICAgICAgICAKICAgICAgICAvLyBTZW5kIHJlZnVuZCByZXF1ZXN0IHRvIGF1Y3Rpb24gY29udHJhY3QKICAgICAgICBzZW5kKFNlbmRQYXJhbWV0ZXJzewogICAgICAgICAgICB0bzogc2VsZi5hdWN0aW9uX2FkZHJlc3MsCiAgICAgICAgICAgIHZhbHVlOiB0b24oIjAuMDUiKSwKICAgICAgICAgICAgbW9kZTogU2VuZElnbm9yZUVycm9ycywKICAgICAgICAgICAgYm91bmNlOiBmYWxzZSwKICAgICAgICAgICAgYm9keTogUHJvY2Vzc1JlZnVuZHsKICAgICAgICAgICAgICAgIHVzZXI6IHNlbGYudXNlcl9hZGRyZXNzLAogICAgICAgICAgICAgICAgYW1vdW50OiBwdXJjaGFzZV9kYXRhLmFtb3VudCwKICAgICAgICAgICAgICAgIGZlZTogKHB1cmNoYXNlX2RhdGEuYW1vdW50ICogNSkgLyAxMDAsCiAgICAgICAgICAgICAgICBjdXJyZW5jeTogcHVyY2hhc2VfZGF0YS5jdXJyZW5jeSwKICAgICAgICAgICAgICAgIHJvdW5kX251bWJlcjogcHVyY2hhc2VfZGF0YS5yb3VuZF9udW1iZXIsCiAgICAgICAgICAgICAgICB1c2R0X2VxdWl2YWxlbnRfYW1vdW50OiBwdXJjaGFzZV9kYXRhLnVzZHRfZXF1aXZhbGVudF9hbW91bnQKICAgICAgICAgICAgfS50b0NlbGwoKQogICAgICAgIH0pOwogICAgfQogICAgCiAgICAvLyBHZXR0ZXJzCiAgICBnZXQgZnVuIHRvdGFsX3B1cmNoYXNlZCgpOiBJbnQgewogICAgICAgIHJldHVybiBzZWxmLnRvdGFsX3B1cmNoYXNlZDsKICAgIH0KICAgIAogICAgZ2V0IGZ1biB0b3RhbF9wYWlkKCk6IEludCB7CiAgICAgICAgcmV0dXJuIHNlbGYudG90YWxfcGFpZDsKICAgIH0KICAgIAogICAgZ2V0IGZ1biBwdXJjaGFzZV9pZF9jb3VudGVyKCk6IEludCB7CiAgICAgICAgcmV0dXJuIHNlbGYucHVyY2hhc2VfaWRfY291bnRlcjsKICAgIH0KICAgIAogICAgZ2V0IGZ1biBwdXJjaGFzZV9kZXRhaWxzKHB1cmNoYXNlX2lkOiBJbnQpOiBQdXJjaGFzZVJlY29yZD8gewogICAgICAgIHJldHVybiBzZWxmLnB1cmNoYXNlX2hpc3RvcnkuZ2V0KHB1cmNoYXNlX2lkKTsKICAgIH0KCiAgICBnZXQgZnVuIGlzX3JlZnVuZGVkKHB1cmNoYXNlX2lkOiBJbnQpOiBCb29sIHsKICAgICAgICByZXR1cm4gc2VsZi5yZWZ1bmRfaGlzdG9yeS5nZXQocHVyY2hhc2VfaWQpICE9IG51bGw7CiAgICB9CgogICAgLy8gTmV3IGdldHRlcnMgZm9yIHNpZ25hdHVyZSB2ZXJpZmljYXRpb24gc3VwcG9ydAogICAgZ2V0IGZ1biBzaWduYXR1cmVfdmVyaWZpZWRfcHVyY2hhc2VzKCk6IEludCB7CiAgICAgICAgbGV0IGNvdW50OiBJbnQgPSAwOwogICAgICAgIGxldCBpOiBJbnQgPSAxOwogICAgICAgIHdoaWxlIChpIDw9IHNlbGYucHVyY2hhc2VfaWRfY291bnRlcikgewogICAgICAgICAgICBsZXQgcHVyY2hhc2U6IFB1cmNoYXNlUmVjb3JkPyA9IHNlbGYucHVyY2hhc2VfaGlzdG9yeS5nZXQoaSk7CiAgICAgICAgICAgIGlmIChwdXJjaGFzZSAhPSBudWxsKSB7CiAgICAgICAgICAgICAgICBsZXQgcmVjb3JkOiBQdXJjaGFzZVJlY29yZCA9IHB1cmNoYXNlISE7CiAgICAgICAgICAgICAgICBpZiAocmVjb3JkLnB1cmNoYXNlX21ldGhvZCA9PSAxKSB7CiAgICAgICAgICAgICAgICAgICAgY291bnQgKz0gMTsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgfQogICAgICAgICAgICBpICs9IDE7CiAgICAgICAgfQogICAgICAgIHJldHVybiBjb3VudDsKICAgIH0KCiAgICBnZXQgZnVuIHB1cmNoYXNlX21ldGhvZF9zdGF0cygpOiBtYXA8SW50LCBJbnQ+IHsKICAgICAgICBsZXQgc3RhdHM6IG1hcDxJbnQsIEludD4gPSBlbXB0eU1hcCgpOwogICAgICAgIGxldCBpOiBJbnQgPSAxOwogICAgICAgIHdoaWxlIChpIDw9IHNlbGYucHVyY2hhc2VfaWRfY291bnRlcikgewogICAgICAgICAgICBsZXQgcHVyY2hhc2U6IFB1cmNoYXNlUmVjb3JkPyA9IHNlbGYucHVyY2hhc2VfaGlzdG9yeS5nZXQoaSk7CiAgICAgICAgICAgIGlmIChwdXJjaGFzZSAhPSBudWxsKSB7CiAgICAgICAgICAgICAgICBsZXQgcmVjb3JkOiBQdXJjaGFzZVJlY29yZCA9IHB1cmNoYXNlISE7CiAgICAgICAgICAgICAgICBsZXQgY3VycmVudF9jb3VudDogSW50PyA9IHN0YXRzLmdldChyZWNvcmQucHVyY2hhc2VfbWV0aG9kKTsKICAgICAgICAgICAgICAgIGlmIChjdXJyZW50X2NvdW50ID09IG51bGwpIHsKICAgICAgICAgICAgICAgICAgICBzdGF0cy5zZXQocmVjb3JkLnB1cmNoYXNlX21ldGhvZCwgMSk7CiAgICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgICAgIHN0YXRzLnNldChyZWNvcmQucHVyY2hhc2VfbWV0aG9kLCBjdXJyZW50X2NvdW50ISEgKyAxKTsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgfQogICAgICAgICAgICBpICs9IDE7CiAgICAgICAgfQogICAgICAgIHJldHVybiBzdGF0czsKICAgIH0KCiAgICAvLyBSb3VuZC1yZWxhdGVkIGdldHRlcnMKICAgIGdldCBmdW4gcHVyY2hhc2VzX2J5X3JvdW5kKHJvdW5kX251bWJlcjogSW50KTogbWFwPEludCwgUHVyY2hhc2VSZWNvcmQ+IHsKICAgICAgICBsZXQgcm91bmRfcHVyY2hhc2VzOiBtYXA8SW50LCBQdXJjaGFzZVJlY29yZD4gPSBlbXB0eU1hcCgpOwogICAgICAgIGxldCBpOiBJbnQgPSAxOwogICAgICAgIHdoaWxlIChpIDw9IHNlbGYucHVyY2hhc2VfaWRfY291bnRlcikgewogICAgICAgICAgICBsZXQgcHVyY2hhc2U6IFB1cmNoYXNlUmVjb3JkPyA9IHNlbGYucHVyY2hhc2VfaGlzdG9yeS5nZXQoaSk7CiAgICAgICAgICAgIGlmIChwdXJjaGFzZSAhPSBudWxsKSB7CiAgICAgICAgICAgICAgICBsZXQgcmVjb3JkOiBQdXJjaGFzZVJlY29yZCA9IHB1cmNoYXNlISE7CiAgICAgICAgICAgICAgICBpZiAocmVjb3JkLnJvdW5kX251bWJlciA9PSByb3VuZF9udW1iZXIpIHsKICAgICAgICAgICAgICAgICAgICByb3VuZF9wdXJjaGFzZXMuc2V0KGksIHJlY29yZCk7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0KICAgICAgICAgICAgaSArPSAxOwogICAgICAgIH0KICAgICAgICByZXR1cm4gcm91bmRfcHVyY2hhc2VzOwogICAgfQoKICAgIC8vIEdldCByb3VuZCBzdGF0aXN0aWNzIGZvciBhIHNwZWNpZmljIHJvdW5kCiAgICBnZXQgZnVuIHJvdW5kX3RvdGFsX2Ftb3VudChyb3VuZF9udW1iZXI6IEludCk6IEludCB7CiAgICAgICAgbGV0IHRvdGFsX2Ftb3VudDogSW50ID0gMDsKICAgICAgICBsZXQgaTogSW50ID0gMTsKICAgICAgICB3aGlsZSAoaSA8PSBzZWxmLnB1cmNoYXNlX2lkX2NvdW50ZXIpIHsKICAgICAgICAgICAgbGV0IHB1cmNoYXNlOiBQdXJjaGFzZVJlY29yZD8gPSBzZWxmLnB1cmNoYXNlX2hpc3RvcnkuZ2V0KGkpOwogICAgICAgICAgICBpZiAocHVyY2hhc2UgIT0gbnVsbCkgewogICAgICAgICAgICAgICAgbGV0IHJlY29yZDogUHVyY2hhc2VSZWNvcmQgPSBwdXJjaGFzZSEhOwogICAgICAgICAgICAgICAgaWYgKHJlY29yZC5yb3VuZF9udW1iZXIgPT0gcm91bmRfbnVtYmVyKSB7CiAgICAgICAgICAgICAgICAgICAgdG90YWxfYW1vdW50ICs9IHJlY29yZC5hbW91bnQ7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0KICAgICAgICAgICAgaSArPSAxOwogICAgICAgIH0KICAgICAgICByZXR1cm4gdG90YWxfYW1vdW50OwogICAgfQoKICAgIGdldCBmdW4gcm91bmRfdG90YWxfdG9rZW5zKHJvdW5kX251bWJlcjogSW50KTogSW50IHsKICAgICAgICBsZXQgdG90YWxfdG9rZW5zOiBJbnQgPSAwOwogICAgICAgIGxldCBpOiBJbnQgPSAxOwogICAgICAgIHdoaWxlIChpIDw9IHNlbGYucHVyY2hhc2VfaWRfY291bnRlcikgewogICAgICAgICAgICBsZXQgcHVyY2hhc2U6IFB1cmNoYXNlUmVjb3JkPyA9IHNlbGYucHVyY2hhc2VfaGlzdG9yeS5nZXQoaSk7CiAgICAgICAgICAgIGlmIChwdXJjaGFzZSAhPSBudWxsKSB7CiAgICAgICAgICAgICAgICBsZXQgcmVjb3JkOiBQdXJjaGFzZVJlY29yZCA9IHB1cmNoYXNlISE7CiAgICAgICAgICAgICAgICBpZiAocmVjb3JkLnJvdW5kX251bWJlciA9PSByb3VuZF9udW1iZXIpIHsKICAgICAgICAgICAgICAgICAgICB0b3RhbF90b2tlbnMgKz0gcmVjb3JkLnRva2VuczsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgfQogICAgICAgICAgICBpICs9IDE7CiAgICAgICAgfQogICAgICAgIHJldHVybiB0b3RhbF90b2tlbnM7CiAgICB9CgogICAgZ2V0IGZ1biByb3VuZF9wdXJjaGFzZV9jb3VudChyb3VuZF9udW1iZXI6IEludCk6IEludCB7CiAgICAgICAgbGV0IHB1cmNoYXNlX2NvdW50OiBJbnQgPSAwOwogICAgICAgIGxldCBpOiBJbnQgPSAxOwogICAgICAgIHdoaWxlIChpIDw9IHNlbGYucHVyY2hhc2VfaWRfY291bnRlcikgewogICAgICAgICAgICBsZXQgcHVyY2hhc2U6IFB1cmNoYXNlUmVjb3JkPyA9IHNlbGYucHVyY2hhc2VfaGlzdG9yeS5nZXQoaSk7CiAgICAgICAgICAgIGlmIChwdXJjaGFzZSAhPSBudWxsKSB7CiAgICAgICAgICAgICAgICBsZXQgcmVjb3JkOiBQdXJjaGFzZVJlY29yZCA9IHB1cmNoYXNlISE7CiAgICAgICAgICAgICAgICBpZiAocmVjb3JkLnJvdW5kX251bWJlciA9PSByb3VuZF9udW1iZXIpIHsKICAgICAgICAgICAgICAgICAgICBwdXJjaGFzZV9jb3VudCArPSAxOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgICAgIGkgKz0gMTsKICAgICAgICB9CiAgICAgICAgcmV0dXJuIHB1cmNoYXNlX2NvdW50OwogICAgfQoKICAgIGdldCBmdW4gcGFydGljaXBhdGVkX3JvdW5kcygpOiBtYXA8SW50LCBCb29sPiB7CiAgICAgICAgcmV0dXJuIHNlbGYucGFydGljaXBhdGVkX3JvdW5kczsKICAgIH0KCiAgICBnZXQgZnVuIHBhcnRpY2lwYXRlZF9pbl9yb3VuZChyb3VuZF9udW1iZXI6IEludCk6IEJvb2wgewogICAgICAgIHJldHVybiBzZWxmLnBhcnRpY2lwYXRlZF9yb3VuZHMuZ2V0KHJvdW5kX251bWJlcikgIT0gbnVsbDsKICAgIH0KCiAgICBnZXQgZnVuIHJvdW5kX2NvdW50KCk6IEludCB7CiAgICAgICAgbGV0IGNvdW50OiBJbnQgPSAwOwogICAgICAgIGxldCBqOiBJbnQgPSAxOwogICAgICAgIHdoaWxlIChqIDw9IDEwMDApIHsgLy8gQXNzdW1pbmcgbWF4IDEwMDAgcm91bmRzCiAgICAgICAgICAgIGlmIChzZWxmLnBhcnRpY2lwYXRlZF9yb3VuZHMuZ2V0KGopICE9IG51bGwpIHsKICAgICAgICAgICAgICAgIGNvdW50ICs9IDE7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgaiArPSAxOwogICAgICAgIH0KICAgICAgICByZXR1cm4gY291bnQ7CiAgICB9CgogICAgZ2V0IGZ1biBhbGxfcmVjb3JkcygpOiBtYXA8SW50LCBQdXJjaGFzZVJlY29yZD4gewogICAgICAgIHJldHVybiBzZWxmLnB1cmNoYXNlX2hpc3Rvcnk7CiAgICB9Cn0="}, "compiler": {"name": "tact", "version": "1.6.13", "parameters": "{\"entrypoint\":\"contracts/user_purchase.tact\",\"options\":{\"debug\":false,\"external\":false}}"}}