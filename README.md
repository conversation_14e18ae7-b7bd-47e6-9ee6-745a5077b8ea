# Onion Auction Statistics Collector

这是一个用于收集和分析 TON 网络上 Onion Auction 合约统计数据的项目。

## 功能特性

- 📊 收集所有用户购买数据
- 👥 统计用户购买行为和代币分配
- 🔄 按轮次分析购买情况
- 💰 统计 TON 和 USDT 募集金额
- 📈 生成详细的统计报告

## 安装和使用

### 1. 安装依赖

```bash
npm install
```

### 2. 运行完整统计

```bash
npm start
```

这将收集所有历史数据并生成详细报告，包括：
- 所有用户购买记录
- 按轮次统计
- 用户排行榜
- 主合约状态

### 3. 运行快速查询

```bash
npm run stats
```

这将显示最近的购买记录和主合约基本信息。

### 4. CSV报告导出 ⭐ 新功能

```bash
# 自动导出 (运行统计收集时自动导出)
npm start

# 单独导出最新统计数据为CSV
npm run export-csv
# 或者
npm run csv
```

### 5. 缓存和断点续传管理

```bash
# 清除用户合约地址缓存
npm run clear-cache

# 清除checkpoint数据 (断点续传数据)
npm run clear-checkpoint

# 清除所有缓存和checkpoint数据
npm run clear-all

# 显示帮助信息
npm run help
```

## 输出文件

运行完整统计后会生成文件：

### JSON数据文件
- `user_contracts_cache.json` - 用户合约地址缓存文件 (持久)
- `user_data_cache.json` - 用户合约数据缓存文件 (持久)
- `checkpoint_data.json` - 处理进度断点数据 (运行时临时文件)
- `raw-stats-YYYY-MM-DD.json` - 原始统计数据
- `stats-report-YYYY-MM-DD.json` - 格式化的统计报告

### CSV报告文件 ⭐ 新功能
- `csv_reports/users-stats-YYYY-MM-DD.csv` - 用户统计表 (含友好地址)
- `csv_reports/rounds-stats-YYYY-MM-DD.csv` - 轮次统计表
- `csv_reports/purchase-details-YYYY-MM-DD.csv` - 购买明细表
- `csv_reports/summary-stats-YYYY-MM-DD.csv` - 汇总统计表

## 项目结构

```
src/
├── api-client.js       # TonCenter API 客户端
├── contract-reader.js  # 合约数据读取器
├── stats-collector.js  # 统计数据收集器
├── index.js           # 主应用入口
└── stats.js           # 快速查询脚本
```

## 配置

### 合约地址

主合约地址在 `src/stats-collector.js` 中配置：

```javascript
this.AUCTION_CONTRACT = '0:78587F92178E8DEFF56631F2DE97493F63DA59798D6BA819048F05B2DC2D8F9D';
```

### API 设置

默认使用 TonCenter 的公共 API。如需修改，请在 `src/api-client.js` 中更改：

```javascript
constructor(baseURL = 'https://toncenter.com/api/v3') {
```

## 数据收集流程

1. **加载缓存**: 检查本地是否有用户合约地址缓存
2. **收集用户购买合约**: 通过监听主合约的 `CreateUserPurchase` 消息，只获取新增的合约
3. **更新缓存**: 将新发现的合约地址添加到缓存中
4. **读取用户数据**: 调用每个用户购买合约的 get 方法
5. **聚合统计**: 按用户、轮次、代币类型等维度统计
6. **生成报告**: 输出 JSON 格式的详细报告

## 🔄 智能缓存机制

### 多层缓存设计
- **合约地址缓存**: 存储所有用户购买合约地址 (`user_contracts_cache.json`)
- **合约数据缓存**: 存储每个合约的完整数据 (`user_data_cache.json`) ⭐ 新功能
- **checkpoint缓存**: 存储处理进度和中间结果 (`checkpoint_data.json`)

### 运行优化
- **首次运行**: 收集所有历史数据，建立完整缓存
- **后续运行**: 极速执行，直接使用缓存数据，只处理新增合约
- **增量更新**: 自动检测新增合约并更新缓存
- **数据一致性**: 缓存数据与区块链数据保持同步
- **手动清除**: 使用 `npm run clear-cache` 可以清除所有缓存

## 🔄 断点续传机制

- **自动checkpoint**: 每处理10个用户合约自动保存进度到 `checkpoint_data.json`
- **错误恢复**: 遇到网络错误、API限制时可重新运行，自动从断点继续
- **进度跟踪**: 显示已处理和剩余的合约数量
- **数据完整性**: checkpoint包含已收集的统计数据，确保不丢失
- **自动清理**: 完成收集后自动删除checkpoint文件

## 🚦 API限制和错误处理

### 智能限流机制
- **动态延迟**: 根据请求频率自动调整延迟时间 (200ms-800ms)
- **批量休息**: 每100个请求自动休息2秒
- **指数退避**: 遇到429错误时使用指数退避算法重试

### 强化重试机制
- **多次重试**: 每个API请求最多重试5次
- **错误分类**: 区分可重试错误(429, 5xx, 网络超时)和不可重试错误
- **随机抖动**: 避免多实例同时重试造成的雷群效应

### 错误恢复
- **断点续传**: 遇到严重错误时保存进度，重新运行自动恢复
- **部分成功**: 单个合约出错不影响整体进度
- **详细日志**: 记录所有错误和重试过程

## 🛠️ 使用建议

### 首次运行
```bash
npm start
```

### 遇到429错误或网络中断
```bash
# 直接重新运行，自动从断点继续
npm start
```

### 完全重新开始
```bash
# 清除所有数据后重新运行
npm run clear-all
npm start
```

## 🚀 性能提升

### 运行速度对比
- **首次运行**: ~5-10分钟 (需要调用所有合约的get方法)
- **后续运行**: ~10-30秒 (直接使用缓存数据) ⚡️ **提升95%+**
- **新增合约**: 只处理增量数据，近乎瞬时完成

### 缓存效率
```
📊 处理统计: 缓存命中 345 个，新获取 3 个，总计 348 个合约
```
- **缓存命中率**: 通常 >99%
- **API调用减少**: 从数千次减少到个位数
- **网络依赖**: 几乎消除对网络稳定性的依赖

## 📈 CSV报告功能

### 自动生成4类报告
1. **用户统计表** (`users-stats-*.csv`)
   - 用户排名、地址(原始+友好格式)、购买统计
   - 包含总金额、代币数量、参与轮次等

2. **轮次统计表** (`rounds-stats-*.csv`) 
   - 每轮参与用户数、交易金额、代币分配
   - 平均值统计便于分析趋势

3. **购买明细表** (`purchase-details-*.csv`)
   - 所有购买交易的详细记录
   - 按时间排序，包含nonce、货币类型等

4. **汇总统计表** (`summary-stats-*.csv`)
   - 关键指标总览
   - 主合约状态和整体数据

### 非技术人员友好
- ✅ Excel/Google Sheets 可直接打开
- ✅ 中文列名，易于理解
- ✅ 用户友好地址格式 (EQxxx...)
- ✅ 自动格式化数字精度
- ✅ UTF-8编码支持中文

## 扩展功能

可以轻松扩展以支持：
- 实时数据监控
- 更多统计维度
- 数据可视化
- 数据库存储

## 注意事项

- 首次运行可能需要较长时间，取决于历史数据量
- 确保网络连接稳定
- 大量API调用可能触发速率限制

## 示例输出

```
📊 统计摘要
================================
📅 生成时间: 2024-01-15T10:30:00.000Z
👥 总用户数: 1,234
🛒 总购买次数: 5,678
🪙 总代币数量: 123,456.7890
🎯 总轮次数: 15

🏢 主合约状态:
  - 状态: 进行中
  - 当前轮次: 15
  - 当前价格: 0.2500 TON
  - TON募集: 10,234.5678 TON
  - USDT募集: 50,000.00 USDT
  - 代币售出: 123,456.7890
  - 剩余代币: 876,543.2110
```