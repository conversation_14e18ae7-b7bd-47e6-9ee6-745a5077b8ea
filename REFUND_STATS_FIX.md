# 退款数据统计修复指南

## 问题描述

虽然`user_data_cache.json`中包含了退款数据，但这些数据没有被正确包含到最终的统计文件和CSV报告中。

## 🔧 已修复的问题

### 1. **统计收集器修复** (`src/stats-collector.js`)
- ✅ 添加了退款数据处理逻辑
- ✅ 在用户统计中包含`refundCount`和`totalRefunded`字段
- ✅ 累计总退款次数到全局统计
- ✅ 添加了退款统计计算方法
- ✅ 在报告中包含退款摘要和详情

### 2. **CSV导出器修复** (`src/csv-exporter.js`)
- ✅ 修复了用户统计CSV中的退款字段
- ✅ 改进了退款明细CSV导出
- ✅ 支持汇总退款数据的导出

### 3. **新增工具和测试**
- ✅ 创建了退款数据测试脚本
- ✅ 创建了统计重新生成工具
- ✅ 添加了数据验证功能

## 🚀 使用方法

### 方法1: 重新生成统计报告（推荐）

```bash
# 使用现有缓存数据重新生成包含退款数据的统计报告
npm run regenerate-stats
```

这将：
- 使用现有的`user_data_cache.json`
- 重新处理所有用户数据，包含退款信息
- 生成新的统计报告和CSV文件
- 自动验证退款数据完整性

### 方法2: 测试退款数据处理

```bash
# 测试退款数据是否正确处理
npm run test-refund-stats
```

这将显示：
- 缓存中的退款数据统计
- 统计收集器的处理结果
- CSV导出功能测试
- 现有统计文件的检查

### 方法3: 验证现有统计

```bash
# 验证现有统计文件是否包含退款数据
npm run validate-stats
```

### 方法4: 完整重新收集（如果需要）

```bash
# 使用修复后的系统重新收集所有数据
npm start
```

## 📊 预期结果

修复后的统计报告将包含：

### 统计摘要
```json
{
  "summary": {
    "totalRefunds": 123,
    "refundSummary": {
      "totalRefundAmount": 1000000000000,
      "usersWithRefunds": 45,
      "refundRate": "12.50",
      "averageRefundAmount": "22.2222",
      "maxRefundAmount": 100000000000,
      "maxRefundUser": "0:abc123..."
    }
  }
}
```

### 用户数据
```json
{
  "address": "0:user123...",
  "refundCount": 2,
  "totalRefunded": 50000000000,
  "totalPurchased": 1000000000000,
  "totalPaid": 100000000000
}
```

### CSV报告
- **用户统计CSV**: 包含"退款次数"和"总退款金额_TON"列
- **退款明细CSV**: 专门的退款详情报告

## 🔍 验证步骤

### 1. 检查统计报告
```bash
# 查看最新的统计报告
cat stats-report-with-refunds-*.json | jq '.summary.refundSummary'
```

### 2. 检查CSV文件
```bash
# 检查用户统计CSV的退款列
head -1 reports/user-stats-*.csv | grep "退款"
```

### 3. 验证数据一致性
```bash
# 运行验证脚本
npm run validate-stats
```

## 📁 生成的文件

### 统计报告
- `stats-report-with-refunds-YYYY-MM-DD.json`: 包含退款数据的完整统计报告

### CSV报告（在reports/目录下）
- `user-stats-YYYY-MM-DD.csv`: 用户统计（包含退款字段）
- `refund-details-YYYY-MM-DD.csv`: 退款明细
- `purchase-details-YYYY-MM-DD.csv`: 购买明细
- `summary-report-YYYY-MM-DD.csv`: 汇总报告

## 🎯 Token发放准备

修复后的数据可以安全用于token发放：

### 1. 净购买金额计算
```
净购买金额 = 总支付金额 - 总退款金额
净代币数量 = 总购买代币 - 退款对应代币
```

### 2. 发放清单生成
```bash
# 生成包含退款调整的发放清单
npm run export-csv
```

### 3. 数据验证
- ✅ 退款金额不超过购买金额
- ✅ 退款次数不超过购买次数
- ✅ 用户净余额为正数
- ✅ 总计数据一致性

## 🚨 注意事项

### 数据完整性
1. **退款时间**: 当前只有汇总数据，没有具体退款时间
2. **退款详情**: 如需详细的退款记录，需要从区块链获取
3. **币种区分**: 当前假设所有退款都是TON

### 建议操作
1. **备份数据**: 操作前备份现有统计文件
2. **验证结果**: 生成报告后务必验证数据准确性
3. **抽查验证**: 随机抽查几个用户的退款数据
4. **区块链对比**: 重要用户的数据建议与区块链记录对比

## 📞 故障排除

### 问题1: 退款数据仍为0
**解决**: 
1. 先运行`npm run fix-refunds`修复缓存数据
2. 再运行`npm run regenerate-stats`重新生成统计

### 问题2: CSV中缺少退款字段
**解决**: 
1. 确认使用了修复后的代码
2. 重新运行`npm run export-csv`

### 问题3: 数据不一致
**解决**: 
1. 运行`npm run validate-stats`检查
2. 查看错误日志文件
3. 必要时重新收集数据

---

通过这些修复，您的统计系统现在能够正确处理和报告退款数据，确保token发放的准确性。
