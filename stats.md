统计购买的用户，金额，token 数量，退款情况。

通过 toncenter 的 api 读取数据进行统计，文档在 https://toncenter.com/api/v3/index.html ，调用文档时注意分页逻辑。

## 1. 所有购买过的用户
通过 `GET api/v3/messages` 来获取所有购买过的用户，创建的 `UserPurchase` 合约实例。

``` sh
curl -X 'GET' \
  'https://toncenter.com/api/v3/messages?source=0%3A78587f92178e8deff56631f2de97493f63da59798d6ba819048f05b2dc2d8f9d&opcode=0x41734c0c&limit=10&offset=0&sort=desc' \
  -H 'accept: application/json'
```

示例响应如下：
```json
{
  "messages": [
    {
      "hash": "Dwn7imSGCgrGyaEA0cRpIAm92KL4w2OW9Lqw7TwOh6c=",
      "source": "0:78587F92178E8DEFF56631F2DE97493F63DA59798D6BA819048F05B2DC2D8F9D",
      "destination": "0:23E73F7780B89E376D59A924029327D0DFECBD84AE5E069AA824DA48E8273055",
      "value": "87901200",
      "value_extra_currencies": {},
      "fwd_fee": "8065929",
      "ihr_fee": "0",
      "created_lt": "60129778000004",
      "created_at": "1754344080",
      "opcode": "0x41734c0c",
      "ihr_disabled": true,
      "bounce": false,
      "bounced": false,
      "import_fee": null,
      "in_msg_tx_hash": "k2eFAEVVbH0DH4O7r1VvhryWqjXQEKqpzru8GygpCVc=",
      "out_msg_tx_hash": "0pbNoDCa3TmepQTqeA35D7J2+Nx2UmBFGLpadk7RrQU=",
      "message_content": {
        "hash": "8MsCyZO9d4EEjiwa47M0Ak/i28Qye7TRFnrPwp/VvgM=",
        "body": "te6cckEBAQEAQwAAgUFzTAyACFU+SoZlr9GipBbPyqPJGgqzifLDdKlZX250h6mHLWsI7msoAKn151uzAAAgAGAOMdDVSQAAAAXG0DWPOuzaEw==",
        "decoded": null
      },
      "init_state": {
        "hash": "I+c/d4C4njdtWakkApMn0N/svYSuXgaaqCTaSOgnMFU=",
        "body": "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",
        "decoded": null
      }
    }
  ],
  "address_book": {
    "0:23E73F7780B89E376D59A924029327D0DFECBD84AE5E069AA824DA48E8273055": {
      "user_friendly": "EQAj5z93gLieN21ZqSQCkyfQ3-y9hK5eBpqoJNpI6CcwVYG2",
      "domain": null
    },
    "0:78587F92178E8DEFF56631F2DE97493F63DA59798D6BA819048F05B2DC2D8F9D": {
      "user_friendly": "EQB4WH-SF46N7_VmMfLel0k_Y9pZeY1rqBkEjwWy3C2PnTtr",
      "domain": null
    }
  },
  "metadata": {}
}
```

## 2. 用户的购买数量和退款数量
通过合约的 get method 来读取用户的购买数量和退款数量。详见 contracts/user_purchase.tact 。