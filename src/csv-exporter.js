#!/usr/bin/env node

import fs from 'fs/promises';
import path from 'path';

// 尝试导入ton-core，如果失败则使用简化版本
let Address;
try {
    const tonCore = await import('ton-core');
    Address = tonCore.Address;
} catch (error) {
    console.warn('ton-core未安装，将使用简化的地址转换');
    Address = null;
}

export class CSVExporter {
    constructor() {
        this.outputDir = 'csv_reports';
    }

    // 将地址转换为用户友好格式
    toUserFriendlyAddress(rawAddress) {
        if (!rawAddress) return '';
        
        // 如果已经是用户友好格式，直接返回
        if (rawAddress.includes('EQ') || rawAddress.includes('UQ')) {
            return rawAddress;
        }
        
        try {
            // 如果有ton-core库，使用正确的转换
            if (Address && rawAddress.startsWith('0:')) {
                const address = Address.parse(rawAddress);
                return address.toString({
                    urlSafe: true,
                    bounceable: true,
                    testOnly: false
                });
            }
            
            // 简化的地址转换
            const hex = rawAddress.replace('0:', '');
            return `EQ${this.hexToBase64Url(hex)}`;
        } catch (error) {
            console.warn(`地址转换失败: ${rawAddress}`, error.message);
            return rawAddress; // 返回原始地址
        }
    }

    // 简化的hex到base64url转换
    hexToBase64Url(hex) {
        try {
            const bytes = hex.match(/.{1,2}/g).map(byte => parseInt(byte, 16));
            const base64 = Buffer.from(bytes).toString('base64');
            return base64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
        } catch (error) {
            return hex.substring(0, 20) + '...'; // 简化显示
        }
    }

    // 转换为CSV格式的字符串
    arrayToCSV(data) {
        if (!data || data.length === 0) return '';
        
        const headers = Object.keys(data[0]);
        const csvContent = [
            headers.join(','),
            ...data.map(row => 
                headers.map(header => {
                    let value = row[header];
                    // 处理包含逗号、引号或换行的值
                    if (value === null || value === undefined) value = '';
                    if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
                        value = `"${value.replace(/"/g, '""')}"`;
                    }
                    return value;
                }).join(',')
            )
        ].join('\n');
        
        return csvContent;
    }

    // 格式化数字
    formatNumber(value, decimals = 4) {
        if (typeof value === 'bigint') {
            value = Number(value);
        }
        return Number(value || 0).toFixed(decimals);
    }

    // 格式化代币数量（从nanotons转换）
    formatTokens(nanoAmount) {
        return this.formatNumber(Number(nanoAmount) / 1e9, 4);
    }

    // 格式化USDT数量
    formatUSDT(microAmount) {
        return this.formatNumber(Number(microAmount) / 1e6, 2);
    }

    // 格式化时间戳
    formatTimestamp(timestamp, format = 'full') {
        try {
            if (!timestamp || timestamp === 0 || timestamp === '0') {
                return format === 'date' ? '' : '';
            }
            
            const timestampNum = Number(timestamp);
            if (isNaN(timestampNum) || timestampNum <= 0) {
                return format === 'date' ? '' : '';
            }
            
            // 处理可能的单位问题（秒 vs 毫秒）
            const milliseconds = timestampNum < 1e12 ? timestampNum * 1000 : timestampNum;
            const date = new Date(milliseconds);
            
            if (isNaN(date.getTime())) {
                return format === 'date' ? '' : '';
            }
            
            return format === 'date' ? date.toISOString().split('T')[0] : date.toISOString();
        } catch (error) {
            console.warn(`时间戳格式化失败: ${timestamp}`, error.message);
            return format === 'date' ? '' : '';
        }
    }

    // 导出用户统计CSV
    async exportUsersCSV(stats) {
        const usersData = stats.userPurchasesArray.map((user, index) => ({
            '排名': index + 1,
            '用户地址_原始': user.address,
            '用户地址_友好格式': this.toUserFriendlyAddress(user.address),
            '用户合约地址_原始': user.contractAddress,
            '用户合约地址_友好格式': this.toUserFriendlyAddress(user.contractAddress),
            '购买次数': user.purchaseCount,
            '总获得代币': this.formatTokens(user.totalPurchased),
            '退款次数': user.refundCount || 0,
            '总退款代币': this.formatTokens(user.totalRefundedTokens || 0),
            '参与轮次数': user.rounds.length,
            //'参与轮次列表': user.rounds.join(';'),
            '平均每次购买_TON': user.purchaseCount > 0 ? 
                this.formatTokens(user.totalPaid / user.purchaseCount) : '0.0000',
            '平均每次获得代币': user.purchaseCount > 0 ? 
                this.formatTokens(user.totalPurchased / user.purchaseCount) : '0.0000',
            '代币价格效率': user.totalPurchased > 0 ? 
                this.formatNumber(user.totalPaid / user.totalPurchased, 6) : '0.000000'
        }));

        const csvContent = this.arrayToCSV(usersData);
        return csvContent;
    }

    // 导出轮次统计CSV
    async exportRoundsCSV(stats) {
        const roundsData = stats.roundsStats.map(round => ({
            '轮次': round.round,
            '参与用户数': round.uniqueUsers,
            '购买次数': round.purchaseCount,
            '总金额_TON': this.formatTokens(round.totalAmount),
            '总代币数': this.formatTokens(round.totalTokens),
            '平均每用户金额_TON': round.uniqueUsers > 0 ? 
                this.formatTokens(round.totalAmount / round.uniqueUsers) : '0.0000',
            '平均每用户代币': round.uniqueUsers > 0 ? 
                this.formatTokens(round.totalTokens / round.uniqueUsers) : '0.0000',
            '平均每次购买金额_TON': round.purchaseCount > 0 ? 
                this.formatTokens(round.totalAmount / round.purchaseCount) : '0.0000'
        }));

        const csvContent = this.arrayToCSV(roundsData);
        return csvContent;
    }

    // 导出用户购买明细CSV
    async exportPurchaseDetailsCSV(stats) {
        const purchaseDetails = [];
        let globalIndex = 1;

        stats.userPurchasesArray.forEach(user => {
            user.purchases.forEach(purchase => {
                purchaseDetails.push({
                    '序号': globalIndex++,
                    '用户地址_原始': user.address,
                    '用户地址_友好格式': this.toUserFriendlyAddress(user.address),
                    '用户合约地址_原始': user.contractAddress,
                    '用户合约地址_友好格式': this.toUserFriendlyAddress(user.contractAddress),
                    '购买ID': purchase.id,
                    '购买金额_TON': this.formatTokens(purchase.amount),
                    '获得代币数量': this.formatTokens(purchase.tokens),
                    '购买轮次': purchase.round_number,
                    '购买时间': this.formatTimestamp(purchase.timestamp),
                    '购买日期': this.formatTimestamp(purchase.timestamp, 'date'),
                    '货币类型': purchase.currency === 0 ? 'TON' : 'USDT',
                    'USDT等值金额': this.formatUSDT(purchase.usdt_equivalent_amount || 0),
                    '单价_TON每代币': purchase.tokens > 0 ? 
                        this.formatNumber(purchase.amount / purchase.tokens, 6) : '0.000000',
                    '是否已退款': purchase.is_refunded ? '是' : '否'
                });
            });
        });

        // 按购买时间排序
        purchaseDetails.sort((a, b) => new Date(a['购买时间']) - new Date(b['购买时间']));

        const csvContent = this.arrayToCSV(purchaseDetails);
        return csvContent;
    }

    // 导出汇总统计CSV
    async exportSummaryCSV(stats) {
        const summaryData = [
            {
                '统计项目': '总用户数',
                '数值': stats.totalUsers,
                '单位': '个',
                '说明': '参与购买的唯一用户数量'
            },
            {
                '统计项目': '总购买次数',
                '数值': stats.purchaseCount,
                '单位': '笔',
                '说明': '所有用户的购买交易总数'
            },
            {
                '统计项目': '总代币数量',
                '数值': this.formatTokens(stats.totalTokens),
                '单位': 'tokens',
                '说明': '已售出的代币总数'
            },
            {
                '统计项目': '总轮次数',
                '数值': stats.roundsStats.length,
                '单位': '轮',
                '说明': '拍卖进行的总轮次数'
            },
            {
                '统计项目': '用户合约数',
                '数值': stats.userContracts.length,
                '单位': '个',
                '说明': '创建的用户购买合约数量'
            }
        ];

        // 添加主合约数据
        if (stats.mainContract) {
            summaryData.push(
                {
                    '统计项目': '主合约状态',
                    '数值': this.getAuctionStatusText(stats.mainContract.auctionStatus),
                    '单位': '',
                    '说明': '拍卖合约当前状态'
                },
                {
                    '统计项目': '当前轮次',
                    '数值': stats.mainContract.currentRound,
                    '单位': '轮',
                    '说明': '拍卖进行到的当前轮次'
                },
                {
                    '统计项目': '当前价格',
                    '数值': this.formatTokens(stats.mainContract.currentPrice),
                    '单位': 'TON',
                    '说明': '当前代币价格'
                },
                {
                    '统计项目': '主合约TON募集',
                    '数值': this.formatTokens(stats.mainContract.totalRaised),
                    '单位': 'TON',
                    '说明': '主合约记录的TON募集总额'
                },
                {
                    '统计项目': '主合约USDT募集',
                    '数值': this.formatUSDT(stats.mainContract.totalRaisedUSDT),
                    '单位': 'USDT',
                    '说明': '主合约记录的USDT募集总额'
                },
                {
                    '统计项目': '主合约代币售出',
                    '数值': this.formatTokens(stats.mainContract.totalTokensSold),
                    '单位': 'tokens',
                    '说明': '主合约记录的代币售出总数'
                },
                {
                    '统计项目': '剩余代币',
                    '数值': this.formatTokens(stats.mainContract.remainingTokens),
                    '单位': 'tokens',
                    '说明': '主合约中剩余的代币数量'
                }
            );
        }

        const csvContent = this.arrayToCSV(summaryData);
        return csvContent;
    }

    getAuctionStatusText(status) {
        const statusMap = {
            0: '待开始',
            1: '进行中',
            2: '成功结束',
            3: '失败结束'
        };
        return statusMap[status] || `未知(${status})`;
    }

    // 导出退款明细CSV
    async exportRefundDetailsCSV(stats) {
        const refundDetails = [];
        let globalIndex = 1;

        stats.userPurchasesArray.forEach(user => {
            if (user.refundCount > 0) {
                // 如果有详细的退款记录，使用详细记录
                if (user.refunds && user.refunds.length > 0) {
                    user.refunds.forEach(refund => {
                        refundDetails.push({
                            '序号': globalIndex++,
                            '用户地址_原始': user.address,
                            '用户地址_友好格式': this.toUserFriendlyAddress(user.address),
                            '用户合约地址_原始': user.contractAddress,
                            '用户合约地址_友好格式': this.toUserFriendlyAddress(user.contractAddress),
                            '退款ID': refund.id || refund.purchase_id || '',
                            '退款金额_TON': this.formatTokens(refund.amount || 0),
                            '退款轮次': refund.round_number || '',
                            '退款时间': this.formatTimestamp(refund.timestamp),
                            '退款日期': this.formatTimestamp(refund.timestamp, 'date'),
                            '原购买金额_TON': refund.original_amount ? this.formatTokens(refund.original_amount) : '',
                            '原获得代币数量': refund.original_tokens ? this.formatTokens(refund.original_tokens) : '',
                            '退款原因': refund.reason || '用户主动退款',
                            '处理状态': refund.status || '已完成'
                        });
                    });
                } else {
                    // 如果只有汇总数据，创建汇总记录
                    refundDetails.push({
                        '序号': globalIndex++,
                        '用户地址_原始': user.address,
                        '用户地址_友好格式': this.toUserFriendlyAddress(user.address),
                        '用户合约地址_原始': user.contractAddress,
                        '用户合约地址_友好格式': this.toUserFriendlyAddress(user.contractAddress),
                        '退款ID': '汇总',
                        '退款金额_TON': this.formatTokens(user.totalRefunded || 0),
                        '退款轮次': '多轮次',
                        '退款时间': '详见区块链记录',
                        '退款日期': '详见区块链记录',
                        '退款次数': user.refundCount,
                        '原购买金额_TON': this.formatTokens(user.totalPaid || 0),
                        '原获得代币数量': this.formatTokens(user.totalPurchased || 0),
                        '退款原因': '用户主动退款',
                        '处理状态': '已完成'
                    });
                }
            }
        });

        // 按用户地址排序
        refundDetails.sort((a, b) => a['用户地址_原始'].localeCompare(b['用户地址_原始']));

        const csvContent = this.arrayToCSV(refundDetails);
        return csvContent;
    }

    // 导出地址映射CSV - 方便查找用户
    async exportAddressMappingCSV(stats) {
        const addressMappingData = stats.userPurchasesArray.map((user, index) => ({
            '序号': index + 1,
            '用户真实地址_原始': user.address,
            '用户真实地址_友好格式': this.toUserFriendlyAddress(user.address),
            '用户合约地址_原始': user.contractAddress,
            '用户合约地址_友好格式': this.toUserFriendlyAddress(user.contractAddress),
            '购买活跃度': user.purchaseCount,
            '总投入_TON': this.formatTokens(user.totalPaid),
            '总获得代币': this.formatTokens(user.totalPurchased),
            '参与轮次数': user.rounds.length,
            '首次购买轮次': user.rounds.length > 0 ? Math.min(...user.rounds) : '',
            '最后购买轮次': user.rounds.length > 0 ? Math.max(...user.rounds) : '',
            '用户标签': this.getUserLabel(user)
        }));

        const csvContent = this.arrayToCSV(addressMappingData);
        return csvContent;
    }

    // 为用户生成标签
    getUserLabel(user) {
        const labels = [];
        
        // 根据购买次数分类
        if (user.purchaseCount >= 10) {
            labels.push('活跃用户');
        } else if (user.purchaseCount >= 3) {
            labels.push('普通用户');
        } else {
            labels.push('新手用户');
        }
        
        // 根据购买金额分类
        const totalPaid = user.totalPaid;
        if (totalPaid >= 1000000000000) { // >= 1000 TON
            labels.push('大投资者');
        } else if (totalPaid >= 100000000000) { // >= 100 TON
            labels.push('中投资者');
        } else if (totalPaid >= 10000000000) { // >= 10 TON
            labels.push('小投资者');
        } else {
            labels.push('微投资者');
        }
        
        // 根据参与轮次数分类
        if (user.rounds.length >= 5) {
            labels.push('长期参与者');
        } else if (user.rounds.length >= 2) {
            labels.push('多轮参与者');
        } else {
            labels.push('单轮参与者');
        }
        
        return labels.join(', ');
    }

    // 主导出函数
    async exportAllToCSV(stats) {
        try {
            // 创建输出目录
            await fs.mkdir(this.outputDir, { recursive: true });

            const timestamp = new Date().toISOString().split('T')[0];
            const reports = [];

            console.log('📊 开始导出CSV报告...');

            // 1. 导出用户统计
            console.log('  导出用户统计...');
            const usersCSV = await this.exportUsersCSV(stats);
            const usersFile = path.join(this.outputDir, `users-stats-${timestamp}.csv`);
            await fs.writeFile(usersFile, '\ufeff' + usersCSV, 'utf-8'); // 添加BOM以支持Excel中文
            reports.push(usersFile);

            // 2. 导出轮次统计
            console.log('  导出轮次统计...');
            const roundsCSV = await this.exportRoundsCSV(stats);
            const roundsFile = path.join(this.outputDir, `rounds-stats-${timestamp}.csv`);
            await fs.writeFile(roundsFile, '\ufeff' + roundsCSV, 'utf-8');
            reports.push(roundsFile);

            // 3. 导出购买明细
            console.log('  导出购买明细...');
            const purchaseCSV = await this.exportPurchaseDetailsCSV(stats);
            const purchaseFile = path.join(this.outputDir, `purchase-details-${timestamp}.csv`);
            await fs.writeFile(purchaseFile, '\ufeff' + purchaseCSV, 'utf-8');
            reports.push(purchaseFile);

            // 4. 导出汇总统计
            console.log('  导出汇总统计...');
            const summaryCSV = await this.exportSummaryCSV(stats);
            const summaryFile = path.join(this.outputDir, `summary-stats-${timestamp}.csv`);
            await fs.writeFile(summaryFile, '\ufeff' + summaryCSV, 'utf-8');
            reports.push(summaryFile);

            // 5. 导出退款明细
            console.log('  导出退款明细...');
            const refundCSV = await this.exportRefundDetailsCSV(stats);
            const refundFile = path.join(this.outputDir, `refund-details-${timestamp}.csv`);
            await fs.writeFile(refundFile, '\ufeff' + refundCSV, 'utf-8');
            reports.push(refundFile);

            // 6. 导出地址映射
            console.log('  导出用户地址映射...');
            const addressMappingCSV = await this.exportAddressMappingCSV(stats);
            const addressMappingFile = path.join(this.outputDir, `address-mapping-${timestamp}.csv`);
            await fs.writeFile(addressMappingFile, '\ufeff' + addressMappingCSV, 'utf-8');
            reports.push(addressMappingFile);

            console.log('\n✅ CSV报告导出完成:');
            reports.forEach((file, index) => {
                const descriptions = [
                    '用户统计报告 - 按投资金额排序的用户数据（含退款信息）',
                    '轮次统计报告 - 各轮次的参与情况和募集数据', 
                    '购买明细报告 - 所有交易的详细记录（含退款状态）',
                    '汇总统计报告 - 项目整体数据概览',
                    '退款明细报告 - 所有退款交易的详细记录',
                    '用户地址映射 - 真实地址与合约地址对应关系'
                ];
                console.log(`  📄 ${file}`);
                console.log(`     ${descriptions[index] || '统计报告'}`);
            });

            console.log(`\n📁 所有文件保存在: ${this.outputDir}/`);
            console.log('💡 可以直接用Excel或Google Sheets打开查看');
            console.log('🔍 地址映射文件方便查找用户真实地址和合约地址的对应关系');
            console.log('💰 退款明细报告包含所有退款交易的详细信息');

            return reports;
        } catch (error) {
            console.error('❌ 导出CSV时出错:', error.message);
            throw error;
        }
    }
}

// 如果直接运行此文件
if (import.meta.url === `file://${process.argv[1]}`) {
    async function main() {
        const args = process.argv.slice(2);
        
        if (args.includes('--help')) {
            console.log(`
CSV导出工具使用方法:

node src/csv-exporter.js [统计文件路径]

参数:
  统计文件路径    指定要导出的统计JSON文件 (可选，默认查找最新文件)
  --help         显示此帮助信息

示例:
  node src/csv-exporter.js                          # 导出最新的统计文件
  node src/csv-exporter.js raw-stats-2024-01-15.json  # 导出指定的统计文件
`);
            return;
        }

        const exporter = new CSVExporter();
        
        try {
            // 查找统计文件
            let statsFile = args[0];
            
            if (!statsFile) {
                // 查找最新的统计文件
                const files = await fs.readdir('.');
                const statsFiles = files
                    .filter(f => f.startsWith('raw-stats-') && f.endsWith('.json'))
                    .sort()
                    .reverse();
                
                if (statsFiles.length === 0) {
                    console.error('❌ 未找到统计文件，请先运行 npm start 生成统计数据');
                    process.exit(1);
                }
                
                statsFile = statsFiles[0];
                console.log(`📄 使用最新统计文件: ${statsFile}`);
            }

            // 读取统计数据
            const statsData = await fs.readFile(statsFile, 'utf-8');
            const stats = JSON.parse(statsData);

            // 导出CSV
            await exporter.exportAllToCSV(stats);

        } catch (error) {
            console.error('❌ 导出失败:', error.message);
            process.exit(1);
        }
    }

    main().catch(console.error);
}