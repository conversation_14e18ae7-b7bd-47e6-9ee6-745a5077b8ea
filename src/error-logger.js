import fs from 'fs/promises';
import path from 'path';

/**
 * 结构化错误记录器
 * 用于记录和追踪统计过程中的所有错误和异常
 */
export class ErrorLogger {
    constructor() {
        this.errors = [];
        this.errorCounts = new Map();
        this.logFile = `error-log-${new Date().toISOString().split('T')[0]}.json`;
        this.startTime = new Date();
    }

    /**
     * 记录错误
     * @param {Object} errorInfo - 错误信息对象
     */
    async logError(errorInfo) {
        const errorRecord = {
            id: this.generateErrorId(),
            timestamp: new Date().toISOString(),
            ...errorInfo,
            sessionStartTime: this.startTime.toISOString()
        };

        this.errors.push(errorRecord);
        
        // 更新错误计数
        const errorKey = `${errorInfo.type}_${errorInfo.operation}`;
        this.errorCounts.set(errorKey, (this.errorCounts.get(errorKey) || 0) + 1);

        // 实时保存到文件
        await this.saveToFile();

        // 根据错误严重程度决定日志级别
        if (errorInfo.severity === 'critical') {
            console.error(`🚨 CRITICAL ERROR [${errorRecord.id}]:`, errorInfo.message);
        } else if (errorInfo.severity === 'warning') {
            console.warn(`⚠️  WARNING [${errorRecord.id}]:`, errorInfo.message);
        } else {
            console.log(`ℹ️  INFO [${errorRecord.id}]:`, errorInfo.message);
        }

        return errorRecord.id;
    }

    /**
     * 记录合约数据获取错误
     */
    async logContractError(contractAddress, operation, error, context = {}) {
        return await this.logError({
            type: 'CONTRACT_ERROR',
            operation,
            contractAddress,
            message: error.message || error,
            stack: error.stack,
            severity: this.determineSeverity(operation, error),
            context,
            retryable: this.isRetryableError(error)
        });
    }

    /**
     * 记录API调用错误
     */
    async logApiError(endpoint, error, retryAttempt = 0, context = {}) {
        return await this.logError({
            type: 'API_ERROR',
            operation: 'API_CALL',
            endpoint,
            message: error.message || error,
            statusCode: error.response?.status,
            retryAttempt,
            severity: error.response?.status === 429 ? 'warning' : 'error',
            context,
            retryable: this.isRetryableError(error)
        });
    }

    /**
     * 记录数据验证错误
     */
    async logValidationError(dataType, validationRule, actualValue, expectedValue, context = {}) {
        return await this.logError({
            type: 'VALIDATION_ERROR',
            operation: 'DATA_VALIDATION',
            dataType,
            validationRule,
            actualValue,
            expectedValue,
            message: `Data validation failed: ${validationRule}`,
            severity: 'warning',
            context,
            retryable: false
        });
    }

    /**
     * 记录重试操作
     */
    async logRetry(operation, attempt, maxAttempts, reason, context = {}) {
        return await this.logError({
            type: 'RETRY_ATTEMPT',
            operation,
            attempt,
            maxAttempts,
            reason,
            message: `Retry attempt ${attempt}/${maxAttempts} for ${operation}: ${reason}`,
            severity: attempt === maxAttempts ? 'error' : 'info',
            context,
            retryable: attempt < maxAttempts
        });
    }

    /**
     * 记录数据恢复操作
     */
    async logRecovery(operation, recoveryMethod, originalError, context = {}) {
        return await this.logError({
            type: 'DATA_RECOVERY',
            operation,
            recoveryMethod,
            originalError: originalError.message || originalError,
            message: `Data recovery applied: ${recoveryMethod} for ${operation}`,
            severity: 'warning',
            context,
            retryable: false
        });
    }

    /**
     * 判断错误严重程度
     */
    determineSeverity(operation, error) {
        // 关键数据获取失败
        if (operation.includes('total_purchased') || operation.includes('total_paid') || operation.includes('refund')) {
            return 'critical';
        }
        
        // API限流
        if (error.response?.status === 429) {
            return 'warning';
        }
        
        // 网络错误
        if (error.code === 'ECONNRESET' || error.code === 'ETIMEDOUT') {
            return 'warning';
        }
        
        return 'error';
    }

    /**
     * 判断错误是否可重试
     */
    isRetryableError(error) {
        // API限流
        if (error.response?.status === 429) return true;
        
        // 网络错误
        if (error.code === 'ECONNRESET' || error.code === 'ETIMEDOUT') return true;
        
        // 服务器错误
        if (error.response?.status >= 500) return true;
        
        // 超时错误
        if (error.message?.includes('timeout')) return true;
        
        return false;
    }

    /**
     * 生成唯一错误ID
     */
    generateErrorId() {
        return `ERR_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 保存错误日志到文件
     */
    async saveToFile() {
        try {
            const logData = {
                sessionInfo: {
                    startTime: this.startTime.toISOString(),
                    lastUpdate: new Date().toISOString(),
                    totalErrors: this.errors.length,
                    errorCounts: Object.fromEntries(this.errorCounts)
                },
                errors: this.errors
            };

            await fs.writeFile(this.logFile, JSON.stringify(logData, null, 2));
        } catch (saveError) {
            console.error(saveError);
            console.error('Failed to save error log:', saveError.message);
        }
    }

    /**
     * 获取错误统计
     */
    getErrorStats() {
        const stats = {
            totalErrors: this.errors.length,
            errorsByType: {},
            errorsBySeverity: {},
            retryableErrors: 0,
            criticalErrors: 0
        };

        this.errors.forEach(error => {
            // 按类型统计
            stats.errorsByType[error.type] = (stats.errorsByType[error.type] || 0) + 1;
            
            // 按严重程度统计
            stats.errorsBySeverity[error.severity] = (stats.errorsBySeverity[error.severity] || 0) + 1;
            
            // 可重试错误计数
            if (error.retryable) stats.retryableErrors++;
            
            // 关键错误计数
            if (error.severity === 'critical') stats.criticalErrors++;
        });

        return stats;
    }

    /**
     * 生成错误报告
     */
    generateErrorReport() {
        const stats = this.getErrorStats();
        const report = {
            summary: {
                sessionDuration: new Date() - this.startTime,
                ...stats
            },
            recentErrors: this.errors.slice(-10), // 最近10个错误
            errorPatterns: this.analyzeErrorPatterns(),
            recommendations: this.generateRecommendations()
        };

        return report;
    }

    /**
     * 分析错误模式
     */
    analyzeErrorPatterns() {
        const patterns = {};
        
        this.errors.forEach(error => {
            const pattern = `${error.type}_${error.operation}`;
            if (!patterns[pattern]) {
                patterns[pattern] = {
                    count: 0,
                    firstOccurrence: error.timestamp,
                    lastOccurrence: error.timestamp,
                    severity: error.severity
                };
            }
            patterns[pattern].count++;
            patterns[pattern].lastOccurrence = error.timestamp;
        });

        return patterns;
    }

    /**
     * 生成改进建议
     */
    generateRecommendations() {
        const recommendations = [];
        const stats = this.getErrorStats();

        if (stats.criticalErrors > 0) {
            recommendations.push({
                priority: 'HIGH',
                issue: 'Critical data retrieval failures detected',
                suggestion: 'Review contract addresses and network connectivity. Consider implementing additional fallback mechanisms.'
            });
        }

        if (stats.errorsByType.API_ERROR > 10) {
            recommendations.push({
                priority: 'MEDIUM',
                issue: 'High API error rate',
                suggestion: 'Consider increasing request delays or implementing more aggressive retry policies.'
            });
        }

        if (stats.retryableErrors / stats.totalErrors > 0.7) {
            recommendations.push({
                priority: 'LOW',
                issue: 'Most errors are retryable',
                suggestion: 'Current retry mechanisms are working well. Monitor for improvement opportunities.'
            });
        }

        return recommendations;
    }

    /**
     * 清理旧的错误日志
     */
    async cleanup(daysToKeep = 7) {
        try {
            const files = await fs.readdir('.');
            const errorLogFiles = files.filter(f => f.startsWith('error-log-') && f.endsWith('.json'));
            
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

            for (const file of errorLogFiles) {
                const match = file.match(/error-log-(\d{4}-\d{2}-\d{2})\.json/);
                if (match) {
                    const fileDate = new Date(match[1]);
                    if (fileDate < cutoffDate) {
                        await fs.unlink(file);
                        console.log(`Cleaned up old error log: ${file}`);
                    }
                }
            }
        } catch (error) {
            console.warn('Failed to cleanup old error logs:', error.message);
        }
    }
}
