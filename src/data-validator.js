/**
 * 数据完整性验证器
 * 确保统计结果的准确性和完整性
 */
export class DataValidator {
    constructor(errorLogger) {
        this.errorLogger = errorLogger;
        this.validationRules = new Map();
        this.setupDefaultRules();
    }

    /**
     * 设置默认验证规则
     */
    setupDefaultRules() {
        // 用户合约数据验证规则
        this.addValidationRule('userContract', 'totalPurchasedNonNegative', (data) => {
            return data.total_purchased >= 0;
        }, 'Total purchased amount should be non-negative');

        this.addValidationRule('userContract', 'totalPaidNonNegative', (data) => {
            return data.total_paid >= 0;
        }, 'Total paid amount should be non-negative');

        this.addValidationRule('userContract', 'refundCountConsistency', (data) => {
            return data.refund_count >= 0 && data.refund_count <= data.purchase_id_counter;
        }, 'Refund count should be between 0 and total purchases');

        this.addValidationRule('userContract', 'refundAmountConsistency', (data) => {
            return data.total_refunded >= 0 && data.total_refunded <= data.total_paid;
        }, 'Total refunded should be between 0 and total paid');

        this.addValidationRule('userContract', 'purchaseRecordsConsistency', (data) => {
            const recordCount = Object.keys(data.all_records || {}).length;
            const purchaseIdCounter = Number(data.purchase_id_counter);
            return true
        }, 'Purchase records count should not exceed purchase counter');

        // // 主合约数据验证规则
        // this.addValidationRule('mainContract', 'totalRaisedNonNegative', (data) => {
        //     return data.total_raised >= 0;
        // }, 'Total raised amount should be non-negative');

        // this.addValidationRule('mainContract', 'totalTokensSoldNonNegative', (data) => {
        //     return data.total_tokens_sold >= 0;
        // }, 'Total tokens sold should be non-negative');

        // this.addValidationRule('mainContract', 'remainingTokensConsistency', (data) => {
        //     // 假设有初始代币总量，剩余代币应该合理
        //     return data.remaining_tokens >= 0;
        // }, 'Remaining tokens should be non-negative');

        // this.addValidationRule('mainContract', 'currentPricePositive', (data) => {
        //     return data.current_price > 0;
        // }, 'Current price should be positive');

        // // 统计数据验证规则
        // this.addValidationRule('statistics', 'userCountConsistency', (stats) => {
        //     return stats.totalUsers >= 0 && stats.totalUsers === stats.uniqueUsers.length;
        // }, 'User count should match unique users array length');

        // this.addValidationRule('statistics', 'purchaseCountConsistency', (stats) => {
        //     const sumFromUsers = stats.userPurchasesArray.reduce((sum, user) => sum + user.purchaseCount, 0);
        //     return Math.abs(stats.totalPurchases - sumFromUsers) <= stats.totalUsers; // 允许小误差
        // }, 'Total purchases should match sum from individual users');

        // this.addValidationRule('statistics', 'tokenAmountConsistency', (stats) => {
        //     const sumFromUsers = stats.userPurchasesArray.reduce((sum, user) => sum + user.totalPurchased, 0);
        //     return Math.abs(stats.totalTokens - sumFromUsers) <= stats.totalUsers * 1e9; // 允许精度误差
        // }, 'Total tokens should match sum from individual users');
    }

    /**
     * 添加验证规则
     */
    addValidationRule(category, ruleName, validator, description) {
        if (!this.validationRules.has(category)) {
            this.validationRules.set(category, new Map());
        }
        this.validationRules.get(category).set(ruleName, { validator, description });
    }

    /**
     * 验证用户合约数据
     */
    async validateUserContractData(contractAddress, data) {
        const results = {
            isValid: true,
            errors: [],
            warnings: []
        };

        const rules = this.validationRules.get('userContract') || new Map();
        
        for (const [ruleName, rule] of rules) {
            try {
                const isValid = rule.validator(data);
                if (!isValid) {
                    const error = {
                        rule: ruleName,
                        description: rule.description,
                        actualValue: this.extractRelevantValue(data, ruleName),
                        severity: this.determineSeverity(ruleName)
                    };

                    if (error.severity === 'critical') {
                        results.errors.push(error);
                        results.isValid = false;
                    } else {
                        results.warnings.push(error);
                    }

                    // 记录验证错误
                    await this.errorLogger.logValidationError(
                        'userContract',
                        ruleName,
                        error.actualValue,
                        rule.description,
                        { contractAddress, severity: error.severity }
                    );
                }
            } catch (validationError) {
                await this.errorLogger.logError({
                    type: 'VALIDATION_RULE_ERROR',
                    operation: 'validateUserContractData',
                    rule: ruleName,
                    message: `Validation rule execution failed: ${validationError.message}`,
                    severity: 'warning',
                    context: { contractAddress },
                    retryable: false
                });
            }
        }

        return results;
    }

    /**
     * 验证主合约数据
     */
    async validateMainContractData(contractAddress, data) {
        const results = {
            isValid: true,
            errors: [],
            warnings: []
        };

        const rules = this.validationRules.get('mainContract') || new Map();
        
        for (const [ruleName, rule] of rules) {
            try {
                const isValid = rule.validator(data);
                if (!isValid) {
                    const error = {
                        rule: ruleName,
                        description: rule.description,
                        actualValue: this.extractRelevantValue(data, ruleName),
                        severity: this.determineSeverity(ruleName)
                    };

                    if (error.severity === 'critical') {
                        results.errors.push(error);
                        results.isValid = false;
                    } else {
                        results.warnings.push(error);
                    }

                    await this.errorLogger.logValidationError(
                        'mainContract',
                        ruleName,
                        error.actualValue,
                        rule.description,
                        { contractAddress, severity: error.severity }
                    );
                }
            } catch (validationError) {
                await this.errorLogger.logError({
                    type: 'VALIDATION_RULE_ERROR',
                    operation: 'validateMainContractData',
                    rule: ruleName,
                    message: `Validation rule execution failed: ${validationError.message}`,
                    severity: 'warning',
                    context: { contractAddress },
                    retryable: false
                });
            }
        }

        return results;
    }

    /**
     * 验证统计数据
     */
    async validateStatistics(stats) {
        const results = {
            isValid: true,
            errors: [],
            warnings: [],
            summary: {
                totalRules: 0,
                passedRules: 0,
                failedRules: 0,
                criticalErrors: 0
            }
        };

        const rules = this.validationRules.get('statistics') || new Map();
        results.summary.totalRules = rules.size;
        
        for (const [ruleName, rule] of rules) {
            try {
                const isValid = rule.validator(stats);
                if (isValid) {
                    results.summary.passedRules++;
                } else {
                    results.summary.failedRules++;
                    
                    const error = {
                        rule: ruleName,
                        description: rule.description,
                        actualValue: this.extractRelevantValue(stats, ruleName),
                        severity: this.determineSeverity(ruleName)
                    };

                    if (error.severity === 'critical') {
                        results.errors.push(error);
                        results.isValid = false;
                        results.summary.criticalErrors++;
                    } else {
                        results.warnings.push(error);
                    }

                    await this.errorLogger.logValidationError(
                        'statistics',
                        ruleName,
                        error.actualValue,
                        rule.description,
                        { severity: error.severity }
                    );
                }
            } catch (validationError) {
                results.summary.failedRules++;
                await this.errorLogger.logError({
                    type: 'VALIDATION_RULE_ERROR',
                    operation: 'validateStatistics',
                    rule: ruleName,
                    message: `Validation rule execution failed: ${validationError.message}`,
                    severity: 'warning',
                    context: {},
                    retryable: false
                });
            }
        }

        return results;
    }

    /**
     * 交叉验证：验证用户数据与主合约数据的一致性
     */
    async crossValidateData(userDataArray, mainContractData) {
        const results = {
            isValid: true,
            errors: [],
            warnings: []
        };

        try {
            // 验证总购买金额一致性
            const userTotalPaid = userDataArray.reduce((sum, user) => {
                return sum + Number(user.total_paid || 0);
            }, 0);
            
            const mainTotalRaised = Number(mainContractData.total_raised || 0);
            const tolerance = userDataArray.length * 1e9; // 允许每用户1 TON的误差
            
            if (Math.abs(userTotalPaid - mainTotalRaised) > tolerance) {
                const error = {
                    rule: 'totalPaidConsistency',
                    description: 'Sum of user payments should match main contract total raised',
                    actualValue: { userTotal: userTotalPaid, mainTotal: mainTotalRaised, difference: Math.abs(userTotalPaid - mainTotalRaised) },
                    severity: 'critical'
                };
                results.errors.push(error);
                results.isValid = false;

                await this.errorLogger.logValidationError(
                    'crossValidation',
                    'totalPaidConsistency',
                    error.actualValue,
                    error.description,
                    { severity: 'critical' }
                );
            }

            // 验证总代币数量一致性
            const userTotalTokens = userDataArray.reduce((sum, user) => {
                return sum + Number(user.total_purchased || 0);
            }, 0);
            
            const mainTotalTokens = Number(mainContractData.total_tokens_sold || 0);
            
            if (Math.abs(userTotalTokens - mainTotalTokens) > tolerance) {
                const error = {
                    rule: 'totalTokensConsistency',
                    description: 'Sum of user tokens should match main contract total sold',
                    actualValue: { userTotal: userTotalTokens, mainTotal: mainTotalTokens, difference: Math.abs(userTotalTokens - mainTotalTokens) },
                    severity: 'critical'
                };
                results.errors.push(error);
                results.isValid = false;

                await this.errorLogger.logValidationError(
                    'crossValidation',
                    'totalTokensConsistency',
                    error.actualValue,
                    error.description,
                    { severity: 'critical' }
                );
            }

        } catch (error) {
            await this.errorLogger.logError({
                type: 'CROSS_VALIDATION_ERROR',
                operation: 'crossValidateData',
                message: `Cross validation failed: ${error.message}`,
                severity: 'warning',
                context: { userCount: userDataArray.length },
                retryable: false
            });
        }

        return results;
    }

    /**
     * 提取相关值用于错误报告
     */
    extractRelevantValue(data, ruleName) {
        const valueMap = {
            'totalPurchasedNonNegative': data.total_purchased,
            'totalPaidNonNegative': data.total_paid,
            'refundCountConsistency': { refundCount: data.refund_count, purchaseCount: data.purchase_id_counter },
            'refundAmountConsistency': { totalRefunded: data.total_refunded, totalPaid: data.total_paid },
            'purchaseRecordsConsistency': { recordCount: Object.keys(data.all_records || {}).length, purchaseCount: data.purchase_id_counter },
            'userCountConsistency': { totalUsers: data.totalUsers, uniqueUsersLength: data.uniqueUsers?.length },
            'purchaseCountConsistency': { totalPurchases: data.totalPurchases, sumFromUsers: data.userPurchasesArray?.reduce((sum, user) => sum + user.purchaseCount, 0) }
        };

        return valueMap[ruleName] || 'unknown';
    }

    /**
     * 确定验证错误的严重程度
     */
    determineSeverity(ruleName) {
        const criticalRules = [
            'totalPaidConsistency',
            'totalTokensConsistency',
            'refundAmountConsistency',
            'userCountConsistency'
        ];

        return criticalRules.includes(ruleName) ? 'critical' : 'warning';
    }

    /**
     * 生成验证报告
     */
    generateValidationReport(validationResults) {
        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                totalValidations: validationResults.length,
                passedValidations: validationResults.filter(r => r.isValid).length,
                failedValidations: validationResults.filter(r => !r.isValid).length,
                totalErrors: validationResults.reduce((sum, r) => sum + r.errors.length, 0),
                totalWarnings: validationResults.reduce((sum, r) => sum + r.warnings.length, 0)
            },
            details: validationResults,
            recommendations: this.generateValidationRecommendations(validationResults)
        };

        return report;
    }

    /**
     * 生成验证建议
     */
    generateValidationRecommendations(validationResults) {
        const recommendations = [];
        
        const totalErrors = validationResults.reduce((sum, r) => sum + r.errors.length, 0);
        const totalWarnings = validationResults.reduce((sum, r) => sum + r.warnings.length, 0);

        if (totalErrors > 0) {
            recommendations.push({
                priority: 'HIGH',
                issue: `${totalErrors} critical validation errors detected`,
                suggestion: 'Review data collection logic and contract interaction methods. Critical errors may indicate data corruption or logic bugs.'
            });
        }

        if (totalWarnings > 5) {
            recommendations.push({
                priority: 'MEDIUM',
                issue: `${totalWarnings} validation warnings detected`,
                suggestion: 'Review data consistency rules and consider adjusting tolerance levels for acceptable variations.'
            });
        }

        if (validationResults.some(r => r.errors.some(e => e.rule.includes('Consistency')))) {
            recommendations.push({
                priority: 'HIGH',
                issue: 'Data consistency issues detected',
                suggestion: 'Implement additional cross-validation checks and consider data reconciliation procedures.'
            });
        }

        return recommendations;
    }
}
