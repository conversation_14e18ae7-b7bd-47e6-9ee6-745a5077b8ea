import { TonClient } from '@ton/ton';
import { Address } from '@ton/core';
import { OnionAuction } from '../build/OnionAuction/OnionAuction_OnionAuction.js';
import { UserPurchase } from '../build/OnionAuction/OnionAuction_UserPurchase.js';
import { ErrorLogger } from './error-logger.js';
import { RetryManager } from './retry-manager.js';
import { DataValidator } from './data-validator.js';

interface UserContractData {
    total_purchased: bigint;
    total_paid: bigint;
    purchase_id_counter: bigint;
    all_records: any;
    participated_rounds: any;
    refund_count: bigint;
    total_refunded: bigint;
    user_address: string;
}

interface MainContractData {
    total_raised: bigint;
    total_raised_usdt: bigint;
    total_tokens_sold: bigint;
    purchase_count: bigint;
    current_round: bigint;
    current_price: bigint;
    auction_status: bigint;
    remaining_tokens: bigint;
    auction_info: any;
}

export class ContractReader {
    private client: any;
    private tonClient: TonClient;
    private errorLogger: ErrorLogger;
    private retryManager: RetryManager;
    private dataValidator: DataValidator;

    constructor(client: any) {
        this.client = client;
        // 创建TonClient实例用于强类型合约调用
        this.tonClient = new TonClient({
            //endpoint: this.client.baseURL || 'https://toncenter.com/api/v2',
            endpoint: 'https://toncenter.com/api/v2/jsonRPC',
            'apiKey': '8424cbe83460d87e34163e47f3103a4b53621790e79d86ba79dc0d143d65816b'
        });
        this.errorLogger = new ErrorLogger();
        this.retryManager = new RetryManager(this.errorLogger);
        this.dataValidator = new DataValidator(this.errorLogger);
    }

    async getUserContractData(userContractAddress: string, contractToUserMap: Map<string, string> | null = null): Promise<UserContractData | null> {
        try {
            const data = {} as UserContractData;
            
            // 创建强类型UserPurchase合约实例
            const contractAddr = Address.parse(userContractAddress);
            const userContract = UserPurchase.fromAddress(contractAddr);
            
            // 使用强类型接口获取基本信息（带重试）
            try {
                data.total_purchased = await this.retryManager.executeCriticalOperation(
                    () => userContract.getTotalPurchased(this.tonClient.provider(contractAddr)),
                    'getTotalPurchased',
                    { contractAddress: userContractAddress }
                );
            } catch (e) {
                await this.errorLogger.logContractError(userContractAddress, 'getTotalPurchased', e, {
                    severity: 'critical',
                    impact: 'Missing total purchased amount for user statistics'
                });
                data.total_purchased = BigInt(0);
            }

            try {
                data.total_paid = await this.retryManager.executeCriticalOperation(
                    () => userContract.getTotalPaid(this.tonClient.provider(contractAddr)),
                    'getTotalPaid',
                    { contractAddress: userContractAddress }
                );
            } catch (e) {
                await this.errorLogger.logContractError(userContractAddress, 'getTotalPaid', e, {
                    severity: 'critical',
                    impact: 'Missing total paid amount for user statistics'
                });
                data.total_paid = BigInt(0);
            }

            try {
                data.purchase_id_counter = await this.retryManager.executeCriticalOperation(
                    () => userContract.getPurchaseIdCounter(this.tonClient.provider(contractAddr)),
                    'getPurchaseIdCounter',
                    { contractAddress: userContractAddress }
                );
            } catch (e) {
                await this.errorLogger.logContractError(userContractAddress, 'getPurchaseIdCounter', e, {
                    severity: 'critical',
                    impact: 'Cannot determine number of purchases for refund analysis'
                });
                data.purchase_id_counter = BigInt(0);
            }

            // 获取所有购买记录 - 使用强类型接口（带重试）
            try {
                data.all_records = await this.retryManager.executeCriticalOperation(
                    () => userContract.getAllRecords(this.tonClient.provider(contractAddr)),
                    'getAllRecords',
                    { contractAddress: userContractAddress }
                );
            } catch (e) {
                await this.errorLogger.logContractError(userContractAddress, 'getAllRecords', e, {
                    severity: 'critical',
                    impact: 'Missing detailed purchase records for user analysis'
                });
                data.all_records = {};
            }

            // 获取参与的轮次（带重试）
            try {
                data.participated_rounds = await this.retryManager.executeWithRetry(
                    () => userContract.getParticipatedRounds(this.tonClient.provider(contractAddr)),
                    {
                        operationName: 'getParticipatedRounds',
                        context: { contractAddress: userContractAddress },
                        isCritical: false
                    }
                );
            } catch (e) {
                await this.errorLogger.logContractError(userContractAddress, 'getParticipatedRounds', e, {
                    severity: 'warning',
                    impact: 'Missing round participation data for user analysis'
                });
                data.participated_rounds = {};
            }

            // 获取退款历史记录数量 - 使用强类型接口
            try {
                data.refund_count = BigInt(0);
                data.total_refunded = BigInt(0);
                
                // 遍历所有购买记录，检查是否已退款
                const counter = this.safeBigIntToNumber(data.purchase_id_counter);
                const provider = this.tonClient.provider(contractAddr);
                
                // 批量处理退款状态检查，使用重试机制
                const refundOperations = [];
                for (let i = 1; i <= counter; i++) {
                    refundOperations.push(async () => {
                        const isRefunded = await this.retryManager.executeWithRetry(
                            () => userContract.getIsRefunded(provider, BigInt(i)),
                            {
                                operationName: 'getIsRefunded',
                                context: { contractAddress: userContractAddress, purchaseId: i },
                                isCritical: true
                            }
                        );

                        if (isRefunded) {
                            // 获取具体的退款金额（从购买详情中获取）
                            const purchaseDetails = await this.retryManager.executeWithRetry(
                                () => userContract.getPurchaseDetails(provider, BigInt(i)),
                                {
                                    operationName: 'getPurchaseDetails',
                                    context: { contractAddress: userContractAddress, purchaseId: i },
                                    isCritical: true
                                }
                            );

                            return {
                                purchaseId: i,
                                isRefunded: true,
                                amount: purchaseDetails?.amount || BigInt(0)
                            };
                        }

                        return { purchaseId: i, isRefunded: false, amount: BigInt(0) };
                    });
                }

                // 执行批量退款检查
                const batchResult = await this.retryManager.executeBatchWithRetry(refundOperations, {
                    operationName: 'refundStatusBatch',
                    context: { contractAddress: userContractAddress, totalPurchases: counter },
                    isCritical: true,
                    failFast: false // 不要因为单个失败而停止整个批次
                });

                // 处理批量结果
                batchResult.results.forEach((result: any) => {
                    if (result && result.isRefunded) {
                        data.refund_count = data.refund_count + BigInt(1);
                        data.total_refunded = data.total_refunded + result.amount;
                    }
                });

                // 记录批量操作的错误
                if (batchResult.errorCount > 0) {
                    await this.errorLogger.logError({
                        type: 'BATCH_REFUND_CHECK_ERRORS',
                        operation: 'refundStatusBatch',
                        message: `Failed to check refund status for ${batchResult.errorCount}/${counter} purchases`,
                        severity: batchResult.errorCount === counter ? 'critical' : 'warning',
                        context: {
                            contractAddress: userContractAddress,
                            totalPurchases: counter,
                            successfulChecks: batchResult.successCount,
                            failedChecks: batchResult.errorCount
                        },
                        retryable: false
                    });
                }
            } catch (e) {
                await this.errorLogger.logContractError(userContractAddress, 'getRefundStatistics', e, {
                    severity: 'critical',
                    impact: 'Complete refund statistics unavailable for user'
                });
                data.refund_count = BigInt(0);
                data.total_refunded = BigInt(0);
            }

            // 获取真正的用户地址
            // 优先使用映射，然后尝试owner方法
            if (contractToUserMap && contractToUserMap.has(userContractAddress)) {
                data.user_address = contractToUserMap.get(userContractAddress);
                console.log(`✓ 从映射获取用户地址: ${data.user_address}`);
            } else {
                try {
                    // 使用强类型接口获取owner
                    const provider = this.tonClient.provider(contractAddr);
                    const ownerAddress = await userContract.getOwner(provider);
                    if (ownerAddress) {
                        data.user_address = ownerAddress.toString();
                        console.log(`✓ 通过owner方法获取用户地址: ${ownerAddress.toString()}`);
                    } else {
                        throw new Error('Owner地址为空');
                    }
                } catch (e) {
                    console.log(`无法获取 owner: ${e.message}`);
                    // 如果获取失败，使用合约地址作为临时fallback
                    data.user_address = this.extractUserFromContractAddress(userContractAddress);
                }
            }

            // 验证数据完整性
            const validationResult = await this.dataValidator.validateUserContractData(userContractAddress, data);
            if (!validationResult.isValid) {
                await this.errorLogger.logError({
                    type: 'DATA_VALIDATION_FAILED',
                    operation: 'getUserContractData',
                    message: `Data validation failed for user contract: ${validationResult.errors.length} errors, ${validationResult.warnings.length} warnings`,
                    severity: 'warning',
                    context: {
                        contractAddress: userContractAddress,
                        errors: validationResult.errors,
                        warnings: validationResult.warnings
                    },
                    retryable: false
                });
            }

            return data;
        } catch (error) {
            console.error(`读取用户合约 ${userContractAddress} 数据时出错:`, error.message);
            return null;
        }
    }

    async getMainContractData(contractAddress: string): Promise<MainContractData | null> {
        try {
            const data = {} as MainContractData;
            
            // 创建强类型OnionAuction合约实例
            const contractAddr = Address.parse(contractAddress);
            const auctionContract = OnionAuction.fromAddress(contractAddr);
            const provider = this.tonClient.provider(contractAddr);

            // 使用强类型接口获取基本拍卖信息
            try {
                data.total_raised = await auctionContract.getTotalRaised(provider);
            } catch (e) {
                console.log(`无法获取 total_raised: ${e.message}`);
                data.total_raised = BigInt(0);
            }

            try {
                data.total_raised_usdt = await auctionContract.getTotalRaisedUsdt(provider);
            } catch (e) {
                console.log(`无法获取 total_raised_usdt: ${e.message}`);
                data.total_raised_usdt = BigInt(0);
            }

            try {
                data.total_tokens_sold = await auctionContract.getTotalTokensSold(provider);
            } catch (e) {
                console.log(`无法获取 total_tokens_sold: ${e.message}`);
                data.total_tokens_sold = BigInt(0);
            }

            try {
                data.purchase_count = await auctionContract.getPurchaseCount(provider);
            } catch (e) {
                console.log(`无法获取 purchase_count: ${e.message}`);
                data.purchase_count = BigInt(0);
            }

            try {
                data.current_round = await auctionContract.getCurrentRound(provider);
            } catch (e) {
                console.log(`无法获取 current_round: ${e.message}`);
                data.current_round = BigInt(0);
            }

            try {
                data.current_price = await auctionContract.getCurrentPrice(provider);
            } catch (e) {
                console.log(`无法获取 current_price: ${e.message}`);
                data.current_price = BigInt(0);
            }

            try {
                data.auction_status = await auctionContract.getAuctionStatus(provider);
            } catch (e) {
                console.log(`无法获取 auction_status: ${e.message}`);
                data.auction_status = BigInt(0);
            }

            try {
                data.remaining_tokens = await auctionContract.getRemainingTokens(provider);
            } catch (e) {
                console.log(`无法获取 remaining_tokens: ${e.message}`);
                data.remaining_tokens = BigInt(0);
            }

            // 拍卖配置信息
            try {
                data.auction_info = await auctionContract.getAuctionInfo(provider);
            } catch (e) {
                console.log(`无法获取 auction_info: ${e.message}`);
                data.auction_info = null;
            }

            // 验证主合约数据完整性
            const validationResult = await this.dataValidator.validateMainContractData(contractAddress, data);
            if (!validationResult.isValid) {
                await this.errorLogger.logError({
                    type: 'MAIN_CONTRACT_VALIDATION_FAILED',
                    operation: 'getMainContractData',
                    message: `Main contract data validation failed: ${validationResult.errors.length} errors, ${validationResult.warnings.length} warnings`,
                    severity: 'warning',
                    context: {
                        contractAddress,
                        errors: validationResult.errors,
                        warnings: validationResult.warnings
                    },
                    retryable: false
                });
            }

            return data;
        } catch (error) {
            console.error(`读取主合约 ${contractAddress} 数据时出错:`, error.message);
            return null;
        }
    }

    async getRoundStats(contractAddress: string, roundNumber: number): Promise<any> {
        try {
            // 由于主合约可能没有直接的round_stats方法，暂时返回null
            // 实际的轮次统计可能需要通过其他方式获取
            console.log(`轮次统计功能待实现: round ${roundNumber}`);
            return null;
        } catch (error) {
            console.error(`获取轮次 ${roundNumber} 统计时出错:`, error.message);
            return null;
        }
    }

    async getAllRoundsStats(contractAddress: string): Promise<any> {
        try {
            // 由于主合约可能没有直接的all_rounds_summary方法，暂时返回空对象
            // 实际的所有轮次统计可能需要通过其他方式获取
            console.log(`所有轮次统计功能待实现`);
            return {};
        } catch (error) {
            console.error('获取所有轮次统计时出错:', error.message);
            return {};
        }
    }

    parseStackValue(stack: any, expectedType: string = 'int'): any {
        if (!stack || stack.length === 0) {
            return expectedType === 'dict' ? {} : expectedType === 'tuple' ? null : 0;
        }

        const item = stack[0];
        
        try {
            switch (expectedType) {
                case 'int':
                    if (item['@type'] === 'tvm.stackEntryNumber') {
                        const num = item.number || '0';
                        return typeof num === 'string' ? BigInt(num) : BigInt(num.toString());
                    }
                    const value = item.value || item.number || '0';
                    return typeof value === 'string' ? BigInt(value) : BigInt(value.toString());
                
                case 'address':
                    if (item['@type'] === 'tvm.stackEntrySlice') {
                        // 处理地址类型的stack entry
                        const address = item.slice || item.value || item.address || '';
                        return this.normalizeAddress(address);
                    }
                    if (item['@type'] === 'tvm.stackEntryCell') {
                        // 某些情况下地址可能以cell形式返回
                        const address = item.cell || item.value || '';
                        return this.normalizeAddress(address);
                    }
                    // 直接返回地址字符串
                    const address = item.address || item.value || item.slice || '';
                    return this.normalizeAddress(address);
                
                case 'dict':
                    // 简化处理字典类型，实际需要更复杂的解析
                    return {};
                
                case 'tuple':
                    // 简化处理元组类型
                    return item;
                
                default:
                    const defaultValue = item.value || item.number || 0;
                    return typeof defaultValue === 'string' ? defaultValue : defaultValue.toString();
            }
        } catch (error) {
            console.warn(`解析stack值时出错:`, error.message, 'item:', item);
            if (expectedType === 'dict') return {};
            if (expectedType === 'tuple') return null;
            if (expectedType === 'address') return '';
            return BigInt(0);
        }
    }

    // 安全地将BigInt转换为Number
    safeBigIntToNumber(value: any): number {
        if (typeof value === 'bigint') {
            return Number(value);
        }
        return Number(value || 0);
    }

    // 辅助方法：格式化代币数量（从nano tokens转换为TON）
    formatTokens(nanoAmount: any): number {
        return this.safeBigIntToNumber(nanoAmount) / 1e9;
    }

    // 辅助方法：格式化USDT数量（从micro USDT转换为USDT）
    formatUSDT(microAmount: any): number {
        return this.safeBigIntToNumber(microAmount) / 1e6;
    }

    // 辅助方法：格式化退款金额（自动检测TON或USDT）
    formatRefundAmount(amount: any, currency: string = 'auto'): number {
        if (currency === 'TON' || (currency === 'auto' && amount >= 1e9)) {
            return this.formatTokens(amount);
        } else {
            return this.formatUSDT(amount);
        }
    }

    // 从合约地址提取用户地址的fallback方法
    extractUserFromContractAddress(contractAddress: string): string {
        // 这是一个fallback方法，在无法通过owner获取用户地址时使用
        // 理想情况下应该从创建合约的消息中提取用户地址
        // 现在先返回合约地址作为临时处理
        console.warn(`使用合约地址作为用户地址的临时处理: ${contractAddress}`);
        return contractAddress;
    }

    // 获取错误统计和报告
    getErrorReport() {
        return this.errorLogger.generateErrorReport();
    }

    // 获取错误记录器实例（用于外部访问）
    getErrorLogger() {
        return this.errorLogger;
    }

    // 获取数据验证器实例（用于外部访问）
    getDataValidator() {
        return this.dataValidator;
    }

    // 获取重试管理器实例（用于外部访问）
    getRetryManager() {
        return this.retryManager;
    }

    // 标准化地址格式
    normalizeAddress(address: any): string {
        if (!address) return '';
        
        // 如果是hex格式且没有0:前缀，添加前缀
        if (typeof address === 'string') {
            address = address.trim();
            // 如果是64个字符的hex字符串，添加0:前缀
            if (/^[0-9a-fA-F]{64}$/.test(address)) {
                return `0:${address}`;
            }
            // 如果已经有0:前缀，直接返回
            if (address.startsWith('0:')) {
                return address;
            }
            // 如果是用户友好格式，直接返回
            if (address.startsWith('EQ') || address.startsWith('UQ')) {
                return address;
            }
        }
        
        return address;
    }
}