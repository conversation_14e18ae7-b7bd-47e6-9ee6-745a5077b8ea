#!/usr/bin/env node

import fs from 'fs/promises';
import { TonCenterClient } from './api-client.js';
import { ContractReader } from './contract-reader.js';

export class AddressMapper {
    constructor() {
        this.client = new TonCenterClient();
        this.contractReader = new ContractReader(this.client);
        this.ADDRESS_MAPPING_CACHE = 'address_mapping_cache.json';
    }

    // 主要方法：为所有合约建立地址映射
    async buildAddressMapping(userContracts) {
        console.log('🔍 开始建立合约-用户地址映射...');
        
        // 加载现有的地址映射缓存
        const existingMapping = await this.loadAddressMappingCache();
        let newMappings = 0;
        let cacheHits = 0;

        const mapping = new Map(Object.entries(existingMapping));

        for (let i = 0; i < userContracts.length; i++) {
            const contract = userContracts[i];
            const contractAddress = contract.address;

            try {
                if (mapping.has(contractAddress)) {
                    // 使用缓存的映射
                    cacheHits++;
                    if ((i + 1) % 50 === 0) {
                        console.log(`  处理进度: ${i + 1}/${userContracts.length} (缓存命中: ${cacheHits}, 新获取: ${newMappings})`);
                    }
                } else {
                    // 获取新的用户地址
                    console.log(`  获取用户地址 ${i + 1}/${userContracts.length}: ${contractAddress}`);
                    
                    const userAddress = await this.getUserAddressFromContract(contractAddress);
                    if (userAddress && userAddress !== contractAddress) {
                        mapping.set(contractAddress, userAddress);
                        newMappings++;
                        
                        // 每处理10个新地址就保存一次缓存
                        if (newMappings % 10 === 0) {
                            await this.saveAddressMappingCache(mapping);
                            console.log(`    已保存 ${newMappings} 个新地址映射到缓存`);
                        }
                    }
                }
            } catch (error) {
                console.error(`  ❌ 获取合约 ${contractAddress} 的用户地址失败:`, error.message);
                // 继续处理其他合约
                continue;
            }
        }

        // 最终保存缓存
        await this.saveAddressMappingCache(mapping);

        console.log(`✅ 地址映射建立完成:`);
        console.log(`  缓存命中: ${cacheHits} 个`);
        console.log(`  新获取: ${newMappings} 个`);
        console.log(`  总映射: ${mapping.size} 个`);

        return mapping;
    }

    // 从单个合约获取用户地址
    async getUserAddressFromContract(contractAddress) {
        try {
            const ownerData = await this.client.runGetMethod(contractAddress, 'owner');
            const userAddress = this.contractReader.parseStackValue(ownerData.stack, 'address');
            
            if (userAddress && userAddress !== '') {
                const normalizedAddress = this.contractReader.normalizeAddress(userAddress);
                console.log(`    ✓ ${contractAddress} -> ${normalizedAddress}`);
                return normalizedAddress;
            } else {
                console.warn(`    ⚠️ ${contractAddress} -> owner返回空地址`);
                return contractAddress; // fallback
            }
        } catch (error) {
            console.warn(`    ❌ ${contractAddress} -> owner调用失败: ${error.message}`);
            return contractAddress; // fallback
        }
    }

    // 加载地址映射缓存
    async loadAddressMappingCache() {
        try {
            const cacheData = await fs.readFile(this.ADDRESS_MAPPING_CACHE, 'utf-8');
            const cache = JSON.parse(cacheData);
            
            console.log(`从缓存加载了 ${Object.keys(cache.mappings || {}).length} 个地址映射`);
            console.log(`地址映射缓存更新时间: ${cache.lastUpdated || '未知'}`);
            
            return cache.mappings || {};
        } catch (error) {
            if (error.code === 'ENOENT') {
                console.log('地址映射缓存文件不存在, 将创建新的缓存');
            } else {
                console.warn('加载地址映射缓存失败:', error.message);
            }
            return {};
        }
    }

    // 保存地址映射缓存
    async saveAddressMappingCache(mapping) {
        try {
            const mappingObject = mapping instanceof Map ? Object.fromEntries(mapping) : mapping;
            
            const cache = {
                lastUpdated: new Date().toISOString(),
                totalMappings: Object.keys(mappingObject).length,
                mappings: mappingObject
            };
            
            await fs.writeFile(
                this.ADDRESS_MAPPING_CACHE, 
                JSON.stringify(cache, null, 2),
                'utf-8'
            );
        } catch (error) {
            console.error('保存地址映射缓存失败:', error.message);
        }
    }

    // 清除地址映射缓存
    async clearAddressMappingCache() {
        try {
            await fs.unlink(this.ADDRESS_MAPPING_CACHE);
            console.log('地址映射缓存已清除');
        } catch (error) {
            if (error.code !== 'ENOENT') {
                console.error('清除地址映射缓存失败:', error.message);
            }
        }
    }

    // 验证地址映射的准确性
    async validateAddressMapping(mapping, sampleSize = 10) {
        console.log(`🔍 验证地址映射准确性 (抽样 ${sampleSize} 个)...`);
        
        const entries = Array.from(mapping.entries());
        const sampleEntries = entries.slice(0, Math.min(sampleSize, entries.length));
        
        let validCount = 0;
        let invalidCount = 0;

        for (const [contractAddress, cachedUserAddress] of sampleEntries) {
            try {
                const actualUserAddress = await this.getUserAddressFromContract(contractAddress);
                
                if (actualUserAddress === cachedUserAddress) {
                    validCount++;
                    console.log(`  ✅ ${contractAddress} -> ${cachedUserAddress} (匹配)`);
                } else {
                    invalidCount++;
                    console.log(`  ❌ ${contractAddress} -> 缓存:${cachedUserAddress}, 实际:${actualUserAddress} (不匹配)`);
                }
            } catch (error) {
                console.log(`  ⚠️ ${contractAddress} -> 验证失败: ${error.message}`);
            }
        }

        console.log(`验证结果: ${validCount} 个正确, ${invalidCount} 个错误`);
        return { validCount, invalidCount, accuracy: validCount / (validCount + invalidCount) };
    }

    // 获取映射统计信息
    async getMappingStats() {
        const cache = await this.loadAddressMappingCache();
        const mappingCount = Object.keys(cache).length;
        
        return {
            totalMappings: mappingCount,
            lastUpdated: cache.lastUpdated || null,
            cacheFile: this.ADDRESS_MAPPING_CACHE
        };
    }
}

// 如果直接运行此文件
if (import.meta.url === `file://${process.argv[1]}`) {
    async function main() {
        const args = process.argv.slice(2);
        
        if (args.includes('--help')) {
            console.log(`
地址映射工具使用方法:

node src/address-mapper.js [选项]

选项:
  --help         显示帮助信息
  --build        建立地址映射 (需要先有用户合约缓存)
  --validate     验证现有地址映射的准确性
  --stats        显示映射统计信息
  --clear        清除地址映射缓存

示例:
  node src/address-mapper.js --build      # 建立/更新地址映射
  node src/address-mapper.js --validate   # 验证地址映射准确性
  node src/address-mapper.js --stats      # 显示统计信息
  node src/address-mapper.js --clear      # 清除缓存
`);
            return;
        }

        const mapper = new AddressMapper();

        if (args.includes('--clear')) {
            await mapper.clearAddressMappingCache();
            return;
        }

        if (args.includes('--stats')) {
            const stats = await mapper.getMappingStats();
            console.log('📊 地址映射统计:');
            console.log(`  总映射数: ${stats.totalMappings}`);
            console.log(`  最后更新: ${stats.lastUpdated || '未知'}`);
            console.log(`  缓存文件: ${stats.cacheFile}`);
            return;
        }

        if (args.includes('--validate')) {
            const existingMapping = await mapper.loadAddressMappingCache();
            const mapping = new Map(Object.entries(existingMapping));
            
            if (mapping.size === 0) {
                console.error('❌ 没有找到地址映射，请先运行 --build');
                return;
            }
            
            await mapper.validateAddressMapping(mapping);
            return;
        }

        if (args.includes('--build')) {
            // 加载用户合约列表
            try {
                const contractsData = await fs.readFile('user_contracts_cache.json', 'utf-8');
                const contractsCache = JSON.parse(contractsData);
                const userContracts = contractsCache.contracts || [];
                
                if (userContracts.length === 0) {
                    console.error('❌ 未找到用户合约数据，请先运行 npm start 收集合约地址');
                    return;
                }
                
                console.log(`📄 加载了 ${userContracts.length} 个用户合约`);
                await mapper.buildAddressMapping(userContracts);
                
            } catch (error) {
                console.error('❌ 加载用户合约缓存失败:', error.message);
                console.log('请先运行 npm start 收集合约地址');
            }
            return;
        }

        // 默认显示帮助
        console.log('请指定操作选项，使用 --help 查看帮助');
    }

    main().catch(console.error);
}