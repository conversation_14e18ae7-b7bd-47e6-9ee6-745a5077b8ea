#!/usr/bin/env node

import { TonCenterClient } from './api-client.js';

async function quickStats() {
    console.log('🔍 快速统计查询');
    console.log('==================');

    const client = new TonCenterClient();
    const AUCTION_CONTRACT = '0:78587F92178E8DEFF56631F2DE97493F63DA59798D6BA819048F05B2DC2D8F9D';
    const OP_CREATE_USER_PURCHASE = '0x41734c0c';

    try {
        // 获取最近的用户购买记录
        console.log('获取最近的用户购买记录...');
        const recentMessages = await client.getMessages(AUCTION_CONTRACT, OP_CREATE_USER_PURCHASE, 10);
        
        console.log(`\n📝 最近 ${recentMessages.messages.length} 笔用户购买记录:`);
        recentMessages.messages.forEach((msg, index) => {
            console.log(`${index + 1}. 合约: ${msg.destination}`);
            console.log(`   交易哈希: ${msg.hash}`);
            console.log(`   时间: ${new Date(msg.created_at * 1000).toLocaleString()}`);
            console.log(`   价值: ${(Number(msg.value) / 1e9).toFixed(4)} TON`);
            console.log('');
        });

        // 尝试获取主合约基本信息
        console.log('获取主合约基本信息...');
        try {
            const totalRaised = await client.runGetMethod(AUCTION_CONTRACT, 'total_raised');
            const totalTokensSold = await client.runGetMethod(AUCTION_CONTRACT, 'total_tokens_sold');
            const purchaseCount = await client.runGetMethod(AUCTION_CONTRACT, 'purchase_count');
            const currentRound = await client.runGetMethod(AUCTION_CONTRACT, 'current_round');
            
            console.log('\n🏢 主合约基本信息:');
            console.log(`总募集: ${formatTokens(parseStackInt(totalRaised.stack))} TON`);
            console.log(`售出代币: ${formatTokens(parseStackInt(totalTokensSold.stack))}`);
            console.log(`购买次数: ${parseStackInt(purchaseCount.stack)}`);
            console.log(`当前轮次: ${parseStackInt(currentRound.stack)}`);
            
        } catch (error) {
            console.log(`⚠️ 无法获取主合约信息: ${error.message}`);
        }

    } catch (error) {
        console.error('❌ 查询出错:', error);
    }
}

function parseStackInt(stack) {
    if (!stack || stack.length === 0) return BigInt(0);
    const item = stack[0];
    return BigInt(item.number || item.value || '0');
}

function formatTokens(nanoAmount) {
    return (Number(nanoAmount) / 1e9).toFixed(4);
}

// 运行快速统计
quickStats().catch(console.error);