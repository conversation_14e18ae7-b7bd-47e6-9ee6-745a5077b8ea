import axios from 'axios';

export class TonCenterClient {
    constructor(baseURL = 'https://toncenter.com/api/v3') {
        this.baseURL = baseURL;
        this.client = axios.create({
            baseURL,
            timeout: 30000,
            headers: {
                'Accept': 'application/json',
                'x-api-key': '8424cbe83460d87e34163e47f3103a4b53621790e79d86ba79dc0d143d65816b'
            }
        });
        
        // 限流设置
        this.requestCount = 0;
        this.lastRequestTime = 0;
        this.minDelay = 200; // 基础延迟200ms
        this.maxRetries = 5;
        this.baseRetryDelay = 1000; // 基础重试延迟1秒
    }

    async getAllMessages(source, opcode, limit = 100) {
        const messages = [];
        let offset = 0;
        let hasMore = true;

        while (hasMore) {
            try {
                const response = await this.client.get('/messages', {
                    params: {
                        source,
                        opcode,
                        limit,
                        offset,
                        sort: 'desc'
                    }
                });

                const data = response.data;
                if (data.messages && data.messages.length > 0) {
                    messages.push(...data.messages);
                    offset += data.messages.length;
                    hasMore = data.messages.length === limit;
                    
                    console.log(`Fetched ${data.messages.length} messages, total: ${messages.length}`);
                    
                    // Add delay to avoid rate limiting
                    await this.throttleRequest();
                } else {
                    hasMore = false;
                }
            } catch (error) {
                console.error(`Error fetching messages at offset ${offset}:`, error.message);
                if (error.response?.status === 429) {
                    const retryDelay = this.calculateRetryDelay(1);
                    console.log(`Rate limited, waiting ${retryDelay/1000} seconds...`);
                    await this.delay(retryDelay);
                    continue;
                }
                break;
            }
        }

        return messages;
    }

    async getMessages(source, opcode, limit = 10, offset = 0) {
        try {
            const response = await this.client.get('/messages', {
                params: {
                    source,
                    opcode,
                    limit,
                    offset,
                    sort: 'desc'
                }
            });
            return response.data;
        } catch (error) {
            console.error('Error fetching messages:', error.message);
            throw error;
        }
    }

    async getContractState(address) {
        try {
            const response = await this.client.get(`/account/${address}`);
            return response.data;
        } catch (error) {
            console.error(`Error fetching contract state for ${address}:`, error.message);
            throw error;
        }
    }

    async runGetMethod(address, method, stack = []) {
        return await this.retryRequest(async () => {
            await this.throttleRequest();
            
            const response = await this.client.post(`/runGetMethod`, {
                address,
                method,
                stack
            });
            return response.data;
        }, `get method ${method} on ${address}`);
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async throttleRequest() {
        const now = Date.now();
        const elapsed = now - this.lastRequestTime;
        
        // 根据请求频率动态调整延迟
        let delay = this.minDelay;
        if (this.requestCount > 10) {
            delay = this.minDelay * 2;
        }
        if (this.requestCount > 50) {
            delay = this.minDelay * 4;
        }
        
        // 如果距离上次请求时间太短，则等待
        if (elapsed < delay) {
            await this.delay(delay - elapsed);
        }
        
        this.requestCount++;
        this.lastRequestTime = Date.now();
        
        // 每100个请求重置计数器和增加延迟
        if (this.requestCount % 100 === 0) {
            console.log(`已发送 ${this.requestCount} 个请求，休息2秒...`);
            await this.delay(2000);
        }
    }

    async retryRequest(requestFn, description = 'request') {
        let lastError = null;
        
        for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
            try {
                return await requestFn();
            } catch (error) {
                lastError = error;
                console.error(error);
                if (error.response?.status === 429) {
                    const retryDelay = this.calculateRetryDelay(attempt);
                    console.log(`Rate limited on ${description} (attempt ${attempt}/${this.maxRetries}), waiting ${retryDelay/1000}s...`);
                    await this.delay(retryDelay);
                    continue;
                } else if (error.code === 'ECONNRESET' || error.code === 'ETIMEDOUT' || error.response?.status >= 500) {
                    const retryDelay = this.calculateRetryDelay(attempt);
                    console.log(`Network/server error on ${description} (attempt ${attempt}/${this.maxRetries}), retrying in ${retryDelay/1000}s...`);
                    await this.delay(retryDelay);
                    continue;
                } else {
                    // 非可重试错误，直接抛出
                    break;
                }
            }
        }
        
        console.error(`Max retries exceeded for ${description}`);
        throw lastError;
    }

    calculateRetryDelay(attempt) {
        // 指数退避算法
        const exponentialDelay = this.baseRetryDelay * Math.pow(2, attempt - 1);
        // 添加随机抖动，避免雷群效应
        const jitter = Math.random() * 1000;
        return Math.min(exponentialDelay + jitter, 30000); // 最大30秒
    }
}