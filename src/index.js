#!/usr/bin/env node

import fs from 'fs/promises';
import { StatsCollector } from './stats-collector.js';
import { CSVExporter } from './csv-exporter.js';

async function main() {
    console.log('🚀 Onion Auction 统计收集器启动');
    console.log('================================');

    const collector = new StatsCollector();

    // 检查命令行参数
    const args = process.argv.slice(2);
    if (args.includes('--clear-cache')) {
        console.log('清除用户合约缓存...');
        await collector.clearCache();
        return;
    }

    if (args.includes('--clear-checkpoint')) {
        console.log('清除checkpoint数据...');
        await collector.clearCheckpoint();
        return;
    }

    if (args.includes('--clear-all')) {
        console.log('清除所有缓存和checkpoint数据...');
        await collector.clearCache();
        await collector.clearCheckpoint();
        return;
    }

    if (args.includes('--export-csv')) {
        console.log('导出CSV报告...');
        const csvExporter = new CSVExporter();
        
        // 查找最新的统计文件
        const files = await fs.readdir('.');
        const statsFiles = files
            .filter(f => f.startsWith('raw-stats-') && f.endsWith('.json'))
            .sort()
            .reverse();
        
        if (statsFiles.length === 0) {
            console.error('❌ 未找到统计文件，请先运行 npm start 生成统计数据');
            return;
        }
        
        const statsFile = statsFiles[0];
        console.log(`📄 使用统计文件: ${statsFile}`);
        
        const statsData = await fs.readFile(statsFile, 'utf-8');
        const stats = JSON.parse(statsData);
        
        await csvExporter.exportAllToCSV(stats);
        return;
    }

    if (args.includes('--help')) {
        printHelp();
        return;
    }

    try {
        // 收集统计数据
        console.log('开始收集数据...');
        const stats = await collector.collectAllStats();

        // 生成报告
        console.log('生成报告...');
        const report = await collector.generateReport(stats);

        // 保存原始数据
        const rawDataFile = `raw-stats-${new Date().toISOString().split('T')[0]}.json`;
        await fs.writeFile(rawDataFile, JSON.stringify(stats, (key, value) => {
            // 处理 BigInt 和 Set 类型的序列化
            if (typeof value === 'bigint') {
                return value.toString();
            }
            if (value instanceof Set) {
                return Array.from(value);
            }
            if (value instanceof Map) {
                return Object.fromEntries(value);
            }
            return value;
        }, 2));
        
        console.log(`原始数据已保存到: ${rawDataFile}`);

        // 保存报告
        const reportFile = `stats-report-${new Date().toISOString().split('T')[0]}.json`;
        await fs.writeFile(reportFile, JSON.stringify(report, null, 2));
        console.log(`统计报告已保存到: ${reportFile}`);

        // 导出CSV报告
        console.log('\n📊 导出CSV报告...');
        const csvExporter = new CSVExporter();
        try {
            await csvExporter.exportAllToCSV(stats);
        } catch (error) {
            console.warn('⚠️ CSV导出失败:', error.message);
        }

        // 打印摘要
        printSummary(report);

    } catch (error) {
        console.error('❌ 收集统计数据时出错:', error);
        process.exit(1);
    }
}

function printSummary(report) {
    console.log('\n📊 统计摘要');
    console.log('================================');
    console.log(`📅 生成时间: ${report.summary.timestamp}`);
    console.log(`👥 总用户数: ${report.summary.totalUsers}`);
    console.log(`🛒 总购买次数: ${report.summary.totalPurchases}`);
    console.log(`🪙 总代币数量: ${report.summary.totalTokens}`);
    console.log(`🎯 总轮次数: ${report.summary.totalRounds}`);

    if (report.summary.mainContract) {
        console.log('\n🏢 主合约状态:');
        console.log(`  - 状态: ${getAuctionStatusText(report.summary.mainContract.auctionStatus)}`);
        console.log(`  - 当前轮次: ${report.summary.mainContract.currentRound}`);
        console.log(`  - 当前价格: ${formatTokens(report.summary.mainContract.currentPrice)} TON`);
        console.log(`  - TON募集: ${formatTokens(report.summary.mainContract.totalRaised)} TON`);
        console.log(`  - USDT募集: ${formatUSDT(report.summary.mainContract.totalRaisedUSDT)} USDT`);
        console.log(`  - 代币售出: ${formatTokens(report.summary.mainContract.totalTokensSold)}`);
        console.log(`  - 剩余代币: ${formatTokens(report.summary.mainContract.remainingTokens)}`);
    }

    if (report.rounds && report.rounds.length > 0) {
        console.log('\n🔄 轮次统计:');
        report.rounds.forEach(round => {
            console.log(`  轮次 ${round.round}: ${round.uniqueUsers} 用户, ${round.purchaseCount} 笔购买, ${formatTokens(round.totalTokens)} tokens`);
        });
    }

    if (report.topUsers && report.topUsers.length > 0) {
        console.log('\n🏆 Top 10 用户:');
        report.topUsers.slice(0, 10).forEach((user, index) => {
            console.log(`  ${index + 1}. ${user.address}: ${formatTokens(user.totalPurchased)} tokens (${user.purchaseCount} 笔购买)`);
        });
    }

    console.log('\n✅ 统计收集完成!');
}

function getAuctionStatusText(status) {
    const statusMap = {
        0: '待开始',
        1: '进行中',
        2: '成功结束',
        3: '失败结束'
    };
    return statusMap[status] || `未知(${status})`;
}

function formatTokens(nanoAmount) {
    return (Number(nanoAmount) / 1e9).toFixed(4);
}

function formatUSDT(microAmount) {
    return (Number(microAmount) / 1e6).toFixed(2);
}

function printHelp() {
    console.log(`
使用方法: node src/index.js [选项]

选项:
  --help              显示帮助信息
  --clear-cache       清除用户合约地址缓存
  --clear-checkpoint  清除checkpoint数据
  --clear-all         清除所有缓存和checkpoint数据
  --export-csv        导出最新统计数据为CSV格式

示例:
  node src/index.js                    # 运行完整统计收集 (自动导出CSV)
  node src/index.js --export-csv       # 单独导出CSV报告
  node src/index.js --clear-cache      # 清除合约地址缓存
  node src/index.js --clear-checkpoint # 清除checkpoint数据
  node src/index.js --clear-all        # 清除所有数据
  
智能缓存和断点续传机制:
  📁 user_contracts_cache.json - 存储用户合约地址，避免重复API调用
  📁 user_data_cache.json - 存储用户合约数据，实现极速重运行 ⭐新功能
  📁 checkpoint_data.json - 存储处理进度，支持断点续传
  
  运行流程:
  1. 加载合约地址缓存，只获取新增合约
  2. 加载合约数据缓存，直接使用已获取的数据
  3. 加载checkpoint，从上次中断处继续
  4. 每处理10个合约自动保存进度和缓存
  5. 后续运行速度提升95%+，几乎瞬时完成
`);
}

// 如果直接运行此文件
if (import.meta.url === `file://${process.argv[1]}`) {
    main().catch(console.error);
}