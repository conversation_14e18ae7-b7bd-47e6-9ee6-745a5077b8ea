#!/usr/bin/env node

import fs from 'fs/promises';
import { TonClient } from '@ton/ton';
import { Address } from '@ton/core';
import { UserPurchase } from '../build/UserPurchase/UserPurchase_UserPurchase.js';
import { TonCenterClient } from './api-client.js';
import { ErrorLogger } from './error-logger.js';
import { RetryManager } from './retry-manager.js';

/**
 * 退款数据修复工具
 * 专门用于修复用户合约的退款数据
 */
export class RefundFixer {
    constructor() {
        this.client = new TonCenterClient();
        this.tonClient = new TonClient({
            endpoint: 'https://toncenter.com/api/v2/jsonRPC',
            apiKey: '8424cbe83460d87e34163e47f3103a4b53621790e79d86ba79dc0d143d65816b'
        });
        this.errorLogger = new ErrorLogger();
        this.retryManager = new RetryManager(this.errorLogger);
        
        this.USER_DATA_CACHE_FILE = 'user_data_cache.json';
        this.REFUND_FIX_LOG_FILE = `refund-fix-log-${new Date().toISOString().split('T')[0]}.json`;
    }

    /**
     * 修复所有用户合约的退款数据
     */
    async fixAllRefundData() {
        console.log('🔧 开始修复退款数据...');
        
        try {
            // 加载用户数据缓存
            const userDataCache = await this.loadUserDataCache();
            const contractAddresses = Object.keys(userDataCache.data || {});
            
            console.log(`找到 ${contractAddresses.length} 个用户合约需要检查退款数据`);
            
            const fixResults = {
                total: contractAddresses.length,
                fixed: 0,
                errors: 0,
                noRefunds: 0,
                details: []
            };

            for (let i = 0; i < contractAddresses.length; i++) {
                const contractAddress = contractAddresses[i];
                console.log(`\n处理合约 ${i + 1}/${contractAddresses.length}: ${contractAddress}`);
                
                try {
                    const result = await this.fixContractRefundData(contractAddress, userDataCache.data[contractAddress]);
                    fixResults.details.push(result);
                    
                    if (result.hasRefunds) {
                        fixResults.fixed++;
                        // 更新缓存数据
                        userDataCache.data[contractAddress].refund_count = result.refundCount.toString();
                        userDataCache.data[contractAddress].total_refunded = result.totalRefunded.toString();
                        console.log(`  ✅ 修复完成: ${result.refundCount} 笔退款, 总额 ${this.formatTokens(result.totalRefunded)} TON`);
                    } else if (result.error) {
                        fixResults.errors++;
                        console.log(`  ❌ 处理失败: ${result.error}`);
                    } else {
                        fixResults.noRefunds++;
                        console.log(`  ℹ️  无退款记录`);
                    }
                } catch (error) {
                    fixResults.errors++;
                    fixResults.details.push({
                        contractAddress,
                        error: error.message,
                        hasRefunds: false
                    });
                    console.log(`  ❌ 处理异常: ${error.message}`);
                }
                
                // 每处理10个合约保存一次进度
                if ((i + 1) % 10 === 0) {
                    await this.saveUserDataCache(userDataCache);
                    console.log(`已保存进度: ${i + 1}/${contractAddresses.length}`);
                }
            }

            // 保存最终结果
            await this.saveUserDataCache(userDataCache);
            await this.saveFixResults(fixResults);
            
            console.log('\n📊 退款数据修复完成!');
            console.log(`总计: ${fixResults.total} 个合约`);
            console.log(`修复: ${fixResults.fixed} 个合约有退款`);
            console.log(`无退款: ${fixResults.noRefunds} 个合约`);
            console.log(`错误: ${fixResults.errors} 个合约处理失败`);
            
            return fixResults;
        } catch (error) {
            console.error('修复退款数据时出错:', error);
            throw error;
        }
    }

    /**
     * 修复单个合约的退款数据
     */
    async fixContractRefundData(contractAddress, cachedData) {
        const result = {
            contractAddress,
            hasRefunds: false,
            refundCount: 0,
            totalRefunded: BigInt(0),
            totalRefundedTokens: BigInt(0),
            refundDetails: [],
            error: null
        };

        try {
            const contractAddr = Address.parse(contractAddress);
            const userContract = UserPurchase.fromAddress(contractAddr);
            const provider = this.tonClient.provider(contractAddr);
            // 获取购买记录数量
            const purchaseCounter = parseInt(cachedData.purchase_id_counter || '0');
            if (purchaseCounter === 0) {
                return result;
            }

            console.log(`  检查 ${purchaseCounter} 笔购买记录的退款状态...`);

            // 逐个检查每笔购买的退款状态
            for (let purchaseId = 1; purchaseId <= purchaseCounter; purchaseId++) {
                try {
                    // 检查是否已退款
                    const isRefunded = await this.retryManager.executeCriticalOperation(
                        () => userContract.getIsRefunded(provider, BigInt(purchaseId)),
                        'getIsRefunded',
                        { contractAddress, purchaseId }
                    );

                    console.log(`  检查购买 #${purchaseId} 退款状态： ${isRefunded ? '已退款' : '未退款'}`);

                    if (isRefunded) {
                        // 获取购买详情以获取退款金额
                        const purchaseDetails = await this.retryManager.executeCriticalOperation(
                            () => userContract.getPurchaseDetails(provider, BigInt(purchaseId)),
                            'getPurchaseDetails',
                            { contractAddress, purchaseId }
                        );

                        if (purchaseDetails && purchaseDetails.amount) {
                            result.hasRefunds = true;
                            result.refundCount++;
                            result.totalRefunded += purchaseDetails.amount;
                            result.totalRefundedTokens += purchaseDetails.tokens;
                            result.refundDetails.push({
                                purchaseId,
                                amount: purchaseDetails.amount.toString(),
                                tokens: purchaseDetails.tokens,
                                roundNumber: purchaseDetails.round_number?.toString() || '0'
                            });
                            
                            console.log(`    退款 #${purchaseId}: ${this.formatTokens(purchaseDetails.amount)} TON`);
                        }
                    }
                } catch (error) {
                    console.error(error);
                    console.log(`    检查购买 #${purchaseId} 退款状态失败: ${error.message}`);
                    await this.errorLogger.logContractError(contractAddress, 'checkRefundStatus', error, {
                        purchaseId,
                        severity: 'warning'
                    });
                }
            }

        } catch (error) {
            result.error = error.message;
            await this.errorLogger.logContractError(contractAddress, 'fixRefundData', error, {
                severity: 'error'
            });
        }

        return result;
    }

    /**
     * 修复特定合约的退款数据
     */
    async fixSpecificContract(contractAddress) {
        console.log(`🔧 修复特定合约的退款数据: ${contractAddress}`);
        
        try {
            // 加载缓存数据
            const userDataCache = await this.loadUserDataCache();
            const cachedData = userDataCache.data[contractAddress];
            
            if (!cachedData) {
                throw new Error(`合约 ${contractAddress} 不在缓存中`);
            }

            const result = await this.fixContractRefundData(contractAddress, cachedData);
            
            if (result.hasRefunds) {
                // 更新缓存
                userDataCache.data[contractAddress].refund_count = result.refundCount.toString();
                userDataCache.data[contractAddress].total_refunded = result.totalRefunded.toString();
                userDataCache.data[contractAddress].total_refunded_tokens = result.totalRefundedTokens.toString();
                await this.saveUserDataCache(userDataCache);
                
                console.log(`✅ 修复完成: ${result.refundCount} 笔退款, 总额 ${this.formatTokens(result.totalRefunded)} TON`);
            } else {
                console.log('ℹ️  该合约无退款记录');
            }
            
            return result;
        } catch (error) {
            console.error(`修复合约 ${contractAddress} 退款数据失败:`, error);
            throw error;
        }
    }

    /**
     * 验证退款数据修复结果
     */
    async validateRefundFix() {
        console.log('🔍 验证退款数据修复结果...');
        
        const userDataCache = await this.loadUserDataCache();
        const contracts = Object.entries(userDataCache.data || {});
        
        let totalRefunds = 0;
        let totalRefundAmount = BigInt(0);
        let contractsWithRefunds = 0;

        for (const [contractAddress, data] of contracts) {
            const refundCount = parseInt(data.refund_count || '0');
            const refundAmount = BigInt(data.total_refunded || '0');
            
            if (refundCount > 0) {
                contractsWithRefunds++;
                totalRefunds += refundCount;
                totalRefundAmount += refundAmount;
                
                console.log(`${contractAddress}: ${refundCount} 笔退款, ${this.formatTokens(refundAmount)} TON`);
            }
        }

        console.log('\n📊 退款数据验证结果:');
        console.log(`有退款的合约数: ${contractsWithRefunds}`);
        console.log(`总退款笔数: ${totalRefunds}`);
        console.log(`总退款金额: ${this.formatTokens(totalRefundAmount)} TON`);
        
        return {
            contractsWithRefunds,
            totalRefunds,
            totalRefundAmount: totalRefundAmount.toString()
        };
    }

    // 辅助方法
    async loadUserDataCache() {
        try {
            const cacheData = await fs.readFile(this.USER_DATA_CACHE_FILE, 'utf-8');
            return JSON.parse(cacheData);
        } catch (error) {
            throw new Error(`无法加载用户数据缓存: ${error.message}`);
        }
    }

    async saveUserDataCache(cache) {
        try {
            cache.lastUpdated = new Date().toISOString();
            await fs.writeFile(
                this.USER_DATA_CACHE_FILE,
                JSON.stringify(cache, null, 2),
                'utf-8'
            );
        } catch (error) {
            console.error('保存用户数据缓存失败:', error.message);
        }
    }

    async saveFixResults(results) {
        try {
            const report = {
                timestamp: new Date().toISOString(),
                summary: {
                    total: results.total,
                    fixed: results.fixed,
                    errors: results.errors,
                    noRefunds: results.noRefunds
                },
                details: results.details,
                errorReport: this.errorLogger.generateErrorReport()
            };

            await fs.writeFile(this.REFUND_FIX_LOG_FILE, JSON.stringify(report, null, 2));
            console.log(`修复日志已保存到: ${this.REFUND_FIX_LOG_FILE}`);
        } catch (error) {
            console.error('保存修复日志失败:', error.message);
        }
    }

    formatTokens(nanoAmount) {
        return (Number(nanoAmount) / 1e9).toFixed(4);
    }
}

// 命令行接口
async function main() {
    const args = process.argv.slice(2);
    const fixer = new RefundFixer();

    try {
        if (args.includes('--contract')) {
            const contractIndex = args.indexOf('--contract');
            const contractAddress = args[contractIndex + 1];
            if (!contractAddress) {
                console.error('请提供合约地址: --contract <address>');
                process.exit(1);
            }
            await fixer.fixSpecificContract(contractAddress);
        } else if (args.includes('--validate')) {
            await fixer.validateRefundFix();
        } else {
            await fixer.fixAllRefundData();
        }
    } catch (error) {
        console.error('执行失败:', error);
        process.exit(1);
    }
}

// 如果直接运行此文件
if (import.meta.url === `file://${process.argv[1]}`) {
    main().catch(console.error);
}
