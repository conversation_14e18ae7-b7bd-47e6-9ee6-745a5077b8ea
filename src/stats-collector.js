import { TonCenterClient } from './api-client.js';
import { ContractReader } from './contract-reader.js';
import { AddressMapper } from './address-mapper.js';
import fs from 'fs/promises';

export class StatsCollector {
    constructor() {
        this.client = new TonCenterClient();
        this.contractReader = new ContractReader(this.client);
        this.addressMapper = new AddressMapper();
        
        // Contract addresses and opcodes from the contracts
        this.AUCTION_CONTRACT = '0:78587F92178E8DEFF56631F2DE97493F63DA59798D6BA819048F05B2DC2D8F9D';
        this.OP_CREATE_USER_PURCHASE = '0x41734c0c';
        this.OP_PURCHASE_WITH_SIGNATURE = '0x5ae22804';
        this.OP_REFUND = '0x31d5b5ac';
        
        // Cache file for user contract addresses
        this.USER_CONTRACTS_CACHE_FILE = 'user_contracts_cache.json';
        // Cache file for user contract data
        this.USER_DATA_CACHE_FILE = 'user_data_cache.json';
        // Checkpoint file for user contract data
        this.CHECKPOINT_FILE = 'checkpoint_data.json';
        this.CHECKPOINT_INTERVAL = 10; // 每处理10个合约保存一次checkpoint
    }

    async collectAllStats() {
        console.log('开始收集统计数据...');
        
        const stats = {
            users: new Set(),
            userPurchases: new Map(),
            totalAmountTON: 0,
            totalAmountUSDT: 0,
            totalTokens: 0,
            totalRefunds: 0,
            purchaseCount: 0,
            userContracts: [],
            rounds: new Map(),
            timestamp: new Date().toISOString()
        };

        try {
            // 1. 加载或收集用户购买合约地址
            console.log('1. 收集用户购买合约地址...');
            const userContracts = await this.loadOrFetchUserContracts();
            
            console.log(`总共 ${userContracts.length} 个用户购买合约`);

            // 存储到stats中
            stats.userContracts = userContracts;

            // 2. 加载用户合约数据缓存
            console.log('2. 加载用户合约数据缓存...');
            const userDataCache = await this.loadUserDataCache();
            
            // 建立合约地址到用户地址的映射
            const contractToUserMap = await this.buildContractUserMapping(userContracts);
            
            // 3. 尝试加载checkpoint数据
            console.log('3. 检查checkpoint数据...');
            const checkpoint = await this.loadCheckpoint();
            let processedContracts = new Set(checkpoint.processedContracts || []);
            let startIndex = 0;

            if (checkpoint.lastProcessedIndex !== undefined) {
                startIndex = Math.max(0, checkpoint.lastProcessedIndex);
                console.log(`从checkpoint恢复: 已处理 ${processedContracts.size} 个合约，从索引 ${startIndex} 继续`);
                
                // 恢复已处理的数据到stats
                if (checkpoint.stats) {
                    this.mergeCheckpointStats(stats, checkpoint.stats);
                }
            }

            // 4. 解析用户购买记录（从断点继续）
            console.log('4. 处理用户合约数据...');
            let processedCount = 0;
            let cacheHits = 0;
            let newDataFetched = 0;
            
            for (let i = startIndex; i < userContracts.length; i++) {
                const contract = userContracts[i];
                
                try {
                    // 跳过已处理的合约
                    if (processedContracts.has(contract.address)) {
                        continue;
                    }

                    const userContractAddress = contract.address;
                    console.log(`处理合约 ${i + 1}/${userContracts.length}: ${userContractAddress}`);
                    
                    // 检查是否有缓存数据
                    if (userDataCache[userContractAddress]) {
                        console.log(`  ✓ 使用缓存数据`);
                        await this.processUserDataFromCache(userContractAddress, userDataCache[userContractAddress], stats);
                        cacheHits++;
                    } else {
                        console.log(`  ↻ 获取新数据`);
                        const contractData = await this.collectUserContractStats(userContractAddress, stats, contractToUserMap);
                        
                        // 保存到用户数据缓存
                        if (contractData) {
                            userDataCache[userContractAddress] = contractData;
                            await this.saveUserDataCache(userDataCache);
                        }
                        newDataFetched++;
                    }
                    
                    // 标记为已处理
                    processedContracts.add(contract.address);
                    processedCount++;
                    
                    // 每处理一定数量的合约保存checkpoint
                    if (processedCount % this.CHECKPOINT_INTERVAL === 0) {
                        console.log(`保存checkpoint... (已处理 ${processedCount} 个合约，缓存命中 ${cacheHits}，新获取 ${newDataFetched})`);
                        await this.saveCheckpoint({
                            lastProcessedIndex: i,
                            processedContracts: Array.from(processedContracts),
                            stats: this.serializeStatsForCheckpoint(stats),
                            timestamp: new Date().toISOString()
                        });
                    }
                } catch (error) {
                    console.error(`处理用户合约 ${contract.address} 时出错:`, error.message);
                    // 记录错误但继续处理其他合约
                    continue;
                }
            }

            // 4. 收集主合约统计信息
            console.log('4. 收集主合约统计信息...');
            await this.collectMainContractStats(stats);

            // 5. 处理统计结果
            console.log('5. 处理统计结果...');
            this.processStats(stats);

            // 6. 最终保存用户数据缓存并显示统计
            await this.saveUserDataCache(userDataCache);
            console.log(`\n📊 处理统计: 缓存命中 ${cacheHits} 个，新获取 ${newDataFetched} 个，总计 ${processedCount} 个合约`);

            // 7. 生成验证和错误报告
            console.log('7. 生成验证和错误报告...');
            await this.generateComprehensiveReport(stats);

            // 8. 清除checkpoint（完成后）
            await this.clearCheckpoint();
            console.log('✅ 数据收集完成，已清除checkpoint');

            return stats;
        } catch (error) {
            console.error('收集统计数据时出错:', error);
            throw error;
        }
    }

    async collectUserContractStats(userContractAddress, stats, contractToUserMap = null) {
        try {
            // 获取用户合约的详细信息
            const contractData = await this.contractReader.getUserContractData(userContractAddress, contractToUserMap);
            
            if (contractData) {
                await this.processUserContractData(userContractAddress, contractData, stats);
                return contractData; // 返回原始数据以便缓存
            }
            return null;
        } catch (error) {
            console.error(`获取用户合约 ${userContractAddress} 数据时出错:`, error.message);
            return null;
        }
    }

    async processUserContractData(userContractAddress, contractData, stats) {
        try {
            if (contractData) {
                const userAddress = contractData.user_address;
                stats.users.add(userAddress);

                // 初始化用户统计
                if (!stats.userPurchases.has(userAddress)) {
                    stats.userPurchases.set(userAddress, {
                        address: userAddress,
                        contractAddress: userContractAddress,
                        owner: userAddress, // 保存owner信息
                        totalPurchased: 0,
                        totalPaid: 0,
                        purchaseCount: 0,
                        rounds: new Set(),
                        purchases: [],
                        refunds: []
                    });
                }

                const userStats = stats.userPurchases.get(userAddress);
                userStats.totalPurchased = this.safeBigIntToNumber(contractData.total_purchased);
                userStats.totalPaid = this.safeBigIntToNumber(contractData.total_paid);
                userStats.purchaseCount = this.safeBigIntToNumber(contractData.purchase_id_counter);

                stats.totalTokens += userStats.totalPurchased;
                stats.totalAmountTON += userStats.totalPaid; // 需要区分TON和USDT
                stats.purchaseCount += userStats.purchaseCount;

                // 获取购买记录
                if (contractData.all_records) {
                    for (const [id, record] of Object.entries(contractData.all_records)) {
                        userStats.purchases.push(record);
                        userStats.rounds.add(record.round_number);
                        
                        // 按轮次统计
                        if (!stats.rounds.has(record.round_number)) {
                            stats.rounds.set(record.round_number, {
                                round: record.round_number,
                                users: new Set(),
                                totalAmount: 0,
                                totalTokens: 0,
                                purchaseCount: 0
                            });
                        }
                        
                        const roundStats = stats.rounds.get(record.round_number);
                        roundStats.users.add(userAddress);
                        roundStats.totalAmount += this.safeBigIntToNumber(record.amount);
                        roundStats.totalTokens += this.safeBigIntToNumber(record.tokens);
                        roundStats.purchaseCount += 1;
                    }
                }

                console.log(`用户 ${userAddress}: ${userStats.purchaseCount} 笔购买, ${userStats.totalPurchased} tokens`);
            }
        } catch (error) {
            console.error(`处理用户合约 ${userContractAddress} 数据时出错:`, error.message);
        }
    }

    async processUserDataFromCache(userContractAddress, cachedData, stats) {
        // 直接使用缓存的数据处理统计
        await this.processUserContractData(userContractAddress, cachedData, stats);
    }

    async collectMainContractStats(stats) {
        try {
            const mainContractData = await this.contractReader.getMainContractData(this.AUCTION_CONTRACT);
            
            if (mainContractData) {
                stats.mainContract = {
                    totalRaised: this.safeBigIntToNumber(mainContractData.total_raised),
                    totalRaisedUSDT: this.safeBigIntToNumber(mainContractData.total_raised_usdt),
                    totalTokensSold: this.safeBigIntToNumber(mainContractData.total_tokens_sold),
                    purchaseCount: this.safeBigIntToNumber(mainContractData.purchase_count),
                    currentRound: this.safeBigIntToNumber(mainContractData.current_round),
                    currentPrice: this.safeBigIntToNumber(mainContractData.current_price),
                    auctionStatus: this.safeBigIntToNumber(mainContractData.auction_status),
                    remainingTokens: this.safeBigIntToNumber(mainContractData.remaining_tokens)
                };

                console.log(`主合约状态: ${stats.mainContract.totalTokensSold} tokens售出, ${stats.mainContract.totalRaised} TON募集`);
            }
        } catch (error) {
            console.error('获取主合约数据时出错:', error.message);
        }
    }

    processStats(stats) {
        // 转换 Set 为数组以便序列化
        stats.uniqueUsers = Array.from(stats.users);
        stats.totalUsers = stats.users.size;
        
        // 处理轮次统计
        const roundsArray = Array.from(stats.rounds.entries()).map(([round, data]) => ({
            round,
            uniqueUsers: data.users.size,
            totalAmount: data.totalAmount,
            totalTokens: data.totalTokens,
            purchaseCount: data.purchaseCount
        }));
        
        stats.roundsStats = roundsArray.sort((a, b) => a.round - b.round);
        
        // 用户购买统计数组
        stats.userPurchasesArray = Array.from(stats.userPurchases.values()).map(user => ({
            ...user,
            rounds: Array.from(user.rounds)
        }));

        console.log(`\n=== 统计汇总 ===`);
        console.log(`总用户数: ${stats.totalUsers}`);
        console.log(`总购买次数: ${stats.purchaseCount}`);
        console.log(`总代币数量: ${stats.totalTokens}`);
        console.log(`总轮次: ${roundsArray.length}`);
        console.log(`用户合约数: ${stats.userContracts.length}`);
    }

    // 安全地将BigInt转换为Number
    safeBigIntToNumber(value) {
        if (typeof value === 'bigint') {
            return Number(value);
        }
        return Number(value || 0);
    }

    async generateReport(stats) {
        const report = {
            summary: {
                timestamp: stats.timestamp,
                totalUsers: stats.totalUsers,
                totalPurchases: stats.purchaseCount,
                totalTokens: stats.totalTokens,
                totalRounds: stats.roundsStats.length,
                mainContract: stats.mainContract
            },
            rounds: stats.roundsStats,
            topUsers: stats.userPurchasesArray
                .sort((a, b) => b.totalPurchased - a.totalPurchased)
                .slice(0, 20),
            userContracts: stats.userContracts
        };

        return report;
    }

    async loadOrFetchUserContracts() {
        let cachedContracts = await this.loadUserContractsCache();
        let newContracts = [];

        // 获取最新的合约创建记录
        console.log('检查新的用户购买合约...');
        const userPurchaseMessages = await this.client.getAllMessages(
            this.AUCTION_CONTRACT, 
            this.OP_CREATE_USER_PURCHASE
        );

        // 将消息转换为合约记录格式
        const allContracts = userPurchaseMessages.map(message => ({
            address: message.destination,
            txHash: message.hash,
            timestamp: parseInt(message.created_at),
            createdAt: new Date(parseInt(message.created_at) * 1000).toISOString()
        }));

        // 找出新的合约(不在缓存中的)
        const cachedAddresses = new Set(cachedContracts.map(c => c.address));
        newContracts = allContracts.filter(c => !cachedAddresses.has(c.address));

        if (newContracts.length > 0) {
            console.log(`发现 ${newContracts.length} 个新的用户合约`);
            // 合并新旧合约
            const updatedContracts = [...cachedContracts, ...newContracts];
            
            // 按时间戳排序
            updatedContracts.sort((a, b) => a.timestamp - b.timestamp);
            
            // 保存到缓存
            await this.saveUserContractsCache(updatedContracts);
            
            return updatedContracts;
        } else {
            console.log('没有发现新的用户合约, 使用缓存数据');
            return cachedContracts.length > 0 ? cachedContracts : allContracts;
        }
    }

    async loadUserContractsCache() {
        try {
            const cacheData = await fs.readFile(this.USER_CONTRACTS_CACHE_FILE, 'utf-8');
            const cache = JSON.parse(cacheData);
            
            console.log(`从缓存加载了 ${cache.contracts?.length || 0} 个用户合约`);
            console.log(`缓存更新时间: ${cache.lastUpdated || '未知'}`);
            
            return cache.contracts || [];
        } catch (error) {
            if (error.code === 'ENOENT') {
                console.log('缓存文件不存在, 将创建新的缓存');
            } else {
                console.warn('加载缓存失败:', error.message);
            }
            return [];
        }
    }

    async saveUserContractsCache(contracts) {
        try {
            const cache = {
                lastUpdated: new Date().toISOString(),
                totalContracts: contracts.length,
                contracts: contracts
            };
            
            await fs.writeFile(
                this.USER_CONTRACTS_CACHE_FILE, 
                JSON.stringify(cache, null, 2),
                'utf-8'
            );
            
            console.log(`缓存已更新: 保存 ${contracts.length} 个用户合约到 ${this.USER_CONTRACTS_CACHE_FILE}`);
        } catch (error) {
            console.error('保存缓存失败:', error.message);
        }
    }

    async clearCache() {
        try {
            await fs.unlink(this.USER_CONTRACTS_CACHE_FILE);
            console.log('用户合约地址缓存已清除');
        } catch (error) {
            if (error.code !== 'ENOENT') {
                console.error('清除用户合约地址缓存失败:', error.message);
            }
        }
        
        try {
            await fs.unlink(this.USER_DATA_CACHE_FILE);
            console.log('用户合约数据缓存已清除');
        } catch (error) {
            if (error.code !== 'ENOENT') {
                console.error('清除用户合约数据缓存失败:', error.message);
            }
        }
    }

    async loadCheckpoint() {
        try {
            const checkpointData = await fs.readFile(this.CHECKPOINT_FILE, 'utf-8');
            const checkpoint = JSON.parse(checkpointData);
            
            console.log(`从checkpoint加载: ${checkpoint.processedContracts?.length || 0} 个已处理合约`);
            console.log(`checkpoint时间: ${checkpoint.timestamp || '未知'}`);
            
            return checkpoint;
        } catch (error) {
            if (error.code === 'ENOENT') {
                console.log('没有找到checkpoint文件，从头开始');
            } else {
                console.warn('加载checkpoint失败:', error.message);
            }
            return {};
        }
    }

    async saveCheckpoint(checkpoint) {
        try {
            await fs.writeFile(
                this.CHECKPOINT_FILE, 
                JSON.stringify(checkpoint, null, 2),
                'utf-8'
            );
        } catch (error) {
            console.error('保存checkpoint失败:', error.message);
        }
    }

    async clearCheckpoint() {
        try {
            await fs.unlink(this.CHECKPOINT_FILE);
        } catch (error) {
            if (error.code !== 'ENOENT') {
                console.error('清除checkpoint失败:', error.message);
            }
        }
    }

    serializeStatsForCheckpoint(stats) {
        // 只保存必要的数据，避免文件过大
        const serializedStats = {
            totalUsers: stats.users.size,
            purchaseCount: stats.purchaseCount,
            totalTokens: stats.totalTokens,
            totalAmountTON: stats.totalAmountTON,
            totalAmountUSDT: stats.totalAmountUSDT,
            // 转换Map和Set为可序列化的对象
            users: Array.from(stats.users),
            userPurchases: Array.from(stats.userPurchases.entries()).map(([key, value]) => [
                key, 
                {
                    ...value,
                    rounds: Array.from(value.rounds || [])
                }
            ]),
            rounds: Array.from(stats.rounds.entries()).map(([key, value]) => [
                key,
                {
                    ...value,
                    users: Array.from(value.users || [])
                }
            ])
        };
        
        return serializedStats;
    }

    mergeCheckpointStats(stats, checkpointStats) {
        if (!checkpointStats) return;

        try {
            // 恢复基本数据
            stats.purchaseCount = checkpointStats.purchaseCount || 0;
            stats.totalTokens = checkpointStats.totalTokens || 0;
            stats.totalAmountTON = checkpointStats.totalAmountTON || 0;
            stats.totalAmountUSDT = checkpointStats.totalAmountUSDT || 0;

            // 恢复用户数据
            if (checkpointStats.users) {
                checkpointStats.users.forEach(user => stats.users.add(user));
            }

            // 恢复用户购买数据
            if (checkpointStats.userPurchases) {
                checkpointStats.userPurchases.forEach(([key, value]) => {
                    const userData = {
                        ...value,
                        rounds: new Set(value.rounds || [])
                    };
                    stats.userPurchases.set(key, userData);
                });
            }

            // 恢复轮次数据
            if (checkpointStats.rounds) {
                checkpointStats.rounds.forEach(([key, value]) => {
                    const roundData = {
                        ...value,
                        users: new Set(value.users || [])
                    };
                    stats.rounds.set(key, roundData);
                });
            }

            console.log(`已恢复checkpoint数据: ${stats.users.size} 用户, ${stats.purchaseCount} 笔购买`);
        } catch (error) {
            console.error('恢复checkpoint数据时出错:', error.message);
        }
    }

    async loadUserDataCache() {
        try {
            const cacheData = await fs.readFile(this.USER_DATA_CACHE_FILE, 'utf-8');
            const cache = JSON.parse(cacheData);
            
            console.log(`从缓存加载了 ${Object.keys(cache.data || {}).length} 个用户合约数据`);
            console.log(`数据缓存更新时间: ${cache.lastUpdated || '未知'}`);
            
            return cache.data || {};
        } catch (error) {
            if (error.code === 'ENOENT') {
                console.log('用户数据缓存文件不存在, 将创建新的缓存');
            } else {
                console.warn('加载用户数据缓存失败:', error.message);
            }
            return {};
        }
    }

    async saveUserDataCache(userDataCache) {
        try {
            const cache = {
                lastUpdated: new Date().toISOString(),
                totalContracts: Object.keys(userDataCache).length,
                data: userDataCache
            };
            
            await fs.writeFile(
                this.USER_DATA_CACHE_FILE, 
                JSON.stringify(cache, (key, value) => {
                    // 处理BigInt序列化
                    if (typeof value === 'bigint') {
                        return value.toString();
                    }
                    return value;
                }, 2),
                'utf-8'
            );
            
            // 不要频繁打印，只在需要时打印
        } catch (error) {
            console.error('保存用户数据缓存失败:', error.message);
        }
    }

    // 从地址映射缓存中加载合约-用户地址映射关系
    async buildContractUserMapping(userContracts) {
        console.log('🔍 加载合约-用户地址映射...');

        try {
            // 尝试从地址映射缓存加载
            const existingMapping = await this.addressMapper.loadAddressMappingCache();
            const mapping = new Map(Object.entries(existingMapping));

            if (mapping.size > 0) {
                console.log(`✅ 从地址映射缓存加载了 ${mapping.size} 个地址映射`);
                return mapping;
            } else {
                console.log('⚠️ 地址映射缓存为空，建议先运行地址映射构建');
                console.log('运行命令: npm run build-address-mapping');
                return new Map();
            }
        } catch (error) {
            console.warn('加载地址映射失败:', error.message);
            console.log('建议先运行地址映射构建: npm run build-address-mapping');
            return new Map();
        }
    }

    // 生成综合报告（包括错误和验证报告）
    async generateComprehensiveReport(stats) {
        try {
            const errorLogger = this.contractReader.getErrorLogger();
            const dataValidator = this.contractReader.getDataValidator();

            // 生成错误报告
            const errorReport = errorLogger.generateErrorReport();

            // 验证统计数据
            const validationResult = await dataValidator.validateStatistics(stats);

            // 交叉验证用户数据与主合约数据
            let crossValidationResult = null;
            if (stats.userPurchasesArray && stats.mainContract) {
                crossValidationResult = await dataValidator.crossValidateData(
                    stats.userPurchasesArray,
                    stats.mainContract
                );
            }

            // 生成验证报告
            const validationReports = [validationResult];
            if (crossValidationResult) {
                validationReports.push(crossValidationResult);
            }
            const validationReport = dataValidator.generateValidationReport(validationReports);

            // 保存综合报告
            const comprehensiveReport = {
                timestamp: new Date().toISOString(),
                summary: {
                    dataCollection: {
                        totalContracts: stats.userContracts?.length || 0,
                        successfulContracts: stats.userPurchasesArray?.length || 0,
                        failedContracts: (stats.userContracts?.length || 0) - (stats.userPurchasesArray?.length || 0)
                    },
                    errorSummary: errorReport.summary,
                    validationSummary: validationReport.summary
                },
                errorReport,
                validationReport,
                recommendations: this.generateOverallRecommendations(errorReport, validationReport)
            };

            const reportFile = `comprehensive-report-${new Date().toISOString().split('T')[0]}.json`;
            await fs.writeFile(reportFile, JSON.stringify(comprehensiveReport, null, 2));
            console.log(`📋 综合报告已保存到: ${reportFile}`);

            // 打印关键信息
            this.printReportSummary(comprehensiveReport);

            return comprehensiveReport;
        } catch (error) {
            console.error('生成综合报告时出错:', error.message);
        }
    }

    // 生成总体建议
    generateOverallRecommendations(errorReport, validationReport) {
        const recommendations = [];

        // 合并错误和验证建议
        recommendations.push(...(errorReport.recommendations || []));
        recommendations.push(...(validationReport.recommendations || []));

        // 添加基于整体情况的建议
        const errorStats = errorReport.summary;
        const validationStats = validationReport.summary;

        if (errorStats.criticalErrors > 0 || validationStats.criticalErrors > 0) {
            recommendations.push({
                priority: 'CRITICAL',
                issue: 'Critical issues detected in data collection or validation',
                suggestion: 'Immediate review required. Consider re-running data collection with enhanced error handling.'
            });
        }

        if (errorStats.retryableErrors / errorStats.totalErrors > 0.8) {
            recommendations.push({
                priority: 'MEDIUM',
                issue: 'High rate of retryable errors suggests network or API issues',
                suggestion: 'Consider running during off-peak hours or implementing more aggressive retry policies.'
            });
        }

        return recommendations;
    }

    // 打印报告摘要
    printReportSummary(report) {
        console.log('\n📊 数据收集和验证摘要');
        console.log('================================');

        const { dataCollection, errorSummary, validationSummary } = report.summary;

        console.log(`📈 数据收集:`);
        console.log(`  - 总合约数: ${dataCollection.totalContracts}`);
        console.log(`  - 成功处理: ${dataCollection.successfulContracts}`);
        console.log(`  - 处理失败: ${dataCollection.failedContracts}`);
        console.log(`  - 成功率: ${((dataCollection.successfulContracts / dataCollection.totalContracts) * 100).toFixed(2)}%`);

        console.log(`\n🚨 错误统计:`);
        console.log(`  - 总错误数: ${errorSummary.totalErrors}`);
        console.log(`  - 关键错误: ${errorSummary.criticalErrors}`);
        console.log(`  - 可重试错误: ${errorSummary.retryableErrors}`);

        console.log(`\n✅ 数据验证:`);
        console.log(`  - 总验证数: ${validationSummary.totalValidations}`);
        console.log(`  - 验证通过: ${validationSummary.passedValidations}`);
        console.log(`  - 验证失败: ${validationSummary.failedValidations}`);
        console.log(`  - 关键错误: ${validationSummary.criticalErrors || 0}`);

        if (report.recommendations && report.recommendations.length > 0) {
            console.log(`\n💡 建议 (${report.recommendations.length} 条):`);
            report.recommendations.slice(0, 3).forEach((rec, index) => {
                console.log(`  ${index + 1}. [${rec.priority}] ${rec.issue}`);
                console.log(`     ${rec.suggestion}`);
            });
            if (report.recommendations.length > 3) {
                console.log(`     ... 更多建议请查看完整报告`);
            }
        }
    }
}