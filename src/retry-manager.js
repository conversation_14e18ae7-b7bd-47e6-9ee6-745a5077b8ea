/**
 * 重试管理器
 * 专门处理关键数据获取的重试逻辑
 */
export class RetryManager {
    constructor(errorLogger) {
        this.errorLogger = errorLogger;
        this.defaultMaxRetries = 3;
        this.criticalMaxRetries = 5; // 关键数据获取的最大重试次数
        this.baseDelay = 1000; // 基础延迟1秒
        this.maxDelay = 30000; // 最大延迟30秒
    }

    /**
     * 执行带重试的操作
     * @param {Function} operation - 要执行的操作
     * @param {Object} options - 重试选项
     */
    async executeWithRetry(operation, options = {}) {
        const {
            maxRetries = this.defaultMaxRetries,
            operationName = 'unknown',
            context = {},
            isCritical = false,
            customRetryCondition = null
        } = options;

        const actualMaxRetries = isCritical ? this.criticalMaxRetries : maxRetries;
        let lastError = null;

        for (let attempt = 1; attempt <= actualMaxRetries; attempt++) {
            try {
                const result = await operation();
                
                // 如果之前有重试，记录成功恢复
                if (attempt > 1) {
                    await this.errorLogger.logRecovery(operationName, `Succeeded on attempt ${attempt}`, lastError, {
                        ...context,
                        totalAttempts: attempt,
                        isCritical
                    });
                }
                
                return result;
            } catch (error) {
                lastError = error;
                
                // 记录重试尝试
                await this.errorLogger.logRetry(operationName, attempt, actualMaxRetries, error.message, {
                    ...context,
                    isCritical,
                    errorType: error.constructor.name
                });

                // 检查是否应该重试
                const shouldRetry = this.shouldRetry(error, attempt, actualMaxRetries, customRetryCondition);
                
                if (!shouldRetry) {
                    break;
                }

                // 计算延迟时间
                const delay = this.calculateDelay(attempt, isCritical);
                await this.delay(delay);
            }
        }

        // 所有重试都失败了
        throw lastError;
    }

    /**
     * 专门用于关键数据获取的重试
     */
    async executeCriticalOperation(operation, operationName, context = {}) {
        return this.executeWithRetry(operation, {
            operationName,
            context,
            isCritical: true,
            maxRetries: this.criticalMaxRetries,
            customRetryCondition: (error) => {
                // 关键数据获取的特殊重试条件
                return this.isCriticalRetryableError(error);
            }
        });
    }

    /**
     * 判断是否应该重试
     */
    shouldRetry(error, attempt, maxRetries, customRetryCondition) {
        // 已达到最大重试次数
        if (attempt >= maxRetries) {
            return false;
        }

        // 使用自定义重试条件
        if (customRetryCondition) {
            return customRetryCondition(error);
        }

        // 默认重试条件
        return this.isRetryableError(error);
    }

    /**
     * 判断错误是否可重试
     */
    isRetryableError(error) {
        // API限流
        if (error.response?.status === 429) return true;
        
        // 网络错误
        if (error.code === 'ECONNRESET' || error.code === 'ETIMEDOUT') return true;
        
        // 服务器错误
        if (error.response?.status >= 500) return true;
        
        // 超时错误
        if (error.message?.includes('timeout')) return true;
        
        // TON网络相关错误
        if (error.message?.includes('network') || error.message?.includes('connection')) return true;
        
        return false;
    }

    /**
     * 关键数据获取的特殊重试条件
     */
    isCriticalRetryableError(error) {
        // 对于关键数据，更宽松的重试条件
        if (this.isRetryableError(error)) return true;
        
        // 合约调用失败（可能是临时的）
        if (error.message?.includes('contract') || error.message?.includes('method')) return true;
        
        // 数据解析错误（可能是网络传输问题）
        if (error.message?.includes('parse') || error.message?.includes('decode')) return true;
        
        return false;
    }

    /**
     * 计算延迟时间
     */
    calculateDelay(attempt, isCritical = false) {
        // 指数退避算法
        let delay = this.baseDelay * Math.pow(2, attempt - 1);
        
        // 关键操作使用更长的延迟
        if (isCritical) {
            delay *= 1.5;
        }
        
        // 添加随机抖动，避免雷群效应
        const jitter = Math.random() * 1000;
        delay += jitter;
        
        // 限制最大延迟
        return Math.min(delay, this.maxDelay);
    }

    /**
     * 延迟函数
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 批量重试操作
     * 用于处理多个相关操作的重试
     */
    async executeBatchWithRetry(operations, options = {}) {
        const {
            operationName = 'batch_operation',
            context = {},
            isCritical = false,
            failFast = false // 是否在第一个失败时立即停止
        } = options;

        const results = [];
        const errors = [];

        for (let i = 0; i < operations.length; i++) {
            try {
                const result = await this.executeWithRetry(operations[i], {
                    operationName: `${operationName}_${i}`,
                    context: { ...context, batchIndex: i },
                    isCritical
                });
                results.push(result);
            } catch (error) {
                errors.push({ index: i, error });
                
                if (failFast) {
                    throw error;
                }
                
                // 对于非fail-fast模式，记录错误但继续
                results.push(null);
            }
        }

        // 如果有错误，记录批量操作的错误统计
        if (errors.length > 0) {
            await this.errorLogger.logError({
                type: 'BATCH_OPERATION_ERRORS',
                operation: operationName,
                message: `Batch operation completed with ${errors.length}/${operations.length} failures`,
                severity: errors.length === operations.length ? 'critical' : 'warning',
                context: {
                    ...context,
                    totalOperations: operations.length,
                    failedOperations: errors.length,
                    errors: errors.map(e => ({ index: e.index, message: e.error.message }))
                },
                retryable: false
            });
        }

        return {
            results,
            errors,
            successCount: results.filter(r => r !== null).length,
            errorCount: errors.length
        };
    }

    /**
     * 智能重试：根据错误类型和历史记录动态调整重试策略
     */
    async executeSmartRetry(operation, options = {}) {
        const {
            operationName = 'smart_operation',
            context = {},
            isCritical = false
        } = options;

        // 获取该操作的历史错误统计
        const errorStats = this.errorLogger.getErrorStats();
        const operationErrors = errorStats.errorsByType[`CONTRACT_ERROR_${operationName}`] || 0;

        // 根据历史错误率动态调整重试次数
        let maxRetries = isCritical ? this.criticalMaxRetries : this.defaultMaxRetries;
        
        if (operationErrors > 10) {
            // 如果该操作历史错误较多，增加重试次数
            maxRetries += 2;
        }

        return this.executeWithRetry(operation, {
            operationName,
            context,
            isCritical,
            maxRetries,
            customRetryCondition: (error) => {
                // 智能重试条件：考虑错误类型和频率
                if (operationErrors > 20 && this.isCriticalRetryableError(error)) {
                    return true; // 高错误率时更积极重试
                }
                return this.isRetryableError(error);
            }
        });
    }
}
