{"name": "onton-stats", "version": "1.0.0", "description": "Statistics collector for Onion Auction TON contracts", "main": "src/index.js", "type": "module", "scripts": {"build": "tsc", "start": "node --loader ts-node/esm src/index.js", "stats": "node --loader ts-node/esm src/stats.js", "export-csv": "node --loader ts-node/esm src/index.js --export-csv", "csv": "node --loader ts-node/esm src/csv-exporter.js", "clear-cache": "node --loader ts-node/esm src/index.js --clear-cache", "clear-checkpoint": "node --loader ts-node/esm src/index.js --clear-checkpoint", "clear-all": "node --loader ts-node/esm src/index.js --clear-all", "help": "node --loader ts-node/esm src/index.js --help", "build-address-mapping": "node --loader ts-node/esm src/address-mapper.js --build", "validate-address-mapping": "node --loader ts-node/esm src/address-mapper.js --validate", "address-mapping-stats": "node --loader ts-node/esm src/address-mapper.js --stats", "clear-address-mapping": "node --loader ts-node/esm src/address-mapper.js --clear", "test-improvements": "node test-improvements.js", "fix-refunds": "node --loader ts-node/esm src/refund-fixer.js", "fix-refund-contract": "node --loader ts-node/esm src/refund-fixer.js --contract", "validate-refunds": "node --loader ts-node/esm src/refund-fixer.js --validate", "test-refund-stats": "node --loader ts-node/esm test-refund-stats.js", "regenerate-stats": "node --loader ts-node/esm regenerate-stats-with-refunds.js", "validate-stats": "node --loader ts-node/esm regenerate-stats-with-refunds.js --validate", "quick-test": "node --loader ts-node/esm quick-test-refunds.js"}, "dependencies": {"@ton/core": "^0.61.0", "@ton/ton": "^15.3.1", "axios": "^1.6.0", "ton": "^13.9.0", "ton-core": "^0.53.0", "ton-crypto": "^3.2.0"}, "devDependencies": {"@types/node": "^20.0.0", "ts-node": "^10.9.2", "typescript": "^5.9.2"}, "author": "", "license": "MIT"}