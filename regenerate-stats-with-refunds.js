#!/usr/bin/env node

/**
 * 重新生成包含退款数据的统计报告
 * 使用现有的用户数据缓存，避免重新获取数据
 */

import fs from 'fs/promises';
import { StatsCollector } from './src/stats-collector.js';
import { CSVExporter } from './src/csv-exporter.js';

async function regenerateStatsWithRefunds() {
    console.log('🔄 重新生成包含退款数据的统计报告...');
    
    try {
        // 1. 加载现有的用户数据缓存
        console.log('1. 加载用户数据缓存...');
        const cacheData = await fs.readFile('user_data_cache.json', 'utf-8');
        const cache = JSON.parse(cacheData);
        
        const contractAddresses = Object.keys(cache.data || {});
        console.log(`找到 ${contractAddresses.length} 个用户合约`);
        
        // 2. 创建统计收集器
        const collector = new StatsCollector();
        
        // 3. 初始化统计数据结构
        const stats = {
            timestamp: new Date().toISOString(),
            totalUsers: 0,
            totalTokens: 0,
            totalAmountTON: 0,
            totalAmountUSDT: 0,
            purchaseCount: 0,
            totalRefunds: 0, // 添加退款统计
            userPurchases: new Map(),
            userPurchasesArray: [],
            roundsStats: [],
            userContracts: contractAddresses,
            mainContract: null
        };
        
        // 4. 处理每个用户合约的数据
        console.log('2. 处理用户合约数据...');
        let processedCount = 0;
        let usersWithRefunds = 0;
        let totalRefundAmount = 0;
        
        for (const contractAddress of contractAddresses) {
            const contractData = cache.data[contractAddress];
            const userAddress = contractData.owner || contractAddress;
            
            // 初始化用户统计
            if (!stats.userPurchases.has(userAddress)) {
                stats.userPurchases.set(userAddress, {
                    address: userAddress,
                    contractAddress: contractAddress,
                    owner: userAddress,
                    totalPurchased: 0,
                    totalPaid: 0,
                    purchaseCount: 0,
                    refundCount: 0,
                    totalRefunded: 0,
                    rounds: new Set(),
                    purchases: [],
                    refunds: []
                });
            }
            
            const userStats = stats.userPurchases.get(userAddress);
            userStats.totalPurchased = collector.safeBigIntToNumber(contractData.total_purchased);
            userStats.totalPaid = collector.safeBigIntToNumber(contractData.total_paid);
            userStats.purchaseCount = collector.safeBigIntToNumber(contractData.purchase_id_counter);
            
            // 处理退款数据
            userStats.refundCount = collector.safeBigIntToNumber(contractData.refund_count || 0);
            userStats.totalRefunded = collector.safeBigIntToNumber(contractData.total_refunded || 0);
            
            // 累计统计
            stats.totalTokens += userStats.totalPurchased;
            stats.totalAmountTON += userStats.totalPaid;
            stats.purchaseCount += userStats.purchaseCount;
            stats.totalRefunds += userStats.refundCount;
            
            if (userStats.refundCount > 0) {
                usersWithRefunds++;
                totalRefundAmount += userStats.totalRefunded;
            }
            
            processedCount++;
            if (processedCount % 100 === 0) {
                console.log(`  已处理 ${processedCount}/${contractAddresses.length} 个合约`);
            }
        }
        
        // 5. 转换为数组并计算最终统计
        stats.userPurchasesArray = Array.from(stats.userPurchases.values());
        stats.totalUsers = stats.userPurchasesArray.length;
        
        console.log('\n📊 统计结果:');
        console.log(`总用户数: ${stats.totalUsers}`);
        console.log(`总购买次数: ${stats.purchaseCount}`);
        console.log(`总代币数量: ${stats.totalTokens}`);
        console.log(`总退款次数: ${stats.totalRefunds}`);
        console.log(`有退款的用户: ${usersWithRefunds}`);
        console.log(`总退款金额: ${(totalRefundAmount / 1e9).toFixed(4)} TON`);
        
        // 6. 生成报告
        console.log('\n3. 生成统计报告...');
        const report = await collector.generateReport(stats);
        
        // 7. 保存报告文件
        const timestamp = new Date().toISOString().split('T')[0];
        const reportFile = `stats-report-with-refunds-${timestamp}.json`;
        
        await fs.writeFile(reportFile, JSON.stringify(report, null, 2));
        console.log(`✅ 统计报告已保存: ${reportFile}`);
        
        // 8. 生成CSV报告
        console.log('\n4. 生成CSV报告...');
        const exporter = new CSVExporter();
        const csvReports = await exporter.exportAllReports(stats);
        
        console.log('✅ CSV报告已生成:');
        csvReports.forEach(file => console.log(`  - ${file}`));
        
        // 9. 显示退款统计摘要
        console.log('\n📋 退款数据摘要:');
        if (report.summary.refundSummary) {
            const refundSummary = report.summary.refundSummary;
            console.log(`  退款率: ${refundSummary.refundRate}%`);
            console.log(`  平均退款金额: ${refundSummary.averageRefundAmount} TON`);
            console.log(`  最大退款金额: ${(refundSummary.maxRefundAmount / 1e9).toFixed(4)} TON`);
            console.log(`  最大退款用户: ${refundSummary.maxRefundUser || '无'}`);
        }
        
        if (report.refundDetails && report.refundDetails.length > 0) {
            console.log(`\n前5名退款用户:`);
            report.refundDetails.slice(0, 5).forEach((user, index) => {
                console.log(`  ${index + 1}. ${user.userAddress}: ${user.refundCount} 笔, ${(user.totalRefunded / 1e9).toFixed(4)} TON`);
            });
        }
        
        console.log('\n🎉 重新生成完成！');
        console.log('现在统计报告包含完整的退款数据。');
        
        return {
            reportFile,
            csvReports,
            stats: report.summary
        };
        
    } catch (error) {
        console.error('重新生成统计报告时出错:', error);
        throw error;
    }
}

// 验证退款数据完整性
async function validateRefundData() {
    console.log('\n🔍 验证退款数据完整性...');
    
    try {
        // 检查最新生成的报告
        const files = await fs.readdir('.');
        const reportFiles = files.filter(f => f.startsWith('stats-report-with-refunds-') && f.endsWith('.json'));
        
        if (reportFiles.length === 0) {
            console.log('❌ 未找到包含退款数据的报告文件');
            return false;
        }
        
        const latestReport = reportFiles.sort().pop();
        console.log(`检查报告文件: ${latestReport}`);
        
        const reportData = await fs.readFile(latestReport, 'utf-8');
        const report = JSON.parse(reportData);
        
        // 验证退款数据
        const hasRefundSummary = report.summary && report.summary.refundSummary;
        const hasRefundDetails = report.refundDetails && Array.isArray(report.refundDetails);
        const hasTotalRefunds = typeof report.summary.totalRefunds === 'number';
        
        console.log(`✅ 退款摘要: ${hasRefundSummary ? '存在' : '缺失'}`);
        console.log(`✅ 退款详情: ${hasRefundDetails ? '存在' : '缺失'} (${report.refundDetails?.length || 0} 条)`);
        console.log(`✅ 总退款次数: ${hasTotalRefunds ? '存在' : '缺失'} (${report.summary.totalRefunds || 0})`);
        
        return hasRefundSummary && hasRefundDetails && hasTotalRefunds;
        
    } catch (error) {
        console.error('验证退款数据时出错:', error);
        return false;
    }
}

// 主函数
async function main() {
    const args = process.argv.slice(2);
    
    if (args.includes('--validate')) {
        const isValid = await validateRefundData();
        process.exit(isValid ? 0 : 1);
    } else {
        const result = await regenerateStatsWithRefunds();
        
        // 自动验证
        const isValid = await validateRefundData();
        if (!isValid) {
            console.warn('⚠️  验证发现退款数据可能不完整');
        }
    }
}

if (import.meta.url === `file://${process.argv[1]}`) {
    main().catch(console.error);
}
