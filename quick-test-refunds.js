#!/usr/bin/env node

/**
 * 快速测试退款数据修复
 */

import fs from 'fs/promises';

async function quickTestRefunds() {
    console.log('🧪 快速测试退款数据修复...');
    
    try {
        // 1. 检查用户数据缓存
        console.log('\n1. 检查用户数据缓存中的退款数据...');
        const cacheData = await fs.readFile('user_data_cache.json', 'utf-8');
        const cache = JSON.parse(cacheData);
        
        let totalRefundsInCache = 0;
        let totalRefundAmountInCache = 0;
        let usersWithRefundsInCache = 0;
        
        const contractAddresses = Object.keys(cache.data || {});
        console.log(`总合约数: ${contractAddresses.length}`);
        
        for (const [contractAddress, data] of Object.entries(cache.data || {})) {
            const refundCount = parseInt(data.refund_count || '0');
            const refundAmount = parseFloat(data.total_refunded || '0');
            
            if (refundCount > 0) {
                usersWithRefundsInCache++;
                totalRefundsInCache += refundCount;
                totalRefundAmountInCache += refundAmount;
                
                if (usersWithRefundsInCache <= 5) {
                    console.log(`  ${contractAddress.substring(0, 20)}...: ${refundCount} 笔退款, ${(refundAmount / 1e9).toFixed(4)} TON`);
                }
            }
        }
        
        console.log(`\n缓存中的退款统计:`);
        console.log(`  有退款的用户: ${usersWithRefundsInCache}`);
        console.log(`  总退款次数: ${totalRefundsInCache}`);
        console.log(`  总退款金额: ${(totalRefundAmountInCache / 1e9).toFixed(4)} TON`);
        
        if (usersWithRefundsInCache === 0) {
            console.log('\n❌ 缓存中没有退款数据！');
            console.log('建议先运行: npm run fix-refunds');
            return false;
        }
        
        // 2. 测试统计收集器的safeBigIntToNumber方法
        console.log('\n2. 测试数据转换...');
        const { StatsCollector } = await import('./src/stats-collector.js');
        const collector = new StatsCollector();
        
        // 测试几个样本数据
        const sampleContracts = contractAddresses.slice(0, 3);
        for (const contractAddress of sampleContracts) {
            const data = cache.data[contractAddress];
            
            const refundCount = collector.safeBigIntToNumber(data.refund_count || 0);
            const totalRefunded = collector.safeBigIntToNumber(data.total_refunded || 0);
            
            console.log(`  ${contractAddress.substring(0, 20)}...: ${refundCount} 笔退款, ${(totalRefunded / 1e9).toFixed(4)} TON`);
        }
        
        // 3. 检查现有统计文件
        console.log('\n3. 检查现有统计文件...');
        const files = await fs.readdir('.');
        const statsFiles = files.filter(f => f.startsWith('stats-report-') && f.endsWith('.json'));
        
        if (statsFiles.length > 0) {
            const latestFile = statsFiles.sort().pop();
            console.log(`检查最新统计文件: ${latestFile}`);
            
            const statsData = await fs.readFile(latestFile, 'utf-8');
            const stats = JSON.parse(statsData);
            
            const hasTotalRefunds = typeof stats.summary?.totalRefunds === 'number';
            const hasRefundSummary = stats.summary?.refundSummary !== undefined;
            const hasRefundDetails = Array.isArray(stats.refundDetails);
            
            console.log(`  总退款次数: ${hasTotalRefunds ? stats.summary.totalRefunds : '❌ 缺失'}`);
            console.log(`  退款摘要: ${hasRefundSummary ? '✅ 存在' : '❌ 缺失'}`);
            console.log(`  退款详情: ${hasRefundDetails ? `✅ ${stats.refundDetails.length} 条` : '❌ 缺失'}`);
            
            if (!hasTotalRefunds || !hasRefundSummary || !hasRefundDetails) {
                console.log('\n⚠️  现有统计文件缺少退款数据！');
                console.log('建议运行: npm run regenerate-stats');
                return false;
            }
        } else {
            console.log('未找到现有统计文件');
            console.log('建议运行: npm run regenerate-stats');
            return false;
        }
        
        // 4. 检查CSV文件
        console.log('\n4. 检查CSV文件...');
        try {
            const reportsDir = 'reports';
            const reportFiles = await fs.readdir(reportsDir);
            const userStatsFiles = reportFiles.filter(f => f.startsWith('users-stats-') && f.endsWith('.csv'));
            
            if (userStatsFiles.length > 0) {
                const latestCSV = userStatsFiles.sort().pop();
                console.log(`检查最新CSV文件: ${latestCSV}`);
                
                const csvData = await fs.readFile(`${reportsDir}/${latestCSV}`, 'utf-8');
                const lines = csvData.split('\n');
                
                if (lines.length > 0) {
                    const headers = lines[0];
                    const hasRefundFields = headers.includes('退款次数') && headers.includes('总退款金额');
                    console.log(`  包含退款字段: ${hasRefundFields ? '✅' : '❌'}`);
                    
                    if (!hasRefundFields) {
                        console.log('⚠️  CSV文件缺少退款字段！');
                        return false;
                    }
                } else {
                    console.log('❌ CSV文件为空');
                    return false;
                }
            } else {
                console.log('未找到用户统计CSV文件');
                return false;
            }
        } catch (error) {
            console.log('检查CSV文件时出错:', error.message);
            return false;
        }
        
        console.log('\n✅ 退款数据修复验证通过！');
        console.log('所有组件都正确包含了退款数据。');
        return true;
        
    } catch (error) {
        console.error('测试过程中出错:', error);
        return false;
    }
}

// 提供修复建议
function provideFix() {
    console.log('\n🔧 修复建议:');
    console.log('1. 如果缓存中没有退款数据:');
    console.log('   npm run fix-refunds');
    console.log('');
    console.log('2. 如果统计文件缺少退款数据:');
    console.log('   npm run regenerate-stats');
    console.log('');
    console.log('3. 如果需要重新收集所有数据:');
    console.log('   npm start');
    console.log('');
    console.log('4. 验证修复结果:');
    console.log('   npm run validate-stats');
}

// 主函数
async function main() {
    const success = await quickTestRefunds();
    
    if (!success) {
        provideFix();
        process.exit(1);
    }
}

if (import.meta.url === `file://${process.argv[1]}`) {
    main().catch(console.error);
}
