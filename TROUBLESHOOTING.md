# 故障排除指南

## 常见问题和解决方案

### 1. BigInt混合运算错误

**错误信息**: `Cannot mix BigInt and other types, use explicit conversions`

**原因**: TON合约的get方法返回BigInt类型数据，但JavaScript进行数学运算时不能直接混合BigInt和普通数字。

**解决方案**: 已在代码中添加`safeBigIntToNumber()`函数，自动处理所有BigInt到Number的转换。

**修复位置**:
- `src/stats-collector.js` - 统计数据处理
- `src/contract-reader.js` - 合约数据解析

### 2. API 429限流错误

**错误信息**: `Request failed with status code 429`

**原因**: 请求频率过高，触发TonCenter API的速率限制。

**解决方案**: 
- 智能限流：动态调整请求间隔(200ms-800ms)
- 指数退避重试：最多重试5次
- 批量休息：每100个请求暂停2秒

**使用建议**: 遇到429错误时直接重新运行`npm start`，程序会自动从断点继续。

### 3. 网络中断或超时

**错误信息**: `ECONNRESET`, `ETIMEDOUT`, 5xx错误

**解决方案**: 
- 自动重试机制
- 断点续传功能
- 进度保存到checkpoint

**操作**: 网络恢复后直接运行`npm start`继续。

### 4. 数据丢失问题

**解决方案**:
- 自动缓存用户合约地址到`user_contracts_cache.json`
- 每10个合约自动保存checkpoint到`checkpoint_data.json`
- 支持断点续传，不会重复处理已完成的合约

### 5. 内存不足

**症状**: 进程被杀死或内存溢出

**解决方案**:
- checkpoint机制限制内存使用
- 流式处理，不在内存中保存所有数据
- 定期清理临时数据

## 调试命令

```bash
# 查看帮助
npm run help

# 清除所有缓存重新开始
npm run clear-all
npm start

# 只清除checkpoint继续处理
npm run clear-checkpoint
npm start

# 查看最新状态
npm run stats
```

## 日志分析

- `处理合约 X/Y: 地址` - 正常处理进度
- `Rate limited...waiting Xs` - 遇到限流，等待重试
- `从checkpoint恢复` - 成功加载断点数据
- `保存checkpoint...` - 自动保存进度
- `✅ 数据收集完成` - 成功完成所有处理

## 性能优化

- **首次运行**: 较慢，需要收集所有历史数据
- **后续运行**: 快速，只处理新增数据
- **断点续传**: 从中断处继续，不重复处理

## 数据验证

收集完成后检查：
- 用户合约缓存文件大小合理
- checkpoint文件已自动删除
- 生成的报告数据完整
- 主合约数据与实际一致