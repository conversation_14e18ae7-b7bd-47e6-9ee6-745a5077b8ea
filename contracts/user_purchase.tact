import "@stdlib/ownable";
import "@stdlib/deploy";

// Error codes for better debugging
const ERROR_UNAUTHORIZED: Int = 62001;
const ERROR_PURCHASE_NOT_FOUND: Int = 62002;
const ERROR_ALREADY_REFUNDED: Int = 62003;

// Op codes for messages
const OP_CREATE_USER_PURCHASE: Int = 0x41734c0c; // 1098164604
const OP_REFUND: Int = 0x31d5b5ac; // 836560172
const OP_PROCESS_REFUND: Int = 0x376e8b12; // 930057106

// Messages with explicit op codes
message(0x41734c0c) CreateUserPurchase {
    user: Address;
    amount: Int as coins;
    tokens: Int as coins;
    currency: Int as uint8; // 0=TON, 1=USDT
    purchase_method: Int as uint8; // 0=direct, 1=signature_verified
    nonce: Int as uint64; // For signature verification purchases
    round_number: Int as uint32; // Round when purchase was made
    usdt_equivalent_amount: Int as coins; // Amount in USDT equivalent units for cap tracking
}

message(0x31d5b5ac) Refund {
    purchase_id: Int as uint32;
}

message(0x376e8b12) ProcessRefund {
    user: Address;
    amount: Int as coins;
    fee: Int as coins;
    currency: Int as uint8; // 0=TON, 1=USDT
    round_number: Int as uint32; // Round when purchase was made
    usdt_equivalent_amount: Int as coins; // Amount in USDT equivalent units for cap tracking
}

// Structs
struct PurchaseRecord {
    id: Int as uint32;
    user: Address;
    amount: Int as coins;
    tokens: Int as coins;
    timestamp: Int as uint64;
    currency: Int as uint8;
    purchase_method: Int as uint8; // 0=direct, 1=signature_verified
    nonce: Int as uint64; // For signature verification purchases
    round_number: Int as uint32; // Round when purchase was made
    usdt_equivalent_amount: Int as coins; // Amount in USDT equivalent units for cap tracking
}

contract UserPurchase with Ownable {
    owner: Address;
    auction_address: Address;
    user_address: Address;
    total_purchased: Int as coins;
    total_paid: Int as coins;
    purchase_history: map<Int, PurchaseRecord>;
    refund_history: map<Int, Int>; // purchase_id -> refund_amount
    purchase_id_counter: Int as uint32;
    participated_rounds: map<Int, Bool>; // round_number -> participated
    
    init(auction_address: Address, user_address: Address) {
        self.owner = user_address; // User owns their purchase contract
        self.auction_address = auction_address;
        self.user_address = user_address;
        self.total_purchased = 0;
        self.total_paid = 0;
        self.purchase_id_counter = 0;
        // No rounds participated initially
    }
    
    
    // Deploy message handler
    receive("Deploy") {
        // Handle deployment - no special logic needed for this contract
    }
    
    // Create new purchase record
    receive(msg: CreateUserPurchase) {
        require(sender() == self.auction_address, "Unauthorized");

        self.purchase_id_counter += 1;
        let new_purchase: PurchaseRecord = PurchaseRecord{
            id: self.purchase_id_counter,
            user: msg.user,
            amount: msg.amount,
            tokens: msg.tokens,
            timestamp: now(),
            currency: msg.currency,
            purchase_method: msg.purchase_method,
            nonce: msg.nonce,
            round_number: msg.round_number,
            usdt_equivalent_amount: msg.usdt_equivalent_amount
        };

        self.purchase_history.set(self.purchase_id_counter, new_purchase);
        self.total_purchased += msg.tokens;
        self.total_paid += msg.amount;

        // Track round participation
        self.participated_rounds.set(msg.round_number, true);

        cashback(sender());
    }
    
    // Request refund
    receive(msg: Refund) {
        require(sender() == self.user_address, "Unauthorized");

        let purchase: PurchaseRecord? = self.purchase_history.get(msg.purchase_id);
        require(purchase != null, "Purchase not found");
        require(self.refund_history.get(msg.purchase_id) == null, "Already refunded");
        
        let purchase_data: PurchaseRecord = purchase!!;
        
        // Mark as refunded
        self.refund_history.set(msg.purchase_id, purchase_data.amount);
        self.total_purchased -= purchase_data.tokens;
        self.total_paid -= purchase_data.amount;
        
        // Send refund request to auction contract
        send(SendParameters{
            to: self.auction_address,
            value: ton("0.05"),
            mode: SendIgnoreErrors,
            bounce: false,
            body: ProcessRefund{
                user: self.user_address,
                amount: purchase_data.amount,
                fee: (purchase_data.amount * 5) / 100,
                currency: purchase_data.currency,
                round_number: purchase_data.round_number,
                usdt_equivalent_amount: purchase_data.usdt_equivalent_amount
            }.toCell()
        });
    }
    
    // Getters
    get fun total_purchased(): Int {
        return self.total_purchased;
    }
    
    get fun total_paid(): Int {
        return self.total_paid;
    }
    
    get fun purchase_id_counter(): Int {
        return self.purchase_id_counter;
    }
    
    get fun purchase_details(purchase_id: Int): PurchaseRecord? {
        return self.purchase_history.get(purchase_id);
    }

    get fun is_refunded(purchase_id: Int): Bool {
        return self.refund_history.get(purchase_id) != null;
    }

    // New getters for signature verification support
    get fun signature_verified_purchases(): Int {
        let count: Int = 0;
        let i: Int = 1;
        while (i <= self.purchase_id_counter) {
            let purchase: PurchaseRecord? = self.purchase_history.get(i);
            if (purchase != null) {
                let record: PurchaseRecord = purchase!!;
                if (record.purchase_method == 1) {
                    count += 1;
                }
            }
            i += 1;
        }
        return count;
    }

    get fun purchase_method_stats(): map<Int, Int> {
        let stats: map<Int, Int> = emptyMap();
        let i: Int = 1;
        while (i <= self.purchase_id_counter) {
            let purchase: PurchaseRecord? = self.purchase_history.get(i);
            if (purchase != null) {
                let record: PurchaseRecord = purchase!!;
                let current_count: Int? = stats.get(record.purchase_method);
                if (current_count == null) {
                    stats.set(record.purchase_method, 1);
                } else {
                    stats.set(record.purchase_method, current_count!! + 1);
                }
            }
            i += 1;
        }
        return stats;
    }

    // Round-related getters
    get fun purchases_by_round(round_number: Int): map<Int, PurchaseRecord> {
        let round_purchases: map<Int, PurchaseRecord> = emptyMap();
        let i: Int = 1;
        while (i <= self.purchase_id_counter) {
            let purchase: PurchaseRecord? = self.purchase_history.get(i);
            if (purchase != null) {
                let record: PurchaseRecord = purchase!!;
                if (record.round_number == round_number) {
                    round_purchases.set(i, record);
                }
            }
            i += 1;
        }
        return round_purchases;
    }

    // Get round statistics for a specific round
    get fun round_total_amount(round_number: Int): Int {
        let total_amount: Int = 0;
        let i: Int = 1;
        while (i <= self.purchase_id_counter) {
            let purchase: PurchaseRecord? = self.purchase_history.get(i);
            if (purchase != null) {
                let record: PurchaseRecord = purchase!!;
                if (record.round_number == round_number) {
                    total_amount += record.amount;
                }
            }
            i += 1;
        }
        return total_amount;
    }

    get fun round_total_tokens(round_number: Int): Int {
        let total_tokens: Int = 0;
        let i: Int = 1;
        while (i <= self.purchase_id_counter) {
            let purchase: PurchaseRecord? = self.purchase_history.get(i);
            if (purchase != null) {
                let record: PurchaseRecord = purchase!!;
                if (record.round_number == round_number) {
                    total_tokens += record.tokens;
                }
            }
            i += 1;
        }
        return total_tokens;
    }

    get fun round_purchase_count(round_number: Int): Int {
        let purchase_count: Int = 0;
        let i: Int = 1;
        while (i <= self.purchase_id_counter) {
            let purchase: PurchaseRecord? = self.purchase_history.get(i);
            if (purchase != null) {
                let record: PurchaseRecord = purchase!!;
                if (record.round_number == round_number) {
                    purchase_count += 1;
                }
            }
            i += 1;
        }
        return purchase_count;
    }

    get fun participated_rounds(): map<Int, Bool> {
        return self.participated_rounds;
    }

    get fun participated_in_round(round_number: Int): Bool {
        return self.participated_rounds.get(round_number) != null;
    }

    get fun round_count(): Int {
        let count: Int = 0;
        let j: Int = 1;
        while (j <= 1000) { // Assuming max 1000 rounds
            if (self.participated_rounds.get(j) != null) {
                count += 1;
            }
            j += 1;
        }
        return count;
    }

    get fun all_records(): map<Int, PurchaseRecord> {
        return self.purchase_history;
    }
}