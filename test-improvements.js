#!/usr/bin/env node

/**
 * 测试改进后的错误处理和数据验证系统
 */

import { StatsCollector } from './src/stats-collector.js';
import { ErrorLogger } from './src/error-logger.js';
import { DataValidator } from './src/data-validator.js';
import { RetryManager } from './src/retry-manager.js';

async function testErrorLogger() {
    console.log('🧪 测试错误记录器...');
    
    const errorLogger = new ErrorLogger();
    
    // 测试不同类型的错误记录
    await errorLogger.logContractError(
        '0:test123',
        'getTotalPurchased',
        new Error('Network timeout'),
        { severity: 'critical' }
    );
    
    await errorLogger.logApiError(
        '/api/test',
        { response: { status: 429 }, message: 'Rate limited' },
        2
    );
    
    await errorLogger.logValidationError(
        'userContract',
        'totalPaidConsistency',
        { actual: 100, expected: 200 },
        'Total paid should match expected value'
    );
    
    // 生成错误报告
    const errorReport = errorLogger.generateErrorReport();
    console.log('✅ 错误记录器测试完成');
    console.log(`   - 记录了 ${errorReport.summary.totalErrors} 个错误`);
    console.log(`   - 关键错误: ${errorReport.summary.criticalErrors}`);
    console.log(`   - 可重试错误: ${errorReport.summary.retryableErrors}`);
    
    return errorLogger;
}

async function testDataValidator() {
    console.log('\n🧪 测试数据验证器...');
    
    const errorLogger = new ErrorLogger();
    const dataValidator = new DataValidator(errorLogger);
    
    // 测试用户合约数据验证
    const testUserData = {
        total_purchased: BigInt(1000),
        total_paid: BigInt(500),
        purchase_id_counter: BigInt(5),
        refund_count: BigInt(2),
        total_refunded: BigInt(100),
        all_records: { '1': {}, '2': {}, '3': {} }
    };
    
    const userValidation = await dataValidator.validateUserContractData('0:test123', testUserData);
    console.log(`✅ 用户合约验证: ${userValidation.isValid ? '通过' : '失败'}`);
    console.log(`   - 错误数: ${userValidation.errors.length}`);
    console.log(`   - 警告数: ${userValidation.warnings.length}`);
    
    // 测试主合约数据验证
    const testMainData = {
        total_raised: BigInt(10000),
        total_tokens_sold: BigInt(5000),
        remaining_tokens: BigInt(5000),
        current_price: BigInt(2000000000) // 2 TON
    };
    
    const mainValidation = await dataValidator.validateMainContractData('0:main123', testMainData);
    console.log(`✅ 主合约验证: ${mainValidation.isValid ? '通过' : '失败'}`);
    
    // 测试统计数据验证
    const testStats = {
        totalUsers: 10,
        uniqueUsers: new Array(10).fill(0).map((_, i) => `user${i}`),
        totalPurchases: 50,
        userPurchasesArray: new Array(10).fill(0).map((_, i) => ({
            purchaseCount: 5,
            totalPurchased: 100
        }))
    };
    
    const statsValidation = await dataValidator.validateStatistics(testStats);
    console.log(`✅ 统计数据验证: ${statsValidation.isValid ? '通过' : '失败'}`);
    
    return dataValidator;
}

async function testRetryManager() {
    console.log('\n🧪 测试重试管理器...');
    
    const errorLogger = new ErrorLogger();
    const retryManager = new RetryManager(errorLogger);
    
    let attemptCount = 0;
    
    // 测试成功的重试操作
    const successOperation = async () => {
        attemptCount++;
        if (attemptCount < 3) {
            throw new Error('Temporary failure');
        }
        return 'Success!';
    };
    
    try {
        const result = await retryManager.executeWithRetry(successOperation, {
            operationName: 'testSuccessOperation',
            maxRetries: 5
        });
        console.log(`✅ 重试成功: ${result} (尝试了 ${attemptCount} 次)`);
    } catch (error) {
        console.log(`❌ 重试失败: ${error.message}`);
    }
    
    // 测试关键操作重试
    attemptCount = 0;
    const criticalOperation = async () => {
        attemptCount++;
        if (attemptCount < 2) {
            const error = new Error('Network timeout');
            error.code = 'ETIMEDOUT';
            throw error;
        }
        return 'Critical data retrieved';
    };
    
    try {
        const result = await retryManager.executeCriticalOperation(
            criticalOperation,
            'getCriticalData'
        );
        console.log(`✅ 关键操作成功: ${result} (尝试了 ${attemptCount} 次)`);
    } catch (error) {
        console.log(`❌ 关键操作失败: ${error.message}`);
    }
    
    return retryManager;
}

async function testIntegration() {
    console.log('\n🧪 测试集成功能...');
    
    try {
        // 创建一个小规模的统计收集器测试
        const collector = new StatsCollector();
        
        // 测试错误记录器集成
        const errorLogger = collector.contractReader.getErrorLogger();
        console.log('✅ 错误记录器集成正常');
        
        // 测试数据验证器集成
        const dataValidator = collector.contractReader.getDataValidator();
        console.log('✅ 数据验证器集成正常');
        
        // 测试重试管理器集成
        const retryManager = collector.contractReader.getRetryManager();
        console.log('✅ 重试管理器集成正常');
        
        console.log('✅ 所有组件集成测试通过');
        
    } catch (error) {
        console.error('❌ 集成测试失败:', error.message);
    }
}

async function runTests() {
    console.log('🚀 开始测试改进后的错误处理和数据验证系统');
    console.log('================================================');
    
    try {
        // 运行各个组件的测试
        await testErrorLogger();
        await testDataValidator();
        await testRetryManager();
        await testIntegration();
        
        console.log('\n🎉 所有测试完成！');
        console.log('================================================');
        console.log('✅ 错误记录和追踪系统已就绪');
        console.log('✅ 数据完整性验证系统已就绪');
        console.log('✅ 智能重试机制已就绪');
        console.log('✅ 综合报告生成系统已就绪');
        
        console.log('\n📋 使用建议:');
        console.log('1. 运行 npm start 开始数据收集');
        console.log('2. 查看生成的 comprehensive-report-*.json 文件了解详细情况');
        console.log('3. 查看 error-log-*.json 文件了解错误详情');
        console.log('4. 根据报告中的建议优化系统配置');
        
    } catch (error) {
        console.error('❌ 测试过程中出现错误:', error);
        process.exit(1);
    }
}

// 运行测试
if (import.meta.url === `file://${process.argv[1]}`) {
    runTests().catch(console.error);
}
