#!/usr/bin/env node

/**
 * 调试退款数据获取问题的简化脚本
 */

import fs from 'fs/promises';
import { TonClient } from '@ton/ton';
import { Address } from '@ton/core';
import { UserPurchase } from './build/UserPurchase/UserPurchase_UserPurchase.js';

async function debugRefunds() {
    console.log('🔍 调试退款数据获取...');
    
    // 创建TON客户端
    const tonClient = new TonClient({
        endpoint: 'https://toncenter.com/api/v2/jsonRPC',
        apiKey: '8424cbe83460d87e34163e47f3103a4b53621790e79d86ba79dc0d143d65816b'
    });

    try {
        // 加载用户数据缓存
        const cacheData = await fs.readFile('user_data_cache.json', 'utf-8');
        const cache = JSON.parse(cacheData);
        
        // 取前5个合约进行测试
        const contractAddresses = Object.keys(cache.data).slice(0, 5);
        console.log(`测试前 ${contractAddresses.length} 个合约的退款数据...`);
        
        for (const contractAddress of contractAddresses) {
            console.log(`\n📋 测试合约: ${contractAddress}`);
            
            try {
                const contractAddr = Address.parse(contractAddress);
                const userContract = UserPurchase.fromAddress(contractAddr);
                const provider = tonClient.provider(contractAddr);
                
                // 获取购买记录数量
                const purchaseCounter = await userContract.getPurchaseIdCounter(provider);
                console.log(`  购买记录数量: ${purchaseCounter}`);
                
                if (purchaseCounter > 0) {
                    // 检查前几笔购买的退款状态
                    const maxCheck = Math.min(Number(purchaseCounter), 3);
                    
                    for (let i = 1; i <= maxCheck; i++) {
                        try {
                            console.log(`  检查购买 #${i}:`);
                            
                            // 获取购买详情
                            const purchaseDetails = await userContract.getPurchaseDetails(provider, BigInt(i));
                            if (purchaseDetails) {
                                console.log(`    金额: ${(Number(purchaseDetails.amount) / 1e9).toFixed(4)} TON`);
                                console.log(`    代币: ${(Number(purchaseDetails.tokens) / 1e9).toFixed(4)}`);
                                console.log(`    轮次: ${purchaseDetails.round_number}`);
                            } else {
                                console.log(`    ❌ 无法获取购买详情`);
                                continue;
                            }
                            
                            // 检查退款状态
                            const isRefunded = await userContract.getIsRefunded(provider, BigInt(i));
                            console.log(`    退款状态: ${isRefunded ? '✅ 已退款' : '❌ 未退款'}`);
                            
                            if (isRefunded) {
                                console.log(`    退款金额: ${(Number(purchaseDetails.amount) / 1e9).toFixed(4)} TON`);
                            }
                            
                        } catch (error) {
                            console.log(`    ❌ 检查购买 #${i} 失败: ${error.message}`);
                        }
                    }
                } else {
                    console.log(`  ℹ️  该合约无购买记录`);
                }
                
            } catch (error) {
                console.log(`  ❌ 处理合约失败: ${error.message}`);
            }
        }
        
        console.log('\n🔍 调试完成！');
        
    } catch (error) {
        console.error('调试过程中出错:', error);
    }
}

// 检查特定合约的详细信息
async function debugSpecificContract(contractAddress) {
    console.log(`🔍 详细调试合约: ${contractAddress}`);
    
    const tonClient = new TonClient({
        endpoint: 'https://toncenter.com/api/v2/jsonRPC',
        apiKey: '8424cbe83460d87e34163e47f3103a4b53621790e79d86ba79dc0d143d65816b'
    });

    try {
        const contractAddr = Address.parse(contractAddress);
        const userContract = UserPurchase.fromAddress(contractAddr);
        const provider = tonClient.provider(contractAddr);
        
        // 获取基本信息
        console.log('\n📊 基本信息:');
        const totalPurchased = await userContract.getTotalPurchased(provider);
        const totalPaid = await userContract.getTotalPaid(provider);
        const purchaseCounter = await userContract.getPurchaseIdCounter(provider);
        
        console.log(`  总购买代币: ${(Number(totalPurchased) / 1e9).toFixed(4)}`);
        console.log(`  总支付金额: ${(Number(totalPaid) / 1e9).toFixed(4)} TON`);
        console.log(`  购买记录数: ${purchaseCounter}`);
        
        // 获取所有购买记录
        console.log('\n📋 购买记录:');
        try {
            const allRecords = await userContract.getAllRecords(provider);
            console.log('  原始记录结构:', JSON.stringify(allRecords, null, 2));
        } catch (error) {
            console.log(`  ❌ 获取所有记录失败: ${error.message}`);
        }
        
        // 逐个检查购买记录
        console.log('\n🔍 逐个检查购买记录:');
        for (let i = 1; i <= Number(purchaseCounter); i++) {
            try {
                console.log(`\n  购买 #${i}:`);
                
                const purchaseDetails = await userContract.getPurchaseDetails(provider, BigInt(i));
                if (purchaseDetails) {
                    console.log(`    用户: ${purchaseDetails.user}`);
                    console.log(`    金额: ${(Number(purchaseDetails.amount) / 1e9).toFixed(4)} TON`);
                    console.log(`    代币: ${(Number(purchaseDetails.tokens) / 1e9).toFixed(4)}`);
                    console.log(`    时间: ${new Date(Number(purchaseDetails.timestamp) * 1000).toLocaleString()}`);
                    console.log(`    轮次: ${purchaseDetails.round_number}`);
                    console.log(`    货币: ${purchaseDetails.currency === 0 ? 'TON' : 'USDT'}`);
                } else {
                    console.log(`    ❌ 无购买详情`);
                    continue;
                }
                
                const isRefunded = await userContract.getIsRefunded(provider, BigInt(i));
                console.log(`    退款状态: ${isRefunded ? '✅ 已退款' : '❌ 未退款'}`);
                
            } catch (error) {
                console.log(`    ❌ 检查失败: ${error.message}`);
            }
        }
        
    } catch (error) {
        console.error('详细调试失败:', error);
    }
}

// 主函数
async function main() {
    const args = process.argv.slice(2);
    
    if (args.length > 0 && args[0] !== '--all') {
        // 调试特定合约
        await debugSpecificContract(args[0]);
    } else {
        // 调试多个合约
        await debugRefunds();
    }
}

if (import.meta.url === `file://${process.argv[1]}`) {
    main().catch(console.error);
}
