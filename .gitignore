# Dependencies
node_modules/
package-lock.json

# Cache files
user_contracts_cache.json
user_data_cache.json
checkpoint_data.json
address_mapping_cache.json

# Output files
raw-stats-*.json
stats-report-*.json

# CSV reports
csv_reports/

# Environment files
.env
.env.local

# Logs
*.log
npm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db