{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": false, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "outDir": "./dist", "allowImportingTsExtensions": false, "noEmit": false, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@build/*": ["build/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "build"], "ts-node": {"esm": true}}