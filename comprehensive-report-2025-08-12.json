{"timestamp": "2025-08-12T08:19:19.676Z", "summary": {"dataCollection": {"totalContracts": 348, "successfulContracts": 292, "failedContracts": 56}, "errorSummary": {"sessionDuration": 1099015, "totalErrors": 143, "errorsByType": {"RETRY_ATTEMPT": 78, "BATCH_OPERATION_ERRORS": 32, "BATCH_REFUND_CHECK_ERRORS": 32, "DATA_RECOVERY": 1}, "errorsBySeverity": {"info": 78, "warning": 9, "critical": 56}, "retryableErrors": 78, "criticalErrors": 56}, "validationSummary": {"totalValidations": 2, "passedValidations": 2, "failedValidations": 0, "totalErrors": 0, "totalWarnings": 0}}, "errorReport": {"summary": {"sessionDuration": 1099015, "totalErrors": 143, "errorsByType": {"RETRY_ATTEMPT": 78, "BATCH_OPERATION_ERRORS": 32, "BATCH_REFUND_CHECK_ERRORS": 32, "DATA_RECOVERY": 1}, "errorsBySeverity": {"info": 78, "warning": 9, "critical": 56}, "retryableErrors": 78, "criticalErrors": 56}, "recentErrors": [{"id": "ERR_1754986622390_122q09zxd", "timestamp": "2025-08-12T08:17:02.390Z", "type": "RETRY_ATTEMPT", "operation": "getTotalPurchased", "attempt": 2, "maxAttempts": 5, "reason": "timeout of 30000ms exceeded", "message": "Retry attempt 2/5 for getTotalPurchased: timeout of 30000ms exceeded", "severity": "info", "context": {"contractAddress": "0:B218109C48E399A5D478EB0C3BDAA7F199BC4DD8760F5C7B06B4895C9F535805", "isCritical": true, "errorType": "AxiosError"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986627345_gccdt0c8v", "timestamp": "2025-08-12T08:17:07.345Z", "type": "DATA_RECOVERY", "operation": "getTotalPurchased", "recoveryMethod": "Succeeded on attempt 3", "originalError": "timeout of 30000ms exceeded", "message": "Data recovery applied: Succeeded on attempt 3 for getTotalPurchased", "severity": "warning", "context": {"contractAddress": "0:B218109C48E399A5D478EB0C3BDAA7F199BC4DD8760F5C7B06B4895C9F535805", "totalAttempts": 3, "isCritical": true}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986655125_s40cn1yez", "timestamp": "2025-08-12T08:17:35.125Z", "type": "RETRY_ATTEMPT", "operation": "getPurchaseDetails", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for getPurchaseDetails: Not a number", "severity": "info", "context": {"contractAddress": "0:7534A26D8B56B37A8A01F3AB3EE5420C82B2C53540B133D1C5908A208F769856", "purchaseId": 1, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986655128_0weeav77j", "timestamp": "2025-08-12T08:17:35.128Z", "type": "RETRY_ATTEMPT", "operation": "refundStatusBatch_0", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for refundStatusBatch_0: Not a number", "severity": "info", "context": {"contractAddress": "0:7534A26D8B56B37A8A01F3AB3EE5420C82B2C53540B133D1C5908A208F769856", "totalPurchases": 1, "batchIndex": 0, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986655131_5lx5xmh4u", "timestamp": "2025-08-12T08:17:35.131Z", "type": "BATCH_OPERATION_ERRORS", "operation": "refundStatusBatch", "message": "Batch operation completed with 1/1 failures", "severity": "critical", "context": {"contractAddress": "0:7534A26D8B56B37A8A01F3AB3EE5420C82B2C53540B133D1C5908A208F769856", "totalPurchases": 1, "totalOperations": 1, "failedOperations": 1, "errors": [{"index": 0, "message": "Not a number"}]}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986655133_b5ixlwjiu", "timestamp": "2025-08-12T08:17:35.133Z", "type": "BATCH_REFUND_CHECK_ERRORS", "operation": "refundStatusBatch", "message": "Failed to check refund status for 1/1 purchases", "severity": "critical", "context": {"contractAddress": "0:7534A26D8B56B37A8A01F3AB3EE5420C82B2C53540B133D1C5908A208F769856", "totalPurchases": 1, "successfulChecks": 0, "failedChecks": 1}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986739503_6yj4h8hvc", "timestamp": "2025-08-12T08:18:59.503Z", "type": "RETRY_ATTEMPT", "operation": "getPurchaseDetails", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for getPurchaseDetails: Not a number", "severity": "info", "context": {"contractAddress": "0:39F15DC1FEA8A4C5377F7BB26441FFDD6A008037898B44AE5681A305FE72EA91", "purchaseId": 1, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986739506_ipmxvcrz3", "timestamp": "2025-08-12T08:18:59.507Z", "type": "RETRY_ATTEMPT", "operation": "refundStatusBatch_0", "attempt": 1, "maxAttempts": 5, "reason": "Not a number", "message": "Retry attempt 1/5 for refundStatusBatch_0: Not a number", "severity": "info", "context": {"contractAddress": "0:39F15DC1FEA8A4C5377F7BB26441FFDD6A008037898B44AE5681A305FE72EA91", "totalPurchases": 1, "batchIndex": 0, "isCritical": true, "errorType": "Error"}, "retryable": true, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986739509_auh7d85xu", "timestamp": "2025-08-12T08:18:59.510Z", "type": "BATCH_OPERATION_ERRORS", "operation": "refundStatusBatch", "message": "Batch operation completed with 1/1 failures", "severity": "critical", "context": {"contractAddress": "0:39F15DC1FEA8A4C5377F7BB26441FFDD6A008037898B44AE5681A305FE72EA91", "totalPurchases": 1, "totalOperations": 1, "failedOperations": 1, "errors": [{"index": 0, "message": "Not a number"}]}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}, {"id": "ERR_1754986739511_fj152yw54", "timestamp": "2025-08-12T08:18:59.511Z", "type": "BATCH_REFUND_CHECK_ERRORS", "operation": "refundStatusBatch", "message": "Failed to check refund status for 1/1 purchases", "severity": "critical", "context": {"contractAddress": "0:39F15DC1FEA8A4C5377F7BB26441FFDD6A008037898B44AE5681A305FE72EA91", "totalPurchases": 1, "successfulChecks": 0, "failedChecks": 1}, "retryable": false, "sessionStartTime": "2025-08-12T08:01:00.658Z"}], "errorPatterns": {"RETRY_ATTEMPT_getPurchaseDetails": {"count": 38, "firstOccurrence": "2025-08-12T08:01:23.440Z", "lastOccurrence": "2025-08-12T08:18:59.503Z", "severity": "info"}, "RETRY_ATTEMPT_refundStatusBatch_2": {"count": 2, "firstOccurrence": "2025-08-12T08:01:23.443Z", "lastOccurrence": "2025-08-12T08:03:20.369Z", "severity": "info"}, "BATCH_OPERATION_ERRORS_refundStatusBatch": {"count": 32, "firstOccurrence": "2025-08-12T08:01:23.445Z", "lastOccurrence": "2025-08-12T08:18:59.510Z", "severity": "warning"}, "BATCH_REFUND_CHECK_ERRORS_refundStatusBatch": {"count": 32, "firstOccurrence": "2025-08-12T08:01:23.448Z", "lastOccurrence": "2025-08-12T08:18:59.511Z", "severity": "warning"}, "RETRY_ATTEMPT_refundStatusBatch_1": {"count": 6, "firstOccurrence": "2025-08-12T08:01:51.793Z", "lastOccurrence": "2025-08-12T08:13:05.742Z", "severity": "info"}, "RETRY_ATTEMPT_refundStatusBatch_0": {"count": 30, "firstOccurrence": "2025-08-12T08:02:00.333Z", "lastOccurrence": "2025-08-12T08:18:59.507Z", "severity": "info"}, "RETRY_ATTEMPT_getTotalPurchased": {"count": 2, "firstOccurrence": "2025-08-12T08:16:30.661Z", "lastOccurrence": "2025-08-12T08:17:02.390Z", "severity": "info"}, "DATA_RECOVERY_getTotalPurchased": {"count": 1, "firstOccurrence": "2025-08-12T08:17:07.345Z", "lastOccurrence": "2025-08-12T08:17:07.345Z", "severity": "warning"}}, "recommendations": [{"priority": "HIGH", "issue": "Critical data retrieval failures detected", "suggestion": "Review contract addresses and network connectivity. Consider implementing additional fallback mechanisms."}]}, "validationReport": {"timestamp": "2025-08-12T08:19:19.676Z", "summary": {"totalValidations": 2, "passedValidations": 2, "failedValidations": 0, "totalErrors": 0, "totalWarnings": 0}, "details": [{"isValid": true, "errors": [], "warnings": [], "summary": {"totalRules": 0, "passedRules": 0, "failedRules": 0, "criticalErrors": 0}}, {"isValid": true, "errors": [], "warnings": []}], "recommendations": []}, "recommendations": [{"priority": "HIGH", "issue": "Critical data retrieval failures detected", "suggestion": "Review contract addresses and network connectivity. Consider implementing additional fallback mechanisms."}, {"priority": "CRITICAL", "issue": "Critical issues detected in data collection or validation", "suggestion": "Immediate review required. Consider re-running data collection with enhanced error handling."}]}