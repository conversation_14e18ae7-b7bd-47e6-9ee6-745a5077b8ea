{"timestamp": "2025-08-12T09:19:14.281Z", "summary": {"dataCollection": {"totalContracts": 348, "successfulContracts": 292, "failedContracts": 56}, "errorSummary": {"sessionDuration": 10872, "totalErrors": 0, "errorsByType": {}, "errorsBySeverity": {}, "retryableErrors": 0, "criticalErrors": 0}, "validationSummary": {"totalValidations": 2, "passedValidations": 2, "failedValidations": 0, "totalErrors": 0, "totalWarnings": 0}}, "errorReport": {"summary": {"sessionDuration": 10872, "totalErrors": 0, "errorsByType": {}, "errorsBySeverity": {}, "retryableErrors": 0, "criticalErrors": 0}, "recentErrors": [], "errorPatterns": {}, "recommendations": []}, "validationReport": {"timestamp": "2025-08-12T09:19:14.281Z", "summary": {"totalValidations": 2, "passedValidations": 2, "failedValidations": 0, "totalErrors": 0, "totalWarnings": 0}, "details": [{"isValid": true, "errors": [], "warnings": [], "summary": {"totalRules": 0, "passedRules": 0, "failedRules": 0, "criticalErrors": 0}}, {"isValid": true, "errors": [], "warnings": []}], "recommendations": []}, "recommendations": []}