import { Cell, Slice, Address, Builder, <PERSON>pleI<PERSON>, TupleReader, Dictionary, ContractProvider, Sender, Contract, ContractABI, DictionaryValue } from '@ton/core';
export type DataSize = {
    $$type: 'DataSize';
    cells: bigint;
    bits: bigint;
    refs: bigint;
};
export declare function storeDataSize(src: DataSize): (builder: Builder) => void;
export declare function loadDataSize(slice: Slice): {
    $$type: "DataSize";
    cells: bigint;
    bits: bigint;
    refs: bigint;
};
export declare function loadTupleDataSize(source: TupleReader): {
    $$type: "DataSize";
    cells: bigint;
    bits: bigint;
    refs: bigint;
};
export declare function loadGetterTupleDataSize(source: TupleReader): {
    $$type: "DataSize";
    cells: bigint;
    bits: bigint;
    refs: bigint;
};
export declare function storeTupleDataSize(source: DataSize): TupleItem[];
export declare function dictValueParserDataSize(): DictionaryValue<DataSize>;
export type SignedBundle = {
    $$type: 'SignedBundle';
    signature: Buffer;
    signedData: Slice;
};
export declare function storeSignedBundle(src: SignedBundle): (builder: Builder) => void;
export declare function loadSignedBundle(slice: Slice): {
    $$type: "SignedBundle";
    signature: Buffer<ArrayBufferLike>;
    signedData: Slice;
};
export declare function loadTupleSignedBundle(source: TupleReader): {
    $$type: "SignedBundle";
    signature: Buffer<ArrayBufferLike>;
    signedData: Slice;
};
export declare function loadGetterTupleSignedBundle(source: TupleReader): {
    $$type: "SignedBundle";
    signature: Buffer<ArrayBufferLike>;
    signedData: Slice;
};
export declare function storeTupleSignedBundle(source: SignedBundle): TupleItem[];
export declare function dictValueParserSignedBundle(): DictionaryValue<SignedBundle>;
export type StateInit = {
    $$type: 'StateInit';
    code: Cell;
    data: Cell;
};
export declare function storeStateInit(src: StateInit): (builder: Builder) => void;
export declare function loadStateInit(slice: Slice): {
    $$type: "StateInit";
    code: Cell;
    data: Cell;
};
export declare function loadTupleStateInit(source: TupleReader): {
    $$type: "StateInit";
    code: Cell;
    data: Cell;
};
export declare function loadGetterTupleStateInit(source: TupleReader): {
    $$type: "StateInit";
    code: Cell;
    data: Cell;
};
export declare function storeTupleStateInit(source: StateInit): TupleItem[];
export declare function dictValueParserStateInit(): DictionaryValue<StateInit>;
export type Context = {
    $$type: 'Context';
    bounceable: boolean;
    sender: Address;
    value: bigint;
    raw: Slice;
};
export declare function storeContext(src: Context): (builder: Builder) => void;
export declare function loadContext(slice: Slice): {
    $$type: "Context";
    bounceable: boolean;
    sender: Address;
    value: bigint;
    raw: Slice;
};
export declare function loadTupleContext(source: TupleReader): {
    $$type: "Context";
    bounceable: boolean;
    sender: Address;
    value: bigint;
    raw: Slice;
};
export declare function loadGetterTupleContext(source: TupleReader): {
    $$type: "Context";
    bounceable: boolean;
    sender: Address;
    value: bigint;
    raw: Slice;
};
export declare function storeTupleContext(source: Context): TupleItem[];
export declare function dictValueParserContext(): DictionaryValue<Context>;
export type SendParameters = {
    $$type: 'SendParameters';
    mode: bigint;
    body: Cell | null;
    code: Cell | null;
    data: Cell | null;
    value: bigint;
    to: Address;
    bounce: boolean;
};
export declare function storeSendParameters(src: SendParameters): (builder: Builder) => void;
export declare function loadSendParameters(slice: Slice): {
    $$type: "SendParameters";
    mode: bigint;
    body: Cell;
    code: Cell;
    data: Cell;
    value: bigint;
    to: Address;
    bounce: boolean;
};
export declare function loadTupleSendParameters(source: TupleReader): {
    $$type: "SendParameters";
    mode: bigint;
    body: Cell;
    code: Cell;
    data: Cell;
    value: bigint;
    to: Address;
    bounce: boolean;
};
export declare function loadGetterTupleSendParameters(source: TupleReader): {
    $$type: "SendParameters";
    mode: bigint;
    body: Cell;
    code: Cell;
    data: Cell;
    value: bigint;
    to: Address;
    bounce: boolean;
};
export declare function storeTupleSendParameters(source: SendParameters): TupleItem[];
export declare function dictValueParserSendParameters(): DictionaryValue<SendParameters>;
export type MessageParameters = {
    $$type: 'MessageParameters';
    mode: bigint;
    body: Cell | null;
    value: bigint;
    to: Address;
    bounce: boolean;
};
export declare function storeMessageParameters(src: MessageParameters): (builder: Builder) => void;
export declare function loadMessageParameters(slice: Slice): {
    $$type: "MessageParameters";
    mode: bigint;
    body: Cell;
    value: bigint;
    to: Address;
    bounce: boolean;
};
export declare function loadTupleMessageParameters(source: TupleReader): {
    $$type: "MessageParameters";
    mode: bigint;
    body: Cell;
    value: bigint;
    to: Address;
    bounce: boolean;
};
export declare function loadGetterTupleMessageParameters(source: TupleReader): {
    $$type: "MessageParameters";
    mode: bigint;
    body: Cell;
    value: bigint;
    to: Address;
    bounce: boolean;
};
export declare function storeTupleMessageParameters(source: MessageParameters): TupleItem[];
export declare function dictValueParserMessageParameters(): DictionaryValue<MessageParameters>;
export type DeployParameters = {
    $$type: 'DeployParameters';
    mode: bigint;
    body: Cell | null;
    value: bigint;
    bounce: boolean;
    init: StateInit;
};
export declare function storeDeployParameters(src: DeployParameters): (builder: Builder) => void;
export declare function loadDeployParameters(slice: Slice): {
    $$type: "DeployParameters";
    mode: bigint;
    body: Cell;
    value: bigint;
    bounce: boolean;
    init: {
        $$type: "StateInit";
        code: Cell;
        data: Cell;
    };
};
export declare function loadTupleDeployParameters(source: TupleReader): {
    $$type: "DeployParameters";
    mode: bigint;
    body: Cell;
    value: bigint;
    bounce: boolean;
    init: {
        $$type: "StateInit";
        code: Cell;
        data: Cell;
    };
};
export declare function loadGetterTupleDeployParameters(source: TupleReader): {
    $$type: "DeployParameters";
    mode: bigint;
    body: Cell;
    value: bigint;
    bounce: boolean;
    init: {
        $$type: "StateInit";
        code: Cell;
        data: Cell;
    };
};
export declare function storeTupleDeployParameters(source: DeployParameters): TupleItem[];
export declare function dictValueParserDeployParameters(): DictionaryValue<DeployParameters>;
export type StdAddress = {
    $$type: 'StdAddress';
    workchain: bigint;
    address: bigint;
};
export declare function storeStdAddress(src: StdAddress): (builder: Builder) => void;
export declare function loadStdAddress(slice: Slice): {
    $$type: "StdAddress";
    workchain: bigint;
    address: bigint;
};
export declare function loadTupleStdAddress(source: TupleReader): {
    $$type: "StdAddress";
    workchain: bigint;
    address: bigint;
};
export declare function loadGetterTupleStdAddress(source: TupleReader): {
    $$type: "StdAddress";
    workchain: bigint;
    address: bigint;
};
export declare function storeTupleStdAddress(source: StdAddress): TupleItem[];
export declare function dictValueParserStdAddress(): DictionaryValue<StdAddress>;
export type VarAddress = {
    $$type: 'VarAddress';
    workchain: bigint;
    address: Slice;
};
export declare function storeVarAddress(src: VarAddress): (builder: Builder) => void;
export declare function loadVarAddress(slice: Slice): {
    $$type: "VarAddress";
    workchain: bigint;
    address: Slice;
};
export declare function loadTupleVarAddress(source: TupleReader): {
    $$type: "VarAddress";
    workchain: bigint;
    address: Slice;
};
export declare function loadGetterTupleVarAddress(source: TupleReader): {
    $$type: "VarAddress";
    workchain: bigint;
    address: Slice;
};
export declare function storeTupleVarAddress(source: VarAddress): TupleItem[];
export declare function dictValueParserVarAddress(): DictionaryValue<VarAddress>;
export type BasechainAddress = {
    $$type: 'BasechainAddress';
    hash: bigint | null;
};
export declare function storeBasechainAddress(src: BasechainAddress): (builder: Builder) => void;
export declare function loadBasechainAddress(slice: Slice): {
    $$type: "BasechainAddress";
    hash: bigint;
};
export declare function loadTupleBasechainAddress(source: TupleReader): {
    $$type: "BasechainAddress";
    hash: bigint;
};
export declare function loadGetterTupleBasechainAddress(source: TupleReader): {
    $$type: "BasechainAddress";
    hash: bigint;
};
export declare function storeTupleBasechainAddress(source: BasechainAddress): TupleItem[];
export declare function dictValueParserBasechainAddress(): DictionaryValue<BasechainAddress>;
export type ChangeOwner = {
    $$type: 'ChangeOwner';
    queryId: bigint;
    newOwner: Address;
};
export declare function storeChangeOwner(src: ChangeOwner): (builder: Builder) => void;
export declare function loadChangeOwner(slice: Slice): {
    $$type: "ChangeOwner";
    queryId: bigint;
    newOwner: Address;
};
export declare function loadTupleChangeOwner(source: TupleReader): {
    $$type: "ChangeOwner";
    queryId: bigint;
    newOwner: Address;
};
export declare function loadGetterTupleChangeOwner(source: TupleReader): {
    $$type: "ChangeOwner";
    queryId: bigint;
    newOwner: Address;
};
export declare function storeTupleChangeOwner(source: ChangeOwner): TupleItem[];
export declare function dictValueParserChangeOwner(): DictionaryValue<ChangeOwner>;
export type ChangeOwnerOk = {
    $$type: 'ChangeOwnerOk';
    queryId: bigint;
    newOwner: Address;
};
export declare function storeChangeOwnerOk(src: ChangeOwnerOk): (builder: Builder) => void;
export declare function loadChangeOwnerOk(slice: Slice): {
    $$type: "ChangeOwnerOk";
    queryId: bigint;
    newOwner: Address;
};
export declare function loadTupleChangeOwnerOk(source: TupleReader): {
    $$type: "ChangeOwnerOk";
    queryId: bigint;
    newOwner: Address;
};
export declare function loadGetterTupleChangeOwnerOk(source: TupleReader): {
    $$type: "ChangeOwnerOk";
    queryId: bigint;
    newOwner: Address;
};
export declare function storeTupleChangeOwnerOk(source: ChangeOwnerOk): TupleItem[];
export declare function dictValueParserChangeOwnerOk(): DictionaryValue<ChangeOwnerOk>;
export type Deploy = {
    $$type: 'Deploy';
    queryId: bigint;
};
export declare function storeDeploy(src: Deploy): (builder: Builder) => void;
export declare function loadDeploy(slice: Slice): {
    $$type: "Deploy";
    queryId: bigint;
};
export declare function loadTupleDeploy(source: TupleReader): {
    $$type: "Deploy";
    queryId: bigint;
};
export declare function loadGetterTupleDeploy(source: TupleReader): {
    $$type: "Deploy";
    queryId: bigint;
};
export declare function storeTupleDeploy(source: Deploy): TupleItem[];
export declare function dictValueParserDeploy(): DictionaryValue<Deploy>;
export type DeployOk = {
    $$type: 'DeployOk';
    queryId: bigint;
};
export declare function storeDeployOk(src: DeployOk): (builder: Builder) => void;
export declare function loadDeployOk(slice: Slice): {
    $$type: "DeployOk";
    queryId: bigint;
};
export declare function loadTupleDeployOk(source: TupleReader): {
    $$type: "DeployOk";
    queryId: bigint;
};
export declare function loadGetterTupleDeployOk(source: TupleReader): {
    $$type: "DeployOk";
    queryId: bigint;
};
export declare function storeTupleDeployOk(source: DeployOk): TupleItem[];
export declare function dictValueParserDeployOk(): DictionaryValue<DeployOk>;
export type FactoryDeploy = {
    $$type: 'FactoryDeploy';
    queryId: bigint;
    cashback: Address;
};
export declare function storeFactoryDeploy(src: FactoryDeploy): (builder: Builder) => void;
export declare function loadFactoryDeploy(slice: Slice): {
    $$type: "FactoryDeploy";
    queryId: bigint;
    cashback: Address;
};
export declare function loadTupleFactoryDeploy(source: TupleReader): {
    $$type: "FactoryDeploy";
    queryId: bigint;
    cashback: Address;
};
export declare function loadGetterTupleFactoryDeploy(source: TupleReader): {
    $$type: "FactoryDeploy";
    queryId: bigint;
    cashback: Address;
};
export declare function storeTupleFactoryDeploy(source: FactoryDeploy): TupleItem[];
export declare function dictValueParserFactoryDeploy(): DictionaryValue<FactoryDeploy>;
export type JettonTransfer = {
    $$type: 'JettonTransfer';
    query_id: bigint;
    amount: bigint;
    destination: Address;
    response_destination: Address;
    custom_payload: Cell | null;
    forward_ton_amount: bigint;
    forward_payload: Cell | null;
};
export declare function storeJettonTransfer(src: JettonTransfer): (builder: Builder) => void;
export declare function loadJettonTransfer(slice: Slice): {
    $$type: "JettonTransfer";
    query_id: bigint;
    amount: bigint;
    destination: Address;
    response_destination: Address;
    custom_payload: Cell;
    forward_ton_amount: bigint;
    forward_payload: Cell;
};
export declare function loadTupleJettonTransfer(source: TupleReader): {
    $$type: "JettonTransfer";
    query_id: bigint;
    amount: bigint;
    destination: Address;
    response_destination: Address;
    custom_payload: Cell;
    forward_ton_amount: bigint;
    forward_payload: Cell;
};
export declare function loadGetterTupleJettonTransfer(source: TupleReader): {
    $$type: "JettonTransfer";
    query_id: bigint;
    amount: bigint;
    destination: Address;
    response_destination: Address;
    custom_payload: Cell;
    forward_ton_amount: bigint;
    forward_payload: Cell;
};
export declare function storeTupleJettonTransfer(source: JettonTransfer): TupleItem[];
export declare function dictValueParserJettonTransfer(): DictionaryValue<JettonTransfer>;
export type JettonTransferNotification = {
    $$type: 'JettonTransferNotification';
    query_id: bigint;
    amount: bigint;
    sender: Address;
    forward_payload: Cell | null;
};
export declare function storeJettonTransferNotification(src: JettonTransferNotification): (builder: Builder) => void;
export declare function loadJettonTransferNotification(slice: Slice): {
    $$type: "JettonTransferNotification";
    query_id: bigint;
    amount: bigint;
    sender: Address;
    forward_payload: Cell;
};
export declare function loadTupleJettonTransferNotification(source: TupleReader): {
    $$type: "JettonTransferNotification";
    query_id: bigint;
    amount: bigint;
    sender: Address;
    forward_payload: Cell;
};
export declare function loadGetterTupleJettonTransferNotification(source: TupleReader): {
    $$type: "JettonTransferNotification";
    query_id: bigint;
    amount: bigint;
    sender: Address;
    forward_payload: Cell;
};
export declare function storeTupleJettonTransferNotification(source: JettonTransferNotification): TupleItem[];
export declare function dictValueParserJettonTransferNotification(): DictionaryValue<JettonTransferNotification>;
export type JettonBurn = {
    $$type: 'JettonBurn';
    query_id: bigint;
    amount: bigint;
    response_destination: Address;
    custom_payload: Cell | null;
};
export declare function storeJettonBurn(src: JettonBurn): (builder: Builder) => void;
export declare function loadJettonBurn(slice: Slice): {
    $$type: "JettonBurn";
    query_id: bigint;
    amount: bigint;
    response_destination: Address;
    custom_payload: Cell;
};
export declare function loadTupleJettonBurn(source: TupleReader): {
    $$type: "JettonBurn";
    query_id: bigint;
    amount: bigint;
    response_destination: Address;
    custom_payload: Cell;
};
export declare function loadGetterTupleJettonBurn(source: TupleReader): {
    $$type: "JettonBurn";
    query_id: bigint;
    amount: bigint;
    response_destination: Address;
    custom_payload: Cell;
};
export declare function storeTupleJettonBurn(source: JettonBurn): TupleItem[];
export declare function dictValueParserJettonBurn(): DictionaryValue<JettonBurn>;
export type JettonExcesses = {
    $$type: 'JettonExcesses';
    query_id: bigint;
};
export declare function storeJettonExcesses(src: JettonExcesses): (builder: Builder) => void;
export declare function loadJettonExcesses(slice: Slice): {
    $$type: "JettonExcesses";
    query_id: bigint;
};
export declare function loadTupleJettonExcesses(source: TupleReader): {
    $$type: "JettonExcesses";
    query_id: bigint;
};
export declare function loadGetterTupleJettonExcesses(source: TupleReader): {
    $$type: "JettonExcesses";
    query_id: bigint;
};
export declare function storeTupleJettonExcesses(source: JettonExcesses): TupleItem[];
export declare function dictValueParserJettonExcesses(): DictionaryValue<JettonExcesses>;
export type JettonInternalTransfer = {
    $$type: 'JettonInternalTransfer';
    query_id: bigint;
    amount: bigint;
    from: Address;
    response_address: Address;
    forward_ton_amount: bigint;
    forward_payload: Cell | null;
};
export declare function storeJettonInternalTransfer(src: JettonInternalTransfer): (builder: Builder) => void;
export declare function loadJettonInternalTransfer(slice: Slice): {
    $$type: "JettonInternalTransfer";
    query_id: bigint;
    amount: bigint;
    from: Address;
    response_address: Address;
    forward_ton_amount: bigint;
    forward_payload: Cell;
};
export declare function loadTupleJettonInternalTransfer(source: TupleReader): {
    $$type: "JettonInternalTransfer";
    query_id: bigint;
    amount: bigint;
    from: Address;
    response_address: Address;
    forward_ton_amount: bigint;
    forward_payload: Cell;
};
export declare function loadGetterTupleJettonInternalTransfer(source: TupleReader): {
    $$type: "JettonInternalTransfer";
    query_id: bigint;
    amount: bigint;
    from: Address;
    response_address: Address;
    forward_ton_amount: bigint;
    forward_payload: Cell;
};
export declare function storeTupleJettonInternalTransfer(source: JettonInternalTransfer): TupleItem[];
export declare function dictValueParserJettonInternalTransfer(): DictionaryValue<JettonInternalTransfer>;
export type JettonBurnNotification = {
    $$type: 'JettonBurnNotification';
    query_id: bigint;
    amount: bigint;
    sender: Address;
    response_destination: Address;
};
export declare function storeJettonBurnNotification(src: JettonBurnNotification): (builder: Builder) => void;
export declare function loadJettonBurnNotification(slice: Slice): {
    $$type: "JettonBurnNotification";
    query_id: bigint;
    amount: bigint;
    sender: Address;
    response_destination: Address;
};
export declare function loadTupleJettonBurnNotification(source: TupleReader): {
    $$type: "JettonBurnNotification";
    query_id: bigint;
    amount: bigint;
    sender: Address;
    response_destination: Address;
};
export declare function loadGetterTupleJettonBurnNotification(source: TupleReader): {
    $$type: "JettonBurnNotification";
    query_id: bigint;
    amount: bigint;
    sender: Address;
    response_destination: Address;
};
export declare function storeTupleJettonBurnNotification(source: JettonBurnNotification): TupleItem[];
export declare function dictValueParserJettonBurnNotification(): DictionaryValue<JettonBurnNotification>;
export type WalletData = {
    $$type: 'WalletData';
    balance: bigint;
    owner: Address;
    jetton: Address;
    jetton_wallet_code: Cell;
};
export declare function storeWalletData(src: WalletData): (builder: Builder) => void;
export declare function loadWalletData(slice: Slice): {
    $$type: "WalletData";
    balance: bigint;
    owner: Address;
    jetton: Address;
    jetton_wallet_code: Cell;
};
export declare function loadTupleWalletData(source: TupleReader): {
    $$type: "WalletData";
    balance: bigint;
    owner: Address;
    jetton: Address;
    jetton_wallet_code: Cell;
};
export declare function loadGetterTupleWalletData(source: TupleReader): {
    $$type: "WalletData";
    balance: bigint;
    owner: Address;
    jetton: Address;
    jetton_wallet_code: Cell;
};
export declare function storeTupleWalletData(source: WalletData): TupleItem[];
export declare function dictValueParserWalletData(): DictionaryValue<WalletData>;
export type CreateUserPurchase = {
    $$type: 'CreateUserPurchase';
    user: Address;
    amount: bigint;
    tokens: bigint;
    currency: bigint;
    purchase_method: bigint;
    nonce: bigint;
    round_number: bigint;
    usdt_equivalent_amount: bigint;
};
export declare function storeCreateUserPurchase(src: CreateUserPurchase): (builder: Builder) => void;
export declare function loadCreateUserPurchase(slice: Slice): {
    $$type: "CreateUserPurchase";
    user: Address;
    amount: bigint;
    tokens: bigint;
    currency: bigint;
    purchase_method: bigint;
    nonce: bigint;
    round_number: bigint;
    usdt_equivalent_amount: bigint;
};
export declare function loadTupleCreateUserPurchase(source: TupleReader): {
    $$type: "CreateUserPurchase";
    user: Address;
    amount: bigint;
    tokens: bigint;
    currency: bigint;
    purchase_method: bigint;
    nonce: bigint;
    round_number: bigint;
    usdt_equivalent_amount: bigint;
};
export declare function loadGetterTupleCreateUserPurchase(source: TupleReader): {
    $$type: "CreateUserPurchase";
    user: Address;
    amount: bigint;
    tokens: bigint;
    currency: bigint;
    purchase_method: bigint;
    nonce: bigint;
    round_number: bigint;
    usdt_equivalent_amount: bigint;
};
export declare function storeTupleCreateUserPurchase(source: CreateUserPurchase): TupleItem[];
export declare function dictValueParserCreateUserPurchase(): DictionaryValue<CreateUserPurchase>;
export type Refund = {
    $$type: 'Refund';
    purchase_id: bigint;
};
export declare function storeRefund(src: Refund): (builder: Builder) => void;
export declare function loadRefund(slice: Slice): {
    $$type: "Refund";
    purchase_id: bigint;
};
export declare function loadTupleRefund(source: TupleReader): {
    $$type: "Refund";
    purchase_id: bigint;
};
export declare function loadGetterTupleRefund(source: TupleReader): {
    $$type: "Refund";
    purchase_id: bigint;
};
export declare function storeTupleRefund(source: Refund): TupleItem[];
export declare function dictValueParserRefund(): DictionaryValue<Refund>;
export type ProcessRefund = {
    $$type: 'ProcessRefund';
    user: Address;
    amount: bigint;
    fee: bigint;
    currency: bigint;
    round_number: bigint;
    usdt_equivalent_amount: bigint;
};
export declare function storeProcessRefund(src: ProcessRefund): (builder: Builder) => void;
export declare function loadProcessRefund(slice: Slice): {
    $$type: "ProcessRefund";
    user: Address;
    amount: bigint;
    fee: bigint;
    currency: bigint;
    round_number: bigint;
    usdt_equivalent_amount: bigint;
};
export declare function loadTupleProcessRefund(source: TupleReader): {
    $$type: "ProcessRefund";
    user: Address;
    amount: bigint;
    fee: bigint;
    currency: bigint;
    round_number: bigint;
    usdt_equivalent_amount: bigint;
};
export declare function loadGetterTupleProcessRefund(source: TupleReader): {
    $$type: "ProcessRefund";
    user: Address;
    amount: bigint;
    fee: bigint;
    currency: bigint;
    round_number: bigint;
    usdt_equivalent_amount: bigint;
};
export declare function storeTupleProcessRefund(source: ProcessRefund): TupleItem[];
export declare function dictValueParserProcessRefund(): DictionaryValue<ProcessRefund>;
export type PurchaseRecord = {
    $$type: 'PurchaseRecord';
    id: bigint;
    user: Address;
    amount: bigint;
    tokens: bigint;
    timestamp: bigint;
    currency: bigint;
    purchase_method: bigint;
    nonce: bigint;
    round_number: bigint;
    usdt_equivalent_amount: bigint;
};
export declare function storePurchaseRecord(src: PurchaseRecord): (builder: Builder) => void;
export declare function loadPurchaseRecord(slice: Slice): {
    $$type: "PurchaseRecord";
    id: bigint;
    user: Address;
    amount: bigint;
    tokens: bigint;
    timestamp: bigint;
    currency: bigint;
    purchase_method: bigint;
    nonce: bigint;
    round_number: bigint;
    usdt_equivalent_amount: bigint;
};
export declare function loadTuplePurchaseRecord(source: TupleReader): {
    $$type: "PurchaseRecord";
    id: bigint;
    user: Address;
    amount: bigint;
    tokens: bigint;
    timestamp: bigint;
    currency: bigint;
    purchase_method: bigint;
    nonce: bigint;
    round_number: bigint;
    usdt_equivalent_amount: bigint;
};
export declare function loadGetterTuplePurchaseRecord(source: TupleReader): {
    $$type: "PurchaseRecord";
    id: bigint;
    user: Address;
    amount: bigint;
    tokens: bigint;
    timestamp: bigint;
    currency: bigint;
    purchase_method: bigint;
    nonce: bigint;
    round_number: bigint;
    usdt_equivalent_amount: bigint;
};
export declare function storeTuplePurchaseRecord(source: PurchaseRecord): TupleItem[];
export declare function dictValueParserPurchaseRecord(): DictionaryValue<PurchaseRecord>;
export type UserPurchase$Data = {
    $$type: 'UserPurchase$Data';
    owner: Address;
    auction_address: Address;
    user_address: Address;
    total_purchased: bigint;
    total_paid: bigint;
    purchase_history: Dictionary<bigint, PurchaseRecord>;
    refund_history: Dictionary<bigint, bigint>;
    purchase_id_counter: bigint;
    participated_rounds: Dictionary<bigint, boolean>;
};
export declare function storeUserPurchase$Data(src: UserPurchase$Data): (builder: Builder) => void;
export declare function loadUserPurchase$Data(slice: Slice): {
    $$type: "UserPurchase$Data";
    owner: Address;
    auction_address: Address;
    user_address: Address;
    total_purchased: bigint;
    total_paid: bigint;
    purchase_history: Dictionary<bigint, PurchaseRecord>;
    refund_history: Dictionary<bigint, bigint>;
    purchase_id_counter: bigint;
    participated_rounds: Dictionary<bigint, boolean>;
};
export declare function loadTupleUserPurchase$Data(source: TupleReader): {
    $$type: "UserPurchase$Data";
    owner: Address;
    auction_address: Address;
    user_address: Address;
    total_purchased: bigint;
    total_paid: bigint;
    purchase_history: Dictionary<bigint, PurchaseRecord>;
    refund_history: Dictionary<bigint, bigint>;
    purchase_id_counter: bigint;
    participated_rounds: Dictionary<bigint, boolean>;
};
export declare function loadGetterTupleUserPurchase$Data(source: TupleReader): {
    $$type: "UserPurchase$Data";
    owner: Address;
    auction_address: Address;
    user_address: Address;
    total_purchased: bigint;
    total_paid: bigint;
    purchase_history: Dictionary<bigint, PurchaseRecord>;
    refund_history: Dictionary<bigint, bigint>;
    purchase_id_counter: bigint;
    participated_rounds: Dictionary<bigint, boolean>;
};
export declare function storeTupleUserPurchase$Data(source: UserPurchase$Data): TupleItem[];
export declare function dictValueParserUserPurchase$Data(): DictionaryValue<UserPurchase$Data>;
export type StartAuction = {
    $$type: 'StartAuction';
    start_time: bigint;
    end_time: bigint;
    soft_cap: bigint;
    hard_cap: bigint;
    initial_price: bigint;
};
export declare function storeStartAuction(src: StartAuction): (builder: Builder) => void;
export declare function loadStartAuction(slice: Slice): {
    $$type: "StartAuction";
    start_time: bigint;
    end_time: bigint;
    soft_cap: bigint;
    hard_cap: bigint;
    initial_price: bigint;
};
export declare function loadTupleStartAuction(source: TupleReader): {
    $$type: "StartAuction";
    start_time: bigint;
    end_time: bigint;
    soft_cap: bigint;
    hard_cap: bigint;
    initial_price: bigint;
};
export declare function loadGetterTupleStartAuction(source: TupleReader): {
    $$type: "StartAuction";
    start_time: bigint;
    end_time: bigint;
    soft_cap: bigint;
    hard_cap: bigint;
    initial_price: bigint;
};
export declare function storeTupleStartAuction(source: StartAuction): TupleItem[];
export declare function dictValueParserStartAuction(): DictionaryValue<StartAuction>;
export type UpdateRound = {
    $$type: 'UpdateRound';
    new_price: bigint;
    round_number: bigint;
};
export declare function storeUpdateRound(src: UpdateRound): (builder: Builder) => void;
export declare function loadUpdateRound(slice: Slice): {
    $$type: "UpdateRound";
    new_price: bigint;
    round_number: bigint;
};
export declare function loadTupleUpdateRound(source: TupleReader): {
    $$type: "UpdateRound";
    new_price: bigint;
    round_number: bigint;
};
export declare function loadGetterTupleUpdateRound(source: TupleReader): {
    $$type: "UpdateRound";
    new_price: bigint;
    round_number: bigint;
};
export declare function storeTupleUpdateRound(source: UpdateRound): TupleItem[];
export declare function dictValueParserUpdateRound(): DictionaryValue<UpdateRound>;
export type SetUSDTAddress = {
    $$type: 'SetUSDTAddress';
    usdt_master: Address;
    usdt_wallet: Address;
};
export declare function storeSetUSDTAddress(src: SetUSDTAddress): (builder: Builder) => void;
export declare function loadSetUSDTAddress(slice: Slice): {
    $$type: "SetUSDTAddress";
    usdt_master: Address;
    usdt_wallet: Address;
};
export declare function loadTupleSetUSDTAddress(source: TupleReader): {
    $$type: "SetUSDTAddress";
    usdt_master: Address;
    usdt_wallet: Address;
};
export declare function loadGetterTupleSetUSDTAddress(source: TupleReader): {
    $$type: "SetUSDTAddress";
    usdt_master: Address;
    usdt_wallet: Address;
};
export declare function storeTupleSetUSDTAddress(source: SetUSDTAddress): TupleItem[];
export declare function dictValueParserSetUSDTAddress(): DictionaryValue<SetUSDTAddress>;
export type PurchaseWithSignature = {
    $$type: 'PurchaseWithSignature';
    calculation: PurchaseCalculation;
    signature: Slice;
};
export declare function storePurchaseWithSignature(src: PurchaseWithSignature): (builder: Builder) => void;
export declare function loadPurchaseWithSignature(slice: Slice): {
    $$type: "PurchaseWithSignature";
    calculation: {
        $$type: "PurchaseCalculation";
        user: Address;
        amount: bigint;
        currency: bigint;
        tokens_to_receive: bigint;
        current_price: bigint;
        current_round: bigint;
        timestamp: bigint;
        nonce: bigint;
        usdt_equivalent_amount: bigint;
    };
    signature: Slice;
};
export declare function loadTuplePurchaseWithSignature(source: TupleReader): {
    $$type: "PurchaseWithSignature";
    calculation: {
        $$type: "PurchaseCalculation";
        user: Address;
        amount: bigint;
        currency: bigint;
        tokens_to_receive: bigint;
        current_price: bigint;
        current_round: bigint;
        timestamp: bigint;
        nonce: bigint;
        usdt_equivalent_amount: bigint;
    };
    signature: Slice;
};
export declare function loadGetterTuplePurchaseWithSignature(source: TupleReader): {
    $$type: "PurchaseWithSignature";
    calculation: {
        $$type: "PurchaseCalculation";
        user: Address;
        amount: bigint;
        currency: bigint;
        tokens_to_receive: bigint;
        current_price: bigint;
        current_round: bigint;
        timestamp: bigint;
        nonce: bigint;
        usdt_equivalent_amount: bigint;
    };
    signature: Slice;
};
export declare function storeTuplePurchaseWithSignature(source: PurchaseWithSignature): TupleItem[];
export declare function dictValueParserPurchaseWithSignature(): DictionaryValue<PurchaseWithSignature>;
export type SetSigningKey = {
    $$type: 'SetSigningKey';
    public_key: bigint;
};
export declare function storeSetSigningKey(src: SetSigningKey): (builder: Builder) => void;
export declare function loadSetSigningKey(slice: Slice): {
    $$type: "SetSigningKey";
    public_key: bigint;
};
export declare function loadTupleSetSigningKey(source: TupleReader): {
    $$type: "SetSigningKey";
    public_key: bigint;
};
export declare function loadGetterTupleSetSigningKey(source: TupleReader): {
    $$type: "SetSigningKey";
    public_key: bigint;
};
export declare function storeTupleSetSigningKey(source: SetSigningKey): TupleItem[];
export declare function dictValueParserSetSigningKey(): DictionaryValue<SetSigningKey>;
export type SetMinPurchase = {
    $$type: 'SetMinPurchase';
    min_purchase: bigint;
};
export declare function storeSetMinPurchase(src: SetMinPurchase): (builder: Builder) => void;
export declare function loadSetMinPurchase(slice: Slice): {
    $$type: "SetMinPurchase";
    min_purchase: bigint;
};
export declare function loadTupleSetMinPurchase(source: TupleReader): {
    $$type: "SetMinPurchase";
    min_purchase: bigint;
};
export declare function loadGetterTupleSetMinPurchase(source: TupleReader): {
    $$type: "SetMinPurchase";
    min_purchase: bigint;
};
export declare function storeTupleSetMinPurchase(source: SetMinPurchase): TupleItem[];
export declare function dictValueParserSetMinPurchase(): DictionaryValue<SetMinPurchase>;
export type WithdrawTON = {
    $$type: 'WithdrawTON';
    amount: bigint;
    destination: Address;
};
export declare function storeWithdrawTON(src: WithdrawTON): (builder: Builder) => void;
export declare function loadWithdrawTON(slice: Slice): {
    $$type: "WithdrawTON";
    amount: bigint;
    destination: Address;
};
export declare function loadTupleWithdrawTON(source: TupleReader): {
    $$type: "WithdrawTON";
    amount: bigint;
    destination: Address;
};
export declare function loadGetterTupleWithdrawTON(source: TupleReader): {
    $$type: "WithdrawTON";
    amount: bigint;
    destination: Address;
};
export declare function storeTupleWithdrawTON(source: WithdrawTON): TupleItem[];
export declare function dictValueParserWithdrawTON(): DictionaryValue<WithdrawTON>;
export type WithdrawUSDT = {
    $$type: 'WithdrawUSDT';
    amount: bigint;
    destination: Address;
};
export declare function storeWithdrawUSDT(src: WithdrawUSDT): (builder: Builder) => void;
export declare function loadWithdrawUSDT(slice: Slice): {
    $$type: "WithdrawUSDT";
    amount: bigint;
    destination: Address;
};
export declare function loadTupleWithdrawUSDT(source: TupleReader): {
    $$type: "WithdrawUSDT";
    amount: bigint;
    destination: Address;
};
export declare function loadGetterTupleWithdrawUSDT(source: TupleReader): {
    $$type: "WithdrawUSDT";
    amount: bigint;
    destination: Address;
};
export declare function storeTupleWithdrawUSDT(source: WithdrawUSDT): TupleItem[];
export declare function dictValueParserWithdrawUSDT(): DictionaryValue<WithdrawUSDT>;
export type SetTreasury = {
    $$type: 'SetTreasury';
    treasury_address: Address;
};
export declare function storeSetTreasury(src: SetTreasury): (builder: Builder) => void;
export declare function loadSetTreasury(slice: Slice): {
    $$type: "SetTreasury";
    treasury_address: Address;
};
export declare function loadTupleSetTreasury(source: TupleReader): {
    $$type: "SetTreasury";
    treasury_address: Address;
};
export declare function loadGetterTupleSetTreasury(source: TupleReader): {
    $$type: "SetTreasury";
    treasury_address: Address;
};
export declare function storeTupleSetTreasury(source: SetTreasury): TupleItem[];
export declare function dictValueParserSetTreasury(): DictionaryValue<SetTreasury>;
export type AuctionStarted = {
    $$type: 'AuctionStarted';
    start_time: bigint;
    end_time: bigint;
    soft_cap: bigint;
    hard_cap: bigint;
    initial_price: bigint;
    total_supply: bigint;
};
export declare function storeAuctionStarted(src: AuctionStarted): (builder: Builder) => void;
export declare function loadAuctionStarted(slice: Slice): {
    $$type: "AuctionStarted";
    start_time: bigint;
    end_time: bigint;
    soft_cap: bigint;
    hard_cap: bigint;
    initial_price: bigint;
    total_supply: bigint;
};
export declare function loadTupleAuctionStarted(source: TupleReader): {
    $$type: "AuctionStarted";
    start_time: bigint;
    end_time: bigint;
    soft_cap: bigint;
    hard_cap: bigint;
    initial_price: bigint;
    total_supply: bigint;
};
export declare function loadGetterTupleAuctionStarted(source: TupleReader): {
    $$type: "AuctionStarted";
    start_time: bigint;
    end_time: bigint;
    soft_cap: bigint;
    hard_cap: bigint;
    initial_price: bigint;
    total_supply: bigint;
};
export declare function storeTupleAuctionStarted(source: AuctionStarted): TupleItem[];
export declare function dictValueParserAuctionStarted(): DictionaryValue<AuctionStarted>;
export type PurchaseCompleted = {
    $$type: 'PurchaseCompleted';
    user: Address;
    amount: bigint;
    tokens_received: bigint;
    currency: bigint;
    purchase_method: bigint;
    round_number: bigint;
    nonce: bigint;
    total_raised: bigint;
    total_raised_usdt: bigint;
    total_tokens_sold: bigint;
};
export declare function storePurchaseCompleted(src: PurchaseCompleted): (builder: Builder) => void;
export declare function loadPurchaseCompleted(slice: Slice): {
    $$type: "PurchaseCompleted";
    user: Address;
    amount: bigint;
    tokens_received: bigint;
    currency: bigint;
    purchase_method: bigint;
    round_number: bigint;
    nonce: bigint;
    total_raised: bigint;
    total_raised_usdt: bigint;
    total_tokens_sold: bigint;
};
export declare function loadTuplePurchaseCompleted(source: TupleReader): {
    $$type: "PurchaseCompleted";
    user: Address;
    amount: bigint;
    tokens_received: bigint;
    currency: bigint;
    purchase_method: bigint;
    round_number: bigint;
    nonce: bigint;
    total_raised: bigint;
    total_raised_usdt: bigint;
    total_tokens_sold: bigint;
};
export declare function loadGetterTuplePurchaseCompleted(source: TupleReader): {
    $$type: "PurchaseCompleted";
    user: Address;
    amount: bigint;
    tokens_received: bigint;
    currency: bigint;
    purchase_method: bigint;
    round_number: bigint;
    nonce: bigint;
    total_raised: bigint;
    total_raised_usdt: bigint;
    total_tokens_sold: bigint;
};
export declare function storeTuplePurchaseCompleted(source: PurchaseCompleted): TupleItem[];
export declare function dictValueParserPurchaseCompleted(): DictionaryValue<PurchaseCompleted>;
export type USDTConfigured = {
    $$type: 'USDTConfigured';
    usdt_master: Address;
    usdt_wallet: Address;
    configured_by: Address;
};
export declare function storeUSDTConfigured(src: USDTConfigured): (builder: Builder) => void;
export declare function loadUSDTConfigured(slice: Slice): {
    $$type: "USDTConfigured";
    usdt_master: Address;
    usdt_wallet: Address;
    configured_by: Address;
};
export declare function loadTupleUSDTConfigured(source: TupleReader): {
    $$type: "USDTConfigured";
    usdt_master: Address;
    usdt_wallet: Address;
    configured_by: Address;
};
export declare function loadGetterTupleUSDTConfigured(source: TupleReader): {
    $$type: "USDTConfigured";
    usdt_master: Address;
    usdt_wallet: Address;
    configured_by: Address;
};
export declare function storeTupleUSDTConfigured(source: USDTConfigured): TupleItem[];
export declare function dictValueParserUSDTConfigured(): DictionaryValue<USDTConfigured>;
export type SigningKeySet = {
    $$type: 'SigningKeySet';
    public_key: bigint;
    set_by: Address;
};
export declare function storeSigningKeySet(src: SigningKeySet): (builder: Builder) => void;
export declare function loadSigningKeySet(slice: Slice): {
    $$type: "SigningKeySet";
    public_key: bigint;
    set_by: Address;
};
export declare function loadTupleSigningKeySet(source: TupleReader): {
    $$type: "SigningKeySet";
    public_key: bigint;
    set_by: Address;
};
export declare function loadGetterTupleSigningKeySet(source: TupleReader): {
    $$type: "SigningKeySet";
    public_key: bigint;
    set_by: Address;
};
export declare function storeTupleSigningKeySet(source: SigningKeySet): TupleItem[];
export declare function dictValueParserSigningKeySet(): DictionaryValue<SigningKeySet>;
export type MinPurchaseUpdated = {
    $$type: 'MinPurchaseUpdated';
    old_min_purchase: bigint;
    new_min_purchase: bigint;
    updated_by: Address;
};
export declare function storeMinPurchaseUpdated(src: MinPurchaseUpdated): (builder: Builder) => void;
export declare function loadMinPurchaseUpdated(slice: Slice): {
    $$type: "MinPurchaseUpdated";
    old_min_purchase: bigint;
    new_min_purchase: bigint;
    updated_by: Address;
};
export declare function loadTupleMinPurchaseUpdated(source: TupleReader): {
    $$type: "MinPurchaseUpdated";
    old_min_purchase: bigint;
    new_min_purchase: bigint;
    updated_by: Address;
};
export declare function loadGetterTupleMinPurchaseUpdated(source: TupleReader): {
    $$type: "MinPurchaseUpdated";
    old_min_purchase: bigint;
    new_min_purchase: bigint;
    updated_by: Address;
};
export declare function storeTupleMinPurchaseUpdated(source: MinPurchaseUpdated): TupleItem[];
export declare function dictValueParserMinPurchaseUpdated(): DictionaryValue<MinPurchaseUpdated>;
export type TreasurySet = {
    $$type: 'TreasurySet';
    old_treasury: Address;
    new_treasury: Address;
    set_by: Address;
};
export declare function storeTreasurySet(src: TreasurySet): (builder: Builder) => void;
export declare function loadTreasurySet(slice: Slice): {
    $$type: "TreasurySet";
    old_treasury: Address;
    new_treasury: Address;
    set_by: Address;
};
export declare function loadTupleTreasurySet(source: TupleReader): {
    $$type: "TreasurySet";
    old_treasury: Address;
    new_treasury: Address;
    set_by: Address;
};
export declare function loadGetterTupleTreasurySet(source: TupleReader): {
    $$type: "TreasurySet";
    old_treasury: Address;
    new_treasury: Address;
    set_by: Address;
};
export declare function storeTupleTreasurySet(source: TreasurySet): TupleItem[];
export declare function dictValueParserTreasurySet(): DictionaryValue<TreasurySet>;
export type FundsWithdrawn = {
    $$type: 'FundsWithdrawn';
    currency: bigint;
    amount: bigint;
    destination: Address;
    withdrawn_by: Address;
    remaining_balance: bigint;
};
export declare function storeFundsWithdrawn(src: FundsWithdrawn): (builder: Builder) => void;
export declare function loadFundsWithdrawn(slice: Slice): {
    $$type: "FundsWithdrawn";
    currency: bigint;
    amount: bigint;
    destination: Address;
    withdrawn_by: Address;
    remaining_balance: bigint;
};
export declare function loadTupleFundsWithdrawn(source: TupleReader): {
    $$type: "FundsWithdrawn";
    currency: bigint;
    amount: bigint;
    destination: Address;
    withdrawn_by: Address;
    remaining_balance: bigint;
};
export declare function loadGetterTupleFundsWithdrawn(source: TupleReader): {
    $$type: "FundsWithdrawn";
    currency: bigint;
    amount: bigint;
    destination: Address;
    withdrawn_by: Address;
    remaining_balance: bigint;
};
export declare function storeTupleFundsWithdrawn(source: FundsWithdrawn): TupleItem[];
export declare function dictValueParserFundsWithdrawn(): DictionaryValue<FundsWithdrawn>;
export type RoundUpdated = {
    $$type: 'RoundUpdated';
    old_round: bigint;
    new_round: bigint;
    old_price: bigint;
    new_price: bigint;
    updated_by: Address;
};
export declare function storeRoundUpdated(src: RoundUpdated): (builder: Builder) => void;
export declare function loadRoundUpdated(slice: Slice): {
    $$type: "RoundUpdated";
    old_round: bigint;
    new_round: bigint;
    old_price: bigint;
    new_price: bigint;
    updated_by: Address;
};
export declare function loadTupleRoundUpdated(source: TupleReader): {
    $$type: "RoundUpdated";
    old_round: bigint;
    new_round: bigint;
    old_price: bigint;
    new_price: bigint;
    updated_by: Address;
};
export declare function loadGetterTupleRoundUpdated(source: TupleReader): {
    $$type: "RoundUpdated";
    old_round: bigint;
    new_round: bigint;
    old_price: bigint;
    new_price: bigint;
    updated_by: Address;
};
export declare function storeTupleRoundUpdated(source: RoundUpdated): TupleItem[];
export declare function dictValueParserRoundUpdated(): DictionaryValue<RoundUpdated>;
export type AuctionEnded = {
    $$type: 'AuctionEnded';
    end_reason: bigint;
    final_status: bigint;
    total_raised: bigint;
    total_raised_usdt: bigint;
    total_tokens_sold: bigint;
    end_time: bigint;
};
export declare function storeAuctionEnded(src: AuctionEnded): (builder: Builder) => void;
export declare function loadAuctionEnded(slice: Slice): {
    $$type: "AuctionEnded";
    end_reason: bigint;
    final_status: bigint;
    total_raised: bigint;
    total_raised_usdt: bigint;
    total_tokens_sold: bigint;
    end_time: bigint;
};
export declare function loadTupleAuctionEnded(source: TupleReader): {
    $$type: "AuctionEnded";
    end_reason: bigint;
    final_status: bigint;
    total_raised: bigint;
    total_raised_usdt: bigint;
    total_tokens_sold: bigint;
    end_time: bigint;
};
export declare function loadGetterTupleAuctionEnded(source: TupleReader): {
    $$type: "AuctionEnded";
    end_reason: bigint;
    final_status: bigint;
    total_raised: bigint;
    total_raised_usdt: bigint;
    total_tokens_sold: bigint;
    end_time: bigint;
};
export declare function storeTupleAuctionEnded(source: AuctionEnded): TupleItem[];
export declare function dictValueParserAuctionEnded(): DictionaryValue<AuctionEnded>;
export type AuctionConfig = {
    $$type: 'AuctionConfig';
    start_time: bigint;
    end_time: bigint;
    soft_cap: bigint;
    hard_cap: bigint;
    total_supply: bigint;
    refund_fee_percent: bigint;
};
export declare function storeAuctionConfig(src: AuctionConfig): (builder: Builder) => void;
export declare function loadAuctionConfig(slice: Slice): {
    $$type: "AuctionConfig";
    start_time: bigint;
    end_time: bigint;
    soft_cap: bigint;
    hard_cap: bigint;
    total_supply: bigint;
    refund_fee_percent: bigint;
};
export declare function loadTupleAuctionConfig(source: TupleReader): {
    $$type: "AuctionConfig";
    start_time: bigint;
    end_time: bigint;
    soft_cap: bigint;
    hard_cap: bigint;
    total_supply: bigint;
    refund_fee_percent: bigint;
};
export declare function loadGetterTupleAuctionConfig(source: TupleReader): {
    $$type: "AuctionConfig";
    start_time: bigint;
    end_time: bigint;
    soft_cap: bigint;
    hard_cap: bigint;
    total_supply: bigint;
    refund_fee_percent: bigint;
};
export declare function storeTupleAuctionConfig(source: AuctionConfig): TupleItem[];
export declare function dictValueParserAuctionConfig(): DictionaryValue<AuctionConfig>;
export type RoundStats = {
    $$type: 'RoundStats';
    round_number: bigint;
    start_time: bigint;
    end_time: bigint;
    price: bigint;
    total_raised_ton: bigint;
    total_raised_usdt: bigint;
    raised_usdt_equivalent: bigint;
    tokens_sold: bigint;
    purchase_count: bigint;
    unique_users: bigint;
    refund_count: bigint;
    refunded_amount_ton: bigint;
    refunded_amount_usdt: bigint;
    refunded_usdt_equivalent: bigint;
};
export declare function storeRoundStats(src: RoundStats): (builder: Builder) => void;
export declare function loadRoundStats(slice: Slice): {
    $$type: "RoundStats";
    round_number: bigint;
    start_time: bigint;
    end_time: bigint;
    price: bigint;
    total_raised_ton: bigint;
    total_raised_usdt: bigint;
    raised_usdt_equivalent: bigint;
    tokens_sold: bigint;
    purchase_count: bigint;
    unique_users: bigint;
    refund_count: bigint;
    refunded_amount_ton: bigint;
    refunded_amount_usdt: bigint;
    refunded_usdt_equivalent: bigint;
};
export declare function loadTupleRoundStats(source: TupleReader): {
    $$type: "RoundStats";
    round_number: bigint;
    start_time: bigint;
    end_time: bigint;
    price: bigint;
    total_raised_ton: bigint;
    total_raised_usdt: bigint;
    raised_usdt_equivalent: bigint;
    tokens_sold: bigint;
    purchase_count: bigint;
    unique_users: bigint;
    refund_count: bigint;
    refunded_amount_ton: bigint;
    refunded_amount_usdt: bigint;
    refunded_usdt_equivalent: bigint;
};
export declare function loadGetterTupleRoundStats(source: TupleReader): {
    $$type: "RoundStats";
    round_number: bigint;
    start_time: bigint;
    end_time: bigint;
    price: bigint;
    total_raised_ton: bigint;
    total_raised_usdt: bigint;
    raised_usdt_equivalent: bigint;
    tokens_sold: bigint;
    purchase_count: bigint;
    unique_users: bigint;
    refund_count: bigint;
    refunded_amount_ton: bigint;
    refunded_amount_usdt: bigint;
    refunded_usdt_equivalent: bigint;
};
export declare function storeTupleRoundStats(source: RoundStats): TupleItem[];
export declare function dictValueParserRoundStats(): DictionaryValue<RoundStats>;
export type USDTConfig = {
    $$type: 'USDTConfig';
    master_address: Address;
    wallet_address: Address | null;
    decimals: bigint;
};
export declare function storeUSDTConfig(src: USDTConfig): (builder: Builder) => void;
export declare function loadUSDTConfig(slice: Slice): {
    $$type: "USDTConfig";
    master_address: Address;
    wallet_address: Address;
    decimals: bigint;
};
export declare function loadTupleUSDTConfig(source: TupleReader): {
    $$type: "USDTConfig";
    master_address: Address;
    wallet_address: Address;
    decimals: bigint;
};
export declare function loadGetterTupleUSDTConfig(source: TupleReader): {
    $$type: "USDTConfig";
    master_address: Address;
    wallet_address: Address;
    decimals: bigint;
};
export declare function storeTupleUSDTConfig(source: USDTConfig): TupleItem[];
export declare function dictValueParserUSDTConfig(): DictionaryValue<USDTConfig>;
export type PurchaseCalculation = {
    $$type: 'PurchaseCalculation';
    user: Address;
    amount: bigint;
    currency: bigint;
    tokens_to_receive: bigint;
    current_price: bigint;
    current_round: bigint;
    timestamp: bigint;
    nonce: bigint;
    usdt_equivalent_amount: bigint;
};
export declare function storePurchaseCalculation(src: PurchaseCalculation): (builder: Builder) => void;
export declare function loadPurchaseCalculation(slice: Slice): {
    $$type: "PurchaseCalculation";
    user: Address;
    amount: bigint;
    currency: bigint;
    tokens_to_receive: bigint;
    current_price: bigint;
    current_round: bigint;
    timestamp: bigint;
    nonce: bigint;
    usdt_equivalent_amount: bigint;
};
export declare function loadTuplePurchaseCalculation(source: TupleReader): {
    $$type: "PurchaseCalculation";
    user: Address;
    amount: bigint;
    currency: bigint;
    tokens_to_receive: bigint;
    current_price: bigint;
    current_round: bigint;
    timestamp: bigint;
    nonce: bigint;
    usdt_equivalent_amount: bigint;
};
export declare function loadGetterTuplePurchaseCalculation(source: TupleReader): {
    $$type: "PurchaseCalculation";
    user: Address;
    amount: bigint;
    currency: bigint;
    tokens_to_receive: bigint;
    current_price: bigint;
    current_round: bigint;
    timestamp: bigint;
    nonce: bigint;
    usdt_equivalent_amount: bigint;
};
export declare function storeTuplePurchaseCalculation(source: PurchaseCalculation): TupleItem[];
export declare function dictValueParserPurchaseCalculation(): DictionaryValue<PurchaseCalculation>;
export type ParsedPurchaseData = {
    $$type: 'ParsedPurchaseData';
    calculation: PurchaseCalculation;
    signature: Slice;
};
export declare function storeParsedPurchaseData(src: ParsedPurchaseData): (builder: Builder) => void;
export declare function loadParsedPurchaseData(slice: Slice): {
    $$type: "ParsedPurchaseData";
    calculation: {
        $$type: "PurchaseCalculation";
        user: Address;
        amount: bigint;
        currency: bigint;
        tokens_to_receive: bigint;
        current_price: bigint;
        current_round: bigint;
        timestamp: bigint;
        nonce: bigint;
        usdt_equivalent_amount: bigint;
    };
    signature: Slice;
};
export declare function loadTupleParsedPurchaseData(source: TupleReader): {
    $$type: "ParsedPurchaseData";
    calculation: {
        $$type: "PurchaseCalculation";
        user: Address;
        amount: bigint;
        currency: bigint;
        tokens_to_receive: bigint;
        current_price: bigint;
        current_round: bigint;
        timestamp: bigint;
        nonce: bigint;
        usdt_equivalent_amount: bigint;
    };
    signature: Slice;
};
export declare function loadGetterTupleParsedPurchaseData(source: TupleReader): {
    $$type: "ParsedPurchaseData";
    calculation: {
        $$type: "PurchaseCalculation";
        user: Address;
        amount: bigint;
        currency: bigint;
        tokens_to_receive: bigint;
        current_price: bigint;
        current_round: bigint;
        timestamp: bigint;
        nonce: bigint;
        usdt_equivalent_amount: bigint;
    };
    signature: Slice;
};
export declare function storeTupleParsedPurchaseData(source: ParsedPurchaseData): TupleItem[];
export declare function dictValueParserParsedPurchaseData(): DictionaryValue<ParsedPurchaseData>;
export type OnionAuction$Data = {
    $$type: 'OnionAuction$Data';
    owner: Address;
    stopped: boolean;
    auction_config: AuctionConfig;
    current_round: bigint;
    current_price: bigint;
    total_raised: bigint;
    total_tokens_sold: bigint;
    auction_status: bigint;
    usdt_config: USDTConfig | null;
    total_raised_usdt: bigint;
    total_raised_usdt_equivalent: bigint;
    purchase_count: bigint;
    round_stats: Dictionary<bigint, RoundStats>;
    user_round_purchases: Dictionary<Address, bigint>;
    signing_public_key: bigint;
    used_nonces: Dictionary<bigint, boolean>;
    signature_timeout: bigint;
    min_purchase: bigint;
    treasury_address: Address | null;
};
export declare function storeOnionAuction$Data(src: OnionAuction$Data): (builder: Builder) => void;
export declare function loadOnionAuction$Data(slice: Slice): {
    $$type: "OnionAuction$Data";
    owner: Address;
    stopped: boolean;
    auction_config: {
        $$type: "AuctionConfig";
        start_time: bigint;
        end_time: bigint;
        soft_cap: bigint;
        hard_cap: bigint;
        total_supply: bigint;
        refund_fee_percent: bigint;
    };
    current_round: bigint;
    current_price: bigint;
    total_raised: bigint;
    total_tokens_sold: bigint;
    auction_status: bigint;
    usdt_config: {
        $$type: "USDTConfig";
        master_address: Address;
        wallet_address: Address;
        decimals: bigint;
    };
    total_raised_usdt: bigint;
    total_raised_usdt_equivalent: bigint;
    purchase_count: bigint;
    round_stats: Dictionary<bigint, RoundStats>;
    user_round_purchases: Dictionary<Address, bigint>;
    signing_public_key: bigint;
    used_nonces: Dictionary<bigint, boolean>;
    signature_timeout: bigint;
    min_purchase: bigint;
    treasury_address: Address;
};
export declare function loadTupleOnionAuction$Data(source: TupleReader): {
    $$type: "OnionAuction$Data";
    owner: Address;
    stopped: boolean;
    auction_config: {
        $$type: "AuctionConfig";
        start_time: bigint;
        end_time: bigint;
        soft_cap: bigint;
        hard_cap: bigint;
        total_supply: bigint;
        refund_fee_percent: bigint;
    };
    current_round: bigint;
    current_price: bigint;
    total_raised: bigint;
    total_tokens_sold: bigint;
    auction_status: bigint;
    usdt_config: {
        $$type: "USDTConfig";
        master_address: Address;
        wallet_address: Address;
        decimals: bigint;
    };
    total_raised_usdt: bigint;
    total_raised_usdt_equivalent: bigint;
    purchase_count: bigint;
    round_stats: Dictionary<bigint, RoundStats>;
    user_round_purchases: Dictionary<Address, bigint>;
    signing_public_key: bigint;
    used_nonces: Dictionary<bigint, boolean>;
    signature_timeout: bigint;
    min_purchase: bigint;
    treasury_address: Address;
};
export declare function loadGetterTupleOnionAuction$Data(source: TupleReader): {
    $$type: "OnionAuction$Data";
    owner: Address;
    stopped: boolean;
    auction_config: {
        $$type: "AuctionConfig";
        start_time: bigint;
        end_time: bigint;
        soft_cap: bigint;
        hard_cap: bigint;
        total_supply: bigint;
        refund_fee_percent: bigint;
    };
    current_round: bigint;
    current_price: bigint;
    total_raised: bigint;
    total_tokens_sold: bigint;
    auction_status: bigint;
    usdt_config: {
        $$type: "USDTConfig";
        master_address: Address;
        wallet_address: Address;
        decimals: bigint;
    };
    total_raised_usdt: bigint;
    total_raised_usdt_equivalent: bigint;
    purchase_count: bigint;
    round_stats: Dictionary<bigint, RoundStats>;
    user_round_purchases: Dictionary<Address, bigint>;
    signing_public_key: bigint;
    used_nonces: Dictionary<bigint, boolean>;
    signature_timeout: bigint;
    min_purchase: bigint;
    treasury_address: Address;
};
export declare function storeTupleOnionAuction$Data(source: OnionAuction$Data): TupleItem[];
export declare function dictValueParserOnionAuction$Data(): DictionaryValue<OnionAuction$Data>;
export declare const UserPurchase_errors: {
    readonly 2: {
        readonly message: "Stack underflow";
    };
    readonly 3: {
        readonly message: "Stack overflow";
    };
    readonly 4: {
        readonly message: "Integer overflow";
    };
    readonly 5: {
        readonly message: "Integer out of expected range";
    };
    readonly 6: {
        readonly message: "Invalid opcode";
    };
    readonly 7: {
        readonly message: "Type check error";
    };
    readonly 8: {
        readonly message: "Cell overflow";
    };
    readonly 9: {
        readonly message: "Cell underflow";
    };
    readonly 10: {
        readonly message: "Dictionary error";
    };
    readonly 11: {
        readonly message: "'Unknown' error";
    };
    readonly 12: {
        readonly message: "Fatal error";
    };
    readonly 13: {
        readonly message: "Out of gas error";
    };
    readonly 14: {
        readonly message: "Virtualization error";
    };
    readonly 32: {
        readonly message: "Action list is invalid";
    };
    readonly 33: {
        readonly message: "Action list is too long";
    };
    readonly 34: {
        readonly message: "Action is invalid or not supported";
    };
    readonly 35: {
        readonly message: "Invalid source address in outbound message";
    };
    readonly 36: {
        readonly message: "Invalid destination address in outbound message";
    };
    readonly 37: {
        readonly message: "Not enough Toncoin";
    };
    readonly 38: {
        readonly message: "Not enough extra currencies";
    };
    readonly 39: {
        readonly message: "Outbound message does not fit into a cell after rewriting";
    };
    readonly 40: {
        readonly message: "Cannot process a message";
    };
    readonly 41: {
        readonly message: "Library reference is null";
    };
    readonly 42: {
        readonly message: "Library change action error";
    };
    readonly 43: {
        readonly message: "Exceeded maximum number of cells in the library or the maximum depth of the Merkle tree";
    };
    readonly 50: {
        readonly message: "Account state size exceeded limits";
    };
    readonly 128: {
        readonly message: "Null reference exception";
    };
    readonly 129: {
        readonly message: "Invalid serialization prefix";
    };
    readonly 130: {
        readonly message: "Invalid incoming message";
    };
    readonly 131: {
        readonly message: "Constraints error";
    };
    readonly 132: {
        readonly message: "Access denied";
    };
    readonly 133: {
        readonly message: "Contract stopped";
    };
    readonly 134: {
        readonly message: "Invalid argument";
    };
    readonly 135: {
        readonly message: "Code of a contract was not found";
    };
    readonly 136: {
        readonly message: "Invalid standard address";
    };
    readonly 138: {
        readonly message: "Not a basechain address";
    };
    readonly 2296: {
        readonly message: "JettonWallet: Only Jetton master or Jetton wallet can call this function";
    };
    readonly 13105: {
        readonly message: "JettonWallet: Not enough jettons to transfer";
    };
    readonly 22411: {
        readonly message: "Already refunded";
    };
    readonly 27831: {
        readonly message: "Only owner can call this function";
    };
    readonly 29133: {
        readonly message: "JettonWallet: Not allow negative balance after internal transfer";
    };
    readonly 37185: {
        readonly message: "Not enough funds to transfer";
    };
    readonly 39144: {
        readonly message: "Purchase not found";
    };
    readonly 47048: {
        readonly message: "JettonWallet: Only owner can burn tokens";
    };
    readonly 49729: {
        readonly message: "Unauthorized";
    };
    readonly 53296: {
        readonly message: "Contract not stopped";
    };
    readonly 60354: {
        readonly message: "JettonWallet: Not enough balance to burn tokens";
    };
};
export declare const UserPurchase_errors_backward: {
    readonly "Stack underflow": 2;
    readonly "Stack overflow": 3;
    readonly "Integer overflow": 4;
    readonly "Integer out of expected range": 5;
    readonly "Invalid opcode": 6;
    readonly "Type check error": 7;
    readonly "Cell overflow": 8;
    readonly "Cell underflow": 9;
    readonly "Dictionary error": 10;
    readonly "'Unknown' error": 11;
    readonly "Fatal error": 12;
    readonly "Out of gas error": 13;
    readonly "Virtualization error": 14;
    readonly "Action list is invalid": 32;
    readonly "Action list is too long": 33;
    readonly "Action is invalid or not supported": 34;
    readonly "Invalid source address in outbound message": 35;
    readonly "Invalid destination address in outbound message": 36;
    readonly "Not enough Toncoin": 37;
    readonly "Not enough extra currencies": 38;
    readonly "Outbound message does not fit into a cell after rewriting": 39;
    readonly "Cannot process a message": 40;
    readonly "Library reference is null": 41;
    readonly "Library change action error": 42;
    readonly "Exceeded maximum number of cells in the library or the maximum depth of the Merkle tree": 43;
    readonly "Account state size exceeded limits": 50;
    readonly "Null reference exception": 128;
    readonly "Invalid serialization prefix": 129;
    readonly "Invalid incoming message": 130;
    readonly "Constraints error": 131;
    readonly "Access denied": 132;
    readonly "Contract stopped": 133;
    readonly "Invalid argument": 134;
    readonly "Code of a contract was not found": 135;
    readonly "Invalid standard address": 136;
    readonly "Not a basechain address": 138;
    readonly "JettonWallet: Only Jetton master or Jetton wallet can call this function": 2296;
    readonly "JettonWallet: Not enough jettons to transfer": 13105;
    readonly "Already refunded": 22411;
    readonly "Only owner can call this function": 27831;
    readonly "JettonWallet: Not allow negative balance after internal transfer": 29133;
    readonly "Not enough funds to transfer": 37185;
    readonly "Purchase not found": 39144;
    readonly "JettonWallet: Only owner can burn tokens": 47048;
    readonly Unauthorized: 49729;
    readonly "Contract not stopped": 53296;
    readonly "JettonWallet: Not enough balance to burn tokens": 60354;
};
export declare const UserPurchase_getterMapping: {
    [key: string]: string;
};
export declare const ERROR_UNAUTHORIZED = 62001n;
export declare const ERROR_PURCHASE_NOT_FOUND = 62002n;
export declare const ERROR_ALREADY_REFUNDED = 62003n;
export declare const OP_CREATE_USER_PURCHASE = 1098075148n;
export declare const OP_REFUND = 836089260n;
export declare const OP_PROCESS_REFUND = 929991442n;
export declare const ERROR_AUCTION_NOT_ACTIVE = 51001n;
export declare const ERROR_AUCTION_NOT_STARTED = 51002n;
export declare const ERROR_AUCTION_ENDED = 51003n;
export declare const ERROR_INVALID_CURRENCY = 51004n;
export declare const ERROR_AMOUNT_BELOW_MINIMUM = 51005n;
export declare const ERROR_INSUFFICIENT_TOKENS = 51006n;
export declare const ERROR_USDT_NOT_CONFIGURED = 51007n;
export declare const ERROR_INVALID_USDT_WALLET = 51008n;
export declare const ERROR_SIGNING_KEY_NOT_SET = 51009n;
export declare const ERROR_SIGNATURE_EXPIRED = 51010n;
export declare const ERROR_FUTURE_TIMESTAMP = 51011n;
export declare const ERROR_NONCE_ALREADY_USED = 51012n;
export declare const ERROR_INVALID_SIGNATURE = 51013n;
export declare const ERROR_CURRENCY_MISMATCH = 51014n;
export declare const ERROR_AMOUNT_MISMATCH = 51015n;
export declare const ERROR_USER_MISMATCH = 51016n;
export declare const ERROR_UNAUTHORIZED_REFUND = 51017n;
export declare const ERROR_MIN_PURCHASE_INVALID = 51018n;
export declare const ERROR_WITHDRAWAL_NOT_ALLOWED = 51019n;
export declare const ERROR_INSUFFICIENT_BALANCE = 51020n;
export declare const ERROR_INVALID_WITHDRAWAL_ADDRESS = 51021n;
export declare const ERROR_ROUND_SOFT_CAP_EXCEEDED = 51022n;
export declare const OP_PURCHASE = 3952774220n;
export declare const OP_START_AUCTION = 1082886929n;
export declare const OP_UPDATE_ROUND = 2861271945n;
export declare const OP_SET_USDT_ADDRESS = 2703444734n;
export declare const OP_PURCHASE_WITH_SIGNATURE = 1524770820n;
export declare const OP_SET_SIGNING_KEY = 3315975678n;
export declare const OP_SET_MIN_PURCHASE = 3390874056n;
export declare const OP_WITHDRAW_TON = 3520188881n;
export declare const OP_WITHDRAW_USDT = 3537031890n;
export declare const OP_SET_TREASURY = 3553874899n;
export declare class UserPurchase implements Contract {
    static readonly storageReserve = 0n;
    static readonly ROUND_DURATION = 3600n;
    static readonly PRICE_INCREMENT = 10000000n;
    static readonly SIGNATURE_TIMEOUT = 300n;
    static readonly errors: {
        readonly "Stack underflow": 2;
        readonly "Stack overflow": 3;
        readonly "Integer overflow": 4;
        readonly "Integer out of expected range": 5;
        readonly "Invalid opcode": 6;
        readonly "Type check error": 7;
        readonly "Cell overflow": 8;
        readonly "Cell underflow": 9;
        readonly "Dictionary error": 10;
        readonly "'Unknown' error": 11;
        readonly "Fatal error": 12;
        readonly "Out of gas error": 13;
        readonly "Virtualization error": 14;
        readonly "Action list is invalid": 32;
        readonly "Action list is too long": 33;
        readonly "Action is invalid or not supported": 34;
        readonly "Invalid source address in outbound message": 35;
        readonly "Invalid destination address in outbound message": 36;
        readonly "Not enough Toncoin": 37;
        readonly "Not enough extra currencies": 38;
        readonly "Outbound message does not fit into a cell after rewriting": 39;
        readonly "Cannot process a message": 40;
        readonly "Library reference is null": 41;
        readonly "Library change action error": 42;
        readonly "Exceeded maximum number of cells in the library or the maximum depth of the Merkle tree": 43;
        readonly "Account state size exceeded limits": 50;
        readonly "Null reference exception": 128;
        readonly "Invalid serialization prefix": 129;
        readonly "Invalid incoming message": 130;
        readonly "Constraints error": 131;
        readonly "Access denied": 132;
        readonly "Contract stopped": 133;
        readonly "Invalid argument": 134;
        readonly "Code of a contract was not found": 135;
        readonly "Invalid standard address": 136;
        readonly "Not a basechain address": 138;
        readonly "JettonWallet: Only Jetton master or Jetton wallet can call this function": 2296;
        readonly "JettonWallet: Not enough jettons to transfer": 13105;
        readonly "Already refunded": 22411;
        readonly "Only owner can call this function": 27831;
        readonly "JettonWallet: Not allow negative balance after internal transfer": 29133;
        readonly "Not enough funds to transfer": 37185;
        readonly "Purchase not found": 39144;
        readonly "JettonWallet: Only owner can burn tokens": 47048;
        readonly Unauthorized: 49729;
        readonly "Contract not stopped": 53296;
        readonly "JettonWallet: Not enough balance to burn tokens": 60354;
    };
    static readonly opcodes: {
        ChangeOwner: number;
        ChangeOwnerOk: number;
        Deploy: number;
        DeployOk: number;
        FactoryDeploy: number;
        JettonTransfer: number;
        JettonTransferNotification: number;
        JettonBurn: number;
        JettonExcesses: number;
        JettonInternalTransfer: number;
        JettonBurnNotification: number;
        CreateUserPurchase: number;
        Refund: number;
        ProcessRefund: number;
        StartAuction: number;
        UpdateRound: number;
        SetUSDTAddress: number;
        PurchaseWithSignature: number;
        SetSigningKey: number;
        SetMinPurchase: number;
        WithdrawTON: number;
        WithdrawUSDT: number;
        SetTreasury: number;
        AuctionStarted: number;
        PurchaseCompleted: number;
        USDTConfigured: number;
        SigningKeySet: number;
        MinPurchaseUpdated: number;
        TreasurySet: number;
        FundsWithdrawn: number;
        RoundUpdated: number;
        AuctionEnded: number;
    };
    static init(auction_address: Address, user_address: Address): Promise<{
        code: Cell;
        data: Cell;
    }>;
    static fromInit(auction_address: Address, user_address: Address): Promise<UserPurchase>;
    static fromAddress(address: Address): UserPurchase;
    readonly address: Address;
    readonly init?: {
        code: Cell;
        data: Cell;
    };
    readonly abi: ContractABI;
    constructor(address: Address, init?: {
        code: Cell;
        data: Cell;
    });
    send(provider: ContractProvider, via: Sender, args: {
        value: bigint;
        bounce?: boolean | null | undefined;
    }, message: "Deploy" | CreateUserPurchase | Refund): Promise<void>;
    getTotalPurchased(provider: ContractProvider): Promise<bigint>;
    getTotalPaid(provider: ContractProvider): Promise<bigint>;
    getPurchaseIdCounter(provider: ContractProvider): Promise<bigint>;
    getPurchaseDetails(provider: ContractProvider, purchase_id: bigint): Promise<{
        $$type: "PurchaseRecord";
        id: bigint;
        user: Address;
        amount: bigint;
        tokens: bigint;
        timestamp: bigint;
        currency: bigint;
        purchase_method: bigint;
        nonce: bigint;
        round_number: bigint;
        usdt_equivalent_amount: bigint;
    }>;
    getIsRefunded(provider: ContractProvider, purchase_id: bigint): Promise<boolean>;
    getSignatureVerifiedPurchases(provider: ContractProvider): Promise<bigint>;
    getPurchaseMethodStats(provider: ContractProvider): Promise<Dictionary<bigint, bigint>>;
    getPurchasesByRound(provider: ContractProvider, round_number: bigint): Promise<Dictionary<bigint, PurchaseRecord>>;
    getRoundTotalAmount(provider: ContractProvider, round_number: bigint): Promise<bigint>;
    getRoundTotalTokens(provider: ContractProvider, round_number: bigint): Promise<bigint>;
    getRoundPurchaseCount(provider: ContractProvider, round_number: bigint): Promise<bigint>;
    getParticipatedRounds(provider: ContractProvider): Promise<Dictionary<bigint, boolean>>;
    getParticipatedInRound(provider: ContractProvider, round_number: bigint): Promise<boolean>;
    getRoundCount(provider: ContractProvider): Promise<bigint>;
    getAllRecords(provider: ContractProvider): Promise<Dictionary<bigint, PurchaseRecord>>;
    getOwner(provider: ContractProvider): Promise<Address>;
}
