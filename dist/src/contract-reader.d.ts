interface UserContractData {
    total_purchased: bigint;
    total_paid: bigint;
    purchase_id_counter: bigint;
    all_records: any;
    participated_rounds: any;
    refund_count: bigint;
    total_refunded: bigint;
    user_address: string;
}
interface MainContractData {
    total_raised: bigint;
    total_raised_usdt: bigint;
    total_tokens_sold: bigint;
    purchase_count: bigint;
    current_round: bigint;
    current_price: bigint;
    auction_status: bigint;
    remaining_tokens: bigint;
    auction_info: any;
}
export declare class ContractReader {
    private client;
    private tonClient;
    constructor(client: any);
    getUserContractData(userContractAddress: string, contractToUserMap?: Map<string, string> | null): Promise<UserContractData | null>;
    getMainContractData(contractAddress: string): Promise<MainContractData | null>;
    getRoundStats(contractAddress: string, roundNumber: number): Promise<any>;
    getAllRoundsStats(contractAddress: string): Promise<any>;
    parseStackValue(stack: any, expectedType?: string): any;
    safeBigIntToNumber(value: any): number;
    formatTokens(nanoAmount: any): number;
    formatUSDT(microAmount: any): number;
    formatRefundAmount(amount: any, currency?: string): number;
    extractUserFromContractAddress(contractAddress: string): string;
    normalizeAddress(address: any): string;
}
export {};
