#!/usr/bin/env node

/**
 * 测试退款数据是否正确包含在统计中
 */

import fs from 'fs/promises';
import { StatsCollector } from './src/stats-collector.js';

async function testRefundStats() {
    console.log('🧪 测试退款数据统计...');
    
    try {
        // 1. 检查用户数据缓存中的退款数据
        console.log('\n1. 检查用户数据缓存中的退款数据...');
        const cacheData = await fs.readFile('user_data_cache.json', 'utf-8');
        const cache = JSON.parse(cacheData);
        
        let totalRefundsInCache = 0;
        let totalRefundAmountInCache = 0;
        let usersWithRefundsInCache = 0;
        
        for (const [contractAddress, data] of Object.entries(cache.data || {})) {
            const refundCount = parseInt(data.refund_count || '0');
            const refundAmount = parseFloat(data.total_refunded || '0');
            
            if (refundCount > 0) {
                usersWithRefundsInCache++;
                totalRefundsInCache += refundCount;
                totalRefundAmountInCache += refundAmount;
                
                console.log(`  ${contractAddress}: ${refundCount} 笔退款, ${(refundAmount / 1e9).toFixed(4)} TON`);
            }
        }
        
        console.log(`\n缓存中的退款统计:`);
        console.log(`  有退款的用户: ${usersWithRefundsInCache}`);
        console.log(`  总退款次数: ${totalRefundsInCache}`);
        console.log(`  总退款金额: ${(totalRefundAmountInCache / 1e9).toFixed(4)} TON`);
        
        // 2. 运行统计收集器测试
        console.log('\n2. 测试统计收集器的退款数据处理...');
        const collector = new StatsCollector();
        
        // 模拟处理几个用户的数据
        const testContracts = Object.keys(cache.data).slice(0, 5);
        console.log(`测试处理 ${testContracts.length} 个合约...`);
        
        let testStats = {
            userPurchases: new Map(),
            totalRefunds: 0,
            userPurchasesArray: []
        };
        
        for (const contractAddress of testContracts) {
            const contractData = cache.data[contractAddress];
            const userAddress = contractData.owner || contractAddress;
            
            // 模拟 processUserContractData 的逻辑
            if (!testStats.userPurchases.has(userAddress)) {
                testStats.userPurchases.set(userAddress, {
                    address: userAddress,
                    contractAddress: contractAddress,
                    totalPurchased: 0,
                    totalPaid: 0,
                    purchaseCount: 0,
                    refundCount: 0,
                    totalRefunded: 0,
                    rounds: new Set(),
                    purchases: [],
                    refunds: []
                });
            }
            
            const userStats = testStats.userPurchases.get(userAddress);
            userStats.totalPurchased = collector.safeBigIntToNumber(contractData.total_purchased);
            userStats.totalPaid = collector.safeBigIntToNumber(contractData.total_paid);
            userStats.purchaseCount = collector.safeBigIntToNumber(contractData.purchase_id_counter);
            userStats.refundCount = collector.safeBigIntToNumber(contractData.refund_count || 0);
            userStats.totalRefunded = collector.safeBigIntToNumber(contractData.total_refunded || 0);
            
            testStats.totalRefunds += userStats.refundCount;
            
            console.log(`  ${userAddress}: ${userStats.refundCount} 笔退款, ${(userStats.totalRefunded / 1e9).toFixed(4)} TON`);
        }
        
        // 转换为数组
        testStats.userPurchasesArray = Array.from(testStats.userPurchases.values());
        
        console.log(`\n统计收集器处理结果:`);
        console.log(`  总退款次数: ${testStats.totalRefunds}`);
        console.log(`  处理的用户数: ${testStats.userPurchasesArray.length}`);
        
        // 3. 测试报告生成
        console.log('\n3. 测试报告生成...');
        const refundStats = collector.calculateRefundStats(testStats);
        console.log('退款统计:', refundStats);
        
        const refundDetails = collector.getRefundDetails(testStats);
        console.log(`退款详情: ${refundDetails.length} 个用户有退款`);
        
        // 4. 检查现有的统计文件
        console.log('\n4. 检查现有的统计文件...');
        try {
            const files = await fs.readdir('.');
            const statsFiles = files.filter(f => f.startsWith('stats-report-') && f.endsWith('.json'));
            
            if (statsFiles.length > 0) {
                const latestFile = statsFiles.sort().pop();
                console.log(`检查最新的统计文件: ${latestFile}`);
                
                const statsData = await fs.readFile(latestFile, 'utf-8');
                const stats = JSON.parse(statsData);
                
                console.log(`文件中的退款统计:`);
                console.log(`  总退款次数: ${stats.summary?.totalRefunds || '未找到'}`);
                console.log(`  退款摘要: ${stats.summary?.refundSummary ? JSON.stringify(stats.summary.refundSummary, null, 2) : '未找到'}`);
                console.log(`  退款详情: ${stats.refundDetails?.length || 0} 条记录`);
            } else {
                console.log('未找到现有的统计文件');
            }
        } catch (error) {
            console.log('检查统计文件时出错:', error.message);
        }
        
        // 5. 测试CSV导出
        console.log('\n5. 测试CSV导出功能...');
        try {
            const { CSVExporter } = await import('./src/csv-exporter.js');
            const exporter = new CSVExporter();
            
            // 测试用户统计CSV
            const userCSV = await exporter.exportUserStatsCSV(testStats);
            const lines = userCSV.split('\n');
            console.log(`用户统计CSV: ${lines.length} 行`);
            
            // 检查CSV头部是否包含退款字段
            if (lines.length > 0) {
                const headers = lines[0];
                const hasRefundFields = headers.includes('退款次数') && headers.includes('总退款金额');
                console.log(`CSV包含退款字段: ${hasRefundFields ? '✅' : '❌'}`);
            }
            
            // 测试退款明细CSV
            const refundCSV = await exporter.exportRefundDetailsCSV(testStats);
            const refundLines = refundCSV.split('\n');
            console.log(`退款明细CSV: ${refundLines.length} 行`);
            
        } catch (error) {
            console.log('测试CSV导出时出错:', error.message);
        }
        
        console.log('\n✅ 退款数据统计测试完成!');
        
    } catch (error) {
        console.error('测试过程中出错:', error);
    }
}

// 运行测试
if (import.meta.url === `file://${process.argv[1]}`) {
    testRefundStats().catch(console.error);
}
